using LexAI.Shared.Application.DTOs.LLM;
using LexAI.Shared.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.Text.Json;

namespace LexAI.Shared.Infrastructure.Services;

/// <summary>
/// Service LLM unifié pour tous les modules LexAI
/// Supporte OpenAI, Ollama, et modèles locaux avec fallback automatique
/// </summary>
public class UnifiedLLMService : IUnifiedLLMService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<UnifiedLLMService> _logger;
    private readonly Kernel _kernel;
    private readonly IChatCompletionService _chatService;

    public UnifiedLLMService(IConfiguration configuration, ILogger<UnifiedLLMService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _kernel = CreateKernel();
        _chatService = _kernel.GetRequiredService<IChatCompletionService>();
    }

    /// <summary>
    /// Envoie un prompt au LLM et retourne la réponse
    /// </summary>
    public async Task<LLMResponse> SendPromptAsync(
        string prompt, 
        LLMOptions? options = null, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        options ??= new LLMOptions();

        try
        {
            _logger.LogDebug("Sending prompt to LLM: {PromptLength} characters", prompt.Length);

            var settings = CreateOpenAIPromptExecutionSettings(options);
            var result = await _kernel.InvokePromptAsync(prompt, new(settings), cancellationToken : cancellationToken);

            stopwatch.Stop();

            var tokenUsage = ExtractTokenUsage(result);
            var response = new LLMResponse
            {
                Content = result.ToString(),
                Success = true,
                Error = null,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName(),
                TokensUsed = tokenUsage,
                EstimatedCost = CalculateEstimatedCost(tokenUsage),
                Metadata = ExtractMetaData(result)
            };

            _logger.LogDebug("LLM response received: {ResponseLength} characters, {Tokens} tokens, {Time}ms", 
                response.Content.Length, response.TokensUsed, response.ProcessingTimeMs);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error sending prompt to LLM");

            return new LLMResponse
            {
                Content = string.Empty,
                Success = false,
                Error = ex.Message,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName(),
                TokensUsed = 0,
                EstimatedCost = 0,
                Metadata = new Dictionary<string, object> { { "Exception", ex.GetType().Name } }
            };
        }
    }

    /// <summary>
    /// Envoie une conversation au LLM
    /// </summary>
    public async Task<LLMResponse> SendConversationAsync(
        IEnumerable<LLMMessage> messages, 
        LLMOptions? options = null, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        options ??= new LLMOptions();

        try
        {
            var chatHistory = new ChatHistory();
            
            foreach (var message in messages)
            {
                switch (message.Role.ToLower())
                {
                    case "system":
                        chatHistory.AddSystemMessage(message.Content);
                        break;
                    case "user":
                        chatHistory.AddUserMessage(message.Content);
                        break;
                    case "assistant":
                        chatHistory.AddAssistantMessage(message.Content);
                        break;
                }
            }

            var settings = CreateOpenAIPromptExecutionSettings(options);
            var result = await _chatService.GetChatMessageContentAsync(chatHistory, settings, cancellationToken: cancellationToken);

            stopwatch.Stop();

            var tokenUsage = ExtractTokenUsageFromChatResult(result);
            var response = new LLMResponse
            {
                Content = result.Content ?? string.Empty,
                Success = true,
                Error = null,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName(),
                TokensUsed = tokenUsage,
                EstimatedCost = CalculateEstimatedCost(tokenUsage),
                Metadata = ExtractMetaDataFromChat(result)
            };

            _logger.LogDebug("LLM conversation response: {ResponseLength} characters, {Tokens} tokens, {Time}ms", 
                response.Content.Length, response.TokensUsed, response.ProcessingTimeMs);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error sending conversation to LLM");

            return new LLMResponse
            {
                Content = string.Empty,
                Success = false,
                Error = ex.Message,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName(),
                TokensUsed = 0,
                EstimatedCost = 0,
                Metadata = new Dictionary<string, object> { { "Exception", ex.GetType().Name } }
            };
        }
    }

    /// <summary>
    /// Analyse un texte et extrait des informations structurées
    /// </summary>
    public async Task<T?> AnalyzeStructuredAsync<T>(
        string text, 
        string analysisPrompt, 
        LLMOptions? options = null, 
        CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var prompt = $@"{analysisPrompt}

Texte à analyser:
{text}

Répondez uniquement avec un JSON valide correspondant à la structure demandée.";

            var response = await SendPromptAsync(prompt, options, cancellationToken);
            
            if (!response.Success || string.IsNullOrEmpty(response.Content))
            {
                return null;
            }

            // Nettoyer la réponse pour extraire le JSON
            var jsonContent = ExtractJsonFromResponse(response.Content);

            _logger.LogDebug("Attempting to parse JSON: {JsonContent}", jsonContent.Substring(0, Math.Min(jsonContent.Length, 500)));

            try
            {
                return JsonSerializer.Deserialize<T>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true,
                    ReadCommentHandling = JsonCommentHandling.Skip
                });
            }
            catch (JsonException jsonEx)
            {
                _logger.LogWarning(jsonEx, "Failed to parse JSON response, attempting fallback parsing. Content: {Content}",
                    jsonContent.Substring(0, Math.Min(jsonContent.Length, 200)));

                // Tentative 1: Nettoyage standard
                var cleanedJson = CleanJsonContent(jsonContent);

                try
                {
                    return JsonSerializer.Deserialize<T>(cleanedJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true,
                        ReadCommentHandling = JsonCommentHandling.Skip
                    });
                }
                catch (JsonException)
                {
                    // Tentative 2: Parsing manuel pour les cas extrêmes
                    var manuallyParsed = TryManualJsonParsing(response.Content);
                    if (!string.IsNullOrEmpty(manuallyParsed))
                    {
                        try
                        {
                            return JsonSerializer.Deserialize<T>(manuallyParsed, new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true,
                                AllowTrailingCommas = true,
                                ReadCommentHandling = JsonCommentHandling.Skip
                            });
                        }
                        catch (JsonException)
                        {
                            // Ignore et continue vers l'erreur finale
                        }
                    }

                    _logger.LogError("Failed to parse JSON even after cleaning. Original: {Original}, Cleaned: {Cleaned}",
                        jsonContent.Substring(0, Math.Min(jsonContent.Length, 100)),
                        cleanedJson.Substring(0, Math.Min(cleanedJson.Length, 100)));
                    return null;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing structured data with LLM");
            return null;
        }
    }

    /// <summary>
    /// Vérifie si le service LLM est disponible
    /// </summary>
    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var testResponse = await SendPromptAsync("Test", new LLMOptions { MaxTokens = 10 }, cancellationToken);
            return testResponse.Success;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Obtient les informations sur le modèle actuel
    /// </summary>
    public LLMModelInfo GetModelInfo()
    {
        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";
        var modelName = GetCurrentModelName();

        return new LLMModelInfo
        {
            Type = modelType,
            Name = modelName,
            MaxTokens = int.Parse(_configuration["LLM:MaxTokens"] ?? "4000"),
            SupportsStreaming = modelType.ToLower() == "openai",
            CostPerToken = GetCostPerToken(modelName)
        };
    }

    private Kernel CreateKernel()
    {
        var builder = Kernel.CreateBuilder();
        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";
        var enableFallback = bool.Parse(_configuration["LLM:EnableFallback"] ?? "true");

        try
        {
            switch (modelType.ToLower())
            {
                case "openai":
                    ConfigureOpenAI(builder);
                    break;
                case "ollama":
                    ConfigureOllama(builder);
                    break;
                case "local":
                    ConfigureLocalModel(builder);
                    break;
                default:
                    if (enableFallback)
                    {
                        _logger.LogWarning("Unknown model type {ModelType}, falling back to OpenAI", modelType);
                        ConfigureOpenAI(builder);
                    }
                    else
                    {
                        throw new InvalidOperationException($"Unsupported model type: {modelType}");
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring primary LLM model: {ModelType}", modelType);
            
            if (enableFallback && modelType.ToLower() != "openai")
            {
                _logger.LogInformation("Attempting fallback to OpenAI");
                ConfigureOpenAI(builder);
            }
            else
            {
                throw;
            }
        }

        return builder.Build();
    }

    private void ConfigureOpenAI(IKernelBuilder builder)
    {
        var azureApiKey = _configuration["Azure:OpenAI:ApiKey"];
        var azureEndpoint = _configuration["Azure:OpenAI:Endpoint"];
        var azureDeployment = _configuration["Azure:OpenAI:DeploymentName"];
        var openAiApiKey = _configuration["OpenAI:ApiKey"];
        var model = _configuration["LLM:Model"] ?? "gpt-4";

        // Priorité à Azure OpenAI si configuré
        if (!string.IsNullOrEmpty(azureApiKey) && !string.IsNullOrEmpty(azureEndpoint) && !string.IsNullOrEmpty(azureDeployment))
        {
            builder.AddAzureOpenAIChatCompletion(azureDeployment, azureEndpoint, azureApiKey);
            _logger.LogInformation("Configured Azure OpenAI LLM with deployment: {Deployment} at {Endpoint}", azureDeployment, azureEndpoint);
        }
        else if (!string.IsNullOrEmpty(openAiApiKey))
        {
            builder.AddOpenAIChatCompletion(model, openAiApiKey);
            _logger.LogInformation("Configured OpenAI LLM with model: {Model}", model);
        }
        else
        {
            throw new InvalidOperationException("Either Azure OpenAI or OpenAI API configuration is required");
        }
    }

    private void ConfigureOllama(IKernelBuilder builder)
    {
        var endpoint = _configuration["LLM:OllamaEndpoint"] ?? "http://localhost:11434";
        var model = _configuration["LLM:OllamaModel"] ?? "llama3.2:3b";
        
        var httpClient = new HttpClient();
        httpClient.BaseAddress = new Uri(endpoint);
        httpClient.Timeout = TimeSpan.FromMinutes(5);
        
        builder.AddOpenAIChatCompletion(model, "dummy-key", httpClient: httpClient);
        _logger.LogInformation("Configured Ollama LLM with model: {Model} at {Endpoint}", model, endpoint);
    }

    private void ConfigureLocalModel(IKernelBuilder builder)
    {
        var endpoint = _configuration["LocalEmbedding:BaseUrl"] ?? "http://localhost:8000";
        
        var httpClient = new HttpClient();
        httpClient.BaseAddress = new Uri(endpoint);
        httpClient.Timeout = TimeSpan.FromMinutes(3);
        
        builder.AddOpenAIChatCompletion("local-model", "dummy-key", httpClient: httpClient);
        _logger.LogInformation("Configured local LLM at: {Endpoint}", endpoint);
    }

    private OpenAIPromptExecutionSettings CreateOpenAIPromptExecutionSettings(LLMOptions options)
    {
        return new OpenAIPromptExecutionSettings
        {
            MaxTokens = options.MaxTokens ?? int.Parse(_configuration["LLM:MaxTokens"] ?? "4000"),
            Temperature = options.Temperature ?? double.Parse(_configuration["LLM:Temperature"] ?? "0.7"),
            TopP = options.TopP ?? 1.0,
            FrequencyPenalty = options.FrequencyPenalty ?? 0.0,
            PresencePenalty = options.PresencePenalty ?? 0.0
        };
    }

    private string GetCurrentModelName()
    {
        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";
        return modelType.ToLower() switch
        {
            "openai" => GetOpenAIModelName(),
            "ollama" => _configuration["LLM:OllamaModel"] ?? "llama3.2:3b",
            "local" => "local-model",
            _ => "unknown"
        };
    }

    private string GetOpenAIModelName()
    {
        // Si Azure OpenAI est configuré, utiliser le nom du déploiement
        var azureDeployment = _configuration["Azure:OpenAI:DeploymentName"];
        if (!string.IsNullOrEmpty(azureDeployment))
        {
            return azureDeployment;
        }

        // Sinon, utiliser le modèle OpenAI standard
        return _configuration["LLM:Model"] ?? "gpt-4";
    }

    private int ExtractTokenUsage(FunctionResult result)
    {
        // Essayer d'extraire l'usage des tokens depuis les métadonnées
        if (result.Metadata?.TryGetValue("Usage", out var usage) == true)
        {
            if (usage is Dictionary<string, object> usageDict && 
                usageDict.TryGetValue("TotalTokens", out var totalTokens))
            {
                return Convert.ToInt32(totalTokens);
            }
        }
        
        // Estimation basée sur la longueur du texte (approximation)
        return EstimateTokenCount(result.ToString());
    }

    private int ExtractTokenUsageFromChatResult(ChatMessageContent result)
    {
        // Essayer d'extraire l'usage des tokens depuis les métadonnées
        if (result.Metadata?.TryGetValue("Usage", out var usage) == true)
        {
            if (usage is Dictionary<string, object> usageDict && 
                usageDict.TryGetValue("TotalTokens", out var totalTokens))
            {
                return Convert.ToInt32(totalTokens);
            }
        }
        
        return EstimateTokenCount(result.Content ?? string.Empty);
    }

    private int EstimateTokenCount(string text)
    {
        // Estimation approximative : 1 token ≈ 4 caractères pour l'anglais/français
        return (int)Math.Ceiling(text.Length / 4.0);
    }

    private decimal CalculateEstimatedCost(int tokens)
    {
        var modelName = GetCurrentModelName();
        var costPerToken = GetCostPerToken(modelName);
        return tokens * costPerToken;
    }

    private decimal GetCostPerToken(string modelName)
    {
        // Coûts approximatifs par token (en USD)
        return modelName.ToLower() switch
        {
            "gpt-4" => 0.00003m,
            "gpt-4-turbo-preview" => 0.00001m,
            "gpt-3.5-turbo" => 0.0000015m,
            _ => 0m // Modèles locaux gratuits
        };
    }

    private string ExtractJsonFromResponse(string response)
    {
        try
        {
            // Nettoyer la réponse pour extraire le JSON
            var trimmed = response.Trim();

            // Supprimer les balises markdown si présentes
            if (trimmed.StartsWith("```json"))
            {
                trimmed = trimmed.Substring(7);
            }
            if (trimmed.StartsWith("```"))
            {
                trimmed = trimmed.Substring(3);
            }
            if (trimmed.EndsWith("```"))
            {
                trimmed = trimmed.Substring(0, trimmed.Length - 3);
            }

            trimmed = trimmed.Trim();

            // Chercher le début et la fin du JSON
            var startIndex = trimmed.IndexOf('{');
            var endIndex = trimmed.LastIndexOf('}');

            if (startIndex >= 0 && endIndex > startIndex)
            {
                var jsonContent = trimmed.Substring(startIndex, endIndex - startIndex + 1);

                // Méthode plus robuste pour nettoyer le JSON
                jsonContent = CleanJsonContentAdvanced(jsonContent);

                return jsonContent;
            }

            return trimmed;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting JSON from response");
            return response;
        }
    }

    private string CleanJsonContentAdvanced(string jsonContent)
    {
        try
        {
            // Étape 1: Remplacer les séquences d'échappement problématiques
            var cleaned = jsonContent;

            // Traiter les \\n, \\r, \\t qui apparaissent littéralement
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\\n", " ");
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\\r", " ");
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\\t", " ");

            // Nettoyer les guillemets mal échappés
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\\""", "\"");

            // Supprimer les caractères de contrôle
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");

            // Nettoyer les espaces multiples
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\s+", " ");

            // Corriger les virgules en trop
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @",\s*([}\]])", "$1");

            // S'assurer que les clés sont entre guillemets
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"([{,]\s*)(\w+)(\s*):", "$1\"$2\"$3:");

            return cleaned;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in advanced JSON cleaning");
            return jsonContent;
        }
    }

    private Dictionary<string, object> ExtractMetaData(FunctionResult result)
    {
        var metaData = new Dictionary<string, object>();

        if (result.Metadata != null)
        {
            foreach (var kvp in result.Metadata)
            {
                metaData[kvp.Key] = kvp.Value;
            }
        }

        metaData["ResponseLength"] = result.ToString().Length;
        metaData["Timestamp"] = DateTime.UtcNow;

        return metaData;
    }

    private Dictionary<string, object> ExtractMetaDataFromChat(ChatMessageContent result)
    {
        var metaData = new Dictionary<string, object>();

        if (result.Metadata != null)
        {
            foreach (var kvp in result.Metadata)
            {
                metaData[kvp.Key] = kvp.Value;
            }
        }

        metaData["ResponseLength"] = (result.Content ?? string.Empty).Length;
        metaData["Timestamp"] = DateTime.UtcNow;
        metaData["Role"] = result.Role.ToString();

        return metaData;
    }

    private string CleanJsonContent(string jsonContent)
    {
        // Nettoyage plus agressif du JSON
        var cleaned = jsonContent;

        try
        {
            // Étape 1: Nettoyer les séquences d'échappement problématiques
            cleaned = cleaned
                .Replace("\\n", "\n")    // Convertir les \\n littéraux
                .Replace("\\r", "\r")    // Convertir les \\r littéraux
                .Replace("\\t", "\t")    // Convertir les \\t littéraux
                .Replace("\\\"", "\"");  // Convertir les \\\" littéraux

            // Étape 2: Re-échapper correctement pour JSON
            cleaned = cleaned
                .Replace("\r\n", "\\n")
                .Replace("\n", "\\n")
                .Replace("\r", "\\n")
                .Replace("\t", "\\t")
                .Replace("\"", "\\\"");  // Échapper les guillemets

            // Étape 3: Restaurer la structure JSON
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"\\""(\w+)\\"":", "\"$1\":");
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @":\\""([^\\""]*?)\\""", ":\"$1\"");

            // Étape 4: Supprimer les caractères de contrôle problématiques
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");

            // Étape 5: Corriger les virgules en trop
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @",\s*([}\]])", "$1");

            // Étape 6: Ajouter des guillemets manquants aux clés (si nécessaire)
            cleaned = System.Text.RegularExpressions.Regex.Replace(cleaned, @"([{,]\s*)(\w+)(\s*):", "$1\"$2\"$3:");

            return cleaned;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during aggressive JSON cleaning, returning original content");
            return jsonContent;
        }
    }

    private string TryManualJsonParsing(string content)
    {
        try
        {
            // Méthode de parsing manuel pour les cas où le JSON est complètement cassé
            var lines = content.Split('\n');
            var jsonLines = new List<string>();
            bool inJsonBlock = false;
            int braceCount = 0;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // Détecter le début du JSON
                if (trimmedLine.StartsWith("{") && !inJsonBlock)
                {
                    inJsonBlock = true;
                    braceCount = 0;
                }

                if (inJsonBlock)
                {
                    // Compter les accolades pour détecter la fin
                    braceCount += trimmedLine.Count(c => c == '{');
                    braceCount -= trimmedLine.Count(c => c == '}');

                    // Nettoyer la ligne
                    var cleanedLine = trimmedLine
                        .Replace("\\n", " ")  // Remplacer les \\n par des espaces
                        .Replace("\\r", " ")  // Remplacer les \\r par des espaces
                        .Replace("\\t", " ")  // Remplacer les \\t par des espaces
                        .Trim();

                    if (!string.IsNullOrEmpty(cleanedLine))
                    {
                        jsonLines.Add(cleanedLine);
                    }

                    // Si on a fermé toutes les accolades, on a fini
                    if (braceCount <= 0 && trimmedLine.Contains("}"))
                    {
                        break;
                    }
                }
            }

            if (jsonLines.Count > 0)
            {
                var reconstructedJson = string.Join(" ", jsonLines);

                // Nettoyage final
                reconstructedJson = System.Text.RegularExpressions.Regex.Replace(reconstructedJson, @"\s+", " ");
                reconstructedJson = reconstructedJson.Replace(" , ", ", ").Replace(" : ", ": ");

                return reconstructedJson;
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during manual JSON parsing");
            return string.Empty;
        }
    }
}
