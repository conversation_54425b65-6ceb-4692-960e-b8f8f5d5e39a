using LexAI.Shared.Application.DTOs.LLM;
using LexAI.Shared.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.Text.Json;

namespace LexAI.Shared.Infrastructure.Services;

/// <summary>
/// Service LLM unifié pour tous les modules LexAI
/// Supporte OpenAI, Ollama, et modèles locaux avec fallback automatique
/// </summary>
public class UnifiedLLMService : IUnifiedLLMService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<UnifiedLLMService> _logger;
    private readonly Kernel _kernel;
    private readonly IChatCompletionService _chatService;

    public UnifiedLLMService(IConfiguration configuration, ILogger<UnifiedLLMService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _kernel = CreateKernel();
        _chatService = _kernel.GetRequiredService<IChatCompletionService>();
    }

    /// <summary>
    /// Envoie un prompt au LLM et retourne la réponse
    /// </summary>
    public async Task<LLMResponse> SendPromptAsync(
        string prompt, 
        LLMOptions? options = null, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        options ??= new LLMOptions();

        try
        {
            _logger.LogDebug("Sending prompt to LLM: {PromptLength} characters", prompt.Length);

            var settings = CreateOpenAIPromptExecutionSettings(options);
            var result = await _kernel.InvokePromptAsync(prompt, new(settings), cancellationToken : cancellationToken);

            stopwatch.Stop();

            var response = new LLMResponse
            {
                Content = result.ToString(),
                Success = true,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName(),
                TokensUsed = ExtractTokenUsage(result),
                EstimatedCost = CalculateEstimatedCost(ExtractTokenUsage(result))
            };

            _logger.LogDebug("LLM response received: {ResponseLength} characters, {Tokens} tokens, {Time}ms", 
                response.Content.Length, response.TokensUsed, response.ProcessingTimeMs);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error sending prompt to LLM");

            return new LLMResponse
            {
                Content = string.Empty,
                Success = false,
                Error = ex.Message,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName()
            };
        }
    }

    /// <summary>
    /// Envoie une conversation au LLM
    /// </summary>
    public async Task<LLMResponse> SendConversationAsync(
        IEnumerable<LLMMessage> messages, 
        LLMOptions? options = null, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        options ??= new LLMOptions();

        try
        {
            var chatHistory = new ChatHistory();
            
            foreach (var message in messages)
            {
                switch (message.Role.ToLower())
                {
                    case "system":
                        chatHistory.AddSystemMessage(message.Content);
                        break;
                    case "user":
                        chatHistory.AddUserMessage(message.Content);
                        break;
                    case "assistant":
                        chatHistory.AddAssistantMessage(message.Content);
                        break;
                }
            }

            var settings = CreateOpenAIPromptExecutionSettings(options);
            var result = await _chatService.GetChatMessageContentAsync(chatHistory, settings, cancellationToken: cancellationToken);

            stopwatch.Stop();

            var response = new LLMResponse
            {
                Content = result.Content ?? string.Empty,
                Success = true,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName(),
                TokensUsed = ExtractTokenUsageFromChatResult(result),
                EstimatedCost = CalculateEstimatedCost(ExtractTokenUsageFromChatResult(result))
            };

            _logger.LogDebug("LLM conversation response: {ResponseLength} characters, {Tokens} tokens, {Time}ms", 
                response.Content.Length, response.TokensUsed, response.ProcessingTimeMs);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error sending conversation to LLM");

            return new LLMResponse
            {
                Content = string.Empty,
                Success = false,
                Error = ex.Message,
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ModelUsed = GetCurrentModelName()
            };
        }
    }

    /// <summary>
    /// Analyse un texte et extrait des informations structurées
    /// </summary>
    public async Task<T?> AnalyzeStructuredAsync<T>(
        string text, 
        string analysisPrompt, 
        LLMOptions? options = null, 
        CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var prompt = $@"{analysisPrompt}

Texte à analyser:
{text}

Répondez uniquement avec un JSON valide correspondant à la structure demandée.";

            var response = await SendPromptAsync(prompt, options, cancellationToken);
            
            if (!response.Success || string.IsNullOrEmpty(response.Content))
            {
                return null;
            }

            // Nettoyer la réponse pour extraire le JSON
            var jsonContent = ExtractJsonFromResponse(response.Content);
            
            return JsonSerializer.Deserialize<T>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing structured data with LLM");
            return null;
        }
    }

    /// <summary>
    /// Vérifie si le service LLM est disponible
    /// </summary>
    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var testResponse = await SendPromptAsync("Test", new LLMOptions { MaxTokens = 10 }, cancellationToken);
            return testResponse.Success;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Obtient les informations sur le modèle actuel
    /// </summary>
    public LLMModelInfo GetModelInfo()
    {
        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";
        var modelName = GetCurrentModelName();

        return new LLMModelInfo
        {
            Type = modelType,
            Name = modelName,
            MaxTokens = int.Parse(_configuration["LLM:MaxTokens"] ?? "4000"),
            SupportsStreaming = modelType.ToLower() == "openai",
            CostPerToken = GetCostPerToken(modelName)
        };
    }

    private Kernel CreateKernel()
    {
        var builder = Kernel.CreateBuilder();
        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";
        var enableFallback = bool.Parse(_configuration["LLM:EnableFallback"] ?? "true");

        try
        {
            switch (modelType.ToLower())
            {
                case "openai":
                    ConfigureOpenAI(builder);
                    break;
                case "ollama":
                    ConfigureOllama(builder);
                    break;
                case "local":
                    ConfigureLocalModel(builder);
                    break;
                default:
                    if (enableFallback)
                    {
                        _logger.LogWarning("Unknown model type {ModelType}, falling back to OpenAI", modelType);
                        ConfigureOpenAI(builder);
                    }
                    else
                    {
                        throw new InvalidOperationException($"Unsupported model type: {modelType}");
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring primary LLM model: {ModelType}", modelType);
            
            if (enableFallback && modelType.ToLower() != "openai")
            {
                _logger.LogInformation("Attempting fallback to OpenAI");
                ConfigureOpenAI(builder);
            }
            else
            {
                throw;
            }
        }

        return builder.Build();
    }

    private void ConfigureOpenAI(IKernelBuilder builder)
    {
        var azureApiKey = _configuration["Azure:OpenAI:ApiKey"];
        var azureEndpoint = _configuration["Azure:OpenAI:Endpoint"];
        var azureDeployment = _configuration["Azure:OpenAI:DeploymentName"];
        var openAiApiKey = _configuration["OpenAI:ApiKey"];
        var model = _configuration["LLM:Model"] ?? "gpt-4";

        // Priorité à Azure OpenAI si configuré
        if (!string.IsNullOrEmpty(azureApiKey) && !string.IsNullOrEmpty(azureEndpoint) && !string.IsNullOrEmpty(azureDeployment))
        {
            builder.AddAzureOpenAIChatCompletion(azureDeployment, azureEndpoint, azureApiKey);
            _logger.LogInformation("Configured Azure OpenAI LLM with deployment: {Deployment} at {Endpoint}", azureDeployment, azureEndpoint);
        }
        else if (!string.IsNullOrEmpty(openAiApiKey))
        {
            builder.AddOpenAIChatCompletion(model, openAiApiKey);
            _logger.LogInformation("Configured OpenAI LLM with model: {Model}", model);
        }
        else
        {
            throw new InvalidOperationException("Either Azure OpenAI or OpenAI API configuration is required");
        }
    }

    private void ConfigureOllama(IKernelBuilder builder)
    {
        var endpoint = _configuration["LLM:OllamaEndpoint"] ?? "http://localhost:11434";
        var model = _configuration["LLM:OllamaModel"] ?? "llama3.2:3b";
        
        var httpClient = new HttpClient();
        httpClient.BaseAddress = new Uri(endpoint);
        httpClient.Timeout = TimeSpan.FromMinutes(5);
        
        builder.AddOpenAIChatCompletion(model, "dummy-key", httpClient: httpClient);
        _logger.LogInformation("Configured Ollama LLM with model: {Model} at {Endpoint}", model, endpoint);
    }

    private void ConfigureLocalModel(IKernelBuilder builder)
    {
        var endpoint = _configuration["LocalEmbedding:BaseUrl"] ?? "http://localhost:8000";
        
        var httpClient = new HttpClient();
        httpClient.BaseAddress = new Uri(endpoint);
        httpClient.Timeout = TimeSpan.FromMinutes(3);
        
        builder.AddOpenAIChatCompletion("local-model", "dummy-key", httpClient: httpClient);
        _logger.LogInformation("Configured local LLM at: {Endpoint}", endpoint);
    }

    private OpenAIPromptExecutionSettings CreateOpenAIPromptExecutionSettings(LLMOptions options)
    {
        return new OpenAIPromptExecutionSettings
        {
            MaxTokens = options.MaxTokens ?? int.Parse(_configuration["LLM:MaxTokens"] ?? "4000"),
            Temperature = options.Temperature ?? double.Parse(_configuration["LLM:Temperature"] ?? "0.7"),
            TopP = options.TopP ?? 1.0,
            FrequencyPenalty = options.FrequencyPenalty ?? 0.0,
            PresencePenalty = options.PresencePenalty ?? 0.0
        };
    }

    private string GetCurrentModelName()
    {
        var modelType = _configuration["LLM:ModelType"] ?? "OpenAI";
        return modelType.ToLower() switch
        {
            "openai" => GetOpenAIModelName(),
            "ollama" => _configuration["LLM:OllamaModel"] ?? "llama3.2:3b",
            "local" => "local-model",
            _ => "unknown"
        };
    }

    private string GetOpenAIModelName()
    {
        // Si Azure OpenAI est configuré, utiliser le nom du déploiement
        var azureDeployment = _configuration["Azure:OpenAI:DeploymentName"];
        if (!string.IsNullOrEmpty(azureDeployment))
        {
            return azureDeployment;
        }

        // Sinon, utiliser le modèle OpenAI standard
        return _configuration["LLM:Model"] ?? "gpt-4";
    }

    private int ExtractTokenUsage(FunctionResult result)
    {
        // Essayer d'extraire l'usage des tokens depuis les métadonnées
        if (result.Metadata?.TryGetValue("Usage", out var usage) == true)
        {
            if (usage is Dictionary<string, object> usageDict && 
                usageDict.TryGetValue("TotalTokens", out var totalTokens))
            {
                return Convert.ToInt32(totalTokens);
            }
        }
        
        // Estimation basée sur la longueur du texte (approximation)
        return EstimateTokenCount(result.ToString());
    }

    private int ExtractTokenUsageFromChatResult(ChatMessageContent result)
    {
        // Essayer d'extraire l'usage des tokens depuis les métadonnées
        if (result.Metadata?.TryGetValue("Usage", out var usage) == true)
        {
            if (usage is Dictionary<string, object> usageDict && 
                usageDict.TryGetValue("TotalTokens", out var totalTokens))
            {
                return Convert.ToInt32(totalTokens);
            }
        }
        
        return EstimateTokenCount(result.Content ?? string.Empty);
    }

    private int EstimateTokenCount(string text)
    {
        // Estimation approximative : 1 token ≈ 4 caractères pour l'anglais/français
        return (int)Math.Ceiling(text.Length / 4.0);
    }

    private decimal CalculateEstimatedCost(int tokens)
    {
        var modelName = GetCurrentModelName();
        var costPerToken = GetCostPerToken(modelName);
        return tokens * costPerToken;
    }

    private decimal GetCostPerToken(string modelName)
    {
        // Coûts approximatifs par token (en USD)
        return modelName.ToLower() switch
        {
            "gpt-4" => 0.00003m,
            "gpt-4-turbo-preview" => 0.00001m,
            "gpt-3.5-turbo" => 0.0000015m,
            _ => 0m // Modèles locaux gratuits
        };
    }

    private string ExtractJsonFromResponse(string response)
    {
        // Nettoyer la réponse pour extraire le JSON
        var trimmed = response.Trim();
        
        // Chercher le début et la fin du JSON
        var startIndex = trimmed.IndexOf('{');
        var endIndex = trimmed.LastIndexOf('}');
        
        if (startIndex >= 0 && endIndex > startIndex)
        {
            return trimmed.Substring(startIndex, endIndex - startIndex + 1);
        }
        
        return trimmed;
    }
}
