using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.Entities;
using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using System.Text.Json;

namespace LexAI.LegalResearch.Infrastructure.Repositories;

/// <summary>
/// Legal document repository implementation using DataProcessing service
/// </summary>
public class LegalDocumentRepository : ILegalDocumentRepository
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LegalDocumentRepository> _logger;
    private readonly string _baseUrl;

    /// <summary>
    /// Initializes a new instance of the LegalDocumentRepository
    /// </summary>
    /// <param name="httpClient">HTTP client</param>
    /// <param name="configuration">Configuration</param>
    /// <param name="logger">Logger</param>
    public LegalDocumentRepository(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<LegalDocumentRepository> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _baseUrl = configuration["DataProcessing:BaseUrl"] ?? "http://localhost:5001";
    }

    /// <summary>
    /// Gets a document by ID
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document or null if not found</returns>
    public async Task<LegalDocument?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting document by ID: {DocumentId}", id);

        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents/{id}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDto = JsonSerializer.Deserialize<DocumentDto>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return documentDto != null ? MapToLegalDocument(documentDto) : null;
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return null;
            }
            else
            {
                _logger.LogWarning("Failed to get document {DocumentId}, Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document by ID: {DocumentId}", id);
            return null;
        }
    }

    /// <summary>
    /// Adds a new document
    /// </summary>
    /// <param name="document">Document to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task AddAsync(LegalDocument document, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Adding new document: {DocumentId}", document.Id);

        try
        {
            var documentDto = MapToDocumentDto(document);
            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/api/documents", documentDto, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to add document {DocumentId}, Status: {StatusCode}", document.Id, response.StatusCode);
                throw new InvalidOperationException($"Failed to add document: {response.StatusCode}");
            }

            _logger.LogInformation("Successfully added document: {DocumentId}", document.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding document: {DocumentId}", document.Id);
            throw;
        }
    }

    /// <summary>
    /// Updates a document
    /// </summary>
    /// <param name="document">Document to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task UpdateAsync(LegalDocument document, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating document: {DocumentId}", document.Id);

        try
        {
            var documentDto = MapToDocumentDto(document);
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/api/documents/{document.Id}", documentDto, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to update document {DocumentId}, Status: {StatusCode}", document.Id, response.StatusCode);
                throw new InvalidOperationException($"Failed to update document: {response.StatusCode}");
            }

            _logger.LogInformation("Successfully updated document: {DocumentId}", document.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document: {DocumentId}", document.Id);
            throw;
        }
    }

    /// <summary>
    /// Deletes a document
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Deleting document: {DocumentId}", id);

        try
        {
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/api/documents/{id}", cancellationToken);

            if (!response.IsSuccessStatusCode && response.StatusCode != System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogError("Failed to delete document {DocumentId}, Status: {StatusCode}", id, response.StatusCode);
                throw new InvalidOperationException($"Failed to delete document: {response.StatusCode}");
            }

            _logger.LogInformation("Successfully deleted document: {DocumentId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {DocumentId}", id);
            throw;
        }
    }

    /// <summary>
    /// Gets documents by legal domain
    /// </summary>
    /// <param name="domain">Legal domain</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents in the domain</returns>
    public async Task<IEnumerable<LegalDocument>> GetByDomainAsync(LegalDomain domain, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting documents by domain: {Domain}", domain);

        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents?domain={domain}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            else
            {
                _logger.LogWarning("Failed to get documents by domain {Domain}, Status: {StatusCode}", domain, response.StatusCode);
                return Enumerable.Empty<LegalDocument>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by domain: {Domain}", domain);
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<IEnumerable<LegalDocument>> GetByTypeAsync(DocumentType type, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting documents by type: {Type}", type);
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents?type={type}", cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            return Enumerable.Empty<LegalDocument>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by type: {Type}", type);
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<IEnumerable<LegalDocument>> GetBySourceAsync(string sourceName, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting documents by source: {Source}", sourceName);
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents?source={Uri.EscapeDataString(sourceName)}", cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            return Enumerable.Empty<LegalDocument>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by source: {Source}", sourceName);
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<IEnumerable<LegalDocument>> GetByPublicationDateRangeAsync(DateTime? startDate, DateTime? endDate, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting documents by date range: {StartDate} - {EndDate}", startDate, endDate);
        try
        {
            var queryParams = new List<string>();
            if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
            if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents{queryString}", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            return Enumerable.Empty<LegalDocument>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by date range");
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<IEnumerable<LegalDocument>> GetUnindexedDocumentsAsync(int limit = 100, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting unindexed documents with limit: {Limit}", limit);
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents/unindexed?limit={limit}", cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            return Enumerable.Empty<LegalDocument>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unindexed documents");
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<IEnumerable<LegalDocument>> SearchByContentAsync(string searchText, int limit = 50, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Searching documents by content: {SearchText}", searchText);
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents/search?q={Uri.EscapeDataString(searchText)}&limit={limit}", cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            return Enumerable.Empty<LegalDocument>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching documents by content: {SearchText}", searchText);
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<IEnumerable<LegalDocument>> GetByTagsAsync(IEnumerable<string> tags, bool matchAll = false, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting documents by tags: {Tags}, MatchAll: {MatchAll}", string.Join(",", tags), matchAll);
        try
        {
            var tagQuery = string.Join(",", tags.Select(Uri.EscapeDataString));
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents/tags?tags={tagQuery}&matchAll={matchAll}", cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var documentDtos = JsonSerializer.Deserialize<List<DocumentDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return documentDtos?.Select(MapToLegalDocument) ?? Enumerable.Empty<LegalDocument>();
            }
            return Enumerable.Empty<LegalDocument>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by tags");
            return Enumerable.Empty<LegalDocument>();
        }
    }

    public async Task<DocumentStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting document statistics");
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/documents/statistics", cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var stats = JsonSerializer.Deserialize<DocumentStatistics>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return stats ?? new DocumentStatistics();
            }
            return new DocumentStatistics();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document statistics");
            return new DocumentStatistics();
        }
    }

    private static LegalDocument MapToLegalDocument(DocumentDto dto)
    {
        // This is a simplified mapping - in a real implementation, you'd need proper mapping
        var document = LegalDocument.Create(
            dto.Title ?? "Unknown",
            dto.Content ?? "",
            DocumentType.Other,
            LegalDomain.Other,
            DocumentSource.Create("Unknown", "Unknown", SourceType.Other, AuthorityLevel.European, "Unknown"),
            "system"
        );

        return document;
    }

    private static DocumentDto MapToDocumentDto(LegalDocument document)
    {
        return new DocumentDto
        {
            Id = document.Id,
            Title = document.Title,
            Content = document.Content,
            Summary = document.Summary,
            CreatedAt = document.CreatedAt,
            UpdatedAt = document.UpdatedAt
        };
    }
}

/// <summary>
/// Simple DTO for document data transfer
/// </summary>
public class DocumentDto
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public string? Content { get; set; }
    public string? Summary { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
