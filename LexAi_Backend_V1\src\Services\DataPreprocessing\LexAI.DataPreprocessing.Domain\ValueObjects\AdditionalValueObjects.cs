using LexAI.Shared.Application.DTOs;
using LexAI.Shared.Domain.Common;

namespace LexAI.DataPreprocessing.Domain.ValueObjects;

/// <summary>
/// Named entity value object
/// </summary>
public class NamedEntity : ValueObject
{
    /// <summary>
    /// Entity text
    /// </summary>
    public string Text { get; private set; }

    /// <summary>
    /// Entity type
    /// </summary>
    public EntityType Type { get; private set; }

    /// <summary>
    /// Start position in the text
    /// </summary>
    public int StartPosition { get; private set; }

    /// <summary>
    /// End position in the text
    /// </summary>
    public int EndPosition { get; private set; }

    /// <summary>
    /// Confidence score (0-1)
    /// </summary>
    public double Confidence { get; private set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private NamedEntity()
    {
        Text = string.Empty;
        Metadata = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a named entity
    /// </summary>
    /// <param name="text">Entity text</param>
    /// <param name="type">Entity type</param>
    /// <param name="startPosition">Start position</param>
    /// <param name="endPosition">End position</param>
    /// <param name="confidence">Confidence score</param>
    /// <returns>Named entity</returns>
    public static NamedEntity Create(string text, EntityType type, int startPosition, int endPosition, double confidence)
    {
        if (string.IsNullOrWhiteSpace(text))
            throw new ArgumentException("Text cannot be empty", nameof(text));

        if (startPosition < 0)
            throw new ArgumentException("Start position cannot be negative", nameof(startPosition));

        if (endPosition <= startPosition)
            throw new ArgumentException("End position must be greater than start position", nameof(endPosition));

        if (confidence < 0.0 || confidence > 1.0)
            throw new ArgumentException("Confidence must be between 0 and 1", nameof(confidence));

        return new NamedEntity
        {
            Text = text.Trim(),
            Type = type,
            StartPosition = startPosition,
            EndPosition = endPosition,
            Confidence = confidence,
            Metadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Text;
        yield return Type;
        yield return StartPosition;
        yield return EndPosition;
        yield return Confidence;
    }
}

/// <summary>
/// Vector metadata value object
/// </summary>
public class VectorMetadata : ValueObject
{
    /// <summary>
    /// Vector ID in the database
    /// </summary>
    public string VectorId { get; private set; }

    /// <summary>
    /// Vector database type
    /// </summary>
    public VectorDatabaseType DatabaseType { get; private set; }

    /// <summary>
    /// Collection or index name
    /// </summary>
    public string Collection { get; private set; }

    /// <summary>
    /// Vector dimension
    /// </summary>
    public int Dimension { get; private set; }

    /// <summary>
    /// Embedding model used
    /// </summary>
    public string EmbeddingModel { get; private set; }

    /// <summary>
    /// Legal domain for routing
    /// </summary>
    public LegalDomain Domain { get; private set; }

    /// <summary>
    /// When the vector was created
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> AdditionalMetadata { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private VectorMetadata()
    {
        VectorId = string.Empty;
        Collection = string.Empty;
        EmbeddingModel = string.Empty;
        AdditionalMetadata = new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates vector metadata
    /// </summary>
    /// <param name="vectorId">Vector ID</param>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="dimension">Vector dimension</param>
    /// <param name="embeddingModel">Embedding model</param>
    /// <param name="domain">Legal domain</param>
    /// <returns>Vector metadata</returns>
    public static VectorMetadata Create(
        string vectorId,
        VectorDatabaseType databaseType,
        string collection,
        int dimension,
        string embeddingModel,
        LegalDomain domain)
    {
        if (string.IsNullOrWhiteSpace(vectorId))
            throw new ArgumentException("Vector ID cannot be empty", nameof(vectorId));

        if (string.IsNullOrWhiteSpace(collection))
            throw new ArgumentException("Collection cannot be empty", nameof(collection));

        if (dimension <= 0)
            throw new ArgumentException("Dimension must be positive", nameof(dimension));

        if (string.IsNullOrWhiteSpace(embeddingModel))
            throw new ArgumentException("Embedding model cannot be empty", nameof(embeddingModel));

        return new VectorMetadata
        {
            VectorId = vectorId.Trim(),
            DatabaseType = databaseType,
            Collection = collection.Trim(),
            Dimension = dimension,
            EmbeddingModel = embeddingModel.Trim(),
            Domain = domain,
            CreatedAt = DateTime.UtcNow,
            AdditionalMetadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return VectorId;
        yield return DatabaseType;
        yield return Collection;
        yield return Dimension;
        yield return EmbeddingModel;
        yield return Domain;
    }
}

/// <summary>
/// Processing statistics value object
/// </summary>
public class ProcessingStatistics : ValueObject
{
    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// File name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// Processing status
    /// </summary>
    public DocumentStatus Status { get; set; }

    /// <summary>
    /// Number of chunks created
    /// </summary>
    public int ChunkCount { get; set; }

    /// <summary>
    /// Total tokens
    /// </summary>
    public int TotalTokens { get; set; }

    /// <summary>
    /// Processing time
    /// </summary>
    public TimeSpan? ProcessingTime { get; set; }

    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal EstimatedCost { get; set; }

    /// <summary>
    /// Number of processing steps
    /// </summary>
    public int StepCount { get; set; }

    /// <summary>
    /// Number of errors
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// Whether vectorized
    /// </summary>
    public bool IsVectorized { get; set; }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return DocumentId;
        yield return Status;
        yield return ChunkCount;
        yield return TotalTokens;
        yield return IsVectorized;
    }
}

/// <summary>
/// Chunk statistics value object
/// </summary>
public class ChunkStatistics : ValueObject
{
    /// <summary>
    /// Chunk ID
    /// </summary>
    public Guid ChunkId { get; set; }

    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Sequence number
    /// </summary>
    public int SequenceNumber { get; set; }

    /// <summary>
    /// Token count
    /// </summary>
    public int TokenCount { get; set; }

    /// <summary>
    /// Character count
    /// </summary>
    public int CharacterCount { get; set; }

    /// <summary>
    /// Quality score
    /// </summary>
    public double QualityScore { get; set; }

    /// <summary>
    /// Importance score
    /// </summary>
    public double ImportanceScore { get; set; }

    /// <summary>
    /// Keyword count
    /// </summary>
    public int KeywordCount { get; set; }

    /// <summary>
    /// Entity count
    /// </summary>
    public int EntityCount { get; set; }

    /// <summary>
    /// Whether vectorized
    /// </summary>
    public bool IsVectorized { get; set; }

    /// <summary>
    /// Vector dimension
    /// </summary>
    public int? VectorDimension { get; set; }

    /// <summary>
    /// Domain relevance count
    /// </summary>
    public int DomainRelevanceCount { get; set; }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return ChunkId;
        yield return DocumentId;
        yield return SequenceNumber;
        yield return TokenCount;
        yield return IsVectorized;
    }
}

/// <summary>
/// Chunking configuration value object
/// </summary>
public class ChunkingConfiguration : ValueObject
{
    /// <summary>
    /// Chunking strategy
    /// </summary>
    public ChunkingStrategy Strategy { get; private set; }

    /// <summary>
    /// Maximum chunk size in characters
    /// </summary>
    public int MaxChunkSize { get; private set; }

    /// <summary>
    /// Overlap size between chunks
    /// </summary>
    public int OverlapSize { get; private set; }

    /// <summary>
    /// Minimum chunk size
    /// </summary>
    public int MinChunkSize { get; private set; }

    /// <summary>
    /// Whether to preserve sentence boundaries
    /// </summary>
    public bool PreserveSentences { get; private set; }

    /// <summary>
    /// Whether to preserve paragraph boundaries
    /// </summary>
    public bool PreserveParagraphs { get; private set; }

    /// <summary>
    /// Custom separators for chunking
    /// </summary>
    public List<string> CustomSeparators { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private ChunkingConfiguration()
    {
        CustomSeparators = new List<string>();
    }

    /// <summary>
    /// Creates chunking configuration
    /// </summary>
    /// <param name="strategy">Chunking strategy</param>
    /// <param name="maxChunkSize">Maximum chunk size</param>
    /// <param name="overlapSize">Overlap size</param>
    /// <param name="minChunkSize">Minimum chunk size</param>
    /// <param name="preserveSentences">Preserve sentences</param>
    /// <param name="preserveParagraphs">Preserve paragraphs</param>
    /// <returns>Chunking configuration</returns>
    public static ChunkingConfiguration Create(
        ChunkingStrategy strategy = ChunkingStrategy.Semantic,
        int maxChunkSize = 1000,
        int overlapSize = 200,
        int minChunkSize = 100,
        bool preserveSentences = true,
        bool preserveParagraphs = true)
    {
        if (maxChunkSize <= 0)
            throw new ArgumentException("Max chunk size must be positive", nameof(maxChunkSize));

        if (overlapSize < 0)
            throw new ArgumentException("Overlap size cannot be negative", nameof(overlapSize));

        if (minChunkSize <= 0)
            throw new ArgumentException("Min chunk size must be positive", nameof(minChunkSize));

        if (minChunkSize >= maxChunkSize)
            throw new ArgumentException("Min chunk size must be less than max chunk size");

        if (overlapSize >= maxChunkSize)
            throw new ArgumentException("Overlap size must be less than max chunk size");

        return new ChunkingConfiguration
        {
            Strategy = strategy,
            MaxChunkSize = maxChunkSize,
            OverlapSize = overlapSize,
            MinChunkSize = minChunkSize,
            PreserveSentences = preserveSentences,
            PreserveParagraphs = preserveParagraphs,
            CustomSeparators = new List<string>()
        };
    }

    /// <summary>
    /// Gets the components for value object equality
    /// </summary>
    /// <returns>Equality components</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Strategy;
        yield return MaxChunkSize;
        yield return OverlapSize;
        yield return MinChunkSize;
        yield return PreserveSentences;
        yield return PreserveParagraphs;
    }
}
