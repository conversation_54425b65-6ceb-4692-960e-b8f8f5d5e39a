using LexAI.Shared.Domain.Common;

namespace LexAI.DocumentAnalysis.Domain.Entities;

/// <summary>
/// Résultat d'analyse d'un document juridique
/// </summary>
public class DocumentAnalysisResult : BaseEntity
{
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public string DocumentHash { get; set; } = string.Empty; // Hash du document stocké localement
    public string DocumentStoragePath { get; set; } = string.Empty; // Chemin de stockage local
    public string ExtractedText { get; set; } = string.Empty;
    public string AnalysisContent { get; set; } = string.Empty;
    public DocumentAnalysisStatus Status { get; set; } = DocumentAnalysisStatus.Pending;
    public double ConfidenceScore { get; set; }
    public int ProcessingTimeMs { get; set; }
    public int TokensUsed { get; set; }
    public decimal EstimatedCost { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;

    // Collections
    public List<ClauseAnalysis> Clauses { get; set; } = new();
    public List<RiskAssessment> Risks { get; set; } = new();
    public List<DocumentRecommendation> Recommendations { get; set; } = new();
    public List<ExtractedEntity> Entities { get; set; } = new();
    public List<DocumentCitation> Citations { get; set; } = new();
}

/// <summary>
/// Analyse d'une clause spécifique
/// </summary>
public class ClauseAnalysis : BaseEntity
{
    public Guid DocumentAnalysisResultId { get; set; }
    public string ClauseText { get; set; } = string.Empty;
    public string ClauseType { get; set; } = string.Empty;
    public string Analysis { get; set; } = string.Empty;
    public ClauseRiskLevel RiskLevel { get; set; } = ClauseRiskLevel.Low;
    public double ConfidenceScore { get; set; }
    public int StartPosition { get; set; }
    public int EndPosition { get; set; }
    public string? SuggestedRevision { get; set; }
    public List<string> Tags { get; set; } = new();

    // Navigation property
    public DocumentAnalysisResult DocumentAnalysisResult { get; set; } = null!;
}

/// <summary>
/// Évaluation des risques identifiés
/// </summary>
public class RiskAssessment : BaseEntity
{
    public Guid DocumentAnalysisResultId { get; set; }
    public string RiskType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RiskSeverity Severity { get; set; } = RiskSeverity.Low;
    public double Probability { get; set; }
    public string Impact { get; set; } = string.Empty;
    public string Mitigation { get; set; } = string.Empty;
    public List<string> AffectedClauses { get; set; } = new();

    // Navigation property
    public DocumentAnalysisResult DocumentAnalysisResult { get; set; } = null!;
}

/// <summary>
/// Recommandations pour améliorer le document
/// </summary>
public class DocumentRecommendation : BaseEntity
{
    public Guid DocumentAnalysisResultId { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RecommendationPriority Priority { get; set; } = RecommendationPriority.Medium;
    public string? SuggestedAction { get; set; }
    public string? LegalBasis { get; set; }
    public List<string> RelatedClauses { get; set; } = new();

    // Navigation property
    public DocumentAnalysisResult DocumentAnalysisResult { get; set; } = null!;
}

/// <summary>
/// Entité extraite du document
/// </summary>
public class ExtractedEntity : BaseEntity
{
    public Guid DocumentAnalysisResultId { get; set; }
    public string Text { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public int StartPosition { get; set; }
    public int EndPosition { get; set; }
    public string? NormalizedValue { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    // Navigation property
    public DocumentAnalysisResult DocumentAnalysisResult { get; set; } = null!;
}

/// <summary>
/// Citation juridique trouvée dans l'analyse
/// </summary>
public class DocumentCitation : BaseEntity
{
    public Guid DocumentAnalysisResultId { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string? Url { get; set; }
    public string? Reference { get; set; }
    public double RelevanceScore { get; set; }
    public string Context { get; set; } = string.Empty;

    // Navigation property
    public DocumentAnalysisResult DocumentAnalysisResult { get; set; } = null!;
}

/// <summary>
/// Statut de l'analyse de document
/// </summary>
public enum DocumentAnalysisStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}

/// <summary>
/// Niveau de risque d'une clause
/// </summary>
public enum ClauseRiskLevel
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// Sévérité d'un risque
/// </summary>
public enum RiskSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// Priorité d'une recommandation
/// </summary>
public enum RecommendationPriority
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}
