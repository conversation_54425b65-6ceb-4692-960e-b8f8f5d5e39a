# LexAI Document Analysis Service

## 📋 Vue d'ensemble

Le service d'analyse de documents LexAI utilise **Azure Document Intelligence** pour l'extraction de texte et des **modèles LLM** (Azure OpenAI GPT-4 ou modèles locaux) pour l'analyse juridique avancée. Ce service permet d'analyser des documents juridiques, d'identifier les risques, d'extraire les clauses importantes et de générer des recommandations.

## 🏗️ Architecture

```
Document → Azure Document Intelligence → LLM Analysis → Résultats formatés
```

### Flux de traitement

1. **Upload & Validation** : Validation du document (taille, type, contenu)
2. **Azure Document Intelligence** : Extraction OCR + parsing structuré
3. **LLM Analysis** : Analyse du contenu extrait avec prompts spécialisés
4. **Résultats** : Synthèse, alertes, suggestions formatées

## 🚀 Fonctionnalités

### Analyse de documents
- ✅ **Extraction de texte** avec Azure Document Intelligence
- ✅ **Analyse de clauses** juridiques
- ✅ **Évaluation des risques** automatisée
- ✅ **Recommandations** d'amélioration
- ✅ **Extraction d'entités** (personnes, dates, montants)
- ✅ **Citations juridiques** automatiques
- ✅ **Résumé exécutif** généré par IA

### Types de documents supportés
- 📄 **PDF** (avec OCR si nécessaire)
- 📝 **DOCX/DOC** (Microsoft Word)
- 📋 **TXT** (texte brut)
- 🖼️ **Images** (JPEG, PNG, TIFF) avec OCR

### Fonctionnalités avancées
- 🔄 **Cache intelligent** pour optimiser les performances
- 🔁 **Retry automatique** en cas d'échec
- 📊 **Métriques détaillées** (temps, tokens, coût)
- 🛡️ **Validation robuste** des documents
- 📈 **Monitoring** et health checks

## 🛠️ Configuration

### Azure Document Intelligence

```json
{
  "Azure": {
    "DocumentIntelligence": {
      "Endpoint": "https://your-resource.cognitiveservices.azure.com/",
      "ApiKey": "your-api-key",
      "DefaultModel": "prebuilt-read",
      "ContractAnalysisModel": "prebuilt-contract",
      "MaxFileSizeMB": 50,
      "EnableCaching": true,
      "EnableRetry": true
    }
  }
}
```

### LLM Configuration

```json
{
  "LLM": {
    "DefaultProvider": "AzureOpenAI",
    "Providers": {
      "AzureOpenAI": {
        "Endpoint": "https://your-openai.openai.azure.com/",
        "ApiKey": "your-openai-key",
        "Models": {
          "Chat": "gpt-4"
        }
      },
      "Local": {
        "Endpoint": "http://localhost:11434",
        "Models": {
          "Chat": "llama3.2:3b"
        }
      }
    }
  }
}
```

## 📡 API Endpoints

### Analyse de documents

```http
POST /api/v1/documentanalysis/analyze
Content-Type: multipart/form-data

{
  "documentFile": [file],
  "documentName": "contrat.pdf",
  "options": {
    "extractClauses": true,
    "performRiskAssessment": true,
    "generateRecommendations": true,
    "useAzureDocumentIntelligence": true
  }
}
```

### Récupération des résultats

```http
GET /api/v1/documentanalysis/{analysisId}
```

### Historique des analyses

```http
GET /api/v1/documentanalysis?page=1&pageSize=10&status=completed
```

### Health & Monitoring

```http
GET /api/v1/health
GET /api/v1/health/detailed
GET /api/v1/health/cache/stats
```

## 🔧 Utilisation

### 1. Analyse simple

```csharp
var request = new DocumentAnalysisRequestDto
{
    DocumentName = "contrat.pdf",
    DocumentContent = fileBytes,
    UserId = userId,
    Options = new AnalysisOptions
    {
        ExtractClauses = true,
        PerformRiskAssessment = true,
        GenerateRecommendations = true
    }
};

var result = await documentAnalysisService.AnalyzeDocumentAsync(request);
```

### 2. Analyse avec cache

```csharp
// Le cache est automatiquement géré par le CachedDocumentAnalysisService
var cachedService = serviceProvider.GetService<IDocumentAnalysisService>();
var result = await cachedService.AnalyzeDocumentAsync(request);
```

### 3. Validation de document

```csharp
var validationResult = await validationService.ValidateDocumentAsync(
    documentContent, 
    contentType, 
    fileName
);

if (!validationResult.IsValid)
{
    // Traiter les erreurs
    foreach (var error in validationResult.Errors)
    {
        Console.WriteLine($"Erreur: {error}");
    }
}
```

## 📊 Modèles de données

### DocumentAnalysisResponse

```csharp
public class DocumentAnalysisResponse
{
    public string Id { get; set; }
    public string DocumentName { get; set; }
    public string Status { get; set; }
    public string AnalysisContent { get; set; }
    public double ConfidenceScore { get; set; }
    public int ProcessingTimeMs { get; set; }
    public int TokensUsed { get; set; }
    public decimal EstimatedCost { get; set; }
    
    // Résultats détaillés
    public List<ClauseAnalysis> Clauses { get; set; }
    public List<RiskAssessment> Risks { get; set; }
    public List<DocumentRecommendation> Recommendations { get; set; }
    public List<ExtractedEntity> Entities { get; set; }
    public DocumentSummary Summary { get; set; }
}
```

### Options d'analyse

```csharp
public class AnalysisOptions
{
    public bool ExtractClauses { get; set; } = true;
    public bool PerformRiskAssessment { get; set; } = true;
    public bool GenerateRecommendations { get; set; } = true;
    public bool ExtractEntities { get; set; } = true;
    public bool FindCitations { get; set; } = true;
    public bool UseAzureDocumentIntelligence { get; set; } = true;
    public List<string> FocusAreas { get; set; } = new();
    public string Language { get; set; } = "fr";
}
```

## 🔍 Monitoring

### Métriques disponibles

- **Temps de traitement** par document
- **Utilisation des tokens** LLM
- **Coûts estimés** par analyse
- **Taux de succès** des analyses
- **Performance du cache** (hit/miss ratio)
- **Santé des services** Azure

### Logs structurés

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "Information",
  "message": "Document analysis completed",
  "properties": {
    "DocumentName": "contrat.pdf",
    "ProcessingTimeMs": 5420,
    "TokensUsed": 3200,
    "ConfidenceScore": 0.95,
    "UserId": "user-123"
  }
}
```

## 🚦 Limites et contraintes

### Limites techniques
- **Taille maximale** : 50 MB par document
- **Types supportés** : PDF, DOCX, DOC, TXT, images
- **Rate limiting** : 10 analyses par 5 minutes par utilisateur
- **Timeout** : 120 secondes par analyse

### Limites Azure Document Intelligence
- **Pages** : Jusqu'à 2000 pages par document
- **Résolution** : Minimum 50x50 pixels pour les images
- **Langues** : Support multilingue avec détection automatique

## 🔧 Développement

### Prérequis

1. **.NET 9.0** SDK
2. **Azure Document Intelligence** resource
3. **Azure OpenAI** ou modèle LLM local
4. **PostgreSQL** (optionnel pour persistance)

### Installation

```bash
# Cloner le repository
git clone [repository-url]

# Naviguer vers le service
cd LexAi_Backend_V1/src/Services/DocumentAnalysis

# Restaurer les packages
dotnet restore

# Configurer appsettings.Development.json
cp appsettings.Development.json.example appsettings.Development.json

# Lancer le service
dotnet run --project LexAI.DocumentAnalysis.API
```

### Tests

```bash
# Tests unitaires
dotnet test LexAI.DocumentAnalysis.Tests

# Tests d'intégration
dotnet test LexAI.DocumentAnalysis.IntegrationTests
```

## 📚 Documentation

- [Guide d'intégration Azure Document Intelligence](docs/azure-integration.md)
- [Configuration des modèles LLM](docs/llm-configuration.md)
- [Guide de déploiement](docs/deployment.md)
- [Troubleshooting](docs/troubleshooting.md)

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

---

**LexAI Document Analysis Service** - Analyse intelligente de documents juridiques avec Azure AI
