using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Queries;
using LexAI.DocumentAnalysis.Domain.Entities;
using LexAI.DocumentAnalysis.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Application.Handlers;

/// <summary>
/// Handler pour la query GetUserAnalysesQuery
/// </summary>
public class GetUserAnalysesQueryHandler : IRequestHandler<GetUserAnalysesQuery, DocumentAnalysisListResponseDto>
{
    private readonly IDocumentAnalysisRepository _repository;
    private readonly ILogger<GetUserAnalysesQueryHandler> _logger;

    public GetUserAnalysesQueryHandler(
        IDocumentAnalysisRepository repository,
        ILogger<GetUserAnalysesQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<DocumentAnalysisListResponseDto> Handle(
        GetUserAnalysesQuery request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving analyses for user: {UserId}", request.UserId);

        try
        {
            // Conversion des filtres
            DocumentAnalysisStatus? status = null;
            if (!string.IsNullOrEmpty(request.Status) && Enum.TryParse<DocumentAnalysisStatus>(request.Status, out var parsedStatus))
            {
                status = parsedStatus;
            }

            DateTime? fromDate = null;
            if (!string.IsNullOrEmpty(request.FromDate) && DateTime.TryParse(request.FromDate, out var parsedFromDate))
            {
                fromDate = parsedFromDate;
            }

            DateTime? toDate = null;
            if (!string.IsNullOrEmpty(request.ToDate) && DateTime.TryParse(request.ToDate, out var parsedToDate))
            {
                toDate = parsedToDate;
            }

            // Appel du repository
            var (items, totalCount) = await _repository.GetUserAnalysesAsync(
                request.UserId,
                request.DocumentType,
                status,
                fromDate,
                toDate,
                request.Page,
                request.PageSize,
                request.SortBy,
                request.SortDescending,
                cancellationToken);

            // Mapping vers DTOs
            var analyses = items.Select(a => new DocumentAnalysisSummaryDto
            {
                Id = a.Id,
                DocumentName = a.DocumentName,
                DocumentType = a.DocumentType,
                Status = a.Status.ToString(),
                ConfidenceScore = a.ConfidenceScore,
                RiskCount = a.Risks?.Count ?? 0,
                RecommendationCount = a.Recommendations?.Count ?? 0,
                OverallRiskLevel = a.OverallRiskLevel,
                //OverallRiskLevel = a.Risks?.Any() == true ?
                //    a.Risks.Max(r => r.Severity).ToString() : "Unknown",
                AnalyzedAt = a.AnalyzedAt,
                ProcessingTimeMs = a.ProcessingTimeMs
            }).ToList();

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            return new DocumentAnalysisListResponseDto
            {
                Items = analyses,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user analyses: {UserId}", request.UserId);
            throw;
        }
    }
}
