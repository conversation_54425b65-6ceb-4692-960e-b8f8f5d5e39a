# Configuration Qdrant pour DocumentAnalysis Service
# Optimisée pour le stockage de vecteurs de documents juridiques

service:
  http_port: 6333
  grpc_port: 6334
  enable_cors: true
  max_request_size_mb: 32
  max_workers: 0  # Auto-detect based on CPU cores

storage:
  # Stockage sur disque pour la persistance
  storage_path: /qdrant/storage
  
  # Configuration de la mémoire
  # Optimisé pour les embeddings de documents (768-1536 dimensions)
  memory_threshold_mb: 512
  
  # Configuration des snapshots
  snapshots_path: /qdrant/snapshots
  snapshot_recovery:
    # Récupération automatique depuis les snapshots
    enabled: true

cluster:
  # Configuration pour un déploiement single-node
  enabled: false

log_level: INFO

# Configuration pour les collections de documents
# Les collections seront créées dynamiquement par l'application avec :
# - document_embeddings : embeddings des documents complets
# - clause_embeddings : embeddings des clauses spécifiques  
# - entity_embeddings : embeddings des entités extraites

# Optimisations pour les performances
telemetry_disabled: true

# Configuration de sécurité (à activer en production)
# api_key: your_qdrant_api_key_here

# Limites de ressources
service:
  max_request_size_mb: 32
  grpc_timeout_ms: 30000
  http_timeout_ms: 30000
