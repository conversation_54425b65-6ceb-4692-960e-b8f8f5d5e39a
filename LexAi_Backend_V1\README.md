# LexAI Backend - Assistant Juridique Intelligent

LexAI est une plateforme d'assistance juridique intelligente développée en .NET 9 avec une architecture microservices, capable d'automatiser et d'optimiser toutes les tâches principales d'un professionnel du droit.

## 🏗️ Architecture Microservices

### 📋 Services Principaux

1. **API Gateway** - Point d'entrée unique avec routage et authentification
2. **Identity Service** - Gestion des utilisateurs, authentification et autorisation (RBAC)
3. **Legal Research Service** - Recherche juridique assistée avec RAG
4. **AI Assistant Service** - Chatbot juridique 24/7
5. **Data Processing Service** - Traitement et indexation des documents juridiques
6. **Document Analysis Service** - Analyse de documents et contrats
7. **Document Generator Service** - Génération de documents juridiques
8. **Client Management Service** - Gestion des clients et dossiers (CRM)
9. **Notification Service** - Gestion des notifications et communications
10. **File Storage Service** - Stockage sécurisé des documents

### 🛠️ Stack Technique

- **.NET 9** avec Clean Architecture
- **PostgreSQL** pour les données relationnelles
- **MongoDB et Qdrant** pour les documents et recherche vectorielle
- **Redis** pour le cache et sessions
- **RabbitMQ** pour la messagerie asynchrone
- **Docker** pour la containerisation
- **OpenAI/Azure OpenAI** pour l'IA
- **Semantic Kernel** pour l'orchestration IA
- **Swagger/OpenAPI** pour la documentation des APIs

### 🎯 Modules Fonctionnels MVP

1. **Recherche Juridique Assistée (RAG)**
   - Pose de questions libres
   - Analyse sémantique + recherche vectorielle
   - Réponse avec citation et sources
   - Historique des recherches
2. **Data Processing Service**
   - Upload de documents (PDF, DOCX, TXT)
   - Extraction de texte et métadonnées
   - Classification juridique
   - Chunking sémantique
   - Vectorisation avec OpenAI embeddings
   - Stockage dans MongoDB, Qdrant, Weaviate, Pinecone
   - Traitement en arrière-plan
   - Rate limiting et sécurité
3. **Assistant IA / Chatbot Juridique**
   - Interface chat 24/7
   - Instructions personnalisables
   - Historique des conversations
   - Multi-langue

4. **Analyse de Documents & Contrats**
   - Upload document (PDF/DOCX)
   - Extraction des clauses importantes
   - Repérage des clauses critiques
   - Mise en évidence des zones de flou

5. **Générateur de Documents Juridiques**
   - Sélection de type de document
   - Formulaire intelligent
   - Génération en PDF/Word
   - Historique des documents générés

### 🔐 Sécurité & Conformité

- **RGPD** : Consentement, droit à l'oubli, traçabilité
- **Chiffrement** : Données au repos et en transit
- **Authentification** : JWT avec MFA et RBAC
- **Audit** : Logs d'accès et horodatage
- **Sauvegardes** : Backup automatisés chiffrés

## 🚀 Démarrage Rapide

### Prérequis
- .NET 9 SDK
- Docker Desktop
- Git

### Installation Rapide

```bash
# 1. Cloner le repository
git clone <repository-url>
cd LexAi_Backend_V1

# 2. Configurer l'environnement
cp .env.example .env
# Éditer .env avec vos clés API (minimum OPENAI_API_KEY)

# 3. Démarrer avec les scripts automatisés
# Windows PowerShell
.\scripts\start-dev.ps1 -All

# Linux/Mac
chmod +x scripts/start-dev.sh
./scripts/start-dev.sh all

# 4. Ou manuellement avec Docker Compose
docker-compose up -d --build
```

### Vérification de l'Installation

```bash
# Test de l'API Gateway
curl http://localhost:8080/api/gateway/ping

# Accéder à la documentation
# http://localhost:8080/swagger

# Vérifier les services
.\scripts\test-architecture.ps1 -All  # Windows
./scripts/test-architecture.sh all    # Linux/Mac
```

## 🔗 Accès aux Services

| Service | URL | Description |
|---------|-----|-------------|
| **API Gateway** | http://localhost:8080 | Point d'entrée principal |
| **Swagger Documentation** | http://localhost:8080/swagger | Documentation API complète |
| **Health Checks** | http://localhost:8080/health | Santé des services |
| **RabbitMQ Management** | http://localhost:15672 | Interface de gestion des messages |

### Identifiants par défaut
- **RabbitMQ**: `lexai_user` / `lexai_rabbitmq_password_2024!`

## 📚 Documentation

- **[Guide de Démarrage Rapide](docs/quick-start.md)** - Installation et premiers pas
- **[Architecture Détaillée](docs/architecture/README.md)** - Vue d'ensemble technique
- **[Guide de Développement](docs/development/getting-started.md)** - Conventions et bonnes pratiques
- **API Documentation** : `/swagger` sur chaque service

## 🧪 Tests et Validation

```bash
# Tests automatisés de l'architecture
.\scripts\test-architecture.ps1 -All

# Tests unitaires (quand disponibles)
dotnet test

# Logs en temps réel
docker-compose logs -f
```