// Types communs pour tous les services API

export interface ApiError {
  message: string
  status: number
  details?: any
}

export class ApiException extends Error {
  status: number
  details?: any

  constructor(message: string, status: number, details?: any) {
    super(message)
    this.name = 'ApiException'
    this.status = status
    this.details = details
  }
}

export interface PaginationRequest {
  page?: number
  pageSize?: number
  limit?: number
  offset?: number
}

export interface PaginationResponse<T> {
  items: T[]
  totalCount: number
  page: number
  pageSize: number
  totalPages: number
}

export interface ApiResponse<T = any> {
  data: T
  success: boolean
  message?: string
  errors?: string[]
}

export interface UploadOptions {
  onProgress?: (progress: number) => void
  signal?: AbortSignal
}

export interface RequestOptions extends RequestInit {
  timeout?: number
  retries?: number
}
