﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.6" />
    <PackageReference Include="AspNetCore.HealthChecks.Npgsql" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="MediatR" Version="12.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LexAI.DocumentAnalysis.Application\LexAI.DocumentAnalysis.Application.csproj" />
    <ProjectReference Include="..\LexAI.DocumentAnalysis.Infrastructure\LexAI.DocumentAnalysis.Infrastructure.csproj" />
    <ProjectReference Include="..\..\..\Shared\LexAI.Shared.Infrastructure\LexAI.Shared.Infrastructure.csproj" />
  </ItemGroup>

</Project>
