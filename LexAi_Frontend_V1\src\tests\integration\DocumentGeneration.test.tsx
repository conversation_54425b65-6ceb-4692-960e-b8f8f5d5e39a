import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { DocumentGenerator } from '../../components/document-generation/DocumentGenerator'
import { aiAssistantApi } from '../../services/api'

// Mock de l'API
vi.mock('../../services/api', () => ({
  aiAssistantApi: {
    generateDocument: vi.fn()
  }
}))

const mockGenerationResponse = {
  generatedContent: `CONTRAT DE PRESTATION DE SERVICES

Entre les soussignés :

D'une part,
Monsieur/Madame [NOM DU CLIENT], demeurant [ADRESSE], ci-après dénommé(e) "le Client",

Et d'autre part,
[NOM DE LA SOCIÉTÉ], soci<PERSON>té [FORME JURIDIQUE], au capital de [CAPITAL] euros, immatriculée au RCS de [VILLE] sous le numéro [NUMÉRO SIRET], dont le siège social est situé [ADRESSE], représentée par [NOM DU REPRÉSENTANT], en qualité de [FONCTION], ci-après dénommée "le Prestataire",

Il a été convenu ce qui suit :

ARTICLE 1 - OBJET
Le présent contrat a pour objet la prestation de services de [DESCRIPTION DES SERVICES] par le Prestataire au profit du Client.

ARTICLE 2 - DURÉE
Le présent contrat est conclu pour une durée de [DURÉE], à compter du [DATE DE DÉBUT].

ARTICLE 3 - PRIX ET MODALITÉS DE PAIEMENT
Le prix de la prestation est fixé à [MONTANT] euros TTC.
Le paiement s'effectuera [MODALITÉS DE PAIEMENT].

ARTICLE 4 - OBLIGATIONS DU PRESTATAIRE
Le Prestataire s'engage à :
- Exécuter la prestation avec diligence et professionnalisme
- Respecter les délais convenus
- Informer le Client de tout élément susceptible d'affecter l'exécution de la prestation

ARTICLE 5 - OBLIGATIONS DU CLIENT
Le Client s'engage à :
- Fournir toutes les informations nécessaires à l'exécution de la prestation
- Effectuer les paiements aux échéances convenues
- Collaborer de bonne foi avec le Prestataire

ARTICLE 6 - RÉSILIATION
Le présent contrat peut être résilié par l'une ou l'autre des parties moyennant un préavis de [PRÉAVIS] jours notifié par lettre recommandée avec accusé de réception.

ARTICLE 7 - DROIT APPLICABLE
Le présent contrat est soumis au droit français. Tout litige relatif à son interprétation ou à son exécution sera de la compétence exclusive des tribunaux de [JURIDICTION].

Fait à [LIEU], le [DATE]
En deux exemplaires originaux

Le Client                    Le Prestataire
[SIGNATURE]                  [SIGNATURE]`,
  documentType: 'contract',
  processingTimeMs: 3200,
  tokensUsed: 1800,
  estimatedCost: 0.036
}

describe('DocumentGenerator', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render the document generator form', () => {
    render(<DocumentGenerator />)
    
    expect(screen.getByText('Générateur de Documents Juridiques')).toBeInTheDocument()
    expect(screen.getByText('Type de document *')).toBeInTheDocument()
    expect(screen.getByText('Exigences et spécifications *')).toBeInTheDocument()
    expect(screen.getByText('Modèle de base')).toBeInTheDocument()
    expect(screen.getByText('Juridiction')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /générer le document/i })).toBeInTheDocument()
  })

  it('should show error when trying to generate without required fields', async () => {
    render(<DocumentGenerator />)
    
    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)
    
    await waitFor(() => {
      expect(screen.getByText('Veuillez remplir le type de document et les exigences')).toBeInTheDocument()
    })
  })

  it('should generate document successfully', async () => {
    const mockApi = vi.mocked(aiAssistantApi.generateDocument)
    mockApi.mockResolvedValue(mockGenerationResponse)

    render(<DocumentGenerator />)
    
    // Sélectionner le type de document
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    // Saisir les exigences
    const requirementsTextarea = screen.getByPlaceholderText(/Décrivez en détail ce que doit contenir le document/)
    fireEvent.change(requirementsTextarea, { 
      target: { value: 'Contrat de prestation de services informatiques entre une société et un client particulier' } 
    })

    // Lancer la génération
    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)

    // Vérifier l'état de chargement
    await waitFor(() => {
      expect(screen.getByText('Génération en cours...')).toBeInTheDocument()
    })

    // Attendre les résultats
    await waitFor(() => {
      expect(screen.getByText('Document généré')).toBeInTheDocument()
      expect(screen.getByText(/CONTRAT DE PRESTATION DE SERVICES/)).toBeInTheDocument()
    })

    // Vérifier que l'API a été appelée avec les bons paramètres
    expect(mockApi).toHaveBeenCalledWith({
      documentType: 'contract',
      requirements: 'Contrat de prestation de services informatiques entre une société et un client particulier',
      template: undefined,
      jurisdiction: 'France',
      parameters: undefined
    })
  })

  it('should display contract-specific parameters when contract type is selected', () => {
    render(<DocumentGenerator />)
    
    // Sélectionner le type contrat
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    // Vérifier que les champs spécifiques au contrat apparaissent
    expect(screen.getByText('Paramètres du contrat')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Partie 1')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Partie 2')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Durée')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Montant')).toBeInTheDocument()
  })

  it('should display employment contract parameters when employment type is selected', () => {
    render(<DocumentGenerator />)
    
    // Sélectionner le type contrat de travail
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'employment_contract' } })

    // Vérifier que les champs spécifiques au contrat de travail apparaissent
    expect(screen.getByText('Paramètres du contrat de travail')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Employeur')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Employé')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Poste')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Salaire')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Type de contrat')).toBeInTheDocument()
  })

  it('should display letter parameters when letter type is selected', () => {
    render(<DocumentGenerator />)
    
    // Sélectionner le type lettre
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'letter' } })

    // Vérifier que les champs spécifiques à la lettre apparaissent
    expect(screen.getByText('Paramètres de la lettre')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Expéditeur')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Destinataire')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Objet')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Ton de la lettre')).toBeInTheDocument()
  })

  it('should include parameters in generation request', async () => {
    const mockApi = vi.mocked(aiAssistantApi.generateDocument)
    mockApi.mockResolvedValue(mockGenerationResponse)

    render(<DocumentGenerator />)
    
    // Sélectionner le type contrat
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    // Remplir les exigences
    const requirementsTextarea = screen.getByPlaceholderText(/Décrivez en détail ce que doit contenir le document/)
    fireEvent.change(requirementsTextarea, { 
      target: { value: 'Contrat simple' } 
    })

    // Remplir les paramètres du contrat
    const party1Input = screen.getByPlaceholderText('Partie 1')
    fireEvent.change(party1Input, { target: { value: 'Société ABC' } })

    const party2Input = screen.getByPlaceholderText('Partie 2')
    fireEvent.change(party2Input, { target: { value: 'Client XYZ' } })

    const durationInput = screen.getByPlaceholderText('Durée')
    fireEvent.change(durationInput, { target: { value: '12 mois' } })

    const amountInput = screen.getByPlaceholderText('Montant')
    fireEvent.change(amountInput, { target: { value: '10000 euros' } })

    // Générer le document
    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)

    // Vérifier que l'API a été appelée avec les paramètres
    await waitFor(() => {
      expect(mockApi).toHaveBeenCalledWith({
        documentType: 'contract',
        requirements: 'Contrat simple',
        template: undefined,
        jurisdiction: 'France',
        parameters: {
          party1: 'Société ABC',
          party2: 'Client XYZ',
          duration: '12 mois',
          amount: '10000 euros'
        }
      })
    })
  })

  it('should display generated document with metadata', async () => {
    const mockApi = vi.mocked(aiAssistantApi.generateDocument)
    mockApi.mockResolvedValue(mockGenerationResponse)

    render(<DocumentGenerator />)
    
    // Générer un document
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    const requirementsTextarea = screen.getByPlaceholderText(/Décrivez en détail ce que doit contenir le document/)
    fireEvent.change(requirementsTextarea, { target: { value: 'Test' } })

    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)

    // Attendre les résultats
    await waitFor(() => {
      // Vérifier le contenu généré
      expect(screen.getByText('Document généré')).toBeInTheDocument()
      expect(screen.getByText(/CONTRAT DE PRESTATION DE SERVICES/)).toBeInTheDocument()

      // Vérifier les métadonnées
      expect(screen.getByText('Informations sur la génération')).toBeInTheDocument()
      expect(screen.getByText('contract')).toBeInTheDocument()
      expect(screen.getByText('3200ms')).toBeInTheDocument()
      expect(screen.getByText('1800')).toBeInTheDocument()

      // Vérifier les boutons d'action
      expect(screen.getByRole('button', { name: /copier/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /télécharger/i })).toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    const mockApi = vi.mocked(aiAssistantApi.generateDocument)
    mockApi.mockRejectedValue(new Error('Erreur de génération'))

    render(<DocumentGenerator />)
    
    // Remplir les champs requis
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    const requirementsTextarea = screen.getByPlaceholderText(/Décrivez en détail ce que doit contenir le document/)
    fireEvent.change(requirementsTextarea, { target: { value: 'Test' } })

    // Lancer la génération
    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)

    // Vérifier l'affichage de l'erreur
    await waitFor(() => {
      expect(screen.getByText('Erreur de génération')).toBeInTheDocument()
    })
  })

  it('should call onDocumentGenerated callback when provided', async () => {
    const mockOnDocumentGenerated = vi.fn()
    const mockApi = vi.mocked(aiAssistantApi.generateDocument)
    mockApi.mockResolvedValue(mockGenerationResponse)

    render(<DocumentGenerator onDocumentGenerated={mockOnDocumentGenerated} />)
    
    // Générer un document
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    const requirementsTextarea = screen.getByPlaceholderText(/Décrivez en détail ce que doit contenir le document/)
    fireEvent.change(requirementsTextarea, { target: { value: 'Test' } })

    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)

    // Vérifier que le callback a été appelé
    await waitFor(() => {
      expect(mockOnDocumentGenerated).toHaveBeenCalledWith(mockGenerationResponse)
    })
  })

  it('should copy document content to clipboard', async () => {
    const mockApi = vi.mocked(aiAssistantApi.generateDocument)
    mockApi.mockResolvedValue(mockGenerationResponse)

    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    })

    render(<DocumentGenerator />)
    
    // Générer un document
    const typeSelect = screen.getByDisplayValue('Sélectionner un type de document')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    const requirementsTextarea = screen.getByPlaceholderText(/Décrivez en détail ce que doit contenir le document/)
    fireEvent.change(requirementsTextarea, { target: { value: 'Test' } })

    const generateButton = screen.getByRole('button', { name: /générer le document/i })
    fireEvent.click(generateButton)

    // Attendre que le document soit généré
    await waitFor(() => {
      expect(screen.getByText('Document généré')).toBeInTheDocument()
    })

    // Cliquer sur le bouton copier
    const copyButton = screen.getByRole('button', { name: /copier/i })
    fireEvent.click(copyButton)

    // Vérifier que le clipboard a été utilisé
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(mockGenerationResponse.generatedContent)
  })
})
