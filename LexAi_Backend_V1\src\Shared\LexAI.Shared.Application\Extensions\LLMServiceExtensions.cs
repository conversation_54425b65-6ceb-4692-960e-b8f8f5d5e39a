﻿using LexAI.Shared.Application.DTOs.LLM;
using LexAI.Shared.Application.Utils;

namespace LexAI.Shared.Application.Extensions
{
    /// <summary>
    /// Extensions utiles pour le service LLM
    /// </summary>
    public static class LLMServiceExtensions
    {
        /// <summary>
        /// Crée des options LLM pour l'analyse juridique
        /// </summary>
        public static LLMOptions ForLegalAnalysis(int maxTokens = 4000)
        {
            return new LLMOptions
            {
                MaxTokens = maxTokens,
                Temperature = LLMTemperature.Conservative,
                SystemPrompt = LLMSystemPrompts.LegalAnalysis
            };
        }

        /// <summary>
        /// Crée des options LLM pour la génération de documents
        /// </summary>
        public static LLMOptions ForDocumentGeneration(int maxTokens = 6000)
        {
            return new LLMOptions
            {
                MaxTokens = maxTokens,
                Temperature = LLMTemperature.Balanced,
                SystemPrompt = LLMSystemPrompts.DocumentGeneration
            };
        }

        /// <summary>
        /// Crée des options LLM pour la recherche juridique
        /// </summary>
        public static LLMOptions ForLegalResearch(int maxTokens = 4000)
        {
            return new LLMOptions
            {
                MaxTokens = maxTokens,
                Temperature = LLMTemperature.Conservative,
                SystemPrompt = LLMSystemPrompts.LegalResearch
            };
        }

        /// <summary>
        /// Crée des options LLM pour la classification
        /// </summary>
        public static LLMOptions ForClassification(int maxTokens = 2000)
        {
            return new LLMOptions
            {
                MaxTokens = maxTokens,
                Temperature = LLMTemperature.Deterministic,
                SystemPrompt = LLMSystemPrompts.Classification
            };
        }

        /// <summary>
        /// Crée des options LLM pour le contrôle qualité
        /// </summary>
        public static LLMOptions ForQualityAssurance(int maxTokens = 3000)
        {
            return new LLMOptions
            {
                MaxTokens = maxTokens,
                Temperature = LLMTemperature.Conservative,
                SystemPrompt = LLMSystemPrompts.QualityAssurance
            };
        }
    }
}
