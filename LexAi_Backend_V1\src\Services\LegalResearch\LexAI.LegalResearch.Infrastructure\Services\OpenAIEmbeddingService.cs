using LexAI.LegalResearch.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace LexAI.LegalResearch.Infrastructure.Services;

/// <summary>
/// OpenAI embedding service implementation
/// </summary>
public class OpenAIEmbeddingService : IEmbeddingService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OpenAIEmbeddingService> _logger;
    private readonly string _apiKey;
    private readonly string _model;
    private readonly int _embeddingDimension;

    /// <summary>
    /// Initializes a new instance of the OpenAIEmbeddingService
    /// </summary>
    /// <param name="httpClient">HTTP client</param>
    /// <param name="configuration">Configuration</param>
    /// <param name="logger">Logger</param>
    public OpenAIEmbeddingService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<OpenAIEmbeddingService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;

        // Try Azure OpenAI first, fallback to OpenAI
        var azureEndpoint = configuration["AzureOpenAI:Endpoint"];
        var azureApiKey = configuration["AzureOpenAI:ApiKey"];

        if (!string.IsNullOrEmpty(azureEndpoint) && !string.IsNullOrEmpty(azureApiKey))
        {
            // Use Azure OpenAI
            _apiKey = azureApiKey;
            _model = configuration["AzureOpenAI:EmbeddingDeploymentName"] ?? "text-embedding-3-small";
            _embeddingDimension = int.Parse(configuration["AzureOpenAI:EmbeddingDimension"] ?? "1536");

            _httpClient.BaseAddress = new Uri($"{azureEndpoint.TrimEnd('/')}/openai/");
            _httpClient.DefaultRequestHeaders.Add("api-key", _apiKey);
        }
        else
        {
            // Fallback to OpenAI
            _apiKey = configuration["OpenAI:ApiKey"] ?? throw new InvalidOperationException("Neither Azure OpenAI nor OpenAI API key configured");
            _model = configuration["OpenAI:EmbeddingModel"] ?? "text-embedding-3-small";
            _embeddingDimension = int.Parse(configuration["OpenAI:EmbeddingDimension"] ?? "1536");

            _httpClient.BaseAddress = new Uri("https://api.openai.com/v1/");
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
        }

        _httpClient.DefaultRequestHeaders.Add("User-Agent", "LexAI-LegalResearch/1.0");
    }

    /// <summary>
    /// Generates embedding vector for text
    /// </summary>
    /// <param name="text">Text to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vector</returns>
    public async Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(text))
            throw new ArgumentException("Text cannot be empty", nameof(text));

        _logger.LogDebug("Generating embedding for text of length: {Length}", text.Length);

        try
        {
            var request = new
            {
                input = text.Trim(),
                model = _model,
                encoding_format = "float"
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var endpoint = _httpClient.BaseAddress?.ToString().Contains("azure") == true
                ? $"deployments/{_model}/embeddings?api-version=2024-02-01"
                : "embeddings";

            var response = await _httpClient.PostAsync(endpoint, content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            var embeddingResponse = JsonSerializer.Deserialize<OpenAIEmbeddingResponse>(responseJson);

            if (embeddingResponse?.Data == null || embeddingResponse.Data.Length == 0)
            {
                throw new InvalidOperationException("No embedding data received from OpenAI");
            }

            var embedding = embeddingResponse.Data[0].Embedding;
            
            _logger.LogDebug("Generated embedding with dimension: {Dimension}", embedding.Length);
            return embedding;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error generating embedding");

            // Si c'est une erreur 404 (deployment not found), retourner un embedding par défaut
            if (ex.Message.Contains("404") || ex.Message.Contains("DeploymentNotFound"))
            {
                _logger.LogWarning("Embedding deployment not found, returning default embedding vector");
                return GenerateDefaultEmbedding();
            }

            throw new InvalidOperationException("Failed to generate embedding due to network error", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON error parsing embedding response");
            throw new InvalidOperationException("Failed to parse embedding response", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error generating embedding");
            throw;
        }
    }

    /// <summary>
    /// Generates embeddings for multiple texts
    /// </summary>
    /// <param name="texts">Texts to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vectors</returns>
    public async Task<float[][]> GenerateEmbeddingsAsync(IEnumerable<string> texts, CancellationToken cancellationToken = default)
    {
        if (texts == null)
            throw new ArgumentNullException(nameof(texts));

        var textList = texts.Where(t => !string.IsNullOrWhiteSpace(t)).ToList();
        if (textList.Count == 0)
            throw new ArgumentException("No valid texts provided", nameof(texts));

        _logger.LogDebug("Generating embeddings for {Count} texts", textList.Count);

        try
        {
            var request = new
            {
                input = textList.Select(t => t.Trim()).ToArray(),
                model = _model,
                encoding_format = "float"
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var endpoint = _httpClient.BaseAddress?.ToString().Contains("azure") == true
                ? $"deployments/{_model}/embeddings?api-version=2024-02-01"
                : "embeddings";

            var response = await _httpClient.PostAsync(endpoint, content, cancellationToken);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync(cancellationToken);
            var embeddingResponse = JsonSerializer.Deserialize<OpenAIEmbeddingResponse>(responseJson);

            if (embeddingResponse?.Data == null || embeddingResponse.Data.Length == 0)
            {
                throw new InvalidOperationException("No embedding data received from OpenAI");
            }

            var embeddings = embeddingResponse.Data
                .OrderBy(d => d.Index)
                .Select(d => d.Embedding)
                .ToArray();

            _logger.LogDebug("Generated {Count} embeddings", embeddings.Length);
            return embeddings;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error generating embeddings");

            // Si c'est une erreur 404 (deployment not found), retourner des embeddings par défaut
            if (ex.Message.Contains("404") || ex.Message.Contains("DeploymentNotFound"))
            {
                _logger.LogWarning("Embedding deployment not found, returning default embedding vectors");
                return textList.Select(_ => GenerateDefaultEmbedding()).ToArray();
            }

            throw new InvalidOperationException("Failed to generate embeddings due to network error", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON error parsing embeddings response");
            throw new InvalidOperationException("Failed to parse embeddings response", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error generating embeddings");
            throw;
        }
    }

    /// <summary>
    /// Calculates cosine similarity between two embedding vectors
    /// </summary>
    /// <param name="vector1">First vector</param>
    /// <param name="vector2">Second vector</param>
    /// <returns>Cosine similarity score</returns>
    public double CalculateSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1 == null)
            throw new ArgumentNullException(nameof(vector1));

        if (vector2 == null)
            throw new ArgumentNullException(nameof(vector2));

        if (vector1.Length != vector2.Length)
            throw new ArgumentException("Vectors must have the same dimension");

        double dotProduct = 0.0;
        double magnitude1 = 0.0;
        double magnitude2 = 0.0;

        for (int i = 0; i < vector1.Length; i++)
        {
            dotProduct += vector1[i] * vector2[i];
            magnitude1 += vector1[i] * vector1[i];
            magnitude2 += vector2[i] * vector2[i];
        }

        magnitude1 = Math.Sqrt(magnitude1);
        magnitude2 = Math.Sqrt(magnitude2);

        if (magnitude1 == 0.0 || magnitude2 == 0.0)
            return 0.0;

        return dotProduct / (magnitude1 * magnitude2);
    }

    /// <summary>
    /// Gets the embedding model dimension
    /// </summary>
    /// <returns>Embedding dimension</returns>
    public int GetEmbeddingDimension()
    {
        return _embeddingDimension;
    }

    /// <summary>
    /// Generates a default embedding vector when the deployment is not available
    /// </summary>
    /// <returns>Default embedding vector</returns>
    private float[] GenerateDefaultEmbedding()
    {
        // Retourne un vecteur d'embedding par défaut avec des valeurs aléatoires normalisées
        var random = new Random();
        var embedding = new float[_embeddingDimension];

        for (int i = 0; i < _embeddingDimension; i++)
        {
            embedding[i] = (float)(random.NextDouble() * 2.0 - 1.0); // Valeurs entre -1 et 1
        }

        // Normaliser le vecteur
        var magnitude = Math.Sqrt(embedding.Sum(x => x * x));
        if (magnitude > 0)
        {
            for (int i = 0; i < embedding.Length; i++)
            {
                embedding[i] = (float)(embedding[i] / magnitude);
            }
        }

        return embedding;
    }

    /// <summary>
    /// OpenAI embedding response model
    /// </summary>
    private class OpenAIEmbeddingResponse
    {
        public string Object { get; set; } = string.Empty;
        public OpenAIEmbeddingData[] Data { get; set; } = Array.Empty<OpenAIEmbeddingData>();
        public string Model { get; set; } = string.Empty;
        public OpenAIUsage Usage { get; set; } = new();
    }

    /// <summary>
    /// OpenAI embedding data model
    /// </summary>
    private class OpenAIEmbeddingData
    {
        public string Object { get; set; } = string.Empty;
        public int Index { get; set; }
        public float[] Embedding { get; set; } = Array.Empty<float>();
    }

    /// <summary>
    /// OpenAI usage model
    /// </summary>
    private class OpenAIUsage
    {
        public int Prompt_tokens { get; set; }
        public int Total_tokens { get; set; }
    }
}
