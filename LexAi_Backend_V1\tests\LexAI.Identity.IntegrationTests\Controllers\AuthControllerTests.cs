using FluentAssertions;
using LexAI.Identity.Application.DTOs;
using LexAI.Identity.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace LexAI.Identity.IntegrationTests.Controllers;

/// <summary>
/// Integration tests for AuthController
/// </summary>
public class AuthControllerTests : IClassFixture<TestWebApplicationFactory>
{
    private readonly TestWebApplicationFactory _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public AuthControllerTests(TestWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task Login_WithValidCredentials_ShouldReturnAuthenticationResponse()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var loginRequest = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "TestPassword123!",
            RememberMe = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var authResponse = JsonSerializer.Deserialize<AuthenticationResponseDto>(content, _jsonOptions);

        authResponse.Should().NotBeNull();
        authResponse!.AccessToken.Should().NotBeNullOrEmpty();
        authResponse.RefreshToken.Should().NotBeNullOrEmpty();
        authResponse.TokenType.Should().Be("Bearer");
        authResponse.ExpiresIn.Should().BeGreaterThan(0);
        authResponse.User.Should().NotBeNull();
        authResponse.User.Email.Should().Be("<EMAIL>");
        authResponse.User.FirstName.Should().Be("John");
        authResponse.User.LastName.Should().Be("Lawyer");
        authResponse.User.IsActive.Should().BeTrue();
        authResponse.User.IsEmailVerified.Should().BeTrue();
    }

    [Fact]
    public async Task Login_WithInvalidCredentials_ShouldReturnUnauthorized()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var loginRequest = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "WrongPassword123!",
            RememberMe = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task Login_WithNonExistentUser_ShouldReturnUnauthorized()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();

        var loginRequest = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "TestPassword123!",
            RememberMe = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Theory]
    [InlineData("", "TestPassword123!")]
    [InlineData("invalid-email", "TestPassword123!")]
    [InlineData("<EMAIL>", "")]
    public async Task Login_WithInvalidInput_ShouldReturnBadRequest(string email, string password)
    {
        // Arrange
        var loginRequest = new LoginDto
        {
            Email = email,
            Password = password,
            RememberMe = false
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task RefreshToken_WithValidToken_ShouldReturnNewTokens()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        // First, login to get tokens
        var loginRequest = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "TestPassword123!",
            RememberMe = false
        };

        var loginResponse = await _client.PostAsJsonAsync("/api/auth/login", loginRequest, _jsonOptions);
        loginResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var loginContent = await loginResponse.Content.ReadAsStringAsync();
        var authResponse = JsonSerializer.Deserialize<AuthenticationResponseDto>(loginContent, _jsonOptions);

        // Act - Use refresh token
        var refreshRequest = new RefreshTokenDto
        {
            RefreshToken = authResponse!.RefreshToken
        };

        var refreshResponse = await _client.PostAsJsonAsync("/api/auth/refresh", refreshRequest, _jsonOptions);

        // Assert
        refreshResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        var refreshContent = await refreshResponse.Content.ReadAsStringAsync();
        var newAuthResponse = JsonSerializer.Deserialize<AuthenticationResponseDto>(refreshContent, _jsonOptions);

        newAuthResponse.Should().NotBeNull();
        newAuthResponse!.AccessToken.Should().NotBeNullOrEmpty();
        newAuthResponse.RefreshToken.Should().NotBeNullOrEmpty();
        newAuthResponse.AccessToken.Should().NotBe(authResponse.AccessToken);
        newAuthResponse.RefreshToken.Should().NotBe(authResponse.RefreshToken);
    }

    [Fact]
    public async Task RefreshToken_WithInvalidToken_ShouldReturnUnauthorized()
    {
        // Arrange
        var refreshRequest = new RefreshTokenDto
        {
            RefreshToken = "invalid-refresh-token"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/refresh", refreshRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task GetCurrentUser_WithValidToken_ShouldReturnUserInfo()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var accessToken = await GetAccessTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        // Act
        var response = await _client.GetAsync("/api/auth/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        var user = JsonSerializer.Deserialize<UserDto>(content, _jsonOptions);

        user.Should().NotBeNull();
        user!.Email.Should().Be("<EMAIL>");
        user.FirstName.Should().Be("John");
        user.LastName.Should().Be("Lawyer");
        user.Role.Should().Be(LexAI.Shared.Domain.Enums.UserRole.Lawyer);
    }

    [Fact]
    public async Task GetCurrentUser_WithoutToken_ShouldReturnUnauthorized()
    {
        // Act
        var response = await _client.GetAsync("/api/auth/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task Logout_WithValidToken_ShouldReturnSuccess()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var accessToken = await GetAccessTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        // Act
        var response = await _client.PostAsync("/api/auth/logout", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Logout successful");
    }

    [Fact]
    public async Task ChangePassword_WithValidData_ShouldReturnSuccess()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var accessToken = await GetAccessTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var changePasswordRequest = new ChangePasswordDto
        {
            CurrentPassword = "TestPassword123!",
            NewPassword = "NewSecurePassword123!",
            ConfirmPassword = "NewSecurePassword123!"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/change-password", changePasswordRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var content = await response.Content.ReadAsStringAsync();
        content.Should().Contain("Password changed successfully");
    }

    [Fact]
    public async Task ChangePassword_WithMismatchedPasswords_ShouldReturnBadRequest()
    {
        // Arrange
        await _factory.ResetDatabaseAsync();
        await _factory.SeedDatabaseAsync();

        var accessToken = await GetAccessTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var changePasswordRequest = new ChangePasswordDto
        {
            CurrentPassword = "TestPassword123!",
            NewPassword = "NewSecurePassword123!",
            ConfirmPassword = "DifferentPassword123!"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/auth/change-password", changePasswordRequest, _jsonOptions);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    private async Task<string> GetAccessTokenAsync()
    {
        var loginRequest = new LoginDto
        {
            Email = "<EMAIL>",
            Password = "TestPassword123!",
            RememberMe = false
        };

        var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest, _jsonOptions);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var authResponse = JsonSerializer.Deserialize<AuthenticationResponseDto>(content, _jsonOptions);

        return authResponse!.AccessToken;
    }
}
