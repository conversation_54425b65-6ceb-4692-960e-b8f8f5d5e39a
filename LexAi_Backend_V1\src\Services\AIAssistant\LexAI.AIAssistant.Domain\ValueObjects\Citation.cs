using LexAI.Shared.Domain.Common;

namespace LexAI.AIAssistant.Domain.ValueObjects;

/// <summary>
/// Represents a citation or reference used in a message
/// </summary>
public class Citation : ValueObject
{
    /// <summary>
    /// Citation ID
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// Citation type
    /// </summary>
    public CitationType Type { get; private set; }

    /// <summary>
    /// Document or source title
    /// </summary>
    public string Title { get; private set; } = string.Empty;

    /// <summary>
    /// Document URL or reference
    /// </summary>
    public string Url { get; private set; } = string.Empty;

    /// <summary>
    /// Source name or publisher
    /// </summary>
    public string Source { get; private set; } = string.Empty;

    /// <summary>
    /// Authors of the document
    /// </summary>
    public string? Authors { get; private set; }

    /// <summary>
    /// Publication date
    /// </summary>
    public DateTime? PublicationDate { get; private set; }

    /// <summary>
    /// Relevance score (0-1)
    /// </summary>
    public double RelevanceScore { get; private set; }

    /// <summary>
    /// Excerpt or summary from the source
    /// </summary>
    public string? Excerpt { get; private set; }

    /// <summary>
    /// Page number or section reference
    /// </summary>
    public string? PageReference { get; private set; }

    /// <summary>
    /// Legal jurisdiction (for legal documents)
    /// </summary>
    public string? Jurisdiction { get; private set; }

    /// <summary>
    /// Court name (for case law)
    /// </summary>
    public string? Court { get; private set; }

    /// <summary>
    /// Case number (for case law)
    /// </summary>
    public string? CaseNumber { get; private set; }

    /// <summary>
    /// Citation format (e.g., APA, MLA, Bluebook)
    /// </summary>
    public string? CitationFormat { get; private set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; private set; } = new();

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Citation() { }

    /// <summary>
    /// Creates a new citation
    /// </summary>
    /// <param name="type">Citation type</param>
    /// <param name="title">Document title</param>
    /// <param name="url">Document URL</param>
    /// <param name="source">Source name</param>
    /// <param name="relevanceScore">Relevance score</param>
    /// <returns>New citation</returns>
    public static Citation Create(
        CitationType type,
        string title,
        string url,
        string source,
        double relevanceScore = 0.0)
    {
        if (string.IsNullOrWhiteSpace(title))
            throw new ArgumentException("Title cannot be empty", nameof(title));

        if (string.IsNullOrWhiteSpace(source))
            throw new ArgumentException("Source cannot be empty", nameof(source));

        if (relevanceScore < 0 || relevanceScore > 1)
            throw new ArgumentException("Relevance score must be between 0 and 1", nameof(relevanceScore));

        return new Citation
        {
            Id = Guid.NewGuid(),
            Type = type,
            Title = title.Trim(),
            Url = url?.Trim() ?? string.Empty,
            Source = source.Trim(),
            RelevanceScore = relevanceScore,
            Metadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Creates a legal document citation
    /// </summary>
    /// <param name="title">Document title</param>
    /// <param name="url">Document URL</param>
    /// <param name="source">Source name</param>
    /// <param name="jurisdiction">Legal jurisdiction</param>
    /// <param name="relevanceScore">Relevance score</param>
    /// <returns>New legal document citation</returns>
    public static Citation CreateLegalDocument(
        string title,
        string url,
        string source,
        string jurisdiction,
        double relevanceScore = 0.0)
    {
        var citation = Create(CitationType.LegalDocument, title, url, source, relevanceScore);
        citation.Jurisdiction = jurisdiction?.Trim();
        return citation;
    }

    /// <summary>
    /// Creates a case law citation
    /// </summary>
    /// <param name="title">Case title</param>
    /// <param name="url">Case URL</param>
    /// <param name="court">Court name</param>
    /// <param name="caseNumber">Case number</param>
    /// <param name="jurisdiction">Jurisdiction</param>
    /// <param name="relevanceScore">Relevance score</param>
    /// <returns>New case law citation</returns>
    public static Citation CreateCaseLaw(
        string title,
        string url,
        string court,
        string caseNumber,
        string jurisdiction,
        double relevanceScore = 0.0)
    {
        var citation = Create(CitationType.CaseLaw, title, url, court, relevanceScore);
        citation.Court = court?.Trim();
        citation.CaseNumber = caseNumber?.Trim();
        citation.Jurisdiction = jurisdiction?.Trim();
        return citation;
    }

    /// <summary>
    /// Sets publication information
    /// </summary>
    /// <param name="publicationDate">Publication date</param>
    /// <param name="authors">Authors</param>
    /// <param name="pageReference">Page reference</param>
    public void SetPublicationInfo(
        DateTime? publicationDate = null,
        string? authors = null,
        string? pageReference = null)
    {
        PublicationDate = publicationDate;
        Authors = authors?.Trim();
        PageReference = pageReference?.Trim();
    }

    /// <summary>
    /// Sets the excerpt from the source
    /// </summary>
    /// <param name="excerpt">Excerpt text</param>
    /// <param name="maxLength">Maximum excerpt length</param>
    public void SetExcerpt(string excerpt, int maxLength = 500)
    {
        if (string.IsNullOrWhiteSpace(excerpt))
        {
            Excerpt = null;
            return;
        }

        var trimmedExcerpt = excerpt.Trim();
        if (trimmedExcerpt.Length > maxLength)
        {
            trimmedExcerpt = trimmedExcerpt.Substring(0, maxLength - 3) + "...";
        }

        Excerpt = trimmedExcerpt;
    }

    /// <summary>
    /// Sets the citation format
    /// </summary>
    /// <param name="format">Citation format</param>
    public void SetCitationFormat(string format)
    {
        CitationFormat = format?.Trim();
    }

    /// <summary>
    /// Adds metadata
    /// </summary>
    /// <param name="key">Metadata key</param>
    /// <param name="value">Metadata value</param>
    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Metadata key cannot be empty", nameof(key));

        Metadata[key] = value;
    }

    /// <summary>
    /// Gets metadata value
    /// </summary>
    /// <typeparam name="T">Value type</typeparam>
    /// <param name="key">Metadata key</param>
    /// <returns>Metadata value or default</returns>
    public T? GetMetadata<T>(string key)
    {
        if (string.IsNullOrWhiteSpace(key) || !Metadata.ContainsKey(key))
            return default;

        try
        {
            return (T)Metadata[key];
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// Checks if this is a legal citation
    /// </summary>
    /// <returns>True if legal citation</returns>
    public bool IsLegalCitation()
    {
        return Type == CitationType.LegalDocument ||
               Type == CitationType.CaseLaw ||
               Type == CitationType.Statute ||
               Type == CitationType.Regulation;
    }

    /// <summary>
    /// Gets formatted citation string
    /// </summary>
    /// <returns>Formatted citation</returns>
    public string GetFormattedCitation()
    {
        var parts = new List<string>();

        if (!string.IsNullOrWhiteSpace(Authors))
            parts.Add(Authors);

        parts.Add(Title);

        if (!string.IsNullOrWhiteSpace(Source))
            parts.Add(Source);

        if (PublicationDate.HasValue)
            parts.Add(PublicationDate.Value.Year.ToString());

        if (!string.IsNullOrWhiteSpace(PageReference))
            parts.Add($"p. {PageReference}");

        return string.Join(", ", parts);
    }

    /// <summary>
    /// Gets the atomic values for value object equality
    /// </summary>
    /// <returns>Atomic values</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Id;
        yield return Type;
        yield return Title;
        yield return Url;
        yield return Source;
    }
}
