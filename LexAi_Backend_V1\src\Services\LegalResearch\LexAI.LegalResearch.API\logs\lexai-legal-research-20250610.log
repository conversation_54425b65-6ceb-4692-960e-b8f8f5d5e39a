2025-06-10 20:21:29.899 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-10 20:21:29.989 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:21:30.332 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-10 20:21:30.336 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-10 20:21:30.473 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:21:30.475 +04:00 [INF] Hosting environment: Development
2025-06-10 20:21:30.478 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-10 20:21:34.112 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-10 20:21:34.734 +04:00 [INF] Request GET / started with correlation ID 66e6a025-104b-4767-ae23-3b3c8ebb2087
2025-06-10 20:21:36.884 +04:00 [INF] Request GET / completed in 2140ms with status 404 (Correlation ID: 66e6a025-104b-4767-ae23-3b3c8ebb2087)
2025-06-10 20:21:36.903 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2793.1487ms
2025-06-10 20:21:36.934 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-10 20:22:33.541 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-10 20:22:33.643 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:22:33.863 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-10 20:22:33.865 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-10 20:22:34.176 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:22:34.178 +04:00 [INF] Hosting environment: Development
2025-06-10 20:22:34.180 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-10 20:22:35.727 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-10 20:22:36.413 +04:00 [INF] Request GET / started with correlation ID cd5341d8-2ce3-4b68-805a-58d1e208dade
2025-06-10 20:22:36.591 +04:00 [INF] Request GET / completed in 154ms with status 404 (Correlation ID: cd5341d8-2ce3-4b68-805a-58d1e208dade)
2025-06-10 20:22:36.618 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 892.6315ms
2025-06-10 20:22:36.645 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
