2025-06-01 21:30:41.209 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 21:30:41.246 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-01 21:30:41.254 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-01 21:30:41.775 +04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-01 21:30:41.922 +04:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Users" (
    "Id" uuid NOT NULL,
    "Email" character varying(254) NOT NULL,
    "FirstName" character varying(50) NOT NULL,
    "LastName" character varying(50) NOT NULL,
    "PhoneNumber" character varying(20),
    "PhoneCountryCode" character varying(5),
    "PasswordHash" character varying(255) NOT NULL,
    "Role" character varying(20) NOT NULL,
    "IsEmailVerified" boolean NOT NULL,
    "IsActive" boolean NOT NULL,
    "IsLocked" boolean NOT NULL,
    "LockedAt" timestamp with time zone,
    "FailedLoginAttempts" integer NOT NULL,
    "LastLoginAt" timestamp with time zone,
    "LastLoginIpAddress" character varying(45),
    "PreferredLanguage" character varying(10) NOT NULL DEFAULT 'fr-FR',
    "TimeZone" character varying(50) NOT NULL DEFAULT 'Europe/Paris',
    "ProfilePictureUrl" character varying(500),
    "CreatedAt" timestamp with time zone NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(50),
    "UpdatedBy" character varying(50),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(50),
    CONSTRAINT "PK_Users" PRIMARY KEY ("Id")
);
2025-06-01 21:30:41.940 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AuditEntries" (
    "Id" uuid NOT NULL,
    "EntityId" uuid NOT NULL,
    "EntityType" character varying(100) NOT NULL,
    "Action" character varying(50) NOT NULL,
    "UserId" character varying(50) NOT NULL,
    "Timestamp" timestamp with time zone NOT NULL,
    "Changes" jsonb,
    "IpAddress" character varying(45),
    "UserAgent" character varying(500),
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" text,
    "UpdatedBy" text,
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" text,
    CONSTRAINT "PK_AuditEntries" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_AuditEntries_Users_EntityId" FOREIGN KEY ("EntityId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-06-01 21:30:41.955 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "RefreshTokens" (
    "Id" uuid NOT NULL,
    "Token" character varying(255) NOT NULL,
    "UserId" uuid NOT NULL,
    "ExpiresAt" timestamp with time zone NOT NULL,
    "IsRevoked" boolean NOT NULL,
    "RevokedAt" timestamp with time zone,
    "RevokedBy" character varying(50),
    "RevocationReason" character varying(200),
    "IpAddress" character varying(45),
    "UserAgent" character varying(500),
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" text,
    "UpdatedBy" text,
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" text,
    CONSTRAINT "PK_RefreshTokens" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_RefreshTokens_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-06-01 21:30:41.971 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "UserPermissions" (
    "Id" uuid NOT NULL,
    "UserId" uuid NOT NULL,
    "Permission" character varying(100) NOT NULL,
    "Resource" character varying(100),
    "Action" character varying(50) NOT NULL,
    "GrantedAt" timestamp with time zone NOT NULL,
    "GrantedBy" character varying(50) NOT NULL,
    "ExpiresAt" timestamp with time zone,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" text,
    "UpdatedBy" text,
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" text,
    CONSTRAINT "PK_UserPermissions" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_UserPermissions_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE
);
2025-06-01 21:30:41.980 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AuditEntries_Action" ON "AuditEntries" ("Action");
2025-06-01 21:30:41.990 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AuditEntries_EntityId" ON "AuditEntries" ("EntityId");
2025-06-01 21:30:42.004 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AuditEntries_EntityType" ON "AuditEntries" ("EntityType");
2025-06-01 21:30:42.013 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AuditEntries_Timestamp" ON "AuditEntries" ("Timestamp");
2025-06-01 21:30:42.026 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AuditEntries_UserId" ON "AuditEntries" ("UserId");
2025-06-01 21:30:42.036 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RefreshTokens_ExpiresAt" ON "RefreshTokens" ("ExpiresAt");
2025-06-01 21:30:42.049 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RefreshTokens_IsRevoked" ON "RefreshTokens" ("IsRevoked");
2025-06-01 21:30:42.065 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_RefreshTokens_Token" ON "RefreshTokens" ("Token");
2025-06-01 21:30:42.082 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_RefreshTokens_UserId" ON "RefreshTokens" ("UserId");
2025-06-01 21:30:42.101 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserPermissions_ExpiresAt" ON "UserPermissions" ("ExpiresAt");
2025-06-01 21:30:42.113 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserPermissions_Unique" ON "UserPermissions" ("UserId", "Permission", "Resource", "Action");
2025-06-01 21:30:42.124 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_CreatedAt" ON "Users" ("CreatedAt");
2025-06-01 21:30:42.146 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "Users" ("Email");
2025-06-01 21:30:42.161 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_IsActive" ON "Users" ("IsActive");
2025-06-01 21:30:42.174 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_IsLocked" ON "Users" ("IsLocked");
2025-06-01 21:30:42.186 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_Role" ON "Users" ("Role");
2025-06-01 21:30:42.207 +04:00 [INF] LexAI Identity Service started successfully
2025-06-01 21:30:42.256 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-01 21:30:42.604 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-01 21:30:42.606 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-01 21:30:42.673 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-01 21:30:42.676 +04:00 [INF] Hosting environment: Development
2025-06-01 21:30:42.679 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-01 21:30:46.779 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-01 21:30:47.098 +04:00 [INF] Request GET / started with correlation ID 16d9706c-acc7-4fd3-ae4b-1cc08a47c61a
2025-06-01 21:30:47.268 +04:00 [INF] Request GET / completed in 162ms with status 404 (Correlation ID: 16d9706c-acc7-4fd3-ae4b-1cc08a47c61a)
2025-06-01 21:30:47.286 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 520.3796ms
2025-06-01 21:30:47.302 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-01 21:32:02.399 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-01 21:32:02.472 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID d75ed3ae-ee30-4017-a2e2-43666da3a24e
2025-06-01 21:32:02.479 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:32:02.487 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 10ms with status 204 (Correlation ID: d75ed3ae-ee30-4017-a2e2-43666da3a24e)
2025-06-01 21:32:02.491 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 92.2616ms
2025-06-01 21:32:02.494 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-01 21:32:02.504 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 7d9295d0-5d40-4446-a785-dd69c638e753
2025-06-01 21:32:02.507 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:32:02.511 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-01 21:32:02.576 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 21:32:02.701 +04:00 [INF] Token refresh attempt
2025-06-01 21:32:02.746 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-01 21:32:03.809 +04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__token_0='fBX7pxlI32iNwAJAVVNO4QnFFsMTJJLn3jZ8+Hf6HizKN9aY+P6ByjlTeCN+BmDyXJIsA9WRgirM2QX/SFWQOg=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-01 21:32:03.828 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-01 21:32:04.252 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-01 21:32:04.323 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-01 21:32:04.366 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 1775.806ms
2025-06-01 21:32:04.374 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-01 21:32:04.382 +04:00 [INF] Request POST /api/auth/refresh completed in 1875ms with status 401 (Correlation ID: 7d9295d0-5d40-4446-a785-dd69c638e753)
2025-06-01 21:32:04.418 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 401 null application/json; charset=utf-8 1923.6065ms
2025-06-01 21:32:04.418 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - null null
2025-06-01 21:32:04.438 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 297c4adb-19df-4979-8fd1-ec3f8c98dafc
2025-06-01 21:32:04.442 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:32:04.444 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 2ms with status 204 (Correlation ID: 297c4adb-19df-4979-8fd1-ec3f8c98dafc)
2025-06-01 21:32:04.451 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/logout - 204 null null 33.2234ms
2025-06-01 21:32:04.454 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/logout - application/json 0
2025-06-01 21:32:04.468 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 6b91dc0c-ec0c-4c09-876a-6f8539d0da11
2025-06-01 21:32:04.472 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:32:04.609 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 4:11:41 PM', Current time (UTC): '6/1/2025 5:32:04 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-01 21:32:04.618 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 4:11:41 PM', Current time (UTC): '6/1/2025 5:32:04 PM'.
2025-06-01 21:32:04.621 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/1/2025 4:11:41 PM', Current time (UTC): '6/1/2025 5:32:04 PM'.
2025-06-01 21:32:04.637 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-01 21:32:04.654 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-01 21:32:04.656 +04:00 [INF] Request POST /api/auth/logout completed in 184ms with status 401 (Correlation ID: 6b91dc0c-ec0c-4c09-876a-6f8539d0da11)
2025-06-01 21:32:04.664 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/logout - 499 null null 210.0874ms
2025-06-01 21:33:41.121 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/register - null null
2025-06-01 21:33:41.176 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 937d05da-c61d-421f-b53e-bd468b891b4e
2025-06-01 21:33:41.178 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:33:41.181 +04:00 [INF] Request OPTIONS /api/auth/register completed in 3ms with status 204 (Correlation ID: 937d05da-c61d-421f-b53e-bd468b891b4e)
2025-06-01 21:33:41.189 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/register - 204 null null 68.3246ms
2025-06-01 21:33:41.191 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/register - application/json 260
2025-06-01 21:33:41.202 +04:00 [INF] Request POST /api/auth/register started with correlation ID d74b2a6c-1700-42dd-b87b-8f6c6f928984
2025-06-01 21:33:41.204 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:33:41.206 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 21:33:41.213 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 21:33:41.243 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-01 21:33:41.341 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-01 21:33:41.441 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-01 21:33:41.526 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 21:33:42.687 +04:00 [INF] Executed DbCommand (31ms) [Parameters=[@p0='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p1='2025-06-01T17:33:42.5262650Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Jules' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$eWMQSH57bq.BO5srOIQ1AutHq3dday.oDdj4wcF798QSqVq1WX93S' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-01T17:33:42.1521122Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='1bd90abf-0fe7-4877-853d-002088cc345e', @p26='PreferencesUpdated' (Nullable = false), @p27='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p28='2025-06-01T17:33:42.5264213Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-01T17:33:42.1521162Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='3c08c50d-d3a5-440b-8c37-1b8380aba11a', @p42='Created' (Nullable = false), @p43='"User created with role SeniorLawyer"' (DbType = Object), @p44='2025-06-01T17:33:42.5264196Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-01T17:33:41.5703641Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='6aae1c2b-4c83-48ae-9a05-2c2620b9b17a', @p58='PasswordChanged' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-01T17:33:42.5264201Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-01T17:33:42.1473196Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='8dda4529-1837-4435-84e8-d5863ee8b475', @p74='ProfileUpdated' (Nullable = false), @p75='null' (DbType = Object), @p76='2025-06-01T17:33:42.5264208Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-01T17:33:42.1515195Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-01 21:33:42.754 +04:00 [INF] User added successfully: "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-01 21:33:42.758 +04:00 [INF] User "62e0a975-363b-47ac-abf8-68fcf260cbb5" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 21:33:42.764 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-01 21:33:42.770 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-01 21:33:42.838 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 1622.2582ms
2025-06-01 21:33:42.841 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-01 21:33:42.843 +04:00 [INF] Request POST /api/auth/register completed in 1638ms with status 201 (Correlation ID: d74b2a6c-1700-42dd-b87b-8f6c6f928984)
2025-06-01 21:33:42.849 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/register - 201 null application/json; charset=utf-8 1657.7746ms
2025-06-01 21:33:51.183 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/login - null null
2025-06-01 21:33:51.204 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 57e1ea79-445a-4382-8c71-388f51f61155
2025-06-01 21:33:51.211 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:33:51.213 +04:00 [INF] Request OPTIONS /api/auth/login completed in 2ms with status 204 (Correlation ID: 57e1ea79-445a-4382-8c71-388f51f61155)
2025-06-01 21:33:51.222 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/login - 204 null null 38.5176ms
2025-06-01 21:33:51.227 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/login - application/json 52
2025-06-01 21:33:51.249 +04:00 [INF] Request POST /api/auth/login started with correlation ID 9ee5dc01-26ae-401a-a19f-e96ae76cff78
2025-06-01 21:33:51.251 +04:00 [INF] CORS policy execution successful.
2025-06-01 21:33:51.253 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 21:33:51.259 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-01 21:33:51.269 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-01 21:33:51.278 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-01 21:33:51.294 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-01 21:33:51.810 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='4a2abb05-0a29-48d2-a7de-693d6404c365', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-01T17:33:51.7847915Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-01T17:33:51.7847940Z' (DbType = DateTime), @p12='2025-06-01T17:33:51.7929201Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5' (Nullable = false), @p37='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p16='2025-06-01T17:33:42.5262650Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-01T17:33:51.7844379Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Jules' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$eWMQSH57bq.BO5srOIQ1AutHq3dday.oDdj4wcF798QSqVq1WX93S' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='SeniorLawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-01T17:33:51.7929174Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='792' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-01 21:33:51.821 +04:00 [INF] User updated successfully: "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-01 21:33:51.924 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='c8bf94c6-327d-4fab-942d-28f40987d3c6', @p1='2025-06-01T17:33:51.9166978Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-08T17:33:51.8521885Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='hadvgM12LTb0eC/VSk91etPoJUA4Lu1jZevwa8GP8e+cEtF5NhaoOS4My1/3Il9TwJ7qbPx56iyIjfmWC+n5jA==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-01 21:33:51.934 +04:00 [INF] Refresh token added successfully: "c8bf94c6-327d-4fab-942d-28f40987d3c6"
2025-06-01 21:33:51.937 +04:00 [INF] User "62e0a975-363b-47ac-abf8-68fcf260cbb5" logged in successfully
2025-06-01 21:33:51.940 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-01 21:33:51.943 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-01 21:33:51.948 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 684.9602ms
2025-06-01 21:33:51.951 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-01 21:33:51.952 +04:00 [INF] Request POST /api/auth/login completed in 700ms with status 200 (Correlation ID: 9ee5dc01-26ae-401a-a19f-e96ae76cff78)
2025-06-01 21:33:51.956 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/login - 200 null application/json; charset=utf-8 728.9614ms
