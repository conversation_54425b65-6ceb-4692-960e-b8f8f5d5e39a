# 🔐 LexAI Identity Service

Service d'authentification et de gestion des utilisateurs pour la plateforme LexAI.

## 📋 Fonctionnalités

### ✅ Implémentées
- **Authentification JWT** - Génération et validation de tokens d'accès
- **Gestion des utilisateurs** - CRUD complet avec validation
- **Gestion des rôles** - RBAC (Role-Based Access Control)
- **Refresh tokens** - Renouvellement sécurisé des tokens
- **Sécurité des mots de passe** - Hachage BCrypt et validation de force
- **Audit et logging** - Traçabilité complète des actions
- **Verrouillage de compte** - Protection contre les attaques par force brute
- **Validation des emails** - Processus de vérification d'email
- **API RESTful** - Endpoints documentés avec Swagger

### 🚧 En cours de développement
- **Multi-Factor Authentication (MFA)**
- **Single Sign-On (SSO)**
- **Intégration OAuth2/OpenID Connect**
- **Notifications par email**

## 🏗️ Architecture

Le service suit le pattern **Clean Architecture** avec 4 couches :

```
LexAI.Identity/
├── Domain/              # Entités métier et logique de domaine
│   ├── Entities/        # User, RefreshToken, UserPermission
│   ├── ValueObjects/    # Email, PhoneNumber
│   └── Events/          # Événements de domaine
├── Application/         # Use cases et DTOs
│   ├── Commands/        # Commandes (Create, Update, Delete)
│   ├── Queries/         # Requêtes (Get, Search)
│   ├── DTOs/           # Objets de transfert de données
│   ├── Interfaces/     # Contrats des services
│   └── Validators/     # Validation FluentValidation
├── Infrastructure/      # Accès aux données et services externes
│   ├── Data/           # Entity Framework DbContext
│   ├── Repositories/   # Implémentation des repositories
│   └── Services/       # Services d'infrastructure
└── API/                # Contrôleurs et configuration
    ├── Controllers/    # Endpoints REST
    └── Configuration/  # Configuration de l'API
```

## 🚀 Démarrage Rapide

### Prérequis
- .NET 9 SDK
- PostgreSQL 16+
- Docker (optionnel)

### Installation

1. **Cloner le repository**
```bash
git clone <repository-url>
cd LexAi_Backend_V1
```

2. **Configurer la base de données**
```bash
# Démarrer PostgreSQL avec Docker
docker-compose up -d postgres

# Ou configurer votre propre instance PostgreSQL
# Chaîne de connexion dans appsettings.json
```

3. **Appliquer les migrations**
```bash
# Générer la migration initiale
.\scripts\manage-migrations.ps1 -Action add -Name "InitialCreate"

# Appliquer les migrations
.\scripts\manage-migrations.ps1 -Action update
```

4. **Lancer le service**
```bash
dotnet run --project src/Services/Identity/LexAI.Identity.API
```

5. **Accéder à la documentation**
- API: http://localhost:8081
- Swagger: http://localhost:8081/swagger

## 🧪 Tests

### Exécuter tous les tests
```bash
.\scripts\run-tests.ps1 -All
```

### Tests unitaires uniquement
```bash
.\scripts\run-tests.ps1 -Unit
```

### Tests d'intégration uniquement
```bash
.\scripts\run-tests.ps1 -Integration
```

### Avec couverture de code
```bash
.\scripts\run-tests.ps1 -All -Coverage
```

### Structure des tests
```
tests/
├── LexAI.Identity.UnitTests/           # Tests unitaires
│   ├── Domain/                         # Tests des entités et value objects
│   ├── Application/                    # Tests des handlers et services
│   └── Infrastructure/                 # Tests des repositories
└── LexAI.Identity.IntegrationTests/    # Tests d'intégration
    ├── Controllers/                    # Tests des endpoints
    └── Infrastructure/                 # Tests de base de données
```

## 📚 API Documentation

### Endpoints principaux

#### Authentification
- `POST /api/auth/login` - Connexion utilisateur
- `POST /api/auth/refresh` - Renouvellement de token
- `POST /api/auth/logout` - Déconnexion
- `GET /api/auth/me` - Informations utilisateur actuel

#### Gestion des mots de passe
- `POST /api/auth/change-password` - Changement de mot de passe
- `POST /api/auth/forgot-password` - Demande de réinitialisation
- `POST /api/auth/reset-password` - Réinitialisation avec token

#### Gestion des utilisateurs
- `GET /api/users` - Liste des utilisateurs (paginée)
- `GET /api/users/{id}` - Détails d'un utilisateur
- `POST /api/users` - Création d'utilisateur
- `PUT /api/users/{id}` - Mise à jour d'utilisateur
- `DELETE /api/users/{id}` - Suppression d'utilisateur

### Modèles de données

#### UserDto
```json
{
  "id": "guid",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "fullName": "string",
  "phoneNumber": "string",
  "role": "Administrator|SeniorLawyer|Lawyer|LegalAssistant|Client",
  "isEmailVerified": "boolean",
  "isActive": "boolean",
  "isLocked": "boolean",
  "lastLoginAt": "datetime",
  "preferredLanguage": "string",
  "timeZone": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

#### AuthenticationResponseDto
```json
{
  "accessToken": "string",
  "refreshToken": "string",
  "tokenType": "Bearer",
  "expiresIn": "number",
  "user": "UserDto"
}
```

## 🔧 Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "PostgreSql": "Host=localhost;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!"
  },
  "Jwt": {
    "SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long",
    "Issuer": "LexAI",
    "Audience": "LexAI-Users",
    "AccessTokenExpirationMinutes": 60,
    "RefreshTokenExpirationDays": 7
  }
}
```

### Variables d'environnement
```bash
# Base de données
ConnectionStrings__PostgreSql=Host=localhost;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!

# JWT
Jwt__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long
Jwt__Issuer=LexAI
Jwt__Audience=LexAI-Users

# Logging
ASPNETCORE_ENVIRONMENT=Development
```

## 🔐 Sécurité

### Authentification
- **JWT Bearer tokens** avec expiration courte (60 min)
- **Refresh tokens** sécurisés avec rotation
- **Validation stricte** des tokens

### Mots de passe
- **Hachage BCrypt** avec salt aléatoire
- **Validation de force** configurable
- **Historique des mots de passe** (à implémenter)

### Protection des comptes
- **Verrouillage automatique** après 5 tentatives échouées
- **Audit des connexions** avec IP et User-Agent
- **Détection d'anomalies** (à implémenter)

## 📊 Monitoring

### Health Checks
- `GET /health` - Santé du service
- Vérification de la connectivité PostgreSQL

### Logs
- **Serilog** avec sortie console et fichier
- **Corrélation des requêtes** avec ID unique
- **Logs structurés** au format JSON

### Métriques
- Temps de réponse des endpoints
- Taux d'erreur d'authentification
- Utilisation des ressources

## 🚀 Déploiement

### Docker
```bash
# Build de l'image
docker build -t lexai-identity:latest -f src/Services/Identity/LexAI.Identity.API/Dockerfile .

# Lancement du conteneur
docker run -p 8081:8081 lexai-identity:latest
```

### Docker Compose
```bash
# Démarrage complet avec infrastructure
docker-compose up -d
```

## 🤝 Contribution

### Standards de code
- **Clean Architecture** respectée
- **Tests unitaires** obligatoires (>80% couverture)
- **Documentation XML** pour toutes les APIs publiques
- **Validation** avec FluentValidation

### Workflow
1. Créer une branche feature
2. Implémenter avec tests
3. Vérifier la couverture de code
4. Créer une Pull Request
5. Review et merge

## 📝 Changelog

### v1.0.0 (En cours)
- ✅ Authentification JWT complète
- ✅ Gestion des utilisateurs CRUD
- ✅ Tests unitaires et d'intégration
- ✅ Documentation Swagger
- ✅ Sécurité des mots de passe
- ✅ Audit et logging

### Prochaines versions
- 🔄 Multi-Factor Authentication
- 🔄 Single Sign-On
- 🔄 Notifications email
- 🔄 API de gestion des permissions
