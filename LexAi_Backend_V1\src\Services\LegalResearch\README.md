# 🔍 LexAI Legal Research Service

Service de recherche juridique avancée avec IA pour la plateforme LexAI. Utilise la recherche vectorielle et RAG (Retrieval-Augmented Generation) pour fournir des résultats de recherche précis et contextuels.

## 📋 Fonctionnalités

### ✅ Implémentées
- **Recherche sémantique** - Recherche basée sur le sens avec embeddings OpenAI
- **Recherche hybride** - Combinaison de recherche par mots-clés et sémantique
- **Indexation vectorielle** - Chunking et vectorisation automatique des documents
- **Analyse de requêtes** - Détection d'intention et extraction d'entités
- **Suggestions de recherche** - Autocomplétion et termes associés
- **Documents similaires** - Recommandations basées sur la similarité
- **Feedback utilisateur** - Système de notation et amélioration continue
- **Analytics de recherche** - Métriques et tendances d'utilisation
- **Cache intelligent** - Mise en cache des résultats fréquents
- **Rate limiting** - Protection contre l'abus d'API

### 🚧 En cours de développement
- **Recherche multilingue** - Support français/anglais
- **Filtres avancés** - Filtrage par juridiction, autorité, etc.
- **Recherche fédérée** - Intégration avec sources externes
- **Résumés automatiques** - Génération de résumés avec IA

## 🏗️ Architecture

Le service suit le pattern **Clean Architecture** avec intégration d'IA :

```
LexAI.LegalResearch/
├── Domain/                  # Entités métier et logique de domaine
│   ├── Entities/           # LegalDocument, SearchQuery
│   ├── ValueObjects/       # DocumentSource, SearchResult, DocumentChunk
│   └── Enums/             # DocumentType, LegalDomain, SearchMethod
├── Application/            # Use cases et services applicatifs
│   ├── Commands/          # PerformSearchCommand, IndexDocumentCommand
│   ├── Queries/           # GetDocumentQuery, GetSearchHistoryQuery
│   ├── DTOs/             # SearchRequestDto, SearchResponseDto
│   ├── Interfaces/       # ILegalSearchService, IEmbeddingService
│   └── Validators/       # Validation FluentValidation
├── Infrastructure/         # Services d'infrastructure et IA
│   ├── Services/         # OpenAIEmbeddingService, LegalSearchService
│   ├── Data/            # Entity Framework DbContext
│   ├── Repositories/    # Implémentation des repositories
│   └── VectorDB/        # Intégration base de données vectorielle
└── API/                   # Contrôleurs et configuration
    ├── Controllers/      # SearchController, DocumentController
    └── Configuration/    # Configuration de l'API
```

## 🤖 Intelligence Artificielle

### Modèles utilisés
- **Embeddings** : OpenAI `text-embedding-3-small` (1536 dimensions)
- **Chunking** : Découpage intelligent par paragraphes/sections
- **Similarité** : Cosine similarity pour la recherche vectorielle
- **Intent Detection** : Classification automatique des requêtes

### Pipeline de recherche
1. **Preprocessing** : Normalisation et nettoyage de la requête
2. **Intent Analysis** : Détection du type de recherche (définition, procédure, etc.)
3. **Embedding Generation** : Conversion en vecteur sémantique
4. **Vector Search** : Recherche de similarité dans la base vectorielle
5. **Hybrid Ranking** : Combinaison scores sémantiques + mots-clés
6. **Post-processing** : Génération de highlights et explications

## 🚀 Démarrage Rapide

### Prérequis
- .NET 9 SDK
- PostgreSQL 16+ (avec extension pgvector)
- MongoDB 7+ (stockage vectoriel)
- Clé API OpenAI
- Docker (optionnel)

### Installation

1. **Configurer les variables d'environnement**
```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# Bases de données
export ConnectionStrings__PostgreSql="Host=localhost;Database=legal_research_db;Username=lexai_user;Password=lexai_password_2024!"
export ConnectionStrings__MongoDB="mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin"
```

2. **Démarrer l'infrastructure**
```bash
# PostgreSQL avec pgvector
docker run -d --name postgres-vector \
  -e POSTGRES_DB=legal_research_db \
  -e POSTGRES_USER=lexai_user \
  -e POSTGRES_PASSWORD=lexai_password_2024! \
  -p 5432:5432 \
  pgvector/pgvector:pg16

# MongoDB
docker run -d --name mongodb \
  -e MONGO_INITDB_ROOT_USERNAME=lexai_admin \
  -e MONGO_INITDB_ROOT_PASSWORD=lexai_mongo_password_2024! \
  -p 27017:27017 \
  mongo:7
```

3. **Appliquer les migrations**
```bash
.\scripts\manage-migrations.ps1 -Action add -Name "InitialCreate" -Service "LegalResearch"
.\scripts\manage-migrations.ps1 -Action update -Service "LegalResearch"
```

4. **Lancer le service**
```bash
dotnet run --project src/Services/LegalResearch/LexAI.LegalResearch.API
```

5. **Accéder à la documentation**
- API: http://localhost:8082
- Swagger: http://localhost:8082/swagger

## 📚 API Documentation

### Endpoints principaux

#### Recherche
- `POST /api/search/search` - Recherche sémantique
- `POST /api/search/hybrid-search` - Recherche hybride
- `GET /api/search/similar/{documentId}` - Documents similaires
- `GET /api/search/suggestions?q={query}` - Suggestions de recherche
- `POST /api/search/analyze` - Analyse de requête

#### Feedback et Analytics
- `POST /api/search/feedback/{queryId}` - Feedback utilisateur
- `GET /api/search/analytics` - Analytics de recherche

### Modèles de données

#### SearchRequestDto
```json
{
  "query": "contrat de travail CDI",
  "userId": "guid",
  "sessionId": "string",
  "method": "Hybrid",
  "domainFilter": "Labor",
  "typeFilter": "Law",
  "languageFilter": "fr",
  "dateFilter": {
    "startDate": "2020-01-01",
    "endDate": "2024-12-31"
  },
  "limit": 20,
  "minRelevanceScore": 0.7,
  "includeHighlights": true,
  "sortOrder": "Relevance"
}
```

#### SearchResponseDto
```json
{
  "queryId": "guid",
  "query": "contrat de travail CDI",
  "processedQuery": "contrat travail cdi",
  "results": [
    {
      "documentId": "guid",
      "title": "Code du travail - Article L1221-1",
      "summary": "Le contrat de travail à durée indéterminée...",
      "relevanceScore": 0.95,
      "similarityScore": 0.92,
      "keywordScore": 0.88,
      "documentType": "Law",
      "legalDomain": "Labor",
      "source": {
        "name": "Légifrance",
        "url": "https://legifrance.gouv.fr/...",
        "type": "Official",
        "authority": "National",
        "jurisdiction": "France",
        "reliabilityScore": 1.0
      },
      "highlights": [
        {
          "text": "contrat de travail à durée indéterminée",
          "startPosition": 45,
          "endPosition": 78,
          "score": 0.95,
          "type": "keyword"
        }
      ],
      "matchedChunks": [
        {
          "chunkId": "guid",
          "content": "Le contrat de travail à durée indéterminée est la forme normale...",
          "type": "Article",
          "similarityScore": 0.92,
          "startPosition": 0,
          "endPosition": 500
        }
      ],
      "publicationDate": "2008-05-01",
      "effectiveDate": "2008-05-01",
      "tags": ["cdi", "contrat", "travail", "durée"],
      "rank": 1
    }
  ],
  "totalResults": 15,
  "executionTimeMs": 245,
  "method": "Hybrid",
  "intent": "Information",
  "qualityScore": 0.89,
  "isCached": false,
  "suggestions": ["contrat travail cdd", "rupture contrat travail"],
  "relatedTerms": ["embauche", "licenciement", "démission"]
}
```

## 🔧 Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "PostgreSql": "Host=localhost;Database=legal_research_db;Username=lexai_user;Password=lexai_password_2024!",
    "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin"
  },
  "OpenAI": {
    "ApiKey": "your-openai-api-key",
    "EmbeddingModel": "text-embedding-3-small",
    "EmbeddingDimension": 1536
  },
  "Search": {
    "DefaultChunkSize": 1000,
    "ChunkOverlap": 200,
    "MaxResultsPerQuery": 100,
    "CacheExpirationMinutes": 15,
    "MinSimilarityThreshold": 0.7
  }
}
```

### Variables d'environnement
```bash
# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSION=1536

# Bases de données
ConnectionStrings__PostgreSql=Host=localhost;Database=legal_research_db;Username=lexai_user;Password=lexai_password_2024!
ConnectionStrings__MongoDB=mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin

# Recherche
Search__DefaultChunkSize=1000
Search__MinSimilarityThreshold=0.7
```

## 🧪 Tests

### Exécuter tous les tests
```bash
.\scripts\run-tests.ps1 -All -Project "LegalResearch"
```

### Tests unitaires
```bash
.\scripts\run-tests.ps1 -Unit -Project "LegalResearch"
```

### Tests d'intégration
```bash
.\scripts\run-tests.ps1 -Integration -Project "LegalResearch"
```

### Structure des tests
```
tests/
├── LexAI.LegalResearch.UnitTests/
│   ├── Domain/                    # Tests des entités et value objects
│   ├── Application/               # Tests des handlers et services
│   └── Infrastructure/            # Tests des services d'infrastructure
└── LexAI.LegalResearch.IntegrationTests/
    ├── Controllers/               # Tests des endpoints
    ├── Search/                    # Tests de recherche end-to-end
    └── Infrastructure/            # Tests avec bases de données
```

## 📊 Monitoring et Observabilité

### Métriques clés
- **Latence de recherche** - Temps de réponse moyen
- **Taux de succès** - Pourcentage de recherches avec résultats
- **Satisfaction utilisateur** - Score moyen des feedbacks
- **Utilisation cache** - Taux de cache hit/miss
- **Coût OpenAI** - Tokens consommés et coût

### Health Checks
- `GET /health` - Santé du service
- Vérification PostgreSQL et MongoDB
- Test de connectivité OpenAI
- Validation de l'index vectoriel

### Logs structurés
- **Corrélation des requêtes** avec ID unique
- **Métriques de performance** par endpoint
- **Erreurs détaillées** avec stack traces
- **Audit des recherches** pour amélioration

## 🔐 Sécurité

### Authentification
- **JWT Bearer tokens** requis pour tous les endpoints
- **Validation des rôles** (Lawyer, SeniorLawyer, Administrator)
- **Rate limiting** : 100 requêtes/minute par utilisateur

### Protection des données
- **Anonymisation** des requêtes dans les logs
- **Chiffrement** des données sensibles
- **Audit trail** complet des accès

### Sécurité API
- **Validation stricte** des entrées
- **Sanitization** des requêtes de recherche
- **Protection CORS** configurée
- **Headers de sécurité** appliqués

## 🚀 Déploiement

### Docker
```bash
# Build de l'image
docker build -t lexai-legal-research:latest -f src/Services/LegalResearch/LexAI.LegalResearch.API/Dockerfile .

# Lancement du conteneur
docker run -p 8082:8082 \
  -e OPENAI_API_KEY=your-key \
  -e ConnectionStrings__PostgreSql=your-connection \
  lexai-legal-research:latest
```

### Docker Compose
```bash
# Démarrage complet avec infrastructure
docker-compose up -d legal-research-service
```

## 📈 Performance

### Optimisations implémentées
- **Cache en mémoire** pour les requêtes fréquentes
- **Pagination** des résultats de recherche
- **Indexation asynchrone** des documents
- **Pool de connexions** optimisé
- **Compression** des réponses API

### Benchmarks
- **Recherche simple** : ~200ms (cache miss), ~50ms (cache hit)
- **Recherche hybride** : ~400ms (cache miss), ~80ms (cache hit)
- **Indexation document** : ~2-5s selon la taille
- **Throughput** : 500+ requêtes/seconde

## 🤝 Contribution

### Standards de code
- **Clean Architecture** respectée
- **Tests obligatoires** (>85% couverture)
- **Documentation XML** complète
- **Validation** avec FluentValidation
- **Logging** structuré avec Serilog

### Workflow de développement
1. Créer une branche feature
2. Implémenter avec tests
3. Vérifier la couverture de code
4. Tester l'intégration IA
5. Créer une Pull Request
6. Review et merge

## 📝 Changelog

### v1.0.0 (En cours)
- ✅ Recherche sémantique avec OpenAI
- ✅ Recherche hybride optimisée
- ✅ Indexation vectorielle automatique
- ✅ Analytics et feedback utilisateur
- ✅ Cache intelligent et rate limiting
- ✅ Tests complets et documentation

### Prochaines versions
- 🔄 Recherche multilingue
- 🔄 Intégration sources externes
- 🔄 Résumés automatiques avec IA
- 🔄 Recherche vocale
