namespace LexAI.AIAssistant.Domain.Entities;

/// <summary>
/// Entité représentant les préférences utilisateur
/// </summary>
public class UserPreference
{
    /// <summary>
    /// Identifiant unique de la préférence
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Clé de la préférence
    /// </summary>
    public string PreferenceKey { get; set; } = string.Empty;

    /// <summary>
    /// Valeur de la préférence (JSON)
    /// </summary>
    public string PreferenceValue { get; set; } = string.Empty;

    /// <summary>
    /// Description de la préférence
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Type de données de la préférence
    /// </summary>
    public string DataType { get; set; } = "string";

    /// <summary>
    /// Indique si la préférence est publique
    /// </summary>
    public bool IsPublic { get; set; } = false;

    /// <summary>
    /// Date de création
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date de dernière modification
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
