import { BaseApiService } from '../api/base'
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  RefreshTokenRequest,
  User
} from './types'

const IDENTITY_API_BASE_URL = import.meta.env.VITE_IDENTITY_API_URL || 'http://localhost:5000'

class AuthApiService extends BaseApiService {
  constructor() {
    super(IDENTITY_API_BASE_URL)
  }

  async login(request: LoginRequest): Promise<LoginResponse> {
    return this.post('/api/auth/login', request)
  }

  async register(request: RegisterRequest): Promise<LoginResponse> {
    return this.post('/api/auth/register', request)
  }

  async logout(): Promise<void> {
    return this.post('/api/auth/logout')
  }

  async refreshToken(request: RefreshTokenRequest): Promise<LoginResponse> {
    return this.post('/api/auth/refresh', request)
  }

  async forgotPassword(request: ForgotPasswordRequest): Promise<void> {
    return this.post('/api/auth/forgot-password', request)
  }

  async resetPassword(request: ResetPasswordRequest): Promise<void> {
    return this.post('/api/auth/reset-password', request)
  }

  async changePassword(request: ChangePasswordRequest): Promise<void> {
    return this.post('/api/auth/change-password', request)
  }

  async getCurrentUser(): Promise<User> {
    return this.get('/api/auth/me')
  }
}

export const authApi = new AuthApiService()
