using LexAI.DataPreprocessing.Domain.ValueObjects;

namespace LexAI.DataPreprocessing.Domain.Entities;

/// <summary>
/// Processing step entity
/// </summary>
public class ProcessingStep
{
    /// <summary>
    /// Step ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Step name
    /// </summary>
    public string StepName { get; set; } = string.Empty;

    /// <summary>
    /// Agent name that executed the step
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// When the step started
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the step completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Whether the step was successful
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// Error message if step failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Step metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Duration of the step
    /// </summary>
    public TimeSpan? Duration => CompletedAt.HasValue ? CompletedAt.Value - StartedAt : null;
}
