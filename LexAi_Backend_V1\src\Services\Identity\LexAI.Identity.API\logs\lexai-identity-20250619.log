2025-06-19 00:30:58.680 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:30:58.775 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:30:58.787 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 00:30:59.925 +04:00 [INF] Executed DbCommand (105ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 00:30:59.945 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 00:31:00.038 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:31:00.411 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 00:31:00.422 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 00:31:00.614 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:31:00.616 +04:00 [INF] Hosting environment: Development
2025-06-19 00:31:00.619 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 00:31:00.999 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 00:31:01.201 +04:00 [INF] Request GET / started with correlation ID 0ac2db0d-d413-417b-93db-2c25cfc5fa81
2025-06-19 00:31:01.363 +04:00 [INF] Request GET / completed in 151ms with status 404 (Correlation ID: 0ac2db0d-d413-417b-93db-2c25cfc5fa81)
2025-06-19 00:31:01.379 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 394.6096ms
2025-06-19 00:31:01.400 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-19 00:52:06.087 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:52:06.134 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:52:06.147 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 00:52:07.116 +04:00 [INF] Executed DbCommand (108ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 00:52:07.142 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 00:52:07.226 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:52:07.671 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 00:52:07.673 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 00:52:08.125 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:52:08.129 +04:00 [INF] Hosting environment: Development
2025-06-19 00:52:08.131 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 00:52:08.517 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 00:52:08.751 +04:00 [INF] Request GET / started with correlation ID ab70ee8c-1498-4a72-8d83-06e7a5035791
2025-06-19 00:52:10.352 +04:00 [INF] Request GET / completed in 1597ms with status 404 (Correlation ID: ab70ee8c-1498-4a72-8d83-06e7a5035791)
2025-06-19 00:52:10.363 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 1858.2173ms
2025-06-19 00:52:10.374 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-19 01:05:37.798 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 01:05:38.404 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 01:05:38.415 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 01:05:39.212 +04:00 [INF] Executed DbCommand (127ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 01:05:39.245 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 01:05:39.505 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 01:05:40.194 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 01:05:40.196 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 01:05:40.306 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 01:05:40.310 +04:00 [INF] Hosting environment: Development
2025-06-19 01:05:40.313 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 01:05:40.621 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 01:05:40.965 +04:00 [INF] Request GET / started with correlation ID 829c8582-2989-482b-ad31-6b7f0caf29f1
2025-06-19 01:05:41.121 +04:00 [INF] Request GET / completed in 148ms with status 404 (Correlation ID: 829c8582-2989-482b-ad31-6b7f0caf29f1)
2025-06-19 01:05:41.135 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 530.11ms
2025-06-19 01:05:41.146 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-19 01:06:34.215 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-19 01:06:34.245 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID a93dacd9-2277-4600-a7b1-44fb2a94c0e6
2025-06-19 01:06:34.254 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:06:34.260 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 9ms with status 204 (Correlation ID: a93dacd9-2277-4600-a7b1-44fb2a94c0e6)
2025-06-19 01:06:34.265 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 50.5959ms
2025-06-19 01:06:34.272 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-19 01:06:34.286 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 4a388c63-d67e-4b8a-a390-809e244f2f5d
2025-06-19 01:06:34.291 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:06:34.299 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-19 01:06:34.369 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-19 01:06:34.479 +04:00 [INF] Token refresh attempt
2025-06-19 01:06:34.537 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-19 01:06:35.956 +04:00 [INF] Executed DbCommand (101ms) [Parameters=[@__token_0='1GQDOA/ehAxqmIJI04EWwfwu01kapIVQCF+Ym3l2VXYtLgyBS7aNdI6dyYiiXFVdyVCNcCPcPGkpETr6FY+xHw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-19 01:06:36.414 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-19 01:06:36.495 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-19 01:06:36.572 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='1GQDOA/ehAxqmIJI04EWwfwu01kapIVQCF+Ym3l2VXYtLgyBS7aNdI6dyYiiXFVdyVCNcCPcPGkpETr6FY+xHw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-19 01:06:36.862 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p16='623373b5-a4ce-4393-85ff-4bacae5583dd', @p0='2025-06-18T19:52:16.4167290Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-25T19:52:16.3898370Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-18T21:06:36.5792154Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='1GQDOA/ehAxqmIJI04EWwfwu01kapIVQCF+Ym3l2VXYtLgyBS7aNdI6dyYiiXFVdyVCNcCPcPGkpETr6FY+xHw==' (Nullable = false), @p12='2025-06-18T21:06:36.7182823Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='819' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-19 01:06:36.899 +04:00 [INF] Refresh token updated successfully: "623373b5-a4ce-4393-85ff-4bacae5583dd"
2025-06-19 01:06:37.124 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='e6d6993f-40df-4af2-9c98-2bad074c4c36', @p1='2025-06-18T21:06:37.1090524Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-25T21:06:37.0799554Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='WMfdbSzOeIxzyhmIF77z0WY7VBERQz2g9088iG6uYBABXU767CsMOTx/kwWTRsLEq2f8+PsTIsTOrcBmvxYe8w==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-19 01:06:37.133 +04:00 [INF] Refresh token added successfully: "e6d6993f-40df-4af2-9c98-2bad074c4c36"
2025-06-19 01:06:37.135 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 01:06:37.142 +04:00 [INF] Token refresh successful
2025-06-19 01:06:37.156 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-19 01:06:37.218 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2833.3019ms
2025-06-19 01:06:37.225 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-19 01:06:37.231 +04:00 [INF] Request POST /api/auth/refresh completed in 2941ms with status 200 (Correlation ID: 4a388c63-d67e-4b8a-a390-809e244f2f5d)
2025-06-19 01:06:37.248 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 2975.7328ms
2025-06-19 19:58:42.423 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 19:58:42.533 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 19:58:42.549 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 19:58:43.947 +04:00 [INF] Executed DbCommand (98ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 19:58:43.966 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 19:58:44.025 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 19:58:44.558 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 19:58:44.560 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 19:58:44.675 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 19:58:44.677 +04:00 [INF] Hosting environment: Development
2025-06-19 19:58:44.679 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 19:58:45.099 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 19:58:45.263 +04:00 [INF] Request GET / started with correlation ID 7e224fe1-de4d-43d1-ae42-40e03e55be0d
2025-06-19 19:58:45.403 +04:00 [INF] Request GET / completed in 135ms with status 404 (Correlation ID: 7e224fe1-de4d-43d1-ae42-40e03e55be0d)
2025-06-19 19:58:45.423 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 329.7478ms
2025-06-19 19:58:45.444 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-19 20:00:44.313 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-19 20:00:44.360 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 2c36b90a-c39b-497e-b793-f5f04c312542
2025-06-19 20:00:44.366 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:00:44.372 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 7ms with status 204 (Correlation ID: 2c36b90a-c39b-497e-b793-f5f04c312542)
2025-06-19 20:00:44.376 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 100.6156ms
2025-06-19 20:00:44.379 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-19 20:00:44.387 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 3edd8aa3-0442-4224-a869-43db165568d6
2025-06-19 20:00:44.390 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:00:44.395 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-19 20:00:44.429 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-19 20:00:44.508 +04:00 [INF] Token refresh attempt
2025-06-19 20:00:44.540 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-19 20:00:45.155 +04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__token_0='WMfdbSzOeIxzyhmIF77z0WY7VBERQz2g9088iG6uYBABXU767CsMOTx/kwWTRsLEq2f8+PsTIsTOrcBmvxYe8w=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-19 20:00:45.360 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-19 20:00:45.390 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-19 20:00:45.422 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='WMfdbSzOeIxzyhmIF77z0WY7VBERQz2g9088iG6uYBABXU767CsMOTx/kwWTRsLEq2f8+PsTIsTOrcBmvxYe8w=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-19 20:00:45.645 +04:00 [INF] Executed DbCommand (20ms) [Parameters=[@p16='e6d6993f-40df-4af2-9c98-2bad074c4c36', @p0='2025-06-18T21:06:37.1090520Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-25T21:06:37.0799550Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-19T16:00:45.4291397Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='WMfdbSzOeIxzyhmIF77z0WY7VBERQz2g9088iG6uYBABXU767CsMOTx/kwWTRsLEq2f8+PsTIsTOrcBmvxYe8w==' (Nullable = false), @p12='2025-06-19T16:00:45.4976672Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='821' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-19 20:00:45.686 +04:00 [INF] Refresh token updated successfully: "e6d6993f-40df-4af2-9c98-2bad074c4c36"
2025-06-19 20:00:45.884 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='0376d4db-c479-48ce-a7f0-bc8e9ac7a960', @p1='2025-06-19T16:00:45.8693991Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-26T16:00:45.8525724Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='W/X0KbLA0t5Ymj35l8bxZlZWhbv8YSP4N1oduKKXWJnFk4zrxMoXdS4LfM87CbyDAmxn1+TOoA3cm+u9tivoeQ==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-19 20:00:45.896 +04:00 [INF] Refresh token added successfully: "0376d4db-c479-48ce-a7f0-bc8e9ac7a960"
2025-06-19 20:00:45.898 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 20:00:45.903 +04:00 [INF] Token refresh successful
2025-06-19 20:00:45.911 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-19 20:00:45.958 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 1521.7022ms
2025-06-19 20:00:45.961 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-19 20:00:45.966 +04:00 [INF] Request POST /api/auth/refresh completed in 1577ms with status 200 (Correlation ID: 3edd8aa3-0442-4224-a869-43db165568d6)
2025-06-19 20:00:45.979 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 1600.1194ms
2025-06-19 20:41:02.508 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 20:41:03.452 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 20:41:04.128 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 20:41:05.817 +04:00 [INF] Executed DbCommand (160ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 20:41:05.846 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 20:41:05.933 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 20:41:06.507 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 20:41:06.511 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 20:41:07.012 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 20:41:07.015 +04:00 [INF] Hosting environment: Development
2025-06-19 20:41:07.017 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 20:41:07.305 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 20:41:07.632 +04:00 [INF] Request GET / started with correlation ID cd66783d-e0bd-44d7-93f7-c4c2f30fc5a2
2025-06-19 20:41:09.661 +04:00 [INF] Request GET / completed in 2020ms with status 404 (Correlation ID: cd66783d-e0bd-44d7-93f7-c4c2f30fc5a2)
2025-06-19 20:41:09.675 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 2393.0503ms
2025-06-19 20:41:09.685 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
