2025-06-19 00:30:58.680 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:30:58.775 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:30:58.787 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 00:30:59.925 +04:00 [INF] Executed DbCommand (105ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 00:30:59.945 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 00:31:00.038 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:31:00.411 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 00:31:00.422 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 00:31:00.614 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:31:00.616 +04:00 [INF] Hosting environment: Development
2025-06-19 00:31:00.619 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 00:31:00.999 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 00:31:01.201 +04:00 [INF] Request GET / started with correlation ID 0ac2db0d-d413-417b-93db-2c25cfc5fa81
2025-06-19 00:31:01.363 +04:00 [INF] Request GET / completed in 151ms with status 404 (Correlation ID: 0ac2db0d-d413-417b-93db-2c25cfc5fa81)
2025-06-19 00:31:01.379 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 394.6096ms
2025-06-19 00:31:01.400 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-19 00:52:06.087 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:52:06.134 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-19 00:52:06.147 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-19 00:52:07.116 +04:00 [INF] Executed DbCommand (108ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-19 00:52:07.142 +04:00 [INF] LexAI Identity Service started successfully
2025-06-19 00:52:07.226 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:52:07.671 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-19 00:52:07.673 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-19 00:52:08.125 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:52:08.129 +04:00 [INF] Hosting environment: Development
2025-06-19 00:52:08.131 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-19 00:52:08.517 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-19 00:52:08.751 +04:00 [INF] Request GET / started with correlation ID ab70ee8c-1498-4a72-8d83-06e7a5035791
2025-06-19 00:52:10.352 +04:00 [INF] Request GET / completed in 1597ms with status 404 (Correlation ID: ab70ee8c-1498-4a72-8d83-06e7a5035791)
2025-06-19 00:52:10.363 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 1858.2173ms
2025-06-19 00:52:10.374 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
