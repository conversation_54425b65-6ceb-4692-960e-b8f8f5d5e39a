2025-06-10 20:22:39.178 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-10 20:22:39.264 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:22:39.524 +04:00 [INF] Now listening on: https://localhost:58323
2025-06-10 20:22:39.527 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-10 20:22:40.114 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:22:40.115 +04:00 [INF] Hosting environment: Development
2025-06-10 20:22:40.118 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-10 20:22:40.763 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/ - null null
2025-06-10 20:22:41.008 +04:00 [INF] Request GET / started with correlation ID 49f9af47-6e19-4d25-8801-d6794222d537
2025-06-10 20:22:42.848 +04:00 [INF] Request GET / completed in 1833ms with status 404 (Correlation ID: 49f9af47-6e19-4d25-8801-d6794222d537)
2025-06-10 20:22:42.861 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/ - 404 0 null 2107.5712ms
2025-06-10 20:22:42.892 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:58323/, Response status code: 404
2025-06-10 20:23:11.385 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger - null null
2025-06-10 20:23:11.431 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger - 301 0 null 46.835ms
2025-06-10 20:23:11.467 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/index.html - null null
2025-06-10 20:23:11.556 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/index.html - 200 null text/html;charset=utf-8 88.9644ms
2025-06-10 20:23:11.581 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/swagger-ui.css - null null
2025-06-10 20:23:11.589 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/index.css - null null
2025-06-10 20:23:11.593 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/swagger-ui-bundle.js - null null
2025-06-10 20:23:11.620 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/swagger-ui-standalone-preset.js - null null
2025-06-10 20:23:11.646 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/index.js - null null
2025-06-10 20:23:11.656 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/_framework/aspnetcore-browser-refresh.js - null null
2025-06-10 20:23:11.745 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-10 20:23:11.752 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/index.js - 200 null application/javascript;charset=utf-8 105.986ms
2025-06-10 20:23:11.774 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/index.css - 200 202 text/css 185.0833ms
2025-06-10 20:23:11.782 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/_framework/aspnetcore-browser-refresh.js - 200 16527 application/javascript; charset=utf-8 126.1631ms
2025-06-10 20:23:11.784 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-10 20:23:11.805 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/swagger-ui.css - 200 152035 text/css 224.5973ms
2025-06-10 20:23:11.848 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-10 20:23:11.852 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/_vs/browserLink - null null
2025-06-10 20:23:11.860 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 263.5783ms
2025-06-10 20:23:12.011 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-10 20:23:12.014 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 420.8618ms
2025-06-10 20:23:12.061 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/_vs/browserLink - 200 null text/javascript; charset=UTF-8 208.7449ms
2025-06-10 20:23:12.151 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/v1/swagger.json - null null
2025-06-10 20:23:12.177 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/swagger/favicon-32x32.png - null null
2025-06-10 20:23:12.182 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-10 20:23:12.186 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/favicon-32x32.png - 200 628 image/png 9.263ms
2025-06-10 20:23:12.213 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 62.1242ms
