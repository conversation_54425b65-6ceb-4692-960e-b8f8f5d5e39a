Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.ApiGateway", "src\ApiGateway\LexAI.ApiGateway.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Identity.Domain", "src\Services\Identity\LexAI.Identity.Domain\LexAI.Identity.Domain.csproj", "{8A995A0E-8D75-4175-B267-341F64F27B12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Identity.Application", "src\Services\Identity\LexAI.Identity.Application\LexAI.Identity.Application.csproj", "{1CFED82B-BB52-4162-98D4-463014013FF5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Identity.Infrastructure", "src\Services\Identity\LexAI.Identity.Infrastructure\LexAI.Identity.Infrastructure.csproj", "{32E1C28C-F033-49F3-A461-116855927092}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Identity.API", "src\Services\Identity\LexAI.Identity.API\LexAI.Identity.API.csproj", "{5D1F99A8-603B-4BE3-B46C-C1C31355B012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.LegalResearch.Domain", "src\Services\LegalResearch\LexAI.LegalResearch.Domain\LexAI.LegalResearch.Domain.csproj", "{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.LegalResearch.Application", "src\Services\LegalResearch\LexAI.LegalResearch.Application\LexAI.LegalResearch.Application.csproj", "{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.LegalResearch.Infrastructure", "src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\LexAI.LegalResearch.Infrastructure.csproj", "{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.LegalResearch.API", "src\Services\LegalResearch\LexAI.LegalResearch.API\LexAI.LegalResearch.API.csproj", "{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.AIAssistant.Domain", "src\Services\AIAssistant\LexAI.AIAssistant.Domain\LexAI.AIAssistant.Domain.csproj", "{843CDE3F-5014-4D54-8F86-7EE31FE3403B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.AIAssistant.Application", "src\Services\AIAssistant\LexAI.AIAssistant.Application\LexAI.AIAssistant.Application.csproj", "{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.AIAssistant.Infrastructure", "src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\LexAI.AIAssistant.Infrastructure.csproj", "{314B5DBD-C13A-429D-ACDD-341D23D394E1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.AIAssistant.API", "src\Services\AIAssistant\LexAI.AIAssistant.API\LexAI.AIAssistant.API.csproj", "{EC161366-B352-4B2A-88C0-334CAC92CD41}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentAnalysis.Domain", "src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Domain\LexAI.DocumentAnalysis.Domain.csproj", "{35EFBF88-55B1-426E-9E40-9F115EAEB587}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentAnalysis.Application", "src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Application\LexAI.DocumentAnalysis.Application.csproj", "{2D9F005E-F801-4E76-8B5C-13588CF401D6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentAnalysis.Infrastructure", "src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Infrastructure\LexAI.DocumentAnalysis.Infrastructure.csproj", "{6D295248-13FA-424E-BB3F-56634A365D3A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentAnalysis.API", "src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\LexAI.DocumentAnalysis.API.csproj", "{DD016E76-776E-4842-BE3C-7997C6046B0A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentGenerator.Domain", "src\Services\DocumentGenerator\LexAI.DocumentGenerator.Domain\LexAI.DocumentGenerator.Domain.csproj", "{EF606C9F-2469-4AB7-851F-2546F7F897F6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentGenerator.Application", "src\Services\DocumentGenerator\LexAI.DocumentGenerator.Application\LexAI.DocumentGenerator.Application.csproj", "{251FB678-EEFF-4D28-AADD-14AE66032C9C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentGenerator.Infrastructure", "src\Services\DocumentGenerator\LexAI.DocumentGenerator.Infrastructure\LexAI.DocumentGenerator.Infrastructure.csproj", "{4CED4C1F-DAB2-4A1A-AA68-340F8F9B17A2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DocumentGenerator.API", "src\Services\DocumentGenerator\LexAI.DocumentGenerator.API\LexAI.DocumentGenerator.API.csproj", "{786353DE-B068-4205-837F-1231CB57F4E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.ClientManagement.Domain", "src\Services\ClientManagement\LexAI.ClientManagement.Domain\LexAI.ClientManagement.Domain.csproj", "{2E4A8C42-62F6-411E-973B-52078EFA6821}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.ClientManagement.Application", "src\Services\ClientManagement\LexAI.ClientManagement.Application\LexAI.ClientManagement.Application.csproj", "{736C492E-2696-4F22-8518-9C36BA5A35CA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.ClientManagement.Infrastructure", "src\Services\ClientManagement\LexAI.ClientManagement.Infrastructure\LexAI.ClientManagement.Infrastructure.csproj", "{4CC8D416-A556-4755-9950-EC0FE3EC79E0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.ClientManagement.API", "src\Services\ClientManagement\LexAI.ClientManagement.API\LexAI.ClientManagement.API.csproj", "{9D8EF968-3942-43A0-898A-E70ED42A41ED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Shared.Domain", "src\Shared\LexAI.Shared.Domain\LexAI.Shared.Domain.csproj", "{2D333B21-37FA-4201-A868-5A56261A570A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Shared.Infrastructure", "src\Shared\LexAI.Shared.Infrastructure\LexAI.Shared.Infrastructure.csproj", "{A2B3C4D5-E6F7-8901-1234-567890123456}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Identity.UnitTests", "tests\LexAI.Identity.UnitTests\LexAI.Identity.UnitTests.csproj", "{3FB2809A-F86F-4256-B054-F5B2EA449293}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Identity.IntegrationTests", "tests\LexAI.Identity.IntegrationTests\LexAI.Identity.IntegrationTests.csproj", "{3E7C1131-B9B7-4388-9159-D6C6439720D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DataPreprocessing.API", "src\Services\DataPreprocessing\LexAI.DataPreprocessing.API\LexAI.DataPreprocessing.API.csproj", "{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DataPreprocessing.Application", "src\Services\DataPreprocessing\LexAI.DataPreprocessing.Application\LexAI.DataPreprocessing.Application.csproj", "{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DataPreprocessing.Domain", "src\Services\DataPreprocessing\LexAI.DataPreprocessing.Domain\LexAI.DataPreprocessing.Domain.csproj", "{97F301B2-C8BD-5CE1-E661-A68F4CA41D8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DataPreprocessing.Infrastructure", "src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\LexAI.DataPreprocessing.Infrastructure.csproj", "{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.DataPreprocessing.UnitTests", "tests\LexAI.DataPreprocessing.UnitTests\LexAI.DataPreprocessing.UnitTests.csproj", "{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LexAI.Shared.Application", "src\Shared\LexAI.Shared.Application\LexAI.Shared.Application.csproj", "{811C9284-8A27-4779-A8A9-BDD412C7C616}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A995A0E-8D75-4175-B267-341F64F27B12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A995A0E-8D75-4175-B267-341F64F27B12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A995A0E-8D75-4175-B267-341F64F27B12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A995A0E-8D75-4175-B267-341F64F27B12}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CFED82B-BB52-4162-98D4-463014013FF5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CFED82B-BB52-4162-98D4-463014013FF5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CFED82B-BB52-4162-98D4-463014013FF5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CFED82B-BB52-4162-98D4-463014013FF5}.Release|Any CPU.Build.0 = Release|Any CPU
		{32E1C28C-F033-49F3-A461-116855927092}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{32E1C28C-F033-49F3-A461-116855927092}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{32E1C28C-F033-49F3-A461-116855927092}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{32E1C28C-F033-49F3-A461-116855927092}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D1F99A8-603B-4BE3-B46C-C1C31355B012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D1F99A8-603B-4BE3-B46C-C1C31355B012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D1F99A8-603B-4BE3-B46C-C1C31355B012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D1F99A8-603B-4BE3-B46C-C1C31355B012}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}.Release|Any CPU.Build.0 = Release|Any CPU
		{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{843CDE3F-5014-4D54-8F86-7EE31FE3403B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{843CDE3F-5014-4D54-8F86-7EE31FE3403B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{843CDE3F-5014-4D54-8F86-7EE31FE3403B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{843CDE3F-5014-4D54-8F86-7EE31FE3403B}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}.Release|Any CPU.Build.0 = Release|Any CPU
		{314B5DBD-C13A-429D-ACDD-341D23D394E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{314B5DBD-C13A-429D-ACDD-341D23D394E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{314B5DBD-C13A-429D-ACDD-341D23D394E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{314B5DBD-C13A-429D-ACDD-341D23D394E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{EC161366-B352-4B2A-88C0-334CAC92CD41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC161366-B352-4B2A-88C0-334CAC92CD41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC161366-B352-4B2A-88C0-334CAC92CD41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC161366-B352-4B2A-88C0-334CAC92CD41}.Release|Any CPU.Build.0 = Release|Any CPU
		{35EFBF88-55B1-426E-9E40-9F115EAEB587}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35EFBF88-55B1-426E-9E40-9F115EAEB587}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35EFBF88-55B1-426E-9E40-9F115EAEB587}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35EFBF88-55B1-426E-9E40-9F115EAEB587}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D9F005E-F801-4E76-8B5C-13588CF401D6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D9F005E-F801-4E76-8B5C-13588CF401D6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D9F005E-F801-4E76-8B5C-13588CF401D6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D9F005E-F801-4E76-8B5C-13588CF401D6}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D295248-13FA-424E-BB3F-56634A365D3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D295248-13FA-424E-BB3F-56634A365D3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D295248-13FA-424E-BB3F-56634A365D3A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D295248-13FA-424E-BB3F-56634A365D3A}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD016E76-776E-4842-BE3C-7997C6046B0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD016E76-776E-4842-BE3C-7997C6046B0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD016E76-776E-4842-BE3C-7997C6046B0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD016E76-776E-4842-BE3C-7997C6046B0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D333B21-37FA-4201-A868-5A56261A570A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D333B21-37FA-4201-A868-5A56261A570A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D333B21-37FA-4201-A868-5A56261A570A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D333B21-37FA-4201-A868-5A56261A570A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2B3C4D5-E6F7-8901-1234-567890123456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2B3C4D5-E6F7-8901-1234-567890123456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2B3C4D5-E6F7-8901-1234-567890123456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2B3C4D5-E6F7-8901-1234-567890123456}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FB2809A-F86F-4256-B054-F5B2EA449293}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FB2809A-F86F-4256-B054-F5B2EA449293}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FB2809A-F86F-4256-B054-F5B2EA449293}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FB2809A-F86F-4256-B054-F5B2EA449293}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E7C1131-B9B7-4388-9159-D6C6439720D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E7C1131-B9B7-4388-9159-D6C6439720D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E7C1131-B9B7-4388-9159-D6C6439720D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E7C1131-B9B7-4388-9159-D6C6439720D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7FB6747D-3AA4-1F0A-E89E-EA1BC5786CF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{97F301B2-C8BD-5CE1-E661-A68F4CA41D8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97F301B2-C8BD-5CE1-E661-A68F4CA41D8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97F301B2-C8BD-5CE1-E661-A68F4CA41D8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97F301B2-C8BD-5CE1-E661-A68F4CA41D8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{376EC7D1-C6D4-2176-3D6A-BBB995A7F2DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{811C9284-8A27-4779-A8A9-BDD412C7C616}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{811C9284-8A27-4779-A8A9-BDD412C7C616}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{811C9284-8A27-4779-A8A9-BDD412C7C616}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{811C9284-8A27-4779-A8A9-BDD412C7C616}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4DA757D4-5E94-4304-ABD5-25C4EA5F8C65}
	EndGlobalSection
EndGlobal
