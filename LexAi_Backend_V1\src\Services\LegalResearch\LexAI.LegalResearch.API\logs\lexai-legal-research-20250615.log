2025-06-15 01:21:41.732 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-15 01:21:41.821 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-15 01:21:42.042 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-15 01:21:42.044 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-15 01:21:42.106 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 01:21:42.108 +04:00 [INF] Hosting environment: Development
2025-06-15 01:21:42.110 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-15 01:21:42.748 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-15 01:21:42.930 +04:00 [INF] Request GET / started with correlation ID 5022eda7-6d8c-4edb-9ec3-93e8f98a74c2
2025-06-15 01:21:44.994 +04:00 [INF] Request GET / completed in 2061ms with status 404 (Correlation ID: 5022eda7-6d8c-4edb-9ec3-93e8f98a74c2)
2025-06-15 01:21:45.005 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2261.8271ms
2025-06-15 01:21:45.024 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-15 01:46:09.876 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-15 01:46:10.011 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-15 01:46:10.453 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-15 01:46:10.455 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-15 01:46:10.522 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 01:46:10.526 +04:00 [INF] Hosting environment: Development
2025-06-15 01:46:10.527 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-15 01:46:11.709 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-15 01:46:11.942 +04:00 [INF] Request GET / started with correlation ID 9ca63ef6-f160-4d6a-a4f7-feaa53530bb1
2025-06-15 01:46:12.127 +04:00 [INF] Request GET / completed in 181ms with status 404 (Correlation ID: 9ca63ef6-f160-4d6a-a4f7-feaa53530bb1)
2025-06-15 01:46:12.139 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 432.4243ms
2025-06-15 01:46:12.159 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-15 02:25:36.453 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-15 02:25:36.544 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-15 02:25:36.880 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-15 02:25:36.901 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-15 02:25:36.990 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 02:25:36.992 +04:00 [INF] Hosting environment: Development
2025-06-15 02:25:36.994 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-15 02:25:37.784 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-15 02:25:38.018 +04:00 [INF] Request GET / started with correlation ID 8221b73a-c9d8-46a9-80b8-f81d3cdbbe48
2025-06-15 02:25:38.348 +04:00 [INF] Request GET / completed in 314ms with status 404 (Correlation ID: 8221b73a-c9d8-46a9-80b8-f81d3cdbbe48)
2025-06-15 02:25:38.369 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 587.5171ms
2025-06-15 02:25:38.396 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
