import React, { useState, useCallback } from 'react'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Upload, FileText, AlertTriangle, CheckCircle, Clock, X, Settings, Download, Eye } from 'lucide-react'
import { documentAnalysisApi, type DocumentAnalysisRequest, type DocumentAnalysisResponse, type AnalysisOptions } from '../../services/documentAnalysisApi'

interface EnhancedDocumentAnalyzerProps {
  onAnalysisComplete?: (analysis: DocumentAnalysisResponse) => void
}

export const EnhancedDocumentAnalyzer: React.FC<EnhancedDocumentAnalyzerProps> = ({ onAnalysisComplete }) => {
  const [file, setFile] = useState<File | null>(null)
  const [documentName, setDocumentName] = useState('')
  const [options, setOptions] = useState<AnalysisOptions>(documentAnalysisApi.getDefaultOptions())
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<DocumentAnalysisResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelection(e.dataTransfer.files[0])
    }
  }, [])

  const handleFileSelection = (selectedFile: File) => {
    // Validation du type de fichier
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
    if (!allowedTypes.includes(selectedFile.type)) {
      setError('Type de fichier non supporté. Formats acceptés: PDF, DOCX, TXT')
      return
    }

    // Validation de la taille (max 50MB)
    if (selectedFile.size > 50 * 1024 * 1024) {
      setError('Le fichier est trop volumineux (maximum 50MB)')
      return
    }

    setFile(selectedFile)
    setDocumentName(selectedFile.name)
    setError(null)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      handleFileSelection(selectedFile)
    }
  }

  const handleAnalyze = async () => {
    if (!file) {
      setError('Veuillez sélectionner un fichier à analyser')
      return
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const request: DocumentAnalysisRequest = {
        documentFile: file,
        documentName: documentName || file.name,
        options
      }

      const response = await documentAnalysisApi.analyzeDocument(request)
      setAnalysis(response)
      onAnalysisComplete?.(response)
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Erreur lors de l\'analyse du document')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const updateOption = (key: keyof AnalysisOptions, value: any) => {
    setOptions(prev => ({ ...prev, [key]: value }))
  }

  const addFocusArea = (area: string) => {
    if (area && !options.focusAreas?.includes(area)) {
      setOptions(prev => ({
        ...prev,
        focusAreas: [...(prev.focusAreas || []), area]
      }))
    }
  }

  const removeFocusArea = (area: string) => {
    setOptions(prev => ({
      ...prev,
      focusAreas: prev.focusAreas?.filter(a => a !== area) || []
    }))
  }

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical':
        return 'border-red-200 bg-red-50 text-red-800'
      case 'high':
        return 'border-orange-200 bg-orange-50 text-orange-800'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50 text-yellow-800'
      case 'low':
        return 'border-green-200 bg-green-50 text-green-800'
      default:
        return 'border-gray-200 bg-gray-50 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Configuration de l'analyse */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Analyse Avancée de Document Juridique
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Zone de drop */}
          <div
            className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-blue-400 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              accept=".pdf,.docx,.txt"
              onChange={handleFileUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <div className="space-y-4">
              <Upload className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <p className="text-lg font-medium text-gray-900">
                  {file ? file.name : 'Glissez votre document ici ou cliquez pour sélectionner'}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Formats supportés: PDF, DOCX, TXT (max 50MB)
                </p>
              </div>
              {file && (
                <div className="flex items-center justify-center gap-2 text-sm text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Fichier sélectionné: {(file.size / 1024 / 1024).toFixed(2)} MB
                </div>
              )}
            </div>
          </div>

          {/* Nom du document */}
          {file && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nom du document
              </label>
              <input
                type="text"
                value={documentName}
                onChange={(e) => setDocumentName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nom du document..."
              />
            </div>
          )}

          {/* Options d'analyse */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Options d'analyse</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              >
                <Settings className="h-4 w-4 mr-2" />
                {showAdvancedOptions ? 'Masquer' : 'Afficher'} les options avancées
              </Button>
            </div>

            {/* Options de base */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.extractClauses}
                  onChange={(e) => updateOption('extractClauses', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Extraire les clauses</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.performRiskAssessment}
                  onChange={(e) => updateOption('performRiskAssessment', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Évaluer les risques</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.generateRecommendations}
                  onChange={(e) => updateOption('generateRecommendations', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Générer des recommandations</span>
              </label>
            </div>

            {/* Options avancées */}
            {showAdvancedOptions && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={options.extractEntities}
                      onChange={(e) => updateOption('extractEntities', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Extraire les entités</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={options.findCitations}
                      onChange={(e) => updateOption('findCitations', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Trouver les citations</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={options.useAzureDocumentIntelligence}
                      onChange={(e) => updateOption('useAzureDocumentIntelligence', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Azure Document Intelligence</span>
                  </label>
                </div>

                {/* Domaines d'analyse spécifiques */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Domaines d'analyse spécifiques
                  </label>
                  <div className="flex gap-2 mb-2">
                    <input
                      type="text"
                      placeholder="Ajouter un domaine..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          addFocusArea((e.target as HTMLInputElement).value)
                          ;(e.target as HTMLInputElement).value = ''
                        }
                      }}
                    />
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {options.focusAreas?.map((area) => (
                      <span
                        key={area}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                      >
                        {area}
                        <button
                          onClick={() => removeFocusArea(area)}
                          className="hover:text-blue-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Boutons d'action */}
          <div className="flex gap-3">
            <Button
              onClick={handleAnalyze}
              disabled={isAnalyzing || !file}
              className="flex-1"
            >
              {isAnalyzing ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Analyse en cours...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Analyser le document
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setOptions(documentAnalysisApi.getQuickAnalysisOptions())}
              disabled={isAnalyzing}
            >
              Analyse rapide
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setOptions(documentAnalysisApi.getComprehensiveAnalysisOptions())}
              disabled={isAnalyzing}
            >
              Analyse complète
            </Button>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
              {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Résultats de l'analyse */}
      {analysis && (
        <div className="space-y-6">
          {/* En-tête des résultats */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Résultats de l'analyse</CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Exporter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    Voir le détail
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{analysis.clauses.length}</div>
                  <div className="text-sm text-gray-600">Clauses analysées</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{analysis.risks.length}</div>
                  <div className="text-sm text-gray-600">Risques identifiés</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{analysis.recommendations.length}</div>
                  <div className="text-sm text-gray-600">Recommandations</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getRiskColor(analysis.summary.overallRiskLevel).split(' ')[2]}`}>
                    {analysis.summary.overallRiskLevel}
                  </div>
                  <div className="text-sm text-gray-600">Niveau de risque</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Résumé exécutif */}
          {analysis.summary.executiveSummary && (
            <Card>
              <CardHeader>
                <CardTitle>Résumé exécutif</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{analysis.summary.executiveSummary}</p>
              </CardContent>
            </Card>
          )}

          {/* Risques critiques */}
          {analysis.risks.filter(r => r.severity === 'Critical' || r.severity === 'High').length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Risques prioritaires
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysis.risks
                    .filter(r => r.severity === 'Critical' || r.severity === 'High')
                    .map((risk) => (
                      <div key={risk.id} className={`p-4 rounded-lg border ${getRiskColor(risk.severity)}`}>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{risk.riskType}</h4>
                            <p className="text-sm mt-1">{risk.description}</p>
                            {risk.mitigation && (
                              <p className="text-sm mt-2 font-medium">Mitigation: {risk.mitigation}</p>
                            )}
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(risk.severity)}`}>
                            {risk.severity}
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommandations prioritaires */}
          {analysis.recommendations.filter(r => r.priority === 'Critical' || r.priority === 'High').length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recommandations prioritaires</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysis.recommendations
                    .filter(r => r.priority === 'Critical' || r.priority === 'High')
                    .map((rec) => (
                      <div key={rec.id} className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-blue-900">{rec.title}</h4>
                            <p className="text-sm text-blue-700 mt-1">{rec.description}</p>
                            {rec.suggestedAction && (
                              <p className="text-sm text-blue-800 mt-2 font-medium">
                                Action suggérée: {rec.suggestedAction}
                              </p>
                            )}
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(rec.priority)}`}>
                            {rec.priority}
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Métadonnées de l'analyse */}
          <Card>
            <CardHeader>
              <CardTitle>Informations sur l'analyse</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium text-gray-900">Temps de traitement</div>
                  <div className="text-gray-600">{analysis.processingTimeMs}ms</div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Tokens utilisés</div>
                  <div className="text-gray-600">{analysis.tokensUsed}</div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Coût estimé</div>
                  <div className="text-gray-600">${analysis.estimatedCost.toFixed(4)}</div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">Modèle utilisé</div>
                  <div className="text-gray-600">{analysis.modelUsed}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
