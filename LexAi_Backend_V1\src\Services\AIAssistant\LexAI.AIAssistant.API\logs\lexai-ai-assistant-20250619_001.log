2025-06-19 22:20:37.738 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-19 22:20:37.881 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 22:20:38.268 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-19 22:20:38.271 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-19 22:20:38.326 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 22:20:38.329 +04:00 [INF] Hosting environment: Development
2025-06-19 22:20:38.903 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-19 22:20:39.606 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-19 22:20:39.807 +04:00 [INF] Request GET / started with correlation ID f3b28935-73d8-43e9-beec-c75e22da3edc
2025-06-19 22:20:40.053 +04:00 [INF] Request GET / completed in 241ms with status 404 (Correlation ID: f3b28935-73d8-43e9-beec-c75e22da3edc)
2025-06-19 22:20:40.074 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 471.0957ms
2025-06-19 22:20:40.103 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-19 22:21:57.588 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:21:57.607 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:21:57.649 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID ee56027f-660e-4752-aedf-471d6b507d0b
2025-06-19 22:21:57.680 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 94c47f5b-272e-4e66-81c2-ee1f258094d3
2025-06-19 22:21:57.696 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:21:57.696 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:21:57.706 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 14ms with status 204 (Correlation ID: 94c47f5b-272e-4e66-81c2-ee1f258094d3)
2025-06-19 22:21:57.706 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 19ms with status 204 (Correlation ID: ee56027f-660e-4752-aedf-471d6b507d0b)
2025-06-19 22:21:57.715 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 108.2579ms
2025-06-19 22:21:57.721 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:21:57.722 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 133.6946ms
2025-06-19 22:21:57.747 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 52457028-41b4-429d-9551-da48e1b17c63
2025-06-19 22:21:57.753 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:21:57.928 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:21:57.944 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:21:58.013 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:21:58.116 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:22:00.241 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 22:22:00.274 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 22:22:02.234 +04:00 [INF] Executed DbCommand (143ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:22:02.573 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:22:02.606 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:22:02.691 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:22:02.700 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 4666.4135ms
2025-06-19 22:22:02.710 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 4f39db9d-77de-4475-a7dd-797a72621499
2025-06-19 22:22:02.714 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:22:02.717 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:22:02.723 +04:00 [INF] Request GET /api/chat/conversations completed in 4969ms with status 200 (Correlation ID: 52457028-41b4-429d-9551-da48e1b17c63)
2025-06-19 22:22:02.728 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:22:02.735 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:22:02.742 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:22:02.756 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:22:02.767 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 5046.1685ms
2025-06-19 22:22:02.828 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:22:02.836 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:22:02.844 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:22:02.847 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 91.4598ms
2025-06-19 22:22:02.849 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:22:02.851 +04:00 [INF] Request GET /api/chat/conversations completed in 134ms with status 200 (Correlation ID: 4f39db9d-77de-4475-a7dd-797a72621499)
2025-06-19 22:22:02.858 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 166.7967ms
2025-06-19 22:22:23.542 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - null null
2025-06-19 22:22:23.550 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined started with correlation ID 1ebc0af0-fd55-4aaf-a7fe-25acf5800edd
2025-06-19 22:22:23.553 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:22:23.554 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined completed in 1ms with status 204 (Correlation ID: 1ebc0af0-fd55-4aaf-a7fe-25acf5800edd)
2025-06-19 22:22:23.557 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - 204 null null 15.1086ms
2025-06-19 22:22:23.560 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - application/json null
2025-06-19 22:22:23.567 +04:00 [INF] Request GET /api/chat/conversations/undefined started with correlation ID 62de689a-b52b-4184-aa6b-9db47bc6e746
2025-06-19 22:22:23.571 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:22:23.573 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:22:23.575 +04:00 [INF] Request GET /api/chat/conversations/undefined completed in 4ms with status 404 (Correlation ID: 62de689a-b52b-4184-aa6b-9db47bc6e746)
2025-06-19 22:22:23.579 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - 404 0 null 19.1829ms
2025-06-19 22:22:23.586 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/api/chat/conversations/undefined, Response status code: 404
2025-06-19 22:22:30.358 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - null null
2025-06-19 22:22:30.364 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined started with correlation ID c4c545db-b254-4ed4-8a11-6c802622fe08
2025-06-19 22:22:30.367 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:22:30.369 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined completed in 2ms with status 204 (Correlation ID: c4c545db-b254-4ed4-8a11-6c802622fe08)
2025-06-19 22:22:30.375 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - 204 null null 16.4983ms
2025-06-19 22:22:30.379 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - application/json null
2025-06-19 22:22:30.389 +04:00 [INF] Request GET /api/chat/conversations/undefined started with correlation ID af7c6f8b-8f39-4273-9505-783b6ebabbab
2025-06-19 22:22:30.391 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:22:30.394 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:22:30.397 +04:00 [INF] Request GET /api/chat/conversations/undefined completed in 5ms with status 404 (Correlation ID: af7c6f8b-8f39-4273-9505-783b6ebabbab)
2025-06-19 22:22:30.402 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - 404 0 null 22.8072ms
2025-06-19 22:22:30.413 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/api/chat/conversations/undefined, Response status code: 404
2025-06-19 22:22:31.721 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - application/json null
2025-06-19 22:22:31.730 +04:00 [INF] Request GET /api/chat/conversations/undefined started with correlation ID 191c716e-c2a1-4163-abe0-ee0b18dc291e
2025-06-19 22:22:31.733 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:22:31.735 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:22:31.737 +04:00 [INF] Request GET /api/chat/conversations/undefined completed in 3ms with status 404 (Correlation ID: 191c716e-c2a1-4163-abe0-ee0b18dc291e)
2025-06-19 22:22:31.742 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - 404 0 null 19.7148ms
2025-06-19 22:22:31.754 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/api/chat/conversations/undefined, Response status code: 404
2025-06-19 22:23:28.577 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 22:23:28.584 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 13147b18-253b-464d-ab69-71dfee4c2b14
2025-06-19 22:23:28.586 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:23:28.588 +04:00 [INF] Request OPTIONS /api/chat/message completed in 1ms with status 204 (Correlation ID: 13147b18-253b-464d-ab69-71dfee4c2b14)
2025-06-19 22:23:28.592 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 15.6703ms
2025-06-19 22:23:28.595 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 51
2025-06-19 22:23:28.605 +04:00 [INF] Request POST /api/chat/message started with correlation ID 397f6f84-05b6-4830-83fc-62ea65fa5976
2025-06-19 22:23:28.608 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:23:28.610 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:23:28.615 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 22:23:28.630 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:23:28.639 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:23:28.732 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": avantage des travailleurs au cameroun
2025-06-19 22:23:28.746 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": avantage des travailleurs au cameroun
2025-06-19 22:23:29.117 +04:00 [INF] Executed DbCommand (20ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-19 22:23:29.170 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": avantage des travailleurs au cameroun
2025-06-19 22:23:36.075 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1052, Cost: 0, Time: 5050ms
2025-06-19 22:23:36.138 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:23:36.361 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-19 22:23:37.030 +04:00 [WRN] Concurrency conflict saving conversation "e3fc9152-89ab-4512-96cb-ce3f284229e8". Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 214
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-19 22:23:37.208 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:23:37.222 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-19 22:23:37.553 +04:00 [WRN] Concurrency conflict saving conversation "e3fc9152-89ab-4512-96cb-ce3f284229e8". Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 214
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-19 22:23:37.779 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:23:37.801 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-19 22:23:38.022 +04:00 [WRN] Concurrency conflict saving conversation "e3fc9152-89ab-4512-96cb-ce3f284229e8". Retry attempt 3/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 214
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-19 22:23:38.033 +04:00 [ERR] Max retry attempts reached for conversation "e3fc9152-89ab-4512-96cb-ce3f284229e8"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 214
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-19 22:23:38.328 +04:00 [ERR] Error processing message for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 214
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 212
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
2025-06-19 22:23:38.656 +04:00 [ERR] Error processing chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 214
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 212
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
   at LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(ChatRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Controllers\ChatController.cs:line 86
2025-06-19 22:23:38.679 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 22:23:38.707 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 10071.354ms
2025-06-19 22:23:38.711 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 22:23:38.713 +04:00 [INF] Request POST /api/chat/message completed in 10105ms with status 500 (Correlation ID: 397f6f84-05b6-4830-83fc-62ea65fa5976)
2025-06-19 22:23:38.716 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null application/json; charset=utf-8 10121.3951ms
2025-06-19 22:25:59.104 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:25:59.109 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:25:59.111 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 969bba3d-0d79-47a6-9739-dd6b7a8d35cf
2025-06-19 22:25:59.120 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:25:59.121 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 9f21578f-c456-46c7-af30-fb3a98590442
2025-06-19 22:25:59.123 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 2ms with status 204 (Correlation ID: 969bba3d-0d79-47a6-9739-dd6b7a8d35cf)
2025-06-19 22:25:59.125 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:25:59.131 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 26.122ms
2025-06-19 22:25:59.134 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 8ms with status 204 (Correlation ID: 9f21578f-c456-46c7-af30-fb3a98590442)
2025-06-19 22:25:59.141 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:25:59.149 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 39.7812ms
2025-06-19 22:25:59.155 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 0309f4b5-321f-4972-adf2-7bc52a37bdb3
2025-06-19 22:25:59.165 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:25:59.180 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 6:19:12 PM', Current time (UTC): '6/19/2025 6:25:59 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-19 22:25:59.196 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 6:19:12 PM', Current time (UTC): '6/19/2025 6:25:59 PM'.
2025-06-19 22:25:59.200 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 6:19:12 PM', Current time (UTC): '6/19/2025 6:25:59 PM'.
2025-06-19 22:25:59.206 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-19 22:25:59.221 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-19 22:25:59.223 +04:00 [INF] Request GET /api/chat/conversations completed in 58ms with status 401 (Correlation ID: 0309f4b5-321f-4972-adf2-7bc52a37bdb3)
2025-06-19 22:25:59.227 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 401 0 null 86.0203ms
2025-06-19 22:25:59.231 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:25:59.242 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 89d85b7d-adc7-401d-a217-071d6713c5d2
2025-06-19 22:25:59.267 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:25:59.271 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 6:19:12 PM', Current time (UTC): '6/19/2025 6:25:59 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-19 22:25:59.279 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 6:19:12 PM', Current time (UTC): '6/19/2025 6:25:59 PM'.
2025-06-19 22:25:59.282 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 6:19:12 PM', Current time (UTC): '6/19/2025 6:25:59 PM'.
2025-06-19 22:25:59.285 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-19 22:25:59.287 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-19 22:25:59.288 +04:00 [INF] Request GET /api/chat/conversations completed in 21ms with status 401 (Correlation ID: 89d85b7d-adc7-401d-a217-071d6713c5d2)
2025-06-19 22:25:59.293 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 401 0 null 62.4751ms
2025-06-19 22:26:05.798 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:26:05.798 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:26:05.806 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 5b7d0fc0-d2e7-4231-9b62-20abd9e8133d
2025-06-19 22:26:05.813 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID c52daff4-724c-458a-813c-627131ca1b93
2025-06-19 22:26:05.815 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:26:05.819 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:26:05.821 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 6ms with status 204 (Correlation ID: 5b7d0fc0-d2e7-4231-9b62-20abd9e8133d)
2025-06-19 22:26:05.822 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 2ms with status 204 (Correlation ID: c52daff4-724c-458a-813c-627131ca1b93)
2025-06-19 22:26:05.825 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 26.4287ms
2025-06-19 22:26:05.829 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 30.2677ms
2025-06-19 22:26:05.828 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:26:05.853 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 01d3c134-ae6b-4aa3-8535-c3ce8cf9cc1b
2025-06-19 22:26:05.855 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:26:05.857 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:26:05.862 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:26:05.872 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:26:05.876 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:26:05.889 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:26:05.906 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:26:05.914 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:26:05.924 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 48.1852ms
2025-06-19 22:26:05.927 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:26:05.928 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:26:05.932 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID f1fc92c6-e472-4559-8379-beb60a8fb964
2025-06-19 22:26:05.934 +04:00 [INF] Request GET /api/chat/conversations completed in 79ms with status 200 (Correlation ID: 01d3c134-ae6b-4aa3-8535-c3ce8cf9cc1b)
2025-06-19 22:26:05.938 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:26:05.942 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 113.8799ms
2025-06-19 22:26:05.944 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:26:05.950 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:26:05.952 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:26:05.956 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:26:05.966 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:26:05.977 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:26:05.982 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:26:05.986 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 30.0787ms
2025-06-19 22:26:05.988 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:26:05.990 +04:00 [INF] Request GET /api/chat/conversations completed in 51ms with status 200 (Correlation ID: f1fc92c6-e472-4559-8379-beb60a8fb964)
2025-06-19 22:26:05.994 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 66.7736ms
2025-06-19 22:26:16.492 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 22:26:16.502 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID bd11a34f-c40d-4a72-b190-56f4650f87be
2025-06-19 22:26:16.509 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:26:16.510 +04:00 [INF] Request OPTIONS /api/chat/message completed in 1ms with status 204 (Correlation ID: bd11a34f-c40d-4a72-b190-56f4650f87be)
2025-06-19 22:26:16.517 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 24.5941ms
2025-06-19 22:26:16.520 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 51
2025-06-19 22:26:16.541 +04:00 [INF] Request POST /api/chat/message started with correlation ID 2fda8783-1b1a-4de1-b997-c675f56abe1e
2025-06-19 22:26:16.547 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:26:16.549 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:26:16.552 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 22:26:16.563 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:26:16.570 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:26:16.596 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": avantage des travailleurs au cameroun
2025-06-19 22:26:16.607 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": avantage des travailleurs au cameroun
2025-06-19 22:26:16.621 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-19 22:26:16.630 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": avantage des travailleurs au cameroun
2025-06-19 22:26:22.732 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 899, Cost: 0, Time: 4786ms
2025-06-19 22:26:22.738 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "2608bd44-f21a-42a4-a663-f85e2ebb1cbb", Tokens: 899, Cost: 0
2025-06-19 22:26:22.745 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "2608bd44-f21a-42a4-a663-f85e2ebb1cbb", Tokens: 899, Cost: 0
2025-06-19 22:26:22.755 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-19 22:26:22.842 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 6271.7531ms
2025-06-19 22:26:22.846 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 22:26:22.849 +04:00 [INF] Request POST /api/chat/message completed in 6302ms with status 200 (Correlation ID: 2fda8783-1b1a-4de1-b997-c675f56abe1e)
2025-06-19 22:26:22.856 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 6335.1678ms
2025-06-19 22:27:41.530 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - null null
2025-06-19 22:27:41.549 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined started with correlation ID d998502f-67b3-4220-97cb-f51215df5a99
2025-06-19 22:27:41.551 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:27:41.553 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined completed in 1ms with status 204 (Correlation ID: d998502f-67b3-4220-97cb-f51215df5a99)
2025-06-19 22:27:41.557 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - 204 null null 27.5894ms
2025-06-19 22:27:41.563 +04:00 [INF] Request starting HTTP/2 DELETE https://localhost:5003/api/chat/conversations/undefined - application/json null
2025-06-19 22:27:41.576 +04:00 [INF] Request DELETE /api/chat/conversations/undefined started with correlation ID 51f30d3b-af7d-46b7-b7a3-26f05700c0b5
2025-06-19 22:27:41.580 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:27:41.581 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:27:41.584 +04:00 [INF] Request DELETE /api/chat/conversations/undefined completed in 4ms with status 404 (Correlation ID: 51f30d3b-af7d-46b7-b7a3-26f05700c0b5)
2025-06-19 22:27:41.591 +04:00 [INF] Request finished HTTP/2 DELETE https://localhost:5003/api/chat/conversations/undefined - 404 0 null 27.5993ms
2025-06-19 22:27:41.605 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: DELETE https://localhost:5003/api/chat/conversations/undefined, Response status code: 404
2025-06-19 22:27:50.292 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - null null
2025-06-19 22:27:50.299 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined started with correlation ID cf452ecd-e85d-4b12-93a5-186c5e187753
2025-06-19 22:27:50.304 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:27:50.307 +04:00 [INF] Request OPTIONS /api/chat/conversations/undefined completed in 3ms with status 204 (Correlation ID: cf452ecd-e85d-4b12-93a5-186c5e187753)
2025-06-19 22:27:50.316 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/undefined - 204 null null 24.0538ms
2025-06-19 22:27:50.319 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - application/json null
2025-06-19 22:27:50.346 +04:00 [INF] Request GET /api/chat/conversations/undefined started with correlation ID 4d49a470-3161-4799-aa90-e90fc5d9f7f1
2025-06-19 22:27:50.352 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:27:50.357 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:27:50.361 +04:00 [INF] Request GET /api/chat/conversations/undefined completed in 9ms with status 404 (Correlation ID: 4d49a470-3161-4799-aa90-e90fc5d9f7f1)
2025-06-19 22:27:50.370 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/undefined - 404 0 null 50.8283ms
2025-06-19 22:27:50.380 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/api/chat/conversations/undefined, Response status code: 404
2025-06-19 22:55:45.549 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:55:45.619 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 7c600041-33ca-4361-ab10-18b8f0809b75
2025-06-19 22:55:45.622 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:55:45.624 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 1ms with status 204 (Correlation ID: 7c600041-33ca-4361-ab10-18b8f0809b75)
2025-06-19 22:55:45.628 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 78.2527ms
2025-06-19 22:55:45.637 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:55:45.643 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID efc534d0-dd8a-4752-8c15-256d5a11800c
2025-06-19 22:55:45.647 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:55:45.653 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:55:45.657 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:55:45.659 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:55:45.668 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:55:45.778 +04:00 [INF] Executed DbCommand (38ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:55:45.793 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:55:45.799 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:55:45.803 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 135.295ms
2025-06-19 22:55:45.806 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:55:45.806 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:55:45.809 +04:00 [INF] Request GET /api/chat/conversations completed in 161ms with status 200 (Correlation ID: efc534d0-dd8a-4752-8c15-256d5a11800c)
2025-06-19 22:55:45.816 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 1e397280-b68b-4924-8c7f-87e36f4f46cf
2025-06-19 22:55:45.819 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 182.267ms
2025-06-19 22:55:45.821 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:55:45.831 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:55:45.835 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:55:45.838 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:55:45.843 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:55:45.862 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:55:45.881 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:55:45.893 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:55:45.897 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 54.3035ms
2025-06-19 22:55:45.903 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:55:45.908 +04:00 [INF] Request GET /api/chat/conversations completed in 86ms with status 200 (Correlation ID: 1e397280-b68b-4924-8c7f-87e36f4f46cf)
2025-06-19 22:55:45.916 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 109.7866ms
2025-06-19 22:56:11.217 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - null null
2025-06-19 22:56:11.245 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 7b0a9a58-ef97-41fa-b807-3852a14d970f
2025-06-19 22:56:11.258 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:11.265 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 7ms with status 204 (Correlation ID: 7b0a9a58-ef97-41fa-b807-3852a14d970f)
2025-06-19 22:56:11.285 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 204 null null 66.8101ms
2025-06-19 22:56:11.292 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-19 22:56:11.325 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 07cbbda6-c9af-44c1-988f-6f76d885d953
2025-06-19 22:56:11.334 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:11.340 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:56:11.344 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:11.358 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:56:11.363 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:56:11.444 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:56:11.485 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-19 22:56:11.595 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 232.7411ms
2025-06-19 22:56:11.611 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:11.630 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 296ms with status 200 (Correlation ID: 07cbbda6-c9af-44c1-988f-6f76d885d953)
2025-06-19 22:56:11.658 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 365.2889ms
2025-06-19 22:56:25.547 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - null null
2025-06-19 22:56:25.564 +04:00 [INF] Request OPTIONS /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 started with correlation ID 90fc6a5c-a6a8-44a5-8611-f6a77fa47d62
2025-06-19 22:56:25.575 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:25.599 +04:00 [INF] Request OPTIONS /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 completed in 24ms with status 204 (Correlation ID: 90fc6a5c-a6a8-44a5-8611-f6a77fa47d62)
2025-06-19 22:56:25.616 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - 204 null null 69.1592ms
2025-06-19 22:56:25.632 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - application/json null
2025-06-19 22:56:25.660 +04:00 [INF] Request GET /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 started with correlation ID 4a46276f-cda7-4a5f-9781-54abc258189d
2025-06-19 22:56:25.667 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:25.672 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:56:25.674 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:25.687 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:56:25.691 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:56:25.699 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:56:25.705 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-19 22:56:25.708 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 17.0559ms
2025-06-19 22:56:25.712 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:25.715 +04:00 [INF] Request GET /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 completed in 47ms with status 200 (Correlation ID: 4a46276f-cda7-4a5f-9781-54abc258189d)
2025-06-19 22:56:25.722 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - 200 null application/json; charset=utf-8 90.2215ms
2025-06-19 22:56:39.611 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef - null null
2025-06-19 22:56:39.625 +04:00 [INF] Request OPTIONS /api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef started with correlation ID 705e73d8-06c0-459b-a337-6ad35f740c8f
2025-06-19 22:56:39.631 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:39.633 +04:00 [INF] Request OPTIONS /api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef completed in 2ms with status 204 (Correlation ID: 705e73d8-06c0-459b-a337-6ad35f740c8f)
2025-06-19 22:56:39.642 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef - 204 null null 30.5614ms
2025-06-19 22:56:39.668 +04:00 [INF] Request starting HTTP/2 DELETE https://localhost:5003/api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef - application/json null
2025-06-19 22:56:39.697 +04:00 [INF] Request DELETE /api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef started with correlation ID c65afefd-45f7-4907-8ef8-3d8f0cfb8beb
2025-06-19 22:56:39.703 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:39.707 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:56:39.711 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:39.724 +04:00 [INF] Route matched with {action = "DeleteConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] DeleteConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:56:39.729 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:56:39.740 +04:00 [INF] Deleting conversation "369f8753-146f-458f-bce2-e90493185eef" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 22:56:39.759 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:56:39.778 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:56:39.814 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "Conversations"
WHERE "Id" = @p0 AND xmin = @p1;
2025-06-19 22:56:39.827 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-19 22:56:39.838 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API) in 109.5693ms
2025-06-19 22:56:39.869 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:39.879 +04:00 [INF] Request DELETE /api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef completed in 175ms with status 200 (Correlation ID: c65afefd-45f7-4907-8ef8-3d8f0cfb8beb)
2025-06-19 22:56:39.895 +04:00 [INF] Request finished HTTP/2 DELETE https://localhost:5003/api/chat/conversations/369f8753-146f-458f-bce2-e90493185eef - 200 null application/json; charset=utf-8 227.4483ms
2025-06-19 22:56:53.348 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 - null null
2025-06-19 22:56:53.364 +04:00 [INF] Request OPTIONS /api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 started with correlation ID b262c7cd-9cd2-4466-8894-3a0d45dfa0b3
2025-06-19 22:56:53.369 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:53.371 +04:00 [INF] Request OPTIONS /api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 completed in 2ms with status 204 (Correlation ID: b262c7cd-9cd2-4466-8894-3a0d45dfa0b3)
2025-06-19 22:56:53.376 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 - 204 null null 27.9933ms
2025-06-19 22:56:53.379 +04:00 [INF] Request starting HTTP/2 DELETE https://localhost:5003/api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 - application/json null
2025-06-19 22:56:53.398 +04:00 [INF] Request DELETE /api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 started with correlation ID *************-4b6f-b378-31988d8e31f2
2025-06-19 22:56:53.401 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:56:53.404 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:56:53.409 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:53.414 +04:00 [INF] Route matched with {action = "DeleteConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] DeleteConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:56:53.418 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:56:53.425 +04:00 [INF] Deleting conversation "d4f2c0ab-0714-489e-922d-62c0ab617598" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 22:56:53.442 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:56:53.457 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:56:53.473 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "Conversations"
WHERE "Id" = @p0 AND xmin = @p1;
2025-06-19 22:56:53.480 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-19 22:56:53.483 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API) in 64.6648ms
2025-06-19 22:56:53.485 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-19 22:56:53.487 +04:00 [INF] Request DELETE /api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 completed in 85ms with status 200 (Correlation ID: *************-4b6f-b378-31988d8e31f2)
2025-06-19 22:56:53.491 +04:00 [INF] Request finished HTTP/2 DELETE https://localhost:5003/api/chat/conversations/d4f2c0ab-0714-489e-922d-62c0ab617598 - 200 null application/json; charset=utf-8 112.4491ms
2025-06-19 22:57:00.550 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 - null null
2025-06-19 22:57:00.564 +04:00 [INF] Request OPTIONS /api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 started with correlation ID 10168085-b600-441e-ba7f-29c582593606
2025-06-19 22:57:00.570 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:57:00.575 +04:00 [INF] Request OPTIONS /api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 completed in 4ms with status 204 (Correlation ID: 10168085-b600-441e-ba7f-29c582593606)
2025-06-19 22:57:00.587 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 - 204 null null 37.1747ms
2025-06-19 22:57:00.591 +04:00 [INF] Request starting HTTP/2 DELETE https://localhost:5003/api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 - application/json null
2025-06-19 22:57:00.614 +04:00 [INF] Request DELETE /api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 started with correlation ID 9740240b-da90-4501-8c63-debd0321cad2
2025-06-19 22:57:00.619 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:57:00.625 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:57:00.636 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-19 22:57:00.645 +04:00 [INF] Route matched with {action = "DeleteConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] DeleteConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:57:00.657 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:57:00.668 +04:00 [INF] Deleting conversation "d73ccc1f-c24e-4b19-9e4f-a3917e2f3459" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 22:57:00.683 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:57:00.702 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:57:00.715 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "Conversations"
WHERE "Id" = @p0 AND xmin = @p1;
2025-06-19 22:57:00.721 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-19 22:57:00.725 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API) in 68.2875ms
2025-06-19 22:57:00.729 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-19 22:57:00.732 +04:00 [INF] Request DELETE /api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 completed in 112ms with status 200 (Correlation ID: 9740240b-da90-4501-8c63-debd0321cad2)
2025-06-19 22:57:00.738 +04:00 [INF] Request finished HTTP/2 DELETE https://localhost:5003/api/chat/conversations/d73ccc1f-c24e-4b19-9e4f-a3917e2f3459 - 200 null application/json; charset=utf-8 146.7279ms
2025-06-19 22:57:07.605 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - null null
2025-06-19 22:57:07.613 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID f08146cb-e533-44ee-9768-235e82c5722d
2025-06-19 22:57:07.620 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:57:07.627 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 7ms with status 204 (Correlation ID: f08146cb-e533-44ee-9768-235e82c5722d)
2025-06-19 22:57:07.636 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 204 null null 30.9926ms
2025-06-19 22:57:07.643 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-19 22:57:07.681 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 2b39b366-b24b-4616-b403-0f48312fb5d8
2025-06-19 22:57:07.690 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:57:07.695 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:57:07.700 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-19 22:57:07.705 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:57:07.715 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:57:07.736 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 22:57:07.741 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-19 22:57:07.748 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 33.8349ms
2025-06-19 22:57:07.752 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-19 22:57:07.755 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 65ms with status 200 (Correlation ID: 2b39b366-b24b-4616-b403-0f48312fb5d8)
2025-06-19 22:57:07.760 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 117.4684ms
