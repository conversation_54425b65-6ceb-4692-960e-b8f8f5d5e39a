import React, { useState, useEffect } from 'react'
import { <PERSON>, Clock, RotateCcw, Co<PERSON>, ExternalLink, Filter, Star, ThumbsUp, ThumbsDown } from 'lucide-react'
import { useThemeStore } from '../../store/themeStore'
import { searchApi } from '../../services/api'

interface Source {
  id: string
  title: string
  url: string
  source: string
  type: string
  publicationDate?: Date
  relevanceScore: number
  excerpt?: string
}

interface SearchResult {
  id: string
  query: string
  results: LegalDocument[]
  totalResults: number
  processingTimeMs: number
  searchMethod: string
  suggestions: string[]
  timestamp: Date
}

interface LegalDocument {
  id: string
  title: string
  content: string
  summary: string
  type: string
  domain: string
  source: string
  url?: string
  publicationDate?: Date
  relevanceScore: number
  highlights: string[]
}

const LegalResearch: React.FC = () => {
  const { theme } = useThemeStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [currentResult, setCurrentResult] = useState<SearchResult | null>(null)
  const [searchHistory, setSearchHistory] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const [selectedDomain, setSelectedDomain] = useState('')
  const [selectedJurisdiction, setSelectedJurisdiction] = useState('')
  const [error, setError] = useState<string | null>(null)

  // Charger l'historique des recherches au démarrage
  useEffect(() => {
    loadSearchHistory()
  }, [])

  const loadSearchHistory = async () => {
    try {
      const history = await searchApi.getSearchHistory()
      setSearchHistory(history.slice(0, 10)) // Limiter à 10 résultats récents
    } catch (err) {
      console.error('Erreur lors du chargement de l\'historique:', err)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await searchApi.performSearch({
        query: searchQuery,
        domain: selectedDomain || undefined,
        jurisdiction: selectedJurisdiction || undefined,
        maxResults: 10,
        includeHighlights: true,
      })

      const searchResult: SearchResult = {
        id: Date.now().toString(),
        query: searchQuery,
        results: response.results || [],
        totalResults: response.totalResults || 0,
        processingTimeMs: response.processingTimeMs || 0,
        searchMethod: response.searchMethod || 'hybrid',
        suggestions: response.suggestions || [],
        timestamp: new Date()
      }

      setCurrentResult(searchResult)
      setSearchHistory(prev => [searchResult, ...prev.slice(0, 9)]) // Garder max 10 éléments

      // Générer des questions de suivi basées sur les suggestions
      if (response.suggestions && response.suggestions.length > 0) {
        setFollowUpQuestions(response.suggestions.slice(0, 3))
      } else {
        setFollowUpQuestions([
          `Quelles sont les implications pratiques de "${searchQuery}" ?`,
          `Y a-t-il des exceptions à "${searchQuery}" ?`,
          `Comment "${searchQuery}" a-t-il évolué récemment ?`
        ])
      }
    } catch (err: any) {
      setError(err.message || 'Erreur lors de la recherche')
      console.error('Erreur de recherche:', err)
    } finally {
      setIsLoading(false)
    }

    setSearchQuery('')
  }

  const handleFollowUpQuestion = (question: string) => {
    setSearchQuery(question)
    handleSearch()
  }

  const regenerateAnswer = () => {
    if (currentResult) {
      // Relancer la même recherche
      const originalQuery = currentResult.query
      setSearchQuery(originalQuery)
      handleSearch()
    }
  }

  const copyResults = () => {
    if (currentResult && currentResult.results.length > 0) {
      const text = currentResult.results
        .map(doc => `${doc.title}\n${doc.summary}\nSource: ${doc.source}\n`)
        .join('\n---\n')
      navigator.clipboard.writeText(text)
    }
  }

  const provideFeedback = async (documentId: string, isRelevant: boolean) => {
    try {
      // Implémenter le feedback sur les résultats
      console.log(`Feedback pour ${documentId}: ${isRelevant ? 'pertinent' : 'non pertinent'}`)
    } catch (err) {
      console.error('Erreur lors de l\'envoi du feedback:', err)
    }
  }

  return (
    <div className={`flex h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Sidebar */}
      <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} border-r ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">ai</span>
            </div>
            <span className="font-semibold">lawyer</span>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            {/* Search History */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-400 mb-3">Recent Searches</h4>
              {searchHistory.length === 0 ? (
                <p className="text-sm text-gray-500">No searches yet</p>
              ) : (
                <div className="space-y-2">
                  {searchHistory.slice(0, 10).map(result => (
                    <div 
                      key={result.id}
                      onClick={() => setCurrentResult(result)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        currentResult?.id === result.id 
                          ? 'bg-gray-700' 
                          : 'hover:bg-gray-700'
                      }`}
                    >
                      <p className="text-sm truncate">{result.query}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {result.timestamp?.toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="p-4 border-t border-gray-700 text-center">
          <p className="text-xs text-gray-400 mb-2">
            Earn with us as an elite distributor! Click{' '}
            <span className="text-blue-400 cursor-pointer">here</span> for the survey 🔥
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {!currentResult ? (
          /* Search Interface */
          <div className="flex-1 flex items-center justify-center">
            <div className="max-w-2xl mx-auto text-center p-8">
              <h1 className="text-3xl font-bold mb-8">Legal Research</h1>
              <div className={`relative mb-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg`}>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="Ask a legal question..."
                  className={`w-full p-4 pr-12 rounded-lg border ${
                    theme === 'dark' 
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:outline-none focus:ring-2 focus:ring-purple-500`}
                />
                <button 
                  onClick={handleSearch}
                  disabled={isLoading}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-purple-500 transition-colors"
                >
                  <Search className="w-5 h-5" />
                </button>
              </div>
              
              {isLoading && (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                  <span className="ml-2 text-gray-500">Searching...</span>
                </div>
              )}
            </div>
          </div>
        ) : (
          /* Results Interface */
          <>
            {/* Header */}
            <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
              <h2 className="text-lg font-semibold truncate flex-1 mr-4">{currentResult.query}</h2>
              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => setCurrentResult(null)}
                  className={`px-4 py-2 rounded-lg border ${
                    theme === 'dark' 
                      ? 'border-gray-600 hover:bg-gray-700' 
                      : 'border-gray-300 hover:bg-gray-50'
                  } transition-colors flex items-center space-x-2`}
                >
                  <Search className="w-4 h-4" />
                  <span>New request</span>
                </button>
                <Clock className="w-5 h-5 text-gray-400" />
              </div>
            </div>

            {/* Results Section */}
            <div className="flex-1 overflow-y-auto p-6">
              {/* Résumé de la recherche */}
              <div className={`rounded-lg p-6 mb-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-lg font-semibold">Résultats de recherche</h3>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={regenerateAnswer}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Nouvelle recherche"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </button>
                    <button
                      onClick={copyResults}
                      className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Copier les résultats"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500 mr-2"></div>
                    <span className="text-gray-500">Recherche en cours...</span>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>{currentResult.totalResults} résultats trouvés</span>
                      <span>Traité en {currentResult.processingTimeMs}ms</span>
                    </div>

                    {error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
                        {error}
                      </div>
                    )}

                    {/* Liste des documents */}
                    <div className="space-y-4">
                      {currentResult.results.map((document, index) => (
                        <div
                          key={document.id}
                          className={`p-4 rounded-lg border ${
                            theme === 'dark'
                              ? 'border-gray-600 bg-gray-700'
                              : 'border-gray-200 bg-gray-50'
                          } hover:shadow-md transition-shadow`}
                        >
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-lg">{document.title}</h4>
                            <div className="flex items-center gap-2">
                              <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                {document.type}
                              </span>
                              <span className="text-xs text-gray-500">
                                Score: {(document.relevanceScore * 100).toFixed(0)}%
                              </span>
                            </div>
                          </div>

                          <p className="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
                            {document.summary}
                          </p>

                          {/* Highlights */}
                          {document.highlights && document.highlights.length > 0 && (
                            <div className="mb-3">
                              <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                Extraits pertinents:
                              </h5>
                              <div className="space-y-1">
                                {document.highlights.slice(0, 3).map((highlight, idx) => (
                                  <div
                                    key={idx}
                                    className="text-sm text-gray-600 dark:text-gray-400 italic"
                                    dangerouslySetInnerHTML={{ __html: highlight }}
                                  />
                                ))}
                              </div>
                            </div>
                          )}

                          <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-4">
                              <span className="text-gray-500">
                                Source: {document.source}
                              </span>
                              {document.publicationDate && (
                                <span className="text-gray-500">
                                  {new Date(document.publicationDate).toLocaleDateString()}
                                </span>
                              )}
                            </div>

                            <div className="flex items-center gap-2">
                              {document.url && (
                                <a
                                  href={document.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
                                >
                                  <ExternalLink className="w-3 h-3" />
                                  Voir
                                </a>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>



              {/* Follow-up Questions */}
              {followUpQuestions.length > 0 && (
                <div className={`rounded-lg p-6 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
                  <h3 className="text-lg font-semibold mb-4">Follow-up Questions</h3>
                  <div className="space-y-3">
                    {followUpQuestions.map((question, index) => (
                      <button
                        key={index}
                        onClick={() => handleFollowUpQuestion(question)}
                        className={`w-full text-left p-3 rounded-lg border ${
                          theme === 'dark' 
                            ? 'border-gray-600 hover:bg-gray-700' 
                            : 'border-gray-200 hover:bg-gray-50'
                        } transition-colors flex items-center justify-between group`}
                      >
                        <span className="text-sm">{question}</span>
                        <Search className="w-4 h-4 text-gray-400 group-hover:text-purple-500 transition-colors" />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Search Input at Bottom */}
            <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  placeholder="Ask to follow-up..."
                  className="flex-1 bg-transparent outline-none placeholder-gray-400"
                />
                <button 
                  onClick={handleSearch}
                  disabled={isLoading}
                  className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  <Search className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default LegalResearch
