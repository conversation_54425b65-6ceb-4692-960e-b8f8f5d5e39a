# Configuration Redis pour DocumentAnalysis Service
# Optimisée pour le cache d'analyse de documents

# <PERSON><PERSON><PERSON>
bind 0.0.0.0
port 6379
protected-mode no

# Mémoire
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistance
save 900 1
save 300 10
save 60 10000

# Logs
loglevel notice
logfile ""

# Performance
tcp-keepalive 300
timeout 0

# Sécurité (à configurer en production)
# requirepass your_redis_password_here

# Configuration pour le cache d'analyse
# TTL par défaut pour les analyses : 1 heure
# Les clés suivront le pattern: "docanalysis:cache:{hash}"

# Optimisations pour les gros objets JSON
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Configuration pour les notifications d'expiration
notify-keyspace-events Ex
