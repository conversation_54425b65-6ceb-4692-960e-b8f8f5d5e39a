﻿
namespace LexAI.Shared.Application.DTOs.LLM
{
    /// <summary>
    /// Informations sur le modèle LLM
    /// </summary>
    public class LLMModelInfo
    {
        public string Type { get; set; } = string.Empty; // "OpenAI", "Ollama", "Local"
        public string Name { get; set; } = string.Empty;
        public int MaxTokens { get; set; }
        public bool SupportsStreaming { get; set; }
        public decimal CostPerToken { get; set; }
        public Dictionary<string, object>? Capabilities { get; set; }
    }
}
