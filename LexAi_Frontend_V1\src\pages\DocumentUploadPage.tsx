import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDropzone } from 'react-dropzone'
import {
  Upload,
  FileText,
  X,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  File
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { useDocumentsStore } from '../store/documentsStore'

interface FileWithPreview extends File {
  preview?: string
}

export function DocumentUploadPage() {
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([])
  const [metadata, setMetadata] = useState({
    title: '',
    description: '',
    tags: ''
  })
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle')
  const [uploadedDocuments, setUploadedDocuments] = useState<any[]>([])

  const { uploadDocument, uploadProgress, isLoading } = useDocumentsStore()
  const navigate = useNavigate()

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const filesWithPreview = acceptedFiles.map(file =>
      Object.assign(file, {
        preview: URL.createObjectURL(file)
      })
    )
    setSelectedFiles(prev => [...prev, ...filesWithPreview])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/html': ['.html'],
      'application/rtf': ['.rtf']
    },
    maxSize: 100 * 1024 * 1024, // 100MB
    multiple: true
  })

  const removeFile = (index: number) => {
    setSelectedFiles(prev => {
      const newFiles = [...prev]
      if (newFiles[index].preview) {
        URL.revokeObjectURL(newFiles[index].preview!)
      }
      newFiles.splice(index, 1)
      return newFiles
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <File className="h-8 w-8 text-red-500" />
    } else if (file.type.includes('word')) {
      return <File className="h-8 w-8 text-blue-500" />
    } else {
      return <FileText className="h-8 w-8 text-gray-500" />
    }
  }

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return

    setUploadStatus('uploading')
    const uploaded = []

    try {
      for (const file of selectedFiles) {
        const documentMetadata = {
          title: metadata.title || file.name,
          description: metadata.description,
          tags: metadata.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        }

        const document = await uploadDocument(file, documentMetadata)
        console.log('Document uploadé:', document) // Debug
        uploaded.push(document)
      }

      setUploadedDocuments(uploaded)
      setUploadStatus('success')
      setSelectedFiles([])
      setMetadata({ title: '', description: '', tags: '' })
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error)
      setUploadStatus('error')
    }
  }

  if (uploadStatus === 'success') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
              <h2 className="mt-4 text-xl font-semibold text-gray-900">
                Téléchargement réussi !
              </h2>
              <p className="mt-2 text-gray-600">
                {uploadedDocuments.length} document(s) téléchargé(s) avec succès.
              </p>
            </div>

            <div className="mt-6 space-y-3">
              {uploadedDocuments.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-sm font-medium text-green-900">
                      {doc.originalFileName}
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/documents/${doc.id}`)}
                  >
                    Voir le document
                  </Button>
                </div>
              ))}
            </div>

            <div className="mt-6 flex gap-3 justify-center">
              <Button onClick={() => setUploadStatus('idle')}>
                Télécharger d'autres documents
              </Button>
              <Button variant="outline" onClick={() => navigate('/documents')}>
                Voir tous mes documents
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          onClick={() => navigate('/documents')}
          className="p-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Télécharger des documents</h1>
          <p className="text-gray-600">Ajoutez vos documents pour les traiter et les analyser</p>
        </div>
      </div>

      {/* Zone de téléchargement */}
      <Card>
        <CardHeader>
          <CardTitle>Sélectionner les fichiers</CardTitle>
          <CardDescription>
            Formats supportés: PDF, DOC, DOCX, TXT, HTML, RTF (max 100MB par fichier)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-4 text-lg font-medium text-gray-900">
              {isDragActive
                ? 'Déposez les fichiers ici...'
                : 'Glissez-déposez vos fichiers ici'
              }
            </p>
            <p className="mt-2 text-sm text-gray-600">
              ou cliquez pour sélectionner des fichiers
            </p>
          </div>

          {/* Liste des fichiers sélectionnés */}
          {selectedFiles.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Fichiers sélectionnés ({selectedFiles.length})
              </h3>
              <div className="space-y-3">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                    <div className="flex items-center space-x-3">
                      {getFileIcon(file)}
                      <div>
                        <p className="text-sm font-medium text-gray-900">{file.name}</p>
                        <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Métadonnées */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Informations supplémentaires</CardTitle>
            <CardDescription>
              Ajoutez des métadonnées pour faciliter l'organisation de vos documents
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Titre (optionnel)
              </label>
              <Input
                value={metadata.title}
                onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Titre du document ou de la collection"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description (optionnel)
              </label>
              <textarea
                value={metadata.description}
                onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Description du contenu ou du contexte"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (optionnel)
              </label>
              <Input
                value={metadata.tags}
                onChange={(e) => setMetadata(prev => ({ ...prev, tags: e.target.value }))}
                placeholder="Séparez les tags par des virgules (ex: contrat, juridique, 2024)"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            {uploadStatus === 'error' && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-sm text-red-600">
                    Erreur lors du téléchargement. Veuillez réessayer.
                  </p>
                </div>
              </div>
            )}

            {uploadStatus === 'uploading' && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Téléchargement en cours...</span>
                  <span className="text-sm text-gray-600">{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
              </div>
            )}

            <div className="flex gap-3">
              <Button
                onClick={handleUpload}
                disabled={isLoading || uploadStatus === 'uploading'}
                className="flex-1"
              >
                {uploadStatus === 'uploading'
                  ? 'Téléchargement...'
                  : `Télécharger ${selectedFiles.length} fichier(s)`
                }
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/documents')}
                disabled={uploadStatus === 'uploading'}
              >
                Annuler
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
