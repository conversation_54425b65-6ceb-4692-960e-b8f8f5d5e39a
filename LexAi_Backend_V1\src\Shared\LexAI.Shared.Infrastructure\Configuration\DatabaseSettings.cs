namespace LexAI.Shared.Infrastructure.Configuration;

/// <summary>
/// Configuration settings for PostgreSQL database connection
/// </summary>
public class PostgreSqlSettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "PostgreSql";

    /// <summary>
    /// Database connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Maximum number of retry attempts for database operations
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// Enable sensitive data logging (for development only)
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// Enable detailed errors (for development only)
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = false;
}

/// <summary>
/// Configuration settings for MongoDB connection
/// </summary>
public class MongoDbSettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "MongoDB";

    /// <summary>
    /// MongoDB connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Database name
    /// </summary>
    public string DatabaseName { get; set; } = string.Empty;

    /// <summary>
    /// Maximum connection pool size
    /// </summary>
    public int MaxConnectionPoolSize { get; set; } = 100;

    /// <summary>
    /// Connection timeout in milliseconds
    /// </summary>
    public int ConnectTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Server selection timeout in milliseconds
    /// </summary>
    public int ServerSelectionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Socket timeout in milliseconds
    /// </summary>
    public int SocketTimeoutMs { get; set; } = 30000;
}

/// <summary>
/// Configuration settings for Redis cache
/// </summary>
public class RedisSettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "Redis";

    /// <summary>
    /// Redis connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Default database number
    /// </summary>
    public int Database { get; set; } = 0;

    /// <summary>
    /// Default expiration time in minutes
    /// </summary>
    public int DefaultExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Key prefix for cache entries
    /// </summary>
    public string KeyPrefix { get; set; } = "LexAI:";

    /// <summary>
    /// Enable compression for cached values
    /// </summary>
    public bool EnableCompression { get; set; } = true;
}

/// <summary>
/// Configuration settings for RabbitMQ message broker
/// </summary>
public class RabbitMqSettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "RabbitMQ";

    /// <summary>
    /// RabbitMQ host name
    /// </summary>
    public string HostName { get; set; } = "localhost";

    /// <summary>
    /// RabbitMQ port
    /// </summary>
    public int Port { get; set; } = 5672;

    /// <summary>
    /// Username for authentication
    /// </summary>
    public string UserName { get; set; } = "guest";

    /// <summary>
    /// Password for authentication
    /// </summary>
    public string Password { get; set; } = "guest";

    /// <summary>
    /// Virtual host
    /// </summary>
    public string VirtualHost { get; set; } = "/";

    /// <summary>
    /// Exchange name for legal research events
    /// </summary>
    public string LegalResearchExchange { get; set; } = "lexai.legal.research";

    /// <summary>
    /// Exchange name for document events
    /// </summary>
    public string DocumentExchange { get; set; } = "lexai.documents";

    /// <summary>
    /// Exchange name for notification events
    /// </summary>
    public string NotificationExchange { get; set; } = "lexai.notifications";

    /// <summary>
    /// Exchange name for audit events
    /// </summary>
    public string AuditExchange { get; set; } = "lexai.audit";
}
