#!/bin/bash

# LexAI Development Startup Script
# This script helps developers start the LexAI microservices architecture

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

show_help() {
    print_color $BLUE "LexAI Development Startup Script"
    print_color $BLUE "================================="
    echo ""
    print_color $YELLOW "Usage:"
    echo "  ./scripts/start-dev.sh [OPTIONS]"
    echo ""
    print_color $YELLOW "Options:"
    echo "  infrastructure    Start only infrastructure services (PostgreSQL, MongoDB, Redis, RabbitMQ)"
    echo "  services         Start only application services"
    echo "  all              Start all services (infrastructure + applications)"
    echo "  stop             Stop all services"
    echo "  clean            Stop all services and remove volumes (⚠️  Data loss)"
    echo "  logs             Show logs for all services"
    echo "  logs <service>   Show logs for specific service"
    echo "  status           Show service status"
    echo ""
    print_color $YELLOW "Examples:"
    echo "  ./scripts/start-dev.sh infrastructure"
    echo "  ./scripts/start-dev.sh all"
    echo "  ./scripts/start-dev.sh logs"
    echo "  ./scripts/start-dev.sh logs api-gateway"
    echo "  ./scripts/start-dev.sh stop"
}

test_prerequisites() {
    print_color $BLUE "🔍 Checking prerequisites..."
    
    # Check Docker
    if command -v docker &> /dev/null; then
        docker_version=$(docker --version)
        print_color $GREEN "✅ Docker: $docker_version"
    else
        print_color $RED "❌ Docker is not installed or not running"
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        compose_version=$(docker-compose --version)
        print_color $GREEN "✅ Docker Compose: $compose_version"
    else
        print_color $RED "❌ Docker Compose is not installed"
        exit 1
    fi
    
    # Check .NET
    if command -v dotnet &> /dev/null; then
        dotnet_version=$(dotnet --version)
        print_color $GREEN "✅ .NET SDK: $dotnet_version"
    else
        print_color $RED "❌ .NET SDK is not installed"
        exit 1
    fi
    
    # Check if .env file exists
    if [ -f ".env" ]; then
        print_color $GREEN "✅ .env file found"
    else
        print_color $YELLOW "⚠️  .env file not found, using default values"
        if [ -f ".env.example" ]; then
            print_color $YELLOW "💡 Consider copying .env.example to .env and updating values"
        fi
    fi
}

start_infrastructure() {
    print_color $BLUE "🚀 Starting infrastructure services..."
    
    docker-compose up -d postgres mongodb redis rabbitmq
    
    print_color $YELLOW "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    services=("postgres" "mongodb" "redis" "rabbitmq")
    for service in "${services[@]}"; do
        status=$(docker-compose ps $service --format "table {{.State}}" | tail -n +2)
        if [[ $status == *"Up"* ]]; then
            print_color $GREEN "✅ $service is running"
        else
            print_color $RED "❌ $service failed to start"
        fi
    done
    
    print_color $GREEN "🎉 Infrastructure services started successfully!"
    print_color $BLUE "📊 RabbitMQ Management UI: http://localhost:15672"
    print_color $BLUE "   Username: lexai_user"
    print_color $BLUE "   Password: lexai_rabbitmq_password_2024!"
}

start_services() {
    print_color $BLUE "🚀 Starting application services..."
    
    # Build and start services
    docker-compose up -d --build api-gateway
    
    print_color $YELLOW "⏳ Waiting for services to be ready..."
    sleep 15
    
    # Test API Gateway
    if curl -f http://localhost:8080/api/gateway/ping &> /dev/null; then
        print_color $GREEN "✅ API Gateway is responding"
    else
        print_color $YELLOW "⚠️  API Gateway might still be starting up..."
    fi
    
    print_color $GREEN "🎉 Application services started successfully!"
    print_color $BLUE "📖 API Documentation: http://localhost:8080/swagger"
    print_color $BLUE "🔍 Health Checks: http://localhost:8080/health"
}

start_all_services() {
    print_color $BLUE "🚀 Starting all services..."
    start_infrastructure
    sleep 5
    start_services
}

stop_services() {
    print_color $BLUE "🛑 Stopping all services..."
    
    docker-compose down
    print_color $GREEN "✅ All services stopped successfully!"
}

clean_services() {
    print_color $YELLOW "🧹 Stopping services and cleaning volumes..."
    print_color $RED "⚠️  This will remove all data in databases!"
    
    read -p "Are you sure? Type 'yes' to continue: " confirmation
    if [ "$confirmation" = "yes" ]; then
        docker-compose down -v
        docker system prune -f
        print_color $GREEN "✅ Services stopped and volumes cleaned!"
    else
        print_color $YELLOW "❌ Operation cancelled"
    fi
}

show_logs() {
    local service_name=$1
    
    if [ -n "$service_name" ]; then
        print_color $BLUE "📋 Showing logs for service: $service_name"
        docker-compose logs -f $service_name
    else
        print_color $BLUE "📋 Showing logs for all services..."
        docker-compose logs -f
    fi
}

show_status() {
    print_color $BLUE "📊 Service Status:"
    docker-compose ps
    
    echo ""
    print_color $BLUE "🔗 Quick Links:"
    echo "  API Gateway:         http://localhost:8080"
    echo "  Swagger Docs:        http://localhost:8080/swagger"
    echo "  Health Checks:       http://localhost:8080/health"
    echo "  RabbitMQ Management: http://localhost:15672"
    
    echo ""
    print_color $BLUE "🧪 Quick Tests:"
    echo "  curl http://localhost:8080/api/gateway/ping"
    echo "  curl http://localhost:8080/api/gateway/info"
    echo "  curl http://localhost:8080/health"
}

# Make script executable
chmod +x "$0"

# Change to project root directory
script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
project_root="$(dirname "$script_dir")"
cd "$project_root"

print_color $BLUE "🏗️  LexAI Development Environment"
print_color $BLUE "Working directory: $(pwd)"
echo ""

# Main script logic
case "${1:-help}" in
    infrastructure)
        test_prerequisites
        start_infrastructure
        show_status
        ;;
    services)
        test_prerequisites
        start_services
        show_status
        ;;
    all)
        test_prerequisites
        start_all_services
        show_status
        ;;
    stop)
        stop_services
        ;;
    clean)
        clean_services
        ;;
    logs)
        if [ -n "$2" ]; then
            show_logs "$2"
        else
            show_logs
        fi
        ;;
    status)
        show_status
        ;;
    help|*)
        show_help
        ;;
esac

echo ""
print_color $GREEN "✨ Done! Use 'docker-compose logs -f' to monitor services."
