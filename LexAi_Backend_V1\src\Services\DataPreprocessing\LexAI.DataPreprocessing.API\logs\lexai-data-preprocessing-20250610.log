2025-06-10 20:21:31.004 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-10 20:21:31.159 +04:00 [INF] Hangfire SQL objects installed.
2025-06-10 20:21:31.169 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-10 20:21:31.757 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-10 20:21:31.786 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:21:32.307 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-10 20:21:32.344 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-10 20:21:32.474 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-10 20:21:32.637 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-10 20:21:33.020 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-10 20:21:33.029 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-10 20:21:33.086 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-10 20:21:33.090 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-10 20:21:33.135 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:21:33.138 +04:00 [INF] Hosting environment: Development
2025-06-10 20:21:33.140 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-10 20:21:33.184 +04:00 [INF] Server datapreprocessing-kevin11:14244:1c9af55c successfully announced in 26.3667 ms
2025-06-10 20:21:33.196 +04:00 [INF] Server datapreprocessing-kevin11:14244:1c9af55c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-10 20:21:33.340 +04:00 [INF] Server datapreprocessing-kevin11:14244:1c9af55c all the dispatchers started
2025-06-10 20:21:33.416 +04:00 [INF] 1 servers were removed due to timeout
2025-06-10 20:21:33.610 +04:00 [INF] Removed 5 outdated record(s) from 'aggregatedcounter' table.
2025-06-10 20:21:34.344 +04:00 [INF] Generating processing statistics
2025-06-10 20:21:34.373 +04:00 [INF] Starting cleanup of failed documents
2025-06-10 20:21:34.733 +04:00 [INF] Removed 5 outdated record(s) from 'job' table.
2025-06-10 20:21:35.147 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-10 20:21:35.404 +04:00 [INF] Request GET / started with correlation ID 856f0f50-b5c7-435c-b3c1-a3e3be22cacf
2025-06-10 20:21:37.375 +04:00 [INF] Request GET / completed in 1961ms with status 404 (Correlation ID: 856f0f50-b5c7-435c-b3c1-a3e3be22cacf)
2025-06-10 20:21:37.385 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 2239.9891ms
2025-06-10 20:21:37.397 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-10 20:22:32.832 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-10 20:22:33.014 +04:00 [INF] Hangfire SQL objects installed.
2025-06-10 20:22:33.027 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-10 20:22:33.432 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-10 20:22:33.463 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:22:33.669 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-10 20:22:33.672 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-10 20:22:33.824 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-10 20:22:33.825 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-10 20:22:33.827 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-10 20:22:33.828 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-10 20:22:33.830 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-10 20:22:33.831 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-10 20:22:33.849 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:22:33.852 +04:00 [INF] Hosting environment: Development
2025-06-10 20:22:33.854 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-10 20:22:33.881 +04:00 [INF] Server datapreprocessing-kevin11:13432:cc7f4594 successfully announced in 14.3152 ms
2025-06-10 20:22:33.890 +04:00 [INF] Server datapreprocessing-kevin11:13432:cc7f4594 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-10 20:22:34.075 +04:00 [INF] Server datapreprocessing-kevin11:13432:cc7f4594 all the dispatchers started
2025-06-10 20:22:35.729 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-10 20:22:36.517 +04:00 [INF] Request GET / started with correlation ID 73d2ffbb-7b02-4dd6-9d94-4a2c4a551f7d
2025-06-10 20:22:36.724 +04:00 [INF] Request GET / completed in 191ms with status 404 (Correlation ID: 73d2ffbb-7b02-4dd6-9d94-4a2c4a551f7d)
2025-06-10 20:22:36.780 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 1048.7987ms
2025-06-10 20:22:36.838 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
