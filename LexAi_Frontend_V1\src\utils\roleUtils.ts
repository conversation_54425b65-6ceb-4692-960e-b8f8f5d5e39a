import { UserRole } from '../types'

/**
 * Convertit un rôle utilisateur en libellé français
 */
export const getRoleLabel = (role: UserRole | number): string => {
  // Gérer les cas où le rôle est un nombre ou une valeur enum
  const roleValue = typeof role === 'number' ? role : role

  switch (roleValue) {
    case UserRole.Administrator:
    case 1:
      return 'Administrateur'
    case UserRole.SeniorLawyer:
    case 2:
      return 'Avocat Senior'
    case UserRole.Lawyer:
    case 3:
      return 'Avocat'
    case UserRole.LegalAssistant:
    case 4:
      return 'Assistant Juridique'
    case UserRole.Client:
    case 5:
      return 'Client'
    case UserRole.Guest:
    case 6:
      return 'Invité'
    default:
      return `Rôle ${roleValue}`
  }
}

/**
 * Convertit un rôle utilisateur en libellé anglais
 */
export const getRoleKey = (role: UserRole): string => {
  switch (role) {
    case UserRole.Administrator:
      return 'Administrator'
    case UserRole.SeniorLawyer:
      return 'SeniorLawyer'
    case UserRole.Lawyer:
      return 'Lawyer'
    case UserRole.LegalAssistant:
      return 'LegalAssistant'
    case UserRole.Client:
      return 'Client'
    case UserRole.Guest:
      return 'Guest'
    default:
      return 'Unknown'
  }
}

/**
 * Obtient la classe CSS d'icône associée à un rôle
 */
export const getRoleIconClass = (role: UserRole | number): string => {
  const roleValue = typeof role === 'number' ? role : role

  switch (roleValue) {
    case UserRole.Administrator:
    case 1:
      return 'h-4 w-4 text-red-500'
    case UserRole.SeniorLawyer:
    case 2:
      return 'h-4 w-4 text-purple-500'
    case UserRole.Lawyer:
    case 3:
      return 'h-4 w-4 text-blue-500'
    case UserRole.LegalAssistant:
    case 4:
      return 'h-4 w-4 text-green-500'
    case UserRole.Client:
    case 5:
      return 'h-4 w-4 text-gray-500'
    case UserRole.Guest:
    case 6:
      return 'h-4 w-4 text-yellow-500'
    default:
      return 'h-4 w-4 text-gray-500'
  }
}

/**
 * Obtient la couleur associée à un rôle
 */
export const getRoleColor = (role: UserRole): string => {
  switch (role) {
    case UserRole.Administrator:
      return 'text-red-600 bg-red-100'
    case UserRole.SeniorLawyer:
      return 'text-purple-600 bg-purple-100'
    case UserRole.Lawyer:
      return 'text-blue-600 bg-blue-100'
    case UserRole.LegalAssistant:
      return 'text-green-600 bg-green-100'
    case UserRole.Client:
      return 'text-gray-600 bg-gray-100'
    case UserRole.Guest:
      return 'text-yellow-600 bg-yellow-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

/**
 * Vérifie si un utilisateur a les permissions pour accéder à une fonctionnalité
 */
export const hasPermission = (userRole: UserRole, requiredRoles: UserRole[]): boolean => {
  return requiredRoles.includes(userRole)
}

/**
 * Obtient tous les rôles disponibles pour la sélection
 */
export const getAvailableRoles = (): Array<{ value: UserRole; label: string; description: string }> => {
  return [
    {
      value: UserRole.SeniorLawyer,
      label: 'Avocat Senior',
      description: 'Avocat expérimenté avec responsabilités de gestion'
    },
    {
      value: UserRole.Lawyer,
      label: 'Avocat',
      description: 'Avocat avec accès standard'
    },
    {
      value: UserRole.LegalAssistant,
      label: 'Assistant Juridique',
      description: 'Assistant avec accès limité'
    },
    {
      value: UserRole.Client,
      label: 'Client',
      description: 'Accès restreint aux données personnelles'
    },
    {
      value: UserRole.Guest,
      label: 'Invité',
      description: 'Accès minimal pour les visiteurs'
    }
  ]
}

/**
 * Obtient tous les rôles pour l'administration
 */
export const getAllRoles = (): Array<{ value: UserRole; label: string }> => {
  return [
    { value: UserRole.Administrator, label: 'Administrateur' },
    { value: UserRole.SeniorLawyer, label: 'Avocat Senior' },
    { value: UserRole.Lawyer, label: 'Avocat' },
    { value: UserRole.LegalAssistant, label: 'Assistant Juridique' },
    { value: UserRole.Client, label: 'Client' },
    { value: UserRole.Guest, label: 'Invité' }
  ]
}
