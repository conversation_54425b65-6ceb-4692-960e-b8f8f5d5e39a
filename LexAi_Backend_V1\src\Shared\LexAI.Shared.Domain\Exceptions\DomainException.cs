namespace LexAI.Shared.Domain.Exceptions;

/// <summary>
/// Base exception class for domain-specific exceptions
/// </summary>
public abstract class DomainException : Exception
{
    /// <summary>
    /// Error code for the exception
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// Additional details about the exception
    /// </summary>
    public Dictionary<string, object> Details { get; }

    /// <summary>
    /// Initializes a new instance of the DomainException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    protected DomainException(string message, string errorCode) : base(message)
    {
        ErrorCode = errorCode;
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Initializes a new instance of the DomainException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    /// <param name="innerException">The inner exception</param>
    protected DomainException(string message, string errorCode, Exception innerException) 
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Details = new Dictionary<string, object>();
    }

    /// <summary>
    /// Adds additional details to the exception
    /// </summary>
    /// <param name="key">The detail key</param>
    /// <param name="value">The detail value</param>
    /// <returns>The current exception instance for method chaining</returns>
    public DomainException WithDetail(string key, object value)
    {
        Details[key] = value;
        return this;
    }
}

/// <summary>
/// Exception thrown when a requested entity is not found
/// </summary>
public class EntityNotFoundException : DomainException
{
    /// <summary>
    /// Initializes a new instance of the EntityNotFoundException class
    /// </summary>
    /// <param name="entityType">The type of entity that was not found</param>
    /// <param name="entityId">The ID of the entity that was not found</param>
    public EntityNotFoundException(string entityType, object entityId)
        : base($"{entityType} with ID '{entityId}' was not found.", "ENTITY_NOT_FOUND")
    {
        WithDetail("EntityType", entityType)
            .WithDetail("EntityId", entityId);
    }
}

/// <summary>
/// Exception thrown when a business rule is violated
/// </summary>
public class BusinessRuleViolationException : DomainException
{
    /// <summary>
    /// Initializes a new instance of the BusinessRuleViolationException class
    /// </summary>
    /// <param name="message">The error message describing the rule violation</param>
    /// <param name="ruleName">The name of the violated business rule</param>
    public BusinessRuleViolationException(string message, string ruleName)
        : base(message, "BUSINESS_RULE_VIOLATION")
    {
        WithDetail("RuleName", ruleName);
    }
}

/// <summary>
/// Exception thrown when an entity already exists
/// </summary>
public class EntityAlreadyExistsException : DomainException
{
    /// <summary>
    /// Initializes a new instance of the EntityAlreadyExistsException class
    /// </summary>
    /// <param name="entityType">The type of entity that already exists</param>
    /// <param name="identifier">The identifier of the existing entity</param>
    public EntityAlreadyExistsException(string entityType, string identifier)
        : base($"{entityType} with identifier '{identifier}' already exists.", "ENTITY_ALREADY_EXISTS")
    {
        WithDetail("EntityType", entityType)
            .WithDetail("Identifier", identifier);
    }
}

/// <summary>
/// Exception thrown when an operation is not permitted
/// </summary>
public class OperationNotPermittedException : DomainException
{
    /// <summary>
    /// Initializes a new instance of the OperationNotPermittedException class
    /// </summary>
    /// <param name="operation">The operation that is not permitted</param>
    /// <param name="reason">The reason why the operation is not permitted</param>
    public OperationNotPermittedException(string operation, string reason)
        : base($"Operation '{operation}' is not permitted: {reason}", "OPERATION_NOT_PERMITTED")
    {
        WithDetail("Operation", operation)
            .WithDetail("Reason", reason);
    }
}

/// <summary>
/// Exception thrown when invalid data is provided
/// </summary>
public class InvalidDataException : DomainException
{
    /// <summary>
    /// Initializes a new instance of the InvalidDataException class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="fieldName">The name of the invalid field</param>
    public InvalidDataException(string message, string fieldName)
        : base(message, "INVALID_DATA")
    {
        WithDetail("FieldName", fieldName);
    }
}
