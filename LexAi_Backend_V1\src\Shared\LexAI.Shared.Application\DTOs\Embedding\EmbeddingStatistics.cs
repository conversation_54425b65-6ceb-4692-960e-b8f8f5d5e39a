﻿
namespace LexAI.Shared.Application.DTOs.Embedding
{
    /// <summary>
    /// Statistiques d'embeddings
    /// </summary>
    public class EmbeddingStatistics
    {
        public int Count { get; set; }
        public int Dimensions { get; set; }
        public double[] Means { get; set; } = Array.Empty<double>();
        public double[] Variances { get; set; } = Array.Empty<double>();
        public double[] StandardDeviations { get; set; } = Array.Empty<double>();
    }

}
