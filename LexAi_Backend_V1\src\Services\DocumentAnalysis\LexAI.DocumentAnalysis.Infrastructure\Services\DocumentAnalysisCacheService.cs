using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service de mise en cache pour les analyses de documents
/// </summary>
public interface IDocumentAnalysisCacheService
{
    /// <summary>
    /// Obtient une analyse depuis le cache
    /// </summary>
    Task<DocumentAnalysisResponseDto?> GetCachedAnalysisAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Met en cache une analyse
    /// </summary>
    Task SetCachedAnalysisAsync(string cacheKey, DocumentAnalysisResponseDto analysis, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Génère une clé de cache pour un document
    /// </summary>
    string GenerateCacheKey(byte[] documentContent, AnalysisOptions options, string userId);

    /// <summary>
    /// Supprime une analyse du cache
    /// </summary>
    Task RemoveCachedAnalysisAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vide le cache pour un utilisateur
    /// </summary>
    Task ClearUserCacheAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les statistiques du cache
    /// </summary>
    Task<CacheStatistics> GetCacheStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistiques du cache
/// </summary>
public class CacheStatistics
{
    public int TotalEntries { get; set; }
    public long TotalSizeBytes { get; set; }
    public int HitCount { get; set; }
    public int MissCount { get; set; }
    public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;
    public int TotalRequests => HitCount + MissCount;
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Implémentation du service de cache
/// </summary>
public class DocumentAnalysisCacheService : IDocumentAnalysisCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<DocumentAnalysisCacheService> _logger;
    private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(24);
    private readonly CacheStatistics _statistics = new();
    private readonly object _statsLock = new();
    private readonly Dictionary<string, HashSet<string>> _userCacheKeys = new(); // Index des clés par utilisateur
    private readonly object _userKeysLock = new();

    public DocumentAnalysisCacheService(
        IMemoryCache memoryCache,
        ILogger<DocumentAnalysisCacheService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
    }

    /// <summary>
    /// Obtient une analyse depuis le cache
    /// </summary>
    public async Task<DocumentAnalysisResponseDto?> GetCachedAnalysisAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            if (_memoryCache.TryGetValue(cacheKey, out var cachedData))
            {
                lock (_statsLock)
                {
                    _statistics.HitCount++;
                }

                if (cachedData is string jsonData)
                {
                    var analysis = JsonSerializer.Deserialize<DocumentAnalysisResponseDto>(jsonData);
                    _logger.LogDebug("Cache hit for key: {CacheKey}", cacheKey);
                    return analysis;
                }
            }

            lock (_statsLock)
            {
                _statistics.MissCount++;
            }

            _logger.LogDebug("Cache miss for key: {CacheKey}", cacheKey);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving from cache for key: {CacheKey}", cacheKey);
            return null;
        }
    }

    /// <summary>
    /// Met en cache une analyse
    /// </summary>
    public async Task SetCachedAnalysisAsync(string cacheKey, DocumentAnalysisResponseDto analysis, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            var jsonData = JsonSerializer.Serialize(analysis);
            var cacheExpiration = expiration ?? _defaultExpiration;

            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = cacheExpiration,
                SlidingExpiration = TimeSpan.FromHours(6), // Renouvelle si accédé dans les 6h
                Priority = CacheItemPriority.Normal,
                Size = jsonData.Length
            };

            // Callback pour nettoyer les statistiques quand l'entrée expire
            cacheEntryOptions.PostEvictionCallbacks.Add(new PostEvictionCallbackRegistration
            {
                EvictionCallback = (key, value, reason, state) =>
                {
                    lock (_statsLock)
                    {
                        _statistics.TotalEntries = Math.Max(0, _statistics.TotalEntries - 1);
                        if (value is string data)
                        {
                            _statistics.TotalSizeBytes = Math.Max(0, _statistics.TotalSizeBytes - data.Length);
                        }
                    }
                    _logger.LogDebug("Cache entry evicted: {Key}, Reason: {Reason}", key, reason);
                }
            });

            _memoryCache.Set(cacheKey, jsonData, cacheEntryOptions);

            // Maintenir l'index des clés par utilisateur
            var userId = ExtractUserIdFromCacheKey(cacheKey);
            if (!string.IsNullOrEmpty(userId))
            {
                lock (_userKeysLock)
                {
                    if (!_userCacheKeys.ContainsKey(userId))
                    {
                        _userCacheKeys[userId] = new HashSet<string>();
                    }
                    _userCacheKeys[userId].Add(cacheKey);
                }
            }

            lock (_statsLock)
            {
                _statistics.TotalEntries++;
                _statistics.TotalSizeBytes += jsonData.Length;
                _statistics.LastUpdated = DateTime.UtcNow;
            }

            _logger.LogDebug("Analysis cached with key: {CacheKey}, Size: {Size} bytes, Expiration: {Expiration}", 
                cacheKey, jsonData.Length, cacheExpiration);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching analysis for key: {CacheKey}", cacheKey);
        }
    }

    /// <summary>
    /// Génère une clé de cache pour un document
    /// </summary>
    public string GenerateCacheKey(byte[] documentContent, AnalysisOptions options, string userId)
    {
        try
        {
            // Créer un hash du contenu du document
            using var sha256 = SHA256.Create();
            var documentHash = Convert.ToBase64String(sha256.ComputeHash(documentContent));

            // Créer un hash des options d'analyse
            var optionsJson = JsonSerializer.Serialize(options, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
            var optionsHash = Convert.ToBase64String(sha256.ComputeHash(Encoding.UTF8.GetBytes(optionsJson)));

            // Combiner tous les éléments pour créer une clé unique
            var keyComponents = new[]
            {
                "doc_analysis",
                userId,
                documentHash[..16], // Prendre les 16 premiers caractères du hash
                optionsHash[..16]   // Prendre les 16 premiers caractères du hash des options
            };

            var cacheKey = string.Join(":", keyComponents);
            
            _logger.LogDebug("Generated cache key: {CacheKey} for user: {UserId}", cacheKey, userId);
            
            return cacheKey;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error generating cache key for user: {UserId}", userId);
            // Fallback: générer une clé basée sur le timestamp
            return $"doc_analysis:{userId}:{DateTime.UtcNow.Ticks}";
        }
    }

    /// <summary>
    /// Supprime une analyse du cache
    /// </summary>
    public async Task RemoveCachedAnalysisAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            _memoryCache.Remove(cacheKey);
            _logger.LogDebug("Removed cache entry: {CacheKey}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error removing cache entry: {CacheKey}", cacheKey);
        }
    }

    /// <summary>
    /// Vide le cache pour un utilisateur
    /// </summary>
    public async Task ClearUserCacheAsync(string userId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            HashSet<string>? userKeys = null;

            lock (_userKeysLock)
            {
                if (_userCacheKeys.TryGetValue(userId, out userKeys))
                {
                    userKeys = new HashSet<string>(userKeys); // Copie pour éviter les modifications concurrentes
                    _userCacheKeys.Remove(userId);
                }
            }

            if (userKeys != null && userKeys.Count > 0)
            {
                foreach (var key in userKeys)
                {
                    _memoryCache.Remove(key);
                }

                _logger.LogInformation("Cleared {Count} cache entries for user: {UserId}", userKeys.Count, userId);
            }
            else
            {
                _logger.LogInformation("No cache entries found for user: {UserId}", userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error clearing cache for user: {UserId}", userId);
        }
    }

    /// <summary>
    /// Obtient les statistiques du cache
    /// </summary>
    public async Task<CacheStatistics> GetCacheStatisticsAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        lock (_statsLock)
        {
            return new CacheStatistics
            {
                TotalEntries = _statistics.TotalEntries,
                TotalSizeBytes = _statistics.TotalSizeBytes,
                HitCount = _statistics.HitCount,
                MissCount = _statistics.MissCount,
                LastUpdated = _statistics.LastUpdated
            };
        }
    }

    /// <summary>
    /// Extrait l'ID utilisateur d'une clé de cache
    /// </summary>
    private static string? ExtractUserIdFromCacheKey(string cacheKey)
    {
        try
        {
            var parts = cacheKey.Split(':');
            return parts.Length >= 2 ? parts[1] : null;
        }
        catch
        {
            return null;
        }
    }
}

/// <summary>
/// Service de cache décoré qui ajoute la fonctionnalité de cache au service d'analyse
/// </summary>
public class CachedDocumentAnalysisService : IDocumentAnalysisService
{
    private readonly IDocumentAnalysisService _innerService;
    private readonly IDocumentAnalysisCacheService _cacheService;
    private readonly ILogger<CachedDocumentAnalysisService> _logger;

    public CachedDocumentAnalysisService(
        IDocumentAnalysisService innerService,
        IDocumentAnalysisCacheService cacheService,
        ILogger<CachedDocumentAnalysisService> logger)
    {
        _innerService = innerService;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken = default)
    {
        // Générer la clé de cache
        var cacheKey = _cacheService.GenerateCacheKey(request.DocumentContent, request.Options, request.UserId.ToString());

        // Vérifier le cache
        var cachedResult = await _cacheService.GetCachedAnalysisAsync(cacheKey, cancellationToken);
        if (cachedResult != null)
        {
            _logger.LogInformation("Returning cached analysis for document: {DocumentName}", request.DocumentName);
            return cachedResult;
        }

        // Exécuter l'analyse
        var result = await _innerService.AnalyzeDocumentAsync(request, cancellationToken);

        // Mettre en cache le résultat
        await _cacheService.SetCachedAnalysisAsync(cacheKey, result, cancellationToken: cancellationToken);

        return result;
    }

    public Task<DocumentAnalysisResponseDto?> GetAnalysisResultAsync(Guid analysisId, CancellationToken cancellationToken = default)
    {
        return _innerService.GetAnalysisResultAsync(analysisId, cancellationToken);
    }

    public Task<DocumentAnalysisListResponseDto> GetUserAnalysesAsync(DocumentAnalysisListRequestDto request, CancellationToken cancellationToken = default)
    {
        return _innerService.GetUserAnalysesAsync(request, cancellationToken);
    }

    public Task<bool> DeleteAnalysisAsync(Guid analysisId, Guid userId, CancellationToken cancellationToken = default)
    {
        return _innerService.DeleteAnalysisAsync(analysisId, userId, cancellationToken);
    }

    public Task<DocumentAnalysisResponseDto> RegenerateAnalysisAsync(Guid analysisId, AnalysisOptions? newOptions = null, CancellationToken cancellationToken = default)
    {
        return _innerService.RegenerateAnalysisAsync(analysisId, newOptions, cancellationToken);
    }
}
