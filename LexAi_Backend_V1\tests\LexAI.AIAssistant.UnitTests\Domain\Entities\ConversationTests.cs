using FluentAssertions;
using LexAI.AIAssistant.Domain.Entities;
using LexAI.AIAssistant.Domain.ValueObjects;
using Xunit;

namespace LexAI.AIAssistant.UnitTests.Domain.Entities;

/// <summary>
/// Unit tests for Conversation entity
/// </summary>
public class ConversationTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateConversation()
    {
        // Arrange
        var title = "Legal Consultation";
        var userId = Guid.NewGuid();
        var sessionId = "session-123";
        var context = ConversationContext.CreateDefault();

        // Act
        var conversation = Conversation.Create(title, userId, sessionId, context);

        // Assert
        conversation.Should().NotBeNull();
        conversation.Id.Should().NotBeEmpty();
        conversation.Title.Should().Be(title);
        conversation.UserId.Should().Be(userId);
        conversation.SessionId.Should().Be(sessionId);
        conversation.Context.Should().Be(context);
        conversation.Status.Should().Be(ConversationStatus.Active);
        conversation.Messages.Should().BeEmpty();
        conversation.MessageCount.Should().Be(0);
        conversation.TotalTokensUsed.Should().Be(0);
        conversation.EstimatedCost.Should().Be(0m);
        conversation.IsArchived.Should().BeFalse();
        conversation.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        conversation.LastActivityAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidTitle_ShouldThrowArgumentException(string invalidTitle)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var sessionId = "session-123";

        // Act & Assert
        var action = () => Conversation.Create(invalidTitle, userId, sessionId);
        action.Should().Throw<ArgumentException>().WithMessage("Title cannot be empty*");
    }

    [Fact]
    public void Create_WithEmptyUserId_ShouldThrowArgumentException()
    {
        // Arrange
        var title = "Valid Title";
        var userId = Guid.Empty;
        var sessionId = "session-123";

        // Act & Assert
        var action = () => Conversation.Create(title, userId, sessionId);
        action.Should().Throw<ArgumentException>().WithMessage("UserId cannot be empty*");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidSessionId_ShouldThrowArgumentException(string invalidSessionId)
    {
        // Arrange
        var title = "Valid Title";
        var userId = Guid.NewGuid();

        // Act & Assert
        var action = () => Conversation.Create(title, userId, invalidSessionId);
        action.Should().Throw<ArgumentException>().WithMessage("SessionId cannot be empty*");
    }

    [Fact]
    public void AddMessage_WithValidMessage_ShouldAddMessageAndUpdateProperties()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var message = Message.CreateUserMessage(conversation.Id, "Test message", conversation.UserId);

        // Act
        conversation.AddMessage(message);

        // Assert
        conversation.Messages.Should().HaveCount(1);
        conversation.Messages.First().Should().Be(message);
        conversation.MessageCount.Should().Be(1);
        conversation.TotalTokensUsed.Should().Be(message.TokensUsed);
        conversation.EstimatedCost.Should().Be(message.EstimatedCost);
        conversation.LastActivityAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AddMessage_WithNullMessage_ShouldThrowArgumentNullException()
    {
        // Arrange
        var conversation = CreateValidConversation();

        // Act & Assert
        var action = () => conversation.AddMessage(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void AddMessage_WhenConversationIsClosed_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var conversation = CreateValidConversation();
        conversation.Close("system");
        var message = Message.CreateUserMessage(conversation.Id, "Test message", conversation.UserId);

        // Act & Assert
        var action = () => conversation.AddMessage(message);
        action.Should().Throw<InvalidOperationException>().WithMessage("Cannot add messages to a closed conversation");
    }

    [Fact]
    public void AddMessage_WithDetectedDomain_ShouldSetPrimaryDomain()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var message = Message.CreateUserMessage(conversation.Id, "Test message", conversation.UserId);
        message.SetDetectedClassification(LegalDomain.Labor, MessageIntent.Advice, 0.9);

        // Act
        conversation.AddMessage(message);

        // Assert
        conversation.PrimaryDomain.Should().Be(LegalDomain.Labor);
    }

    [Fact]
    public void AddMessage_FirstUserMessage_ShouldUpdateTitleIfGeneric()
    {
        // Arrange
        var conversation = Conversation.Create("New Conversation", Guid.NewGuid(), "session-123");
        var message = Message.CreateUserMessage(conversation.Id, "What are the employment law requirements?", conversation.UserId);

        // Act
        conversation.AddMessage(message);

        // Assert
        conversation.Title.Should().NotBe("New Conversation");
        conversation.Title.Should().Contain("What are the employment law");
    }

    [Fact]
    public void UpdateTitle_WithValidTitle_ShouldUpdateTitle()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var newTitle = "Updated Legal Consultation";
        var updatedBy = "user123";

        // Act
        conversation.UpdateTitle(newTitle, updatedBy);

        // Assert
        conversation.Title.Should().Be(newTitle);
        conversation.UpdatedBy.Should().Be(updatedBy);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void UpdateTitle_WithInvalidTitle_ShouldThrowArgumentException(string invalidTitle)
    {
        // Arrange
        var conversation = CreateValidConversation();
        var updatedBy = "user123";

        // Act & Assert
        var action = () => conversation.UpdateTitle(invalidTitle, updatedBy);
        action.Should().Throw<ArgumentException>().WithMessage("Title cannot be empty*");
    }

    [Fact]
    public void UpdateContext_WithValidContext_ShouldUpdateContext()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var newContext = ConversationContext.CreateForResearch("France", "Lawyer");

        // Act
        conversation.UpdateContext(newContext);

        // Assert
        conversation.Context.Should().Be(newContext);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void UpdateContext_WithNullContext_ShouldThrowArgumentNullException()
    {
        // Arrange
        var conversation = CreateValidConversation();

        // Act & Assert
        var action = () => conversation.UpdateContext(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void AddTags_WithValidTags_ShouldAddNormalizedTags()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var tags = new[] { "Employment", "CONTRACT", "  labor  ", "Employment" }; // Duplicates and mixed case

        // Act
        conversation.AddTags(tags);

        // Assert
        conversation.Tags.Should().HaveCount(3);
        conversation.Tags.Should().Contain("employment");
        conversation.Tags.Should().Contain("contract");
        conversation.Tags.Should().Contain("labor");
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void RemoveTags_WithExistingTags_ShouldRemoveTags()
    {
        // Arrange
        var conversation = CreateValidConversation();
        conversation.AddTags("employment", "contract", "labor");

        // Act
        conversation.RemoveTags("contract", "nonexistent");

        // Assert
        conversation.Tags.Should().HaveCount(2);
        conversation.Tags.Should().Contain("employment");
        conversation.Tags.Should().Contain("labor");
        conversation.Tags.Should().NotContain("contract");
    }

    [Fact]
    public void Close_ShouldSetStatusToClosedAndUpdateMetadata()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var closedBy = "user123";

        // Act
        conversation.Close(closedBy);

        // Assert
        conversation.Status.Should().Be(ConversationStatus.Closed);
        conversation.UpdatedBy.Should().Be(closedBy);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Reopen_WhenClosed_ShouldSetStatusToActiveAndUpdateActivity()
    {
        // Arrange
        var conversation = CreateValidConversation();
        conversation.Close("user123");
        var reopenedBy = "user456";

        // Act
        conversation.Reopen(reopenedBy);

        // Assert
        conversation.Status.Should().Be(ConversationStatus.Active);
        conversation.UpdatedBy.Should().Be(reopenedBy);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        conversation.LastActivityAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Archive_ShouldSetArchivedAndStatusToArchived()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var archivedBy = "user123";

        // Act
        conversation.Archive(archivedBy);

        // Assert
        conversation.IsArchived.Should().BeTrue();
        conversation.Status.Should().Be(ConversationStatus.Archived);
        conversation.UpdatedBy.Should().Be(archivedBy);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Unarchive_ShouldSetUnarchivedAndStatusToActive()
    {
        // Arrange
        var conversation = CreateValidConversation();
        conversation.Archive("user123");
        var unarchivedBy = "user456";

        // Act
        conversation.Unarchive(unarchivedBy);

        // Assert
        conversation.IsArchived.Should().BeFalse();
        conversation.Status.Should().Be(ConversationStatus.Active);
        conversation.UpdatedBy.Should().Be(unarchivedBy);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        conversation.LastActivityAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SetUserRating_WithValidRating_ShouldSetRatingAndFeedback()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var rating = 4;
        var feedback = "Very helpful conversation";
        var ratedBy = "user123";

        // Act
        conversation.SetUserRating(rating, feedback, ratedBy);

        // Assert
        conversation.UserRating.Should().Be(rating);
        conversation.UserFeedback.Should().Be(feedback);
        conversation.UpdatedBy.Should().Be(ratedBy);
        conversation.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData(0)]
    [InlineData(6)]
    [InlineData(-1)]
    public void SetUserRating_WithInvalidRating_ShouldThrowArgumentException(int invalidRating)
    {
        // Arrange
        var conversation = CreateValidConversation();
        var feedback = "Test feedback";
        var ratedBy = "user123";

        // Act & Assert
        var action = () => conversation.SetUserRating(invalidRating, feedback, ratedBy);
        action.Should().Throw<ArgumentException>().WithMessage("Rating must be between 1 and 5*");
    }

    [Fact]
    public void IsActive_WhenActiveAndNotArchived_ShouldReturnTrue()
    {
        // Arrange
        var conversation = CreateValidConversation();

        // Act
        var isActive = conversation.IsActive();

        // Assert
        isActive.Should().BeTrue();
    }

    [Fact]
    public void IsActive_WhenClosed_ShouldReturnFalse()
    {
        // Arrange
        var conversation = CreateValidConversation();
        conversation.Close("user123");

        // Act
        var isActive = conversation.IsActive();

        // Assert
        isActive.Should().BeFalse();
    }

    [Fact]
    public void IsActive_WhenArchived_ShouldReturnFalse()
    {
        // Arrange
        var conversation = CreateValidConversation();
        conversation.Archive("user123");

        // Act
        var isActive = conversation.IsActive();

        // Assert
        isActive.Should().BeFalse();
    }

    [Fact]
    public void GetLastMessages_ShouldReturnMessagesInChronologicalOrder()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var message1 = Message.CreateUserMessage(conversation.Id, "First message", conversation.UserId);
        var message2 = Message.CreateAssistantMessage(conversation.Id, "AI response");
        var message3 = Message.CreateUserMessage(conversation.Id, "Second message", conversation.UserId);

        conversation.AddMessage(message1);
        Thread.Sleep(10); // Ensure different timestamps
        conversation.AddMessage(message2);
        Thread.Sleep(10);
        conversation.AddMessage(message3);

        // Act
        var lastMessages = conversation.GetLastMessages(2).ToList();

        // Assert
        lastMessages.Should().HaveCount(2);
        lastMessages[0].Should().Be(message2); // Second chronologically
        lastMessages[1].Should().Be(message3); // Last chronologically
    }

    [Fact]
    public void GetMessagesByRole_ShouldReturnOnlyMessagesWithSpecifiedRole()
    {
        // Arrange
        var conversation = CreateValidConversation();
        var userMessage1 = Message.CreateUserMessage(conversation.Id, "User message 1", conversation.UserId);
        var assistantMessage = Message.CreateAssistantMessage(conversation.Id, "AI response");
        var userMessage2 = Message.CreateUserMessage(conversation.Id, "User message 2", conversation.UserId);

        conversation.AddMessage(userMessage1);
        conversation.AddMessage(assistantMessage);
        conversation.AddMessage(userMessage2);

        // Act
        var userMessages = conversation.GetMessagesByRole(MessageRole.User).ToList();

        // Assert
        userMessages.Should().HaveCount(2);
        userMessages.Should().Contain(userMessage1);
        userMessages.Should().Contain(userMessage2);
        userMessages.Should().NotContain(assistantMessage);
    }

    private static Conversation CreateValidConversation()
    {
        var title = "Test Conversation";
        var userId = Guid.NewGuid();
        var sessionId = "test-session-123";
        var context = ConversationContext.CreateDefault();

        return Conversation.Create(title, userId, sessionId, context);
    }
}
