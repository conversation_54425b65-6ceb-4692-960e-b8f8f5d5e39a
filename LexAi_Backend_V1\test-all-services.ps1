# Script PowerShell pour tester tous les services LexAI
Write-Host "Test complet de tous les services LexAI..." -ForegroundColor Green

$ErrorActionPreference = "Continue"
$services = @(
    @{
        Name = "DocumentAnalysis"
        Path = "src/Services/DocumentAnalysis/LexAI.DocumentAnalysis.API"
        Port = "5001"
        HealthEndpoint = "http://localhost:5001/health"
    },
    @{
        Name = "AIAssistant"
        Path = "src/Services/AIAssistant/LexAI.AIAssistant.API"
        Port = "5002"
        HealthEndpoint = "http://localhost:5002/health"
    },
    @{
        Name = "LegalResearch"
        Path = "src/Services/LegalResearch/LexAI.LegalResearch.API"
        Port = "5003"
        HealthEndpoint = "http://localhost:5003/health"
    }
)

# Fonction pour tester la compilation
function Test-ServiceCompilation {
    param($ServiceName, $ServicePath)
    
    Write-Host "Test de compilation: ${ServiceName}" -ForegroundColor Blue
    
    try {
        $result = dotnet build $ServicePath --verbosity quiet
        if ($LASTEXITCODE -eq 0) {
            Write-Host "OK ${ServiceName}: Compilation reussie" -ForegroundColor Green
            return $true
        } else {
            Write-Host "ERREUR ${ServiceName}: Echec de compilation" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "ERREUR ${ServiceName}: Erreur de compilation - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour tester la configuration
function Test-ServiceConfiguration {
    param($ServiceName, $ServicePath)
    
    Write-Host "⚙️ Test de configuration: $ServiceName" -ForegroundColor Blue
    
    $appsettingsPath = Join-Path $ServicePath "appsettings.Development.json"
    
    if (Test-Path $appsettingsPath) {
        try {
            $config = Get-Content $appsettingsPath | ConvertFrom-Json
            
            # Vérifier les configurations essentielles
            $hasAzureOpenAI = $config.PSObject.Properties.Name -contains "AzureOpenAI"
            $hasConnectionStrings = $config.PSObject.Properties.Name -contains "ConnectionStrings"
            $hasJwtSettings = $config.PSObject.Properties.Name -contains "JwtSettings"
            
            if ($hasAzureOpenAI -and $hasConnectionStrings -and $hasJwtSettings) {
                Write-Host "✅ $ServiceName: Configuration complète" -ForegroundColor Green
                return $true
            } else {
                Write-Host "⚠️ $ServiceName: Configuration incomplète" -ForegroundColor Yellow
                if (-not $hasAzureOpenAI) { Write-Host "  - Manque: AzureOpenAI" -ForegroundColor Yellow }
                if (-not $hasConnectionStrings) { Write-Host "  - Manque: ConnectionStrings" -ForegroundColor Yellow }
                if (-not $hasJwtSettings) { Write-Host "  - Manque: JwtSettings" -ForegroundColor Yellow }
                return $false
            }
        } catch {
            Write-Host "❌ $ServiceName: Erreur de lecture de configuration - $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "❌ $ServiceName: Fichier appsettings.Development.json introuvable" -ForegroundColor Red
        return $false
    }
}

# Fonction pour tester les dépendances
function Test-ServiceDependencies {
    param($ServiceName, $ServicePath)
    
    Write-Host "📦 Test des dépendances: $ServiceName" -ForegroundColor Blue
    
    try {
        $result = dotnet restore $ServicePath --verbosity quiet
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ServiceName: Dépendances restaurées" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $ServiceName: Échec de restauration des dépendances" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $ServiceName: Erreur de restauration - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour générer un rapport
function Generate-TestReport {
    param($Results)
    
    Write-Host "`n📊 RAPPORT DE TEST COMPLET" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Cyan
    
    $totalServices = $Results.Count
    $successfulCompilations = ($Results | Where-Object { $_.CompilationSuccess }).Count
    $successfulConfigurations = ($Results | Where-Object { $_.ConfigurationSuccess }).Count
    $successfulDependencies = ($Results | Where-Object { $_.DependenciesSuccess }).Count
    
    Write-Host "📈 Statistiques globales:" -ForegroundColor White
    Write-Host "  • Services testés: $totalServices" -ForegroundColor White
    Write-Host "  • Compilations réussies: $successfulCompilations/$totalServices" -ForegroundColor White
    Write-Host "  • Configurations valides: $successfulConfigurations/$totalServices" -ForegroundColor White
    Write-Host "  • Dépendances OK: $successfulDependencies/$totalServices" -ForegroundColor White
    
    Write-Host "`n📋 Détails par service:" -ForegroundColor White
    foreach ($result in $Results) {
        $status = if ($result.CompilationSuccess -and $result.ConfigurationSuccess -and $result.DependenciesSuccess) { "✅ OK" } else { "❌ PROBLÈME" }
        Write-Host "  • $($result.ServiceName): $status" -ForegroundColor White
        
        if (-not $result.CompilationSuccess) {
            Write-Host "    - Compilation échouée" -ForegroundColor Red
        }
        if (-not $result.ConfigurationSuccess) {
            Write-Host "    - Configuration incomplète" -ForegroundColor Yellow
        }
        if (-not $result.DependenciesSuccess) {
            Write-Host "    - Problème de dépendances" -ForegroundColor Red
        }
    }
    
    # Recommandations
    Write-Host "`n💡 Recommandations:" -ForegroundColor Cyan
    if ($successfulCompilations -lt $totalServices) {
        Write-Host "  • Corriger les erreurs de compilation avant de continuer" -ForegroundColor Yellow
    }
    if ($successfulConfigurations -lt $totalServices) {
        Write-Host "  • Compléter les configurations manquantes (Azure OpenAI, JWT, etc.)" -ForegroundColor Yellow
    }
    if ($successfulDependencies -lt $totalServices) {
        Write-Host "  • Vérifier et restaurer les packages NuGet" -ForegroundColor Yellow
    }
    
    if ($successfulCompilations -eq $totalServices -and $successfulConfigurations -eq $totalServices -and $successfulDependencies -eq $totalServices) {
        Write-Host "  🎉 Tous les services sont prêts pour le déploiement !" -ForegroundColor Green
    }
}

# Exécution des tests
Write-Host "🚀 Début des tests..." -ForegroundColor Green
$testResults = @()

foreach ($service in $services) {
    Write-Host "`n" + "=" * 60 -ForegroundColor Gray
    Write-Host "🔍 Test du service: $($service.Name)" -ForegroundColor Magenta
    Write-Host "=" * 60 -ForegroundColor Gray
    
    $dependenciesSuccess = Test-ServiceDependencies -ServiceName $service.Name -ServicePath $service.Path
    $compilationSuccess = Test-ServiceCompilation -ServiceName $service.Name -ServicePath $service.Path
    $configurationSuccess = Test-ServiceConfiguration -ServiceName $service.Name -ServicePath $service.Path
    
    $testResults += @{
        ServiceName = $service.Name
        ServicePath = $service.Path
        CompilationSuccess = $compilationSuccess
        ConfigurationSuccess = $configurationSuccess
        DependenciesSuccess = $dependenciesSuccess
    }
}

# Génération du rapport final
Generate-TestReport -Results $testResults

Write-Host "`n🏁 Tests terminés!" -ForegroundColor Green
