<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.DocumentAnalysis.API</name>
    </assembly>
    <members>
        <member name="T:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController">
            <summary>
            Contrôleur pour l'analyse de documents juridiques
            </summary>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest,System.Threading.CancellationToken)">
            <summary>
            Analyse un document juridique complet
            </summary>
            <param name="request">Données du document à analyser</param>
            <param name="cancellationToken">Token d'annulation</param>
            <returns>Résultat de l'analyse complète</returns>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtient le résultat d'une analyse par ID
            </summary>
            <param name="analysisId">ID de l'analyse</param>
            <param name="cancellationToken">Token d'annulation</param>
            <returns>Résultat de l'analyse</returns>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto,System.Threading.CancellationToken)">
            <summary>
            Obtient la liste des analyses pour l'utilisateur connecté
            </summary>
            <param name="request">Paramètres de filtrage et pagination</param>
            <param name="cancellationToken">Token d'annulation</param>
            <returns>Liste paginée des analyses</returns>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis(System.Guid,LexAI.DocumentAnalysis.Application.DTOs.AnalysisOptions,System.Threading.CancellationToken)">
            <summary>
            Régénère une analyse existante avec de nouvelles options
            </summary>
            <param name="analysisId">ID de l'analyse à régénérer</param>
            <param name="options">Nouvelles options d'analyse</param>
            <param name="cancellationToken">Token d'annulation</param>
            <returns>Nouvelle analyse</returns>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.DeleteAnalysis(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Supprime une analyse
            </summary>
            <param name="analysisId">ID de l'analyse à supprimer</param>
            <param name="cancellationToken">Token d'annulation</param>
            <returns>Résultat de la suppression</returns>
        </member>
        <member name="T:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest">
            <summary>
            Modèle pour l'upload de document
            </summary>
        </member>
        <member name="P:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest.DocumentFile">
            <summary>
            Fichier document à analyser
            </summary>
        </member>
        <member name="P:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest.DocumentName">
            <summary>
            Nom du document (optionnel, utilise le nom du fichier par défaut)
            </summary>
        </member>
        <member name="P:LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest.Options">
            <summary>
            Options d'analyse
            </summary>
        </member>
        <member name="T:LexAI.DocumentAnalysis.API.Controllers.HealthController">
            <summary>
            Contrôleur pour la santé et les statistiques du service
            </summary>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.HealthController.GetHealth">
            <summary>
            Vérification de santé basique
            </summary>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.HealthController.GetDetailedHealth">
            <summary>
            Vérification de santé détaillée
            </summary>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.HealthController.GetCacheStatistics">
            <summary>
            Statistiques du cache
            </summary>
        </member>
        <member name="M:LexAI.DocumentAnalysis.API.Controllers.HealthController.ClearCache(System.String)">
            <summary>
            Vider le cache
            </summary>
        </member>
    </members>
</doc>
