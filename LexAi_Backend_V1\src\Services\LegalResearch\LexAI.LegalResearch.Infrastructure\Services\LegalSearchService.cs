using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace LexAI.LegalResearch.Infrastructure.Services;

/// <summary>
/// Legal search service implementation
/// </summary>
public class LegalSearchService : ILegalSearchService
{
    private readonly IEmbeddingService _embeddingService;
    private readonly IVectorDatabaseService _vectorService;
    private readonly IQueryProcessingService _queryProcessor;
    private readonly ILegalDocumentRepository _documentRepository;
    private readonly IMemoryCache _cache;
    private readonly ILogger<LegalSearchService> _logger;

    /// <summary>
    /// Initializes a new instance of the LegalSearchService
    /// </summary>
    /// <param name="embeddingService">Embedding service</param>
    /// <param name="vectorService">Vector database service</param>
    /// <param name="queryProcessor">Query processing service</param>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="cache">Memory cache</param>
    /// <param name="logger">Logger</param>
    public LegalSearchService(
        IEmbeddingService embeddingService,
        IVectorDatabaseService vectorService,
        IQueryProcessingService queryProcessor,
        ILegalDocumentRepository documentRepository,
        IMemoryCache cache,
        ILogger<LegalSearchService> logger)
    {
        _embeddingService = embeddingService;
        _vectorService = vectorService;
        _queryProcessor = queryProcessor;
        _documentRepository = documentRepository;
        _cache = cache;
        _logger = logger;
    }

    /// <summary>
    /// Performs a semantic search for legal documents
    /// </summary>
    /// <param name="request">Search request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search response with results</returns>
    public async Task<SearchResponseDto> SearchAsync(SearchRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Performing semantic search for query: {Query}", request.Query);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // Check cache first
            var cacheKey = GenerateCacheKey(request);
            if (_cache.TryGetValue(cacheKey, out SearchResponseDto? cachedResponse))
            {
                _logger.LogDebug("Returning cached search results for query: {Query}", request.Query);
                cachedResponse!.IsCached = true;
                return cachedResponse;
            }

            // Process the query
            var processedQuery = await _queryProcessor.ProcessQueryAsync(request.Query, cancellationToken);
            var intent = await _queryProcessor.DetectIntentAsync(request.Query, cancellationToken);

            // Generate embedding for the query
            var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(processedQuery, cancellationToken);

            // Prepare filters for vector search
            var filters = new Dictionary<string, object>();
            if (request.DomainFilter.HasValue)
                filters["domain"] = request.DomainFilter.Value.ToString();
            if (request.TypeFilter.HasValue)
                filters["type"] = request.TypeFilter.Value.ToString();
            if (!string.IsNullOrEmpty(request.LanguageFilter))
                filters["language"] = request.LanguageFilter;

            // Perform vector similarity search
            var vectorResults = await _vectorService.SearchSimilarAsync(
                queryEmbedding,
                request.Limit * 2, // Get more results for re-ranking
                request.MinRelevanceScore,
                filters,
                cancellationToken);

            // Convert vector results to search results
            var searchResults = new List<SearchResultDto>();
            foreach (var vectorResult in vectorResults.Take(request.Limit))
            {
                var document = await _documentRepository.GetByIdAsync(vectorResult.DocumentId, cancellationToken);
                if (document == null) continue;

                var searchResult = new SearchResultDto
                {
                    DocumentId = document.Id,
                    Title = document.Title,
                    Summary = document.Summary,
                    RelevanceScore = vectorResult.SimilarityScore,
                    SimilarityScore = vectorResult.SimilarityScore,
                    KeywordScore = 0.0, // Will be calculated in hybrid search
                    DocumentType = document.Type,
                    LegalDomain = document.Domain,
                    Source = new DocumentSourceDto
                    {
                        Name = document.Source.Name,
                        Url = document.Source.Url,
                        Type = document.Source.Type,
                        Authority = document.Source.Authority,
                        Jurisdiction = document.Source.Jurisdiction,
                        ReliabilityScore = document.Source.ReliabilityScore
                    },
                    PublicationDate = document.PublicationDate,
                    EffectiveDate = document.EffectiveDate,
                    Tags = document.Tags.ToList(),
                    DocumentUrl = document.FilePath,
                    Rank = searchResults.Count + 1
                };

                // Add matched chunks
                var matchedChunk = vectorResult.MatchedChunk;
                if (matchedChunk != null)
                {
                    searchResult.MatchedChunks.Add(new MatchedChunkDto
                    {
                        ChunkId = matchedChunk.ChunkId,
                        Content = matchedChunk.Content,
                        Type = matchedChunk.Type,
                        SimilarityScore = vectorResult.SimilarityScore,
                        StartPosition = matchedChunk.StartPosition,
                        EndPosition = matchedChunk.EndPosition
                    });
                }

                // Generate highlights if requested
                if (request.IncludeHighlights)
                {
                    searchResult.Highlights = GenerateHighlights(document.Content, request.Query);
                }

                searchResults.Add(searchResult);
            }

            stopwatch.Stop();

            // Calculate quality score
            var qualityScore = CalculateQualityScore(searchResults, request.Query);

            // Create response
            var response = new SearchResponseDto
            {
                Query = request.Query,
                ProcessedQuery = processedQuery,
                Results = searchResults,
                TotalResults = searchResults.Count,
                ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                Method = SearchMethod.Semantic,
                Intent = intent,
                QualityScore = qualityScore,
                IsCached = false
            };

            // Cache the response
            _cache.Set(cacheKey, response, TimeSpan.FromMinutes(15));

            _logger.LogInformation("Semantic search completed. Results: {ResultCount}, Time: {ExecutionTime}ms",
                searchResults.Count, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error performing semantic search for query: {Query}", request.Query);
            throw;
        }
    }

    /// <summary>
    /// Performs a hybrid search combining keyword and semantic search
    /// </summary>
    /// <param name="request">Search request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search response with results</returns>
    public async Task<SearchResponseDto> HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Performing hybrid search for query: {Query}", request.Query);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // Check cache first
            var cacheKey = GenerateCacheKey(request, "hybrid");
            if (_cache.TryGetValue(cacheKey, out SearchResponseDto? cachedResponse))
            {
                _logger.LogDebug("Returning cached hybrid search results for query: {Query}", request.Query);
                cachedResponse!.IsCached = true;
                return cachedResponse;
            }

            // Perform semantic search
            var semanticRequest = new SearchRequestDto
            {
                Query = request.Query,
                UserId = request.UserId,
                SessionId = request.SessionId,
                Method = SearchMethod.Semantic,
                DomainFilter = request.DomainFilter,
                TypeFilter = request.TypeFilter,
                LanguageFilter = request.LanguageFilter,
                DateFilter = request.DateFilter,
                Limit = request.Limit,
                MinRelevanceScore = request.MinRelevanceScore * 0.8, // Lower threshold for semantic
                IncludeHighlights = request.IncludeHighlights
            };

            var semanticResults = await SearchAsync(semanticRequest, cancellationToken);

            // Perform keyword search
            var keywordResults = await PerformKeywordSearchAsync(request, cancellationToken);

            // Combine and re-rank results
            var combinedResults = CombineAndRerankResults(
                semanticResults.Results,
                keywordResults,
                request.Query);

            // Take only the requested number of results
            var finalResults = combinedResults.Take(request.Limit).ToList();

            // Update ranks
            for (int i = 0; i < finalResults.Count; i++)
            {
                finalResults[i].Rank = i + 1;
            }

            stopwatch.Stop();

            // Calculate quality score
            var qualityScore = CalculateQualityScore(finalResults, request.Query);

            // Create response
            var response = new SearchResponseDto
            {
                Query = request.Query,
                ProcessedQuery = semanticResults.ProcessedQuery,
                Results = finalResults,
                TotalResults = finalResults.Count,
                ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                Method = SearchMethod.Hybrid,
                Intent = semanticResults.Intent,
                QualityScore = qualityScore,
                IsCached = false
            };

            // Cache the response
            _cache.Set(cacheKey, response, TimeSpan.FromMinutes(15));

            _logger.LogInformation("Hybrid search completed. Results: {ResultCount}, Time: {ExecutionTime}ms",
                finalResults.Count, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error performing hybrid search for query: {Query}", request.Query);
            throw;
        }
    }

    /// <summary>
    /// Finds similar documents to a given document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="limit">Maximum number of similar documents</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar documents</returns>
    public async Task<IEnumerable<SearchResultDto>> FindSimilarDocumentsAsync(Guid documentId, int limit = 10, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Finding similar documents for document: {DocumentId}", documentId);

        try
        {
            var document = await _documentRepository.GetByIdAsync(documentId, cancellationToken);
            if (document == null)
            {
                _logger.LogWarning("Document not found: {DocumentId}", documentId);
                return Enumerable.Empty<SearchResultDto>();
            }

            // Use document content to find similar documents
            var request = new SearchRequestDto
            {
                Query = document.Summary,
                Method = SearchMethod.Semantic,
                DomainFilter = document.Domain,
                TypeFilter = document.Type,
                Limit = limit + 1, // +1 to exclude the original document
                MinRelevanceScore = 0.6
            };

            var searchResponse = await SearchAsync(request, cancellationToken);

            // Filter out the original document
            var similarDocuments = searchResponse.Results
                .Where(r => r.DocumentId != documentId)
                .Take(limit)
                .ToList();

            _logger.LogInformation("Found {Count} similar documents for document: {DocumentId}",
                similarDocuments.Count, documentId);

            return similarDocuments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding similar documents for document: {DocumentId}", documentId);
            throw;
        }
    }

    /// <summary>
    /// Gets search suggestions based on partial query
    /// </summary>
    /// <param name="partialQuery">Partial query text</param>
    /// <param name="limit">Maximum number of suggestions</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search suggestions</returns>
    public async Task<IEnumerable<string>> GetSearchSuggestionsAsync(string partialQuery, int limit = 10, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting search suggestions for partial query: {PartialQuery}", partialQuery);

        try
        {
            // Expand query to get related terms
            var expandedTerms = await _queryProcessor.ExpandQueryAsync(partialQuery, cancellationToken);

            // Return top suggestions
            return expandedTerms.Take(limit);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting search suggestions for partial query: {PartialQuery}", partialQuery);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// Analyzes query intent and extracts entities
    /// </summary>
    /// <param name="query">Query text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Query analysis result</returns>
    public async Task<QueryAnalysisDto> AnalyzeQueryAsync(string query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Analyzing query: {Query}", query);

        try
        {
            var intent = await _queryProcessor.DetectIntentAsync(query, cancellationToken);
            var entities = await _queryProcessor.ExtractEntitiesAsync(query, cancellationToken);
            var expandedTerms = await _queryProcessor.ExpandQueryAsync(query, cancellationToken);

            return new QueryAnalysisDto
            {
                OriginalQuery = query,
                Intent = intent,
                Entities = entities.ToList(),
                ExpandedTerms = expandedTerms.ToList(),
                ProcessedQuery = await _queryProcessor.ProcessQueryAsync(query, cancellationToken)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing query: {Query}", query);
            throw;
        }
    }

    /// <summary>
    /// Gets search analytics for a user or session
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="sessionId">Session ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search analytics</returns>
    public async Task<SearchAnalyticsDto> GetSearchAnalyticsAsync(Guid? userId = null, string? sessionId = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting search analytics for user: {UserId}, session: {SessionId}", userId, sessionId);

        // This would typically query the search query repository for analytics
        // For now, return a placeholder
        return new SearchAnalyticsDto
        {
            TotalSearches = 0,
            AverageExecutionTime = 0,
            AverageResultCount = 0,
            TopQueries = new List<string>(),
            TopDomains = new Dictionary<LegalDomain, int>(),
            SearchTrends = new Dictionary<DateTime, int>()
        };
    }

    private async Task<List<SearchResultDto>> PerformKeywordSearchAsync(SearchRequestDto request, CancellationToken cancellationToken)
    {
        // This would implement keyword-based search using full-text search
        // For now, return empty results
        await Task.CompletedTask;
        return new List<SearchResultDto>();
    }

    private List<SearchResultDto> CombineAndRerankResults(
        List<SearchResultDto> semanticResults,
        List<SearchResultDto> keywordResults,
        string query)
    {
        // Combine results and remove duplicates
        var combinedResults = new Dictionary<Guid, SearchResultDto>();

        // Add semantic results
        foreach (var result in semanticResults)
        {
            combinedResults[result.DocumentId] = result;
        }

        // Merge keyword results
        foreach (var result in keywordResults)
        {
            if (combinedResults.TryGetValue(result.DocumentId, out var existing))
            {
                // Combine scores
                existing.KeywordScore = result.KeywordScore;
                existing.RelevanceScore = (existing.SimilarityScore * 0.6) + (result.KeywordScore * 0.4);
            }
            else
            {
                combinedResults[result.DocumentId] = result;
            }
        }

        // Re-rank by combined score
        return combinedResults.Values
            .OrderByDescending(r => r.RelevanceScore)
            .ToList();
    }

    private List<TextHighlightDto> GenerateHighlights(string content, string query)
    {
        // Simple highlight generation - in production, use more sophisticated highlighting
        var highlights = new List<TextHighlightDto>();
        var queryTerms = query.ToLowerInvariant().Split(' ', StringSplitOptions.RemoveEmptyEntries);

        foreach (var term in queryTerms)
        {
            var index = content.ToLowerInvariant().IndexOf(term);
            if (index >= 0)
            {
                var start = Math.Max(0, index - 50);
                var end = Math.Min(content.Length, index + term.Length + 50);
                var highlightText = content.Substring(start, end - start);

                highlights.Add(new TextHighlightDto
                {
                    Text = highlightText,
                    StartPosition = start,
                    EndPosition = end,
                    Score = 1.0,
                    Type = "keyword"
                });
            }
        }

        return highlights;
    }

    private double CalculateQualityScore(List<SearchResultDto> results, string query)
    {
        if (results.Count == 0)
            return 0.0;

        // Simple quality score based on average relevance and result count
        var avgRelevance = results.Average(r => r.RelevanceScore);
        var countScore = Math.Min(1.0, results.Count / 10.0);

        return (avgRelevance * 0.8) + (countScore * 0.2);
    }

    private string GenerateCacheKey(SearchRequestDto request, string suffix = "")
    {
        var keyData = $"{request.Query}|{request.Method}|{request.DomainFilter}|{request.TypeFilter}|{request.LanguageFilter}|{request.Limit}|{request.MinRelevanceScore}|{suffix}";
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(keyData));
        return Convert.ToBase64String(hash);
    }
}
