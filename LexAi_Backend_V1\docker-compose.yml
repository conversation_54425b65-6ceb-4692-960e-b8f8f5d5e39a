version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: lexai-postgres
    environment:
      POSTGRES_DB: lexai_db
      POSTGRES_USER: lexai_user
      POSTGRES_PASSWORD: lexai_password_2024!
      POSTGRES_MULTIPLE_DATABASES: identity_db,legal_research_db,client_management_db,document_analysis_db,document_generator_db
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sh:/docker-entrypoint-initdb.d/init-databases.sh
    networks:
      - lexai-network-identity
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lexai_user -d lexai_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  # mongodb:
  #   image: mongo:7.0
  #   container_name: lexai-mongodb
  #   environment:
  #     MONGO_INITDB_ROOT_USERNAME: lexai_admin
  #     MONGO_INITDB_ROOT_PASSWORD: lexai_mongo_password_2024!
  #     #MONGO_INITDB_DATABASE: lexai_documents
  #   ports:
  #     - "27018:27017"
  #   volumes:
  #     - mongodb_data:/data/db
  #     - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: lexai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lexai-network-identity
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass lexai_redis_password_2024!
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  # rabbitmq:
  #   image: rabbitmq:3.12-management-alpine
  #   container_name: lexai-rabbitmq
  #   environment:
  #     RABBITMQ_DEFAULT_USER: lexai_user
  #     RABBITMQ_DEFAULT_PASS: lexai_rabbitmq_password_2024!
  #     RABBITMQ_DEFAULT_VHOST: lexai_vhost
  #   ports:
  #     - "5672:5672"   # AMQP port
  #     - "15672:15672" # Management UI
  #   volumes:
  #     - rabbitmq_data:/var/lib/rabbitmq
  #     - ./scripts/rabbitmq-definitions.json:/etc/rabbitmq/definitions.json:ro
  #     - ./scripts/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "rabbitmq-diagnostics", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Qdrant Vector Database
  # qdrant:
  #   image: qdrant/qdrant:v1.7.4
  #   container_name: lexai-qdrant
  #   ports:
  #     - "6333:6333"   # HTTP API
  #     - "6334:6334"   # gRPC API
  #   volumes:
  #     - qdrant_data:/qdrant/storage
  #   environment:
  #     - QDRANT__SERVICE__HTTP_PORT=6333
  #     - QDRANT__SERVICE__GRPC_PORT=6334
  #     - QDRANT__LOG_LEVEL=INFO
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # API Gateway
  # api-gateway:
  #   build:
  #     context: .
  #     dockerfile: src/ApiGateway/Dockerfile
  #   container_name: lexai-api-gateway
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:8080
  #     - ConnectionStrings__Redis=redis:6379
  #     - Jwt__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long
  #     - Jwt__Issuer=LexAI
  #     - Jwt__Audience=LexAI-Users
  #   ports:
  #     - "8080:8080"
  #   depends_on:
  #     - redis
  #     - rabbitmq
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped

  # Identity Service
  # identity-service:
  #   build:
  #     context: .
  #     dockerfile: src/Services/Identity/LexAI.Identity.API/Dockerfile
  #   container_name: lexai-identity-service
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:8081
  #     - ConnectionStrings__PostgreSql=Host=postgres;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!
  #     - ConnectionStrings__Redis=redis:6379
  #     - Jwt__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long
  #     - Jwt__Issuer=LexAI
  #     - Jwt__Audience=LexAI-Users
  #     - RabbitMQ__HostName=rabbitmq
  #     - RabbitMQ__UserName=lexai_user
  #     - RabbitMQ__Password=lexai_rabbitmq_password_2024!
  #     - RabbitMQ__VirtualHost=lexai_vhost
  #   ports:
  #     - "8081:8081"
  #   depends_on:
  #     - postgres
  #     - redis
  #     - rabbitmq
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped

  # Legal Research Service
  # legal-research-service:
  #   build:
  #     context: .
  #     dockerfile: src/Services/LegalResearch/LexAI.LegalResearch.API/Dockerfile
  #   container_name: lexai-legal-research-service
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:8082
  #     - ConnectionStrings__PostgreSql=Host=postgres;Database=legal_research_db;Username=lexai_user;Password=lexai_password_2024!
  #     - ConnectionStrings__MongoDB=mongodb://lexai_admin:lexai_mongo_password_2024!@mongodb:27017/lexai_documents?authSource=admin
  #     - ConnectionStrings__Redis=redis:6379
  #     - OpenAI__ApiKey=${OPENAI_API_KEY}
  #     - OpenAI__DefaultModel=gpt-4
  #     - OpenAI__EmbeddingModel=text-embedding-ada-002
  #     - RabbitMQ__HostName=rabbitmq
  #     - RabbitMQ__UserName=lexai_user
  #     - RabbitMQ__Password=lexai_rabbitmq_password_2024!
  #     - RabbitMQ__VirtualHost=lexai_vhost
  #   ports:
  #     - "8082:8082"
  #   depends_on:
  #     - postgres
  #     - mongodb
  #     - redis
  #     - rabbitmq
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped

  # AI Assistant Service
  # ai-assistant-service:
  #   build:
  #     context: .
  #     dockerfile: src/Services/AIAssistant/LexAI.AIAssistant.API/Dockerfile
  #   container_name: lexai-ai-assistant-service
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:8083
  #     - ConnectionStrings__PostgreSql=Host=postgres;Database=ai_assistant_db;Username=lexai_user;Password=lexai_password_2024!
  #     - ConnectionStrings__MongoDB=mongodb://lexai_admin:lexai_mongo_password_2024!@mongodb:27017/lexai_documents?authSource=admin
  #     - ConnectionStrings__Redis=redis:6379
  #     - OpenAI__ApiKey=${OPENAI_API_KEY}
  #     - OpenAI__DefaultModel=gpt-4
  #     - RabbitMQ__HostName=rabbitmq
  #     - RabbitMQ__UserName=lexai_user
  #     - RabbitMQ__Password=lexai_rabbitmq_password_2024!
  #     - RabbitMQ__VirtualHost=lexai_vhost
  #   ports:
  #     - "8083:8083"
  #   depends_on:
  #     - postgres
  #     - mongodb
  #     - redis
  #     - rabbitmq
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped
# Interface d'administration MongoDB (optionnel)
  # mongo-express:
  #   image: mongo-express:latest
  #   container_name: lexai-mongo-express-0
  #   ports:
  #     - "8081:8081"
  #   environment:
  #     - ME_CONFIG_MONGODB_ADMINUSERNAME=lexai_admin
  #     - ME_CONFIG_MONGODB_ADMINPASSWORD=lexai_mongo_password_2024!
  #     - ME_CONFIG_MONGODB_URL=mongodb://lexai_admin:lexai_mongo_password_2024!@mongodb:27017/
  #     - ME_CONFIG_BASICAUTH_USERNAME=admin
  #     - ME_CONFIG_BASICAUTH_PASSWORD=mongoexpress_2024!
  #   depends_on:
  #     - mongodb
  #   networks:
  #     - lexai-network-identity
  #   restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  # rabbitmq_data:
  #   driver: local
  # mongodb_data:
  #   driver: local
  # qdrant_data:
  #   driver: local

networks:
  lexai-network-identity:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
