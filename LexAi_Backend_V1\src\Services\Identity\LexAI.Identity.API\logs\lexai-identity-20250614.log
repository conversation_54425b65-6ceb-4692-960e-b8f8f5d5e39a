2025-06-14 15:53:26.948 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 15:53:26.979 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 15:53:26.984 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 15:53:27.432 +04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 15:53:27.443 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 15:53:27.472 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:53:27.711 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 15:53:27.713 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 15:53:27.751 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:53:27.754 +04:00 [INF] Hosting environment: Development
2025-06-14 15:53:27.756 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 15:53:28.263 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 15:53:28.503 +04:00 [INF] Request GET / started with correlation ID f5a2b6bd-f85f-4b45-8388-dd772f5f245d
2025-06-14 15:53:28.594 +04:00 [INF] Request GET / completed in 87ms with status 404 (Correlation ID: f5a2b6bd-f85f-4b45-8388-dd772f5f245d)
2025-06-14 15:53:28.608 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 370.6465ms
2025-06-14 15:53:28.628 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 16:48:10.081 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 16:48:10.122 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 16:48:10.131 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 16:48:10.435 +04:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 16:48:10.446 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 16:48:10.469 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 16:48:10.686 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 16:48:10.688 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 16:48:10.725 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 16:48:10.731 +04:00 [INF] Hosting environment: Development
2025-06-14 16:48:10.732 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 16:48:11.756 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 16:48:11.893 +04:00 [INF] Request GET / started with correlation ID e26566e0-7d2a-4baa-a8db-e9ba04350db0
2025-06-14 16:48:11.951 +04:00 [INF] Request GET / completed in 53ms with status 404 (Correlation ID: e26566e0-7d2a-4baa-a8db-e9ba04350db0)
2025-06-14 16:48:11.968 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 227.1086ms
2025-06-14 16:48:11.982 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 16:49:12.245 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 16:49:12.264 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 903ec861-afdf-42c5-9182-aec72ff0c8bb
2025-06-14 16:49:12.275 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:49:12.283 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 11ms with status 204 (Correlation ID: 903ec861-afdf-42c5-9182-aec72ff0c8bb)
2025-06-14 16:49:12.291 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 45.5067ms
2025-06-14 16:49:12.298 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 16:49:12.318 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 36b93e2d-7e6c-4fdb-8c7c-2de734c97272
2025-06-14 16:49:12.322 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:49:12.329 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 16:49:12.374 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 16:49:12.430 +04:00 [INF] Token refresh attempt
2025-06-14 16:49:12.456 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 16:49:12.919 +04:00 [INF] Executed DbCommand (22ms) [Parameters=[@__token_0='LKcKfClaH5+i162EjK+AlrY7eTPniEXtwB4xp9bGommtm4ho+AoK7xGKuhH10RnLhvNX9rdiWUdCfDxneVfqsg=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 16:49:13.097 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 16:49:13.121 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 16:49:13.143 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__token_0='LKcKfClaH5+i162EjK+AlrY7eTPniEXtwB4xp9bGommtm4ho+AoK7xGKuhH10RnLhvNX9rdiWUdCfDxneVfqsg=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 16:49:13.260 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p16='c332a55e-5586-498f-a271-b25534445f6f', @p0='2025-06-14T11:30:21.2440450Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-21T11:30:21.2247110Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-14T12:49:13.1472809Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='LKcKfClaH5+i162EjK+AlrY7eTPniEXtwB4xp9bGommtm4ho+AoK7xGKuhH10RnLhvNX9rdiWUdCfDxneVfqsg==' (Nullable = false), @p12='2025-06-14T12:49:13.1827918Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='795' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-14 16:49:13.296 +04:00 [INF] Refresh token updated successfully: "c332a55e-5586-498f-a271-b25534445f6f"
2025-06-14 16:49:13.368 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='73685a7c-4cce-4616-a862-a94c74771d80', @p1='2025-06-14T12:49:13.3613343Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T12:49:13.3474755Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='VGr8jth8Wt+6gXaxyOlVWpcx+aspFMtNdQc9GnwHFVSD5AI73BCBitqS+3Vovn+KDbhXVykKZpfbOtv8EDjjZg==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 16:49:13.376 +04:00 [INF] Refresh token added successfully: "73685a7c-4cce-4616-a862-a94c74771d80"
2025-06-14 16:49:13.377 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 16:49:13.380 +04:00 [INF] Token refresh successful
2025-06-14 16:49:13.386 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 16:49:13.411 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 1021.2602ms
2025-06-14 16:49:13.419 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 16:49:13.427 +04:00 [INF] Request POST /api/auth/refresh completed in 1106ms with status 200 (Correlation ID: 36b93e2d-7e6c-4fdb-8c7c-2de734c97272)
2025-06-14 16:49:13.460 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 1162.4632ms
2025-06-14 17:04:53.109 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 17:04:53.197 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 17:04:53.213 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 17:04:53.918 +04:00 [INF] Executed DbCommand (123ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 17:04:53.940 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 17:04:54.001 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 17:04:54.337 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 17:04:54.340 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 17:04:54.422 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 17:04:54.425 +04:00 [INF] Hosting environment: Development
2025-06-14 17:04:54.427 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 17:04:55.620 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 17:04:55.884 +04:00 [INF] Request GET / started with correlation ID af274da9-e8d9-44ce-92fa-5954b0ca3fbe
2025-06-14 17:04:55.983 +04:00 [INF] Request GET / completed in 92ms with status 404 (Correlation ID: af274da9-e8d9-44ce-92fa-5954b0ca3fbe)
2025-06-14 17:04:55.995 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 378.316ms
2025-06-14 17:04:56.007 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 17:56:28.577 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 17:56:28.805 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 17:56:28.830 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 17:56:29.992 +04:00 [INF] Executed DbCommand (157ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 17:56:30.026 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 17:56:30.084 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 17:56:30.472 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 17:56:30.476 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 17:56:30.743 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 17:56:30.745 +04:00 [INF] Hosting environment: Development
2025-06-14 17:56:30.747 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 17:56:31.307 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 17:56:31.576 +04:00 [INF] Request GET / started with correlation ID 9a407391-adde-4a56-ab57-0ecaa800bcf3
2025-06-14 17:56:31.719 +04:00 [INF] Request GET / completed in 133ms with status 404 (Correlation ID: 9a407391-adde-4a56-ab57-0ecaa800bcf3)
2025-06-14 17:56:31.729 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 444.8ms
2025-06-14 17:56:31.743 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 17:57:27.185 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 17:57:27.300 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 73ecd2d5-cda8-43dd-aab0-7f25da09b037
2025-06-14 17:57:27.334 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:27.346 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 19ms with status 204 (Correlation ID: 73ecd2d5-cda8-43dd-aab0-7f25da09b037)
2025-06-14 17:57:27.358 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 174.8497ms
2025-06-14 17:57:27.371 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 17:57:27.405 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 18b9e809-c113-46e8-90e7-0828f069688f
2025-06-14 17:57:27.439 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:27.452 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 17:57:27.523 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 17:57:27.634 +04:00 [INF] Token refresh attempt
2025-06-14 17:57:27.728 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 17:57:29.125 +04:00 [INF] Executed DbCommand (51ms) [Parameters=[@__token_0='VGr8jth8Wt+6gXaxyOlVWpcx+aspFMtNdQc9GnwHFVSD5AI73BCBitqS+3Vovn+KDbhXVykKZpfbOtv8EDjjZg=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 17:57:29.757 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 17:57:29.818 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 17:57:29.872 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='VGr8jth8Wt+6gXaxyOlVWpcx+aspFMtNdQc9GnwHFVSD5AI73BCBitqS+3Vovn+KDbhXVykKZpfbOtv8EDjjZg=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 17:57:30.147 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p16='73685a7c-4cce-4616-a862-a94c74771d80', @p0='2025-06-14T12:49:13.3613340Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-21T12:49:13.3474750Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-14T13:57:29.8800177Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='VGr8jth8Wt+6gXaxyOlVWpcx+aspFMtNdQc9GnwHFVSD5AI73BCBitqS+3Vovn+KDbhXVykKZpfbOtv8EDjjZg==' (Nullable = false), @p12='2025-06-14T13:57:29.9841957Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='797' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-14 17:57:30.185 +04:00 [INF] Refresh token updated successfully: "73685a7c-4cce-4616-a862-a94c74771d80"
2025-06-14 17:57:30.317 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='13613879-8128-4e13-ad37-8441ffd94e3a', @p1='2025-06-14T13:57:30.2889061Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T13:57:30.2566181Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='woN+UwoZHlSkyI+FxZ3xCYUJrSTsGilZQQGOPp9Dh4xImGtdSs0vxtBKugxfuZjJR8IOMAgg9QddyChe+3ThNA==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 17:57:30.327 +04:00 [INF] Refresh token added successfully: "13613879-8128-4e13-ad37-8441ffd94e3a"
2025-06-14 17:57:30.331 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:57:30.337 +04:00 [INF] Token refresh successful
2025-06-14 17:57:30.357 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 17:57:30.430 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2888.9662ms
2025-06-14 17:57:30.438 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 17:57:30.444 +04:00 [INF] Request POST /api/auth/refresh completed in 3007ms with status 200 (Correlation ID: 18b9e809-c113-46e8-90e7-0828f069688f)
2025-06-14 17:57:30.474 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 3102.6199ms
2025-06-14 19:30:47.519 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 19:30:47.577 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 19:30:47.588 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 19:30:48.111 +04:00 [INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 19:30:48.124 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 19:30:48.160 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 19:30:48.436 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 19:30:48.439 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 19:30:48.609 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 19:30:48.620 +04:00 [INF] Hosting environment: Development
2025-06-14 19:30:48.624 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 19:30:49.547 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 19:30:49.889 +04:00 [INF] Request GET / started with correlation ID 8849febc-c744-481e-ba79-b71bc700a318
2025-06-14 19:30:50.101 +04:00 [INF] Request GET / completed in 202ms with status 404 (Correlation ID: 8849febc-c744-481e-ba79-b71bc700a318)
2025-06-14 19:30:50.125 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 594.9194ms
2025-06-14 19:30:50.138 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 19:31:14.638 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 19:31:14.701 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 9eaa9237-5ec9-43a0-806e-ebc35898961b
2025-06-14 19:31:14.713 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:31:14.720 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 10ms with status 204 (Correlation ID: 9eaa9237-5ec9-43a0-806e-ebc35898961b)
2025-06-14 19:31:14.731 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 93.3914ms
2025-06-14 19:31:14.737 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 19:31:14.755 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 72fa5899-6f3d-4591-9c3a-3718832c05f0
2025-06-14 19:31:14.762 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:31:14.767 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 19:31:14.817 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 19:31:14.933 +04:00 [INF] Token refresh attempt
2025-06-14 19:31:15.053 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 19:31:15.991 +04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__token_0='woN+UwoZHlSkyI+FxZ3xCYUJrSTsGilZQQGOPp9Dh4xImGtdSs0vxtBKugxfuZjJR8IOMAgg9QddyChe+3ThNA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 19:31:16.422 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 19:31:16.485 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 19:31:16.525 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='woN+UwoZHlSkyI+FxZ3xCYUJrSTsGilZQQGOPp9Dh4xImGtdSs0vxtBKugxfuZjJR8IOMAgg9QddyChe+3ThNA=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 19:31:16.794 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p16='13613879-8128-4e13-ad37-8441ffd94e3a', @p0='2025-06-14T13:57:30.2889060Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-21T13:57:30.2566180Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-14T15:31:16.5318102Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='woN+UwoZHlSkyI+FxZ3xCYUJrSTsGilZQQGOPp9Dh4xImGtdSs0vxtBKugxfuZjJR8IOMAgg9QddyChe+3ThNA==' (Nullable = false), @p12='2025-06-14T15:31:16.6489154Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='799' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-14 19:31:16.832 +04:00 [INF] Refresh token updated successfully: "13613879-8128-4e13-ad37-8441ffd94e3a"
2025-06-14 19:31:16.935 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='13c6333d-1d1c-409f-b189-24c009cabe3a', @p1='2025-06-14T15:31:16.9221812Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T15:31:16.8999272Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='ROV58Ul7cNOmbpMS8YwMROSK5JvlKhX2i2b5AFblPKR/psqGUvB6g68Ks+AYF9e/rgT+mfa2ttBkdEZQqFbAdQ==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 19:31:16.941 +04:00 [INF] Refresh token added successfully: "13c6333d-1d1c-409f-b189-24c009cabe3a"
2025-06-14 19:31:16.943 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:31:16.948 +04:00 [INF] Token refresh successful
2025-06-14 19:31:16.968 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 19:31:17.022 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2187.6805ms
2025-06-14 19:31:17.028 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 19:31:17.031 +04:00 [INF] Request POST /api/auth/refresh completed in 2270ms with status 200 (Correlation ID: 72fa5899-6f3d-4591-9c3a-3718832c05f0)
2025-06-14 19:31:17.043 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 2306.3501ms
2025-06-14 20:42:12.771 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 20:42:12.780 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 060fc33b-8993-42fb-bbca-c1233d2bd8b2
2025-06-14 20:42:12.783 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:42:12.784 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: 060fc33b-8993-42fb-bbca-c1233d2bd8b2)
2025-06-14 20:42:12.787 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 76.7507ms
2025-06-14 20:42:12.789 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 20:42:12.796 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID e505697c-eaff-4cf5-a8d2-8737e316dd8f
2025-06-14 20:42:12.799 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:42:12.806 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 20:42:12.811 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 20:42:12.816 +04:00 [INF] Token refresh attempt
2025-06-14 20:42:12.860 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 20:42:12.888 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__token_0='ROV58Ul7cNOmbpMS8YwMROSK5JvlKhX2i2b5AFblPKR/psqGUvB6g68Ks+AYF9e/rgT+mfa2ttBkdEZQqFbAdQ=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 20:42:12.897 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 20:42:12.905 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='ROV58Ul7cNOmbpMS8YwMROSK5JvlKhX2i2b5AFblPKR/psqGUvB6g68Ks+AYF9e/rgT+mfa2ttBkdEZQqFbAdQ=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 20:42:12.913 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p16='13c6333d-1d1c-409f-b189-24c009cabe3a', @p0='2025-06-14T15:31:16.9221810Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-21T15:31:16.8999270Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-14T16:42:12.9079506Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='ROV58Ul7cNOmbpMS8YwMROSK5JvlKhX2i2b5AFblPKR/psqGUvB6g68Ks+AYF9e/rgT+mfa2ttBkdEZQqFbAdQ==' (Nullable = false), @p12='2025-06-14T16:42:12.9082886Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='801' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-14 20:42:12.916 +04:00 [INF] Refresh token updated successfully: "13c6333d-1d1c-409f-b189-24c009cabe3a"
2025-06-14 20:42:12.924 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='3c1de0a7-b57d-4e99-aec4-9f15786b7ca8', @p1='2025-06-14T16:42:12.9203980Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T16:42:12.9183893Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='8ipVRFzTF1JJkUT68rzeDL1HSBn3MEKAseMCJ1S/Arwm5hNiJV5+Ov76QLuS3g2mikLgzhrM+H3WaA+lgOoH0w==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 20:42:12.927 +04:00 [INF] Refresh token added successfully: "3c1de0a7-b57d-4e99-aec4-9f15786b7ca8"
2025-06-14 20:42:12.929 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:42:12.930 +04:00 [INF] Token refresh successful
2025-06-14 20:42:12.931 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 20:42:12.934 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 120.3249ms
2025-06-14 20:42:12.936 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 20:42:12.938 +04:00 [INF] Request POST /api/auth/refresh completed in 138ms with status 200 (Correlation ID: e505697c-eaff-4cf5-a8d2-8737e316dd8f)
2025-06-14 20:42:12.941 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 152.3627ms
2025-06-14 21:21:27.485 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 21:21:27.547 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 21:21:27.559 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 21:21:28.096 +04:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 21:21:28.113 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 21:21:28.161 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 21:21:28.616 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 21:21:28.619 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 21:21:28.716 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 21:21:28.718 +04:00 [INF] Hosting environment: Development
2025-06-14 21:21:28.720 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 21:21:30.160 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 21:21:30.551 +04:00 [INF] Request GET / started with correlation ID 05105690-fd8e-47c0-b6a1-43348d9aaa72
2025-06-14 21:21:30.765 +04:00 [INF] Request GET / completed in 205ms with status 404 (Correlation ID: 05105690-fd8e-47c0-b6a1-43348d9aaa72)
2025-06-14 21:21:30.785 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 634.2411ms
2025-06-14 21:21:30.799 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 22:22:41.634 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 22:22:41.685 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 22:22:41.694 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 22:22:42.202 +04:00 [INF] Executed DbCommand (64ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 22:22:42.220 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 22:22:42.266 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 22:22:42.704 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 22:22:42.706 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 22:22:42.827 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 22:22:42.926 +04:00 [INF] Hosting environment: Development
2025-06-14 22:22:42.929 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 22:22:44.082 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 22:22:44.397 +04:00 [INF] Request GET / started with correlation ID f6f36e21-3e40-4cda-bd64-0d9920b8ae72
2025-06-14 22:22:44.586 +04:00 [INF] Request GET / completed in 181ms with status 404 (Correlation ID: f6f36e21-3e40-4cda-bd64-0d9920b8ae72)
2025-06-14 22:22:44.606 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 541.8153ms
2025-06-14 22:22:44.620 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 22:23:12.822 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 22:23:12.872 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 0603a500-995e-4dcc-ae46-41d15f095f88
2025-06-14 22:23:12.885 +04:00 [INF] CORS policy execution successful.
2025-06-14 22:23:12.895 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 14ms with status 204 (Correlation ID: 0603a500-995e-4dcc-ae46-41d15f095f88)
2025-06-14 22:23:12.908 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 85.6345ms
2025-06-14 22:23:12.918 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 22:23:12.945 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 27f36bd1-a6c1-4a3c-9522-3e2f957151c4
2025-06-14 22:23:12.951 +04:00 [INF] CORS policy execution successful.
2025-06-14 22:23:12.958 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 22:23:13.022 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 22:23:13.102 +04:00 [INF] Token refresh attempt
2025-06-14 22:23:13.142 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 22:23:14.236 +04:00 [INF] Executed DbCommand (42ms) [Parameters=[@__token_0='8ipVRFzTF1JJkUT68rzeDL1HSBn3MEKAseMCJ1S/Arwm5hNiJV5+Ov76QLuS3g2mikLgzhrM+H3WaA+lgOoH0w=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 22:23:14.716 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 22:23:14.787 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 22:23:14.843 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='8ipVRFzTF1JJkUT68rzeDL1HSBn3MEKAseMCJ1S/Arwm5hNiJV5+Ov76QLuS3g2mikLgzhrM+H3WaA+lgOoH0w=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 22:23:15.149 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@p16='3c1de0a7-b57d-4e99-aec4-9f15786b7ca8', @p0='2025-06-14T16:42:12.9203980Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-21T16:42:12.9183890Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-14T18:23:14.8531449Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='8ipVRFzTF1JJkUT68rzeDL1HSBn3MEKAseMCJ1S/Arwm5hNiJV5+Ov76QLuS3g2mikLgzhrM+H3WaA+lgOoH0w==' (Nullable = false), @p12='2025-06-14T18:23:14.9678378Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='803' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-14 22:23:15.187 +04:00 [INF] Refresh token updated successfully: "3c1de0a7-b57d-4e99-aec4-9f15786b7ca8"
2025-06-14 22:23:15.315 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='45dbd57e-759c-4799-b9a2-db13db9b9d94', @p1='2025-06-14T18:23:15.2958090Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T18:23:15.2679329Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='DiFfsizBJXtvQ6eObOZaH9zSxR3g426ejneA68pgPo9VUjYp1WdRH9MBHPCOs0KapOShv/0RTHGcZB0ImoUKTQ==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 22:23:15.327 +04:00 [INF] Refresh token added successfully: "45dbd57e-759c-4799-b9a2-db13db9b9d94"
2025-06-14 22:23:15.332 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 22:23:15.344 +04:00 [INF] Token refresh successful
2025-06-14 22:23:15.368 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 22:23:15.459 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2426.2538ms
2025-06-14 22:23:15.466 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 22:23:15.472 +04:00 [INF] Request POST /api/auth/refresh completed in 2521ms with status 200 (Correlation ID: 27f36bd1-a6c1-4a3c-9522-3e2f957151c4)
2025-06-14 22:23:15.484 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 2565.9901ms
