2025-06-14 13:28:17.179 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 13:28:17.216 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 13:28:17.225 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 13:28:17.792 +04:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 13:28:17.815 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 13:28:17.853 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:28:18.290 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 13:28:18.299 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 13:28:18.352 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:28:18.355 +04:00 [INF] Hosting environment: Development
2025-06-14 13:28:18.357 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 13:28:19.555 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 13:28:19.776 +04:00 [INF] Request GET / started with correlation ID d192d595-0321-4c92-86c6-a5df277f364c
2025-06-14 13:28:20.513 +04:00 [INF] Request GET / completed in 733ms with status 404 (Correlation ID: d192d595-0321-4c92-86c6-a5df277f364c)
2025-06-14 13:28:20.524 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 995.8617ms
2025-06-14 13:28:20.536 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 13:29:22.152 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/search/history - null null
2025-06-14 13:29:22.152 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/search/history - null null
2025-06-14 13:29:22.203 +04:00 [INF] Request OPTIONS /api/search/history started with correlation ID 452b2dca-e37f-4d77-a59f-85a396a944cf
2025-06-14 13:29:22.206 +04:00 [INF] Request OPTIONS /api/search/history started with correlation ID 370df5b3-4e8f-47ae-838e-3a9e43ac55cb
2025-06-14 13:29:22.210 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:22.212 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:22.216 +04:00 [INF] Request OPTIONS /api/search/history completed in 6ms with status 204 (Correlation ID: 452b2dca-e37f-4d77-a59f-85a396a944cf)
2025-06-14 13:29:22.216 +04:00 [INF] Request OPTIONS /api/search/history completed in 4ms with status 204 (Correlation ID: 370df5b3-4e8f-47ae-838e-3a9e43ac55cb)
2025-06-14 13:29:22.220 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/search/history - 204 null null 67.9975ms
2025-06-14 13:29:22.222 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/search/history - 204 null null 70.3355ms
2025-06-14 13:29:22.230 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/api/search/history - application/json null
2025-06-14 13:29:22.244 +04:00 [INF] Request GET /api/search/history started with correlation ID bb4e3020-29fe-4a47-a346-5e8f501ba88d
2025-06-14 13:29:22.246 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:22.297 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:22 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 13:29:22.335 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:22 AM'.
2025-06-14 13:29:22.338 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:22 AM'.
2025-06-14 13:29:22.339 +04:00 [INF] Request GET /api/search/history completed in 93ms with status 404 (Correlation ID: bb4e3020-29fe-4a47-a346-5e8f501ba88d)
2025-06-14 13:29:22.350 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/api/search/history - 404 0 null 119.7559ms
2025-06-14 13:29:22.355 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/api/search/history - application/json null
2025-06-14 13:29:22.359 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/api/search/history, Response status code: 404
2025-06-14 13:29:22.367 +04:00 [INF] Request GET /api/search/history started with correlation ID d2510b66-d3ce-4612-8e31-3ae1096a9cdd
2025-06-14 13:29:22.377 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:22.382 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:22 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 13:29:22.389 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:22 AM'.
2025-06-14 13:29:22.393 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:22 AM'.
2025-06-14 13:29:22.395 +04:00 [INF] Request GET /api/search/history completed in 18ms with status 404 (Correlation ID: d2510b66-d3ce-4612-8e31-3ae1096a9cdd)
2025-06-14 13:29:22.400 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/api/search/history - 404 0 null 45.1204ms
2025-06-14 13:29:22.405 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/api/search/history, Response status code: 404
2025-06-14 13:29:33.504 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-14 13:29:33.512 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 6abc98a7-b678-4606-a43c-dc28b4efda9d
2025-06-14 13:29:33.517 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:33.518 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 1ms with status 204 (Correlation ID: 6abc98a7-b678-4606-a43c-dc28b4efda9d)
2025-06-14 13:29:33.521 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 16.7707ms
2025-06-14 13:29:33.524 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-14 13:29:33.531 +04:00 [INF] Request POST /api/auth/logout started with correlation ID e36a82c7-6d52-4995-bd31-db02ba4d35e2
2025-06-14 13:29:33.533 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:33.534 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:33 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 13:29:33.536 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:33 AM'.
2025-06-14 13:29:33.537 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/12/2025 4:56:21 PM', Current time (UTC): '6/14/2025 9:29:33 AM'.
2025-06-14 13:29:33.542 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 13:29:33.546 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 13:29:33.548 +04:00 [INF] Request POST /api/auth/logout completed in 15ms with status 401 (Correlation ID: e36a82c7-6d52-4995-bd31-db02ba4d35e2)
2025-06-14 13:29:33.550 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 26.1751ms
2025-06-14 13:29:33.552 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 13:29:33.555 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 16b7c302-a251-4f19-8681-86c2d3e333b9
2025-06-14 13:29:33.556 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:33.557 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 0ms with status 204 (Correlation ID: 16b7c302-a251-4f19-8681-86c2d3e333b9)
2025-06-14 13:29:33.559 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 6.9641ms
2025-06-14 13:29:33.560 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 13:29:33.566 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 3d1e7477-d2ac-4816-af3e-85196c8741a0
2025-06-14 13:29:33.568 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:33.569 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 13:29:33.584 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 13:29:33.614 +04:00 [INF] Token refresh attempt
2025-06-14 13:29:33.670 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 13:29:33.993 +04:00 [INF] Executed DbCommand (31ms) [Parameters=[@__token_0='64ylJxITCvvDbY3rokVez5PAq2Q9TrAfY+fOmY2f0dKB8H75ke6ZJkWdKxeg0/nU0Qi377TD1TzaHZFZ+klT9Q=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 13:29:34.127 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 13:29:34.149 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='44e9958d-1537-4614-bdbb-f4fe57509fd9'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 13:29:34.167 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='64ylJxITCvvDbY3rokVez5PAq2Q9TrAfY+fOmY2f0dKB8H75ke6ZJkWdKxeg0/nU0Qi377TD1TzaHZFZ+klT9Q=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 13:29:34.262 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p16='14aa35df-7a69-4650-af30-e0943aa71995', @p0='2025-06-12T15:56:21.9727940Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-19T15:56:21.9701890Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-14T09:29:34.1712000Z' (Nullable = true) (DbType = DateTime), @p10='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p11='64ylJxITCvvDbY3rokVez5PAq2Q9TrAfY+fOmY2f0dKB8H75ke6ZJkWdKxeg0/nU0Qi377TD1TzaHZFZ+klT9Q==' (Nullable = false), @p12='2025-06-14T09:29:34.2049084Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p17='834' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-14 13:29:34.275 +04:00 [INF] Refresh token updated successfully: "14aa35df-7a69-4650-af30-e0943aa71995"
2025-06-14 13:29:34.300 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='3d67fdf8-0e47-45c3-a5fd-7006762a2b5c', @p1='2025-06-14T09:29:34.2927561Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T09:29:34.2849564Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='dLtNYnoXisD3naLcLDuqYASMrsXEZVDXrKWYPoUv9/+vtu5RQdwXQTLpWziLo3ufCnGKPuWDFbAlY3sfSJLVDg==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='44e9958d-1537-4614-bdbb-f4fe57509fd9'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 13:29:34.304 +04:00 [INF] Refresh token added successfully: "3d67fdf8-0e47-45c3-a5fd-7006762a2b5c"
2025-06-14 13:29:34.308 +04:00 [INF] Token refreshed successfully for user "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-14 13:29:34.310 +04:00 [INF] Token refresh successful
2025-06-14 13:29:34.316 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 13:29:34.333 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 744.8937ms
2025-06-14 13:29:34.335 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 13:29:34.338 +04:00 [INF] Request POST /api/auth/refresh completed in 770ms with status 200 (Correlation ID: 3d1e7477-d2ac-4816-af3e-85196c8741a0)
2025-06-14 13:29:34.343 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 783.5616ms
2025-06-14 13:29:34.353 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-14 13:29:34.357 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 8f811f4d-9434-4c25-8efd-a98ae2d08d20
2025-06-14 13:29:34.359 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:29:34.366 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-14 13:29:34.370 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API)'
2025-06-14 13:29:34.383 +04:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout(Boolean) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 13:29:34.394 +04:00 [INF] Logout attempt for user "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-14 13:29:34.440 +04:00 [INF] Logout completed successfully
2025-06-14 13:29:34.441 +04:00 [INF] Logout successful for user "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-14 13:29:34.443 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-14 13:29:34.447 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API) in 57.8804ms
2025-06-14 13:29:34.449 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API)'
2025-06-14 13:29:34.450 +04:00 [INF] Request POST /api/auth/logout completed in 91ms with status 200 (Correlation ID: 8f811f4d-9434-4c25-8efd-a98ae2d08d20)
2025-06-14 13:29:34.453 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 200 null application/json; charset=utf-8 99.1526ms
2025-06-14 13:31:09.967 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-14 13:31:09.980 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 6c9717d6-f723-49d7-9c0e-2da0855b3705
2025-06-14 13:31:09.982 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:31:09.982 +04:00 [INF] Request OPTIONS /api/auth/login completed in 0ms with status 204 (Correlation ID: 6c9717d6-f723-49d7-9c0e-2da0855b3705)
2025-06-14 13:31:09.985 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 18.0888ms
2025-06-14 13:31:09.986 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 52
2025-06-14 13:31:09.990 +04:00 [INF] Request POST /api/auth/login started with correlation ID 8759b81c-d052-4e05-8c24-cb2adbeab554
2025-06-14 13:31:09.992 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:31:09.993 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 13:31:09.996 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 13:31:10.002 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-14 13:31:10.006 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-14 13:31:10.015 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 13:31:10.029 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 13:31:10.215 +04:00 [WRN] Failed login attempt for user "44e9958d-1537-4614-bdbb-f4fe57509fd9" - invalid password
2025-06-14 13:31:10.266 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='3653ad96-89e7-4a63-aac9-9e7159045da5', @p1='FailedLogin' (Nullable = false), @p2='"Failed login attempt from ::1. Attempts: 1"' (DbType = Object), @p3='2025-06-14T09:31:10.2185015Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-14T09:31:10.2186244Z' (DbType = DateTime), @p12='2025-06-14T09:31:10.2356178Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='44e9958d-1537-4614-bdbb-f4fe57509fd9' (Nullable = false), @p37='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p16='2025-06-04T16:36:29.4616840Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='1', @p21='Senior' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-12T15:56:17.2030530Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Lawyer' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$IwkLcL8eN5wkkUnfgHtMZeW8Amjcv2Ot9F4VxIZ3WFp9cAD.cqVjy' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='Lawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-14T09:31:10.2356164Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='831' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-14 13:31:10.281 +04:00 [INF] User updated successfully: "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-14 13:31:10.610 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 109
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-14 13:31:10.622 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-14 13:31:10.633 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 634.1441ms
2025-06-14 13:31:10.635 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 13:31:10.636 +04:00 [INF] Request POST /api/auth/login completed in 644ms with status 401 (Correlation ID: 8759b81c-d052-4e05-8c24-cb2adbeab554)
2025-06-14 13:31:10.638 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 401 null application/json; charset=utf-8 651.7067ms
2025-06-14 13:33:46.815 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/register - null null
2025-06-14 13:33:46.828 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID aae574b6-a57e-46d5-93c4-b065d7c3de8e
2025-06-14 13:33:46.838 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:33:46.841 +04:00 [INF] Request OPTIONS /api/auth/register completed in 2ms with status 204 (Correlation ID: aae574b6-a57e-46d5-93c4-b065d7c3de8e)
2025-06-14 13:33:46.845 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/register - 204 null null 30.1316ms
2025-06-14 13:33:46.847 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/register - application/json 261
2025-06-14 13:33:46.853 +04:00 [INF] Request POST /api/auth/register started with correlation ID 63b7860a-4169-4439-ae13-bb3a98994f32
2025-06-14 13:33:46.855 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:33:46.856 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-14 13:33:46.860 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 13:33:46.869 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-14 13:33:46.873 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-14 13:33:46.895 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 13:33:46.901 +04:00 [WRN] Attempt to register with <NAME_EMAIL>
2025-06-14 13:33:47.053 +04:00 [WRN] Registration failed - user already exists <NAME_EMAIL>
2025-06-14 13:33:47.056 +04:00 [INF] Executing ConflictObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-14 13:33:47.059 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 196.4159ms
2025-06-14 13:33:47.061 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-14 13:33:47.063 +04:00 [INF] Request POST /api/auth/register completed in 207ms with status 409 (Correlation ID: 63b7860a-4169-4439-ae13-bb3a98994f32)
2025-06-14 13:33:47.066 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/register - 409 null application/json; charset=utf-8 218.9713ms
2025-06-14 13:34:35.559 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/register - null null
2025-06-14 13:34:35.580 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID c03c9020-9c29-44ef-b9f4-b853feb902f7
2025-06-14 13:34:35.586 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:34:35.588 +04:00 [INF] Request OPTIONS /api/auth/register completed in 2ms with status 204 (Correlation ID: c03c9020-9c29-44ef-b9f4-b853feb902f7)
2025-06-14 13:34:35.594 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/register - 204 null null 34.7389ms
2025-06-14 13:34:35.597 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/register - application/json 262
2025-06-14 13:34:35.608 +04:00 [INF] Request POST /api/auth/register started with correlation ID ab3d6007-398b-48a3-9b5b-4c0988094e15
2025-06-14 13:34:35.612 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:34:35.614 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-14 13:34:35.617 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 13:34:35.622 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-14 13:34:35.624 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-14 13:34:35.629 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 13:34:35.844 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='cdce4235-a597-4037-b34a-b22e3901d9f9', @p1='2025-06-14T09:34:35.8329100Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Lawyer' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$bxq4grQv1DxQVK9Vb6RWku7EixhvOgH0mz0doV7ALQqotj/0sqm56' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='Lawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-14T09:34:35.8300195Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='1da3cb9b-8f8f-4c01-bf5e-b1db50fe009c', @p26='Created' (Nullable = false), @p27='"User created with role Lawyer"' (DbType = Object), @p28='2025-06-14T09:34:35.8329104Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='cdce4235-a597-4037-b34a-b22e3901d9f9', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-14T09:34:35.6570559Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='2b20eb0c-c75e-4e65-89ee-36aedb5616ff', @p42='ProfileUpdated' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-06-14T09:34:35.8329106Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='cdce4235-a597-4037-b34a-b22e3901d9f9', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-14T09:34:35.8297117Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='4195327f-2c64-4229-9717-eff272070fd1', @p58='PasswordChanged' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-14T09:34:35.8329105Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='cdce4235-a597-4037-b34a-b22e3901d9f9', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-14T09:34:35.8279164Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='bbe8a8ac-31a8-4a88-bf70-7ab98df74dec', @p74='PreferencesUpdated' (Nullable = false), @p75='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p76='2025-06-14T09:34:35.8329107Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='cdce4235-a597-4037-b34a-b22e3901d9f9', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-14T09:34:35.8300224Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-14 13:34:35.850 +04:00 [INF] User added successfully: "cdce4235-a597-4037-b34a-b22e3901d9f9"
2025-06-14 13:34:35.852 +04:00 [INF] User "cdce4235-a597-4037-b34a-b22e3901d9f9" registered successfully <NAME_EMAIL> and role "Lawyer"
2025-06-14 13:34:35.854 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "Lawyer"
2025-06-14 13:34:35.857 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-14 13:34:35.871 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 250.2702ms
2025-06-14 13:34:35.881 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-14 13:34:35.891 +04:00 [INF] Request POST /api/auth/register completed in 279ms with status 201 (Correlation ID: ab3d6007-398b-48a3-9b5b-4c0988094e15)
2025-06-14 13:34:35.894 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/register - 201 null application/json; charset=utf-8 297.2192ms
2025-06-14 13:34:52.958 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-14 13:34:52.963 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 8e59b348-212f-4898-9cd5-198778c97a1b
2025-06-14 13:34:52.965 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:34:52.966 +04:00 [INF] Request OPTIONS /api/auth/login completed in 1ms with status 204 (Correlation ID: 8e59b348-212f-4898-9cd5-198778c97a1b)
2025-06-14 13:34:52.969 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 11.0665ms
2025-06-14 13:34:52.971 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 53
2025-06-14 13:34:52.977 +04:00 [INF] Request POST /api/auth/login started with correlation ID 7ad22f4f-d522-4947-aff3-7b5b1f261733
2025-06-14 13:34:52.979 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:34:52.980 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 13:34:52.981 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 13:34:52.983 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-14 13:34:52.985 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-14 13:34:52.989 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 13:34:53.111 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='983fd225-ffe3-48f9-89a4-ac0fa4d556cc', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-14T09:34:53.1070484Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='cdce4235-a597-4037-b34a-b22e3901d9f9', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-14T09:34:53.1070503Z' (DbType = DateTime), @p12='2025-06-14T09:34:53.1074495Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='cdce4235-a597-4037-b34a-b22e3901d9f9' (Nullable = false), @p37='cdce4235-a597-4037-b34a-b22e3901d9f9', @p16='2025-06-14T09:34:35.8329100Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-14T09:34:53.1069371Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Lawyer' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$bxq4grQv1DxQVK9Vb6RWku7EixhvOgH0mz0doV7ALQqotj/0sqm56' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='Lawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-14T09:34:53.1074486Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='838' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-14 13:34:53.116 +04:00 [INF] User updated successfully: "cdce4235-a597-4037-b34a-b22e3901d9f9"
2025-06-14 13:34:53.123 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='46d3b696-ba8b-4c51-9c97-d18666d1c9b9', @p1='2025-06-14T09:34:53.1197037Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T09:34:53.1183760Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='qqyGtWEBYdk5ZcSXjgBt/x9lHcK/snsdZZ1oYy/uqSdJHJG7IUbnwVDMs/pfq9NzIO5rRlWFiCk2f+l1jh7p9A==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='cdce4235-a597-4037-b34a-b22e3901d9f9'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 13:34:53.126 +04:00 [INF] Refresh token added successfully: "46d3b696-ba8b-4c51-9c97-d18666d1c9b9"
2025-06-14 13:34:53.127 +04:00 [INF] User "cdce4235-a597-4037-b34a-b22e3901d9f9" logged in successfully
2025-06-14 13:34:53.128 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-14 13:34:53.130 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 13:34:53.133 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 149.3948ms
2025-06-14 13:34:53.135 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 13:34:53.137 +04:00 [INF] Request POST /api/auth/login completed in 157ms with status 200 (Correlation ID: 7ad22f4f-d522-4947-aff3-7b5b1f261733)
2025-06-14 13:34:53.138 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 200 null application/json; charset=utf-8 167.4537ms
2025-06-14 14:00:50.915 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 14:00:50.991 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 14:00:51.002 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 14:00:51.418 +04:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 14:00:51.431 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 14:00:51.471 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 14:00:51.781 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 14:00:51.786 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 14:00:51.869 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 14:00:51.872 +04:00 [INF] Hosting environment: Development
2025-06-14 14:00:51.875 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 14:00:52.448 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 14:00:52.614 +04:00 [INF] Request GET / started with correlation ID b2b3e71b-25b0-4e5c-b68b-1afa3feb7069
2025-06-14 14:00:54.729 +04:00 [INF] Request GET / completed in 2107ms with status 404 (Correlation ID: b2b3e71b-25b0-4e5c-b68b-1afa3feb7069)
2025-06-14 14:00:54.740 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 2302.8349ms
2025-06-14 14:00:54.768 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 15:28:08.629 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 15:28:08.668 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 15:28:08.675 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 15:28:08.990 +04:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 15:28:09.002 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 15:28:09.030 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:28:09.232 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 15:28:09.236 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 15:28:09.331 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:28:09.334 +04:00 [INF] Hosting environment: Development
2025-06-14 15:28:09.337 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 15:28:09.670 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 15:28:09.836 +04:00 [INF] Request GET / started with correlation ID 3d9685b8-f0fa-45c1-a377-8505c44b2afa
2025-06-14 15:28:09.906 +04:00 [INF] Request GET / completed in 66ms with status 404 (Correlation ID: 3d9685b8-f0fa-45c1-a377-8505c44b2afa)
2025-06-14 15:28:09.913 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 247.0737ms
2025-06-14 15:28:09.925 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-14 15:28:32.521 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-14 15:28:32.605 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID ebf7b76c-452b-452f-a6ac-54cf3068d2b4
2025-06-14 15:28:32.614 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:32.618 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 6ms with status 204 (Correlation ID: ebf7b76c-452b-452f-a6ac-54cf3068d2b4)
2025-06-14 15:28:32.622 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 101.4189ms
2025-06-14 15:28:32.625 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-14 15:28:32.631 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 7656005c-9a82-44c5-bbfb-34904cdf0bda
2025-06-14 15:28:32.633 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:32.733 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:32 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 15:28:32.753 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:32 AM'.
2025-06-14 15:28:32.755 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:32 AM'.
2025-06-14 15:28:32.760 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 15:28:32.765 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 15:28:32.768 +04:00 [INF] Request POST /api/auth/logout completed in 135ms with status 401 (Correlation ID: 7656005c-9a82-44c5-bbfb-34904cdf0bda)
2025-06-14 15:28:32.772 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 147.1968ms
2025-06-14 15:28:32.776 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-14 15:28:32.782 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 218409b6-53b9-40b6-a16b-b3b03d6f80ac
2025-06-14 15:28:32.784 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:32.785 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: 218409b6-53b9-40b6-a16b-b3b03d6f80ac)
2025-06-14 15:28:32.788 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 11.7894ms
2025-06-14 15:28:32.790 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-14 15:28:32.797 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 41672c07-6e34-4793-9d40-ee84d4094d8f
2025-06-14 15:28:32.800 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:32.803 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 15:28:32.818 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 15:28:32.846 +04:00 [INF] Token refresh attempt
2025-06-14 15:28:32.889 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-14 15:28:33.187 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__token_0='qqyGtWEBYdk5ZcSXjgBt/x9lHcK/snsdZZ1oYy/uqSdJHJG7IUbnwVDMs/pfq9NzIO5rRlWFiCk2f+l1jh7p9A=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-14 15:28:33.196 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-14 15:28:33.331 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-14 15:28:33.351 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-14 15:28:33.373 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 549.255ms
2025-06-14 15:28:33.376 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-14 15:28:33.377 +04:00 [INF] Request POST /api/auth/refresh completed in 576ms with status 401 (Correlation ID: 41672c07-6e34-4793-9d40-ee84d4094d8f)
2025-06-14 15:28:33.382 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 401 null application/json; charset=utf-8 592.2452ms
2025-06-14 15:28:33.384 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-14 15:28:33.402 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 7ad1728d-78b2-4687-99e8-390f96a8a75b
2025-06-14 15:28:33.407 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:33.413 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:33 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 15:28:33.423 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:33 AM'.
2025-06-14 15:28:33.426 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:33 AM'.
2025-06-14 15:28:33.428 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 15:28:33.430 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 15:28:33.434 +04:00 [INF] Request POST /api/auth/logout completed in 27ms with status 401 (Correlation ID: 7ad1728d-78b2-4687-99e8-390f96a8a75b)
2025-06-14 15:28:33.440 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 55.5695ms
2025-06-14 15:30:03.362 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/register - null null
2025-06-14 15:30:03.374 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 30ddab36-d404-4396-8806-10ccc1a5c507
2025-06-14 15:30:03.375 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:03.377 +04:00 [INF] Request OPTIONS /api/auth/register completed in 1ms with status 204 (Correlation ID: 30ddab36-d404-4396-8806-10ccc1a5c507)
2025-06-14 15:30:03.380 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/register - 204 null null 17.4886ms
2025-06-14 15:30:03.383 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/register - application/json 261
2025-06-14 15:30:03.390 +04:00 [INF] Request POST /api/auth/register started with correlation ID de0dcdf2-cba7-4e0e-9f13-1cca40314c9e
2025-06-14 15:30:03.393 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:03.395 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-14 15:30:03.401 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 15:30:03.415 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-14 15:30:03.467 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-14 15:30:03.491 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 15:30:03.517 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 15:30:04.040 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p1='2025-06-14T11:30:03.9807707Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Kevin' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Lawyer' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$cpH6Hikt4rom0NJXjjvc0.xcq/CZQPsXKpDSrriQO9vFE/c6CYWci' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='SeniorLawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-14T11:30:03.8716168Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='115a3892-e1b2-435c-974d-ac7d5a7ccb59', @p26='PasswordChanged' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-06-14T11:30:03.9808266Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-14T11:30:03.8696537Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='454b83f6-8f6c-461f-a66c-83c6c2332d6a', @p42='ProfileUpdated' (Nullable = false), @p43='null' (DbType = Object), @p44='2025-06-14T11:30:03.9808272Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-14T11:30:03.8713402Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='6422f8d8-6740-4559-98f1-4829e252503f', @p58='PreferencesUpdated' (Nullable = false), @p59='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p60='2025-06-14T11:30:03.9808275Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-14T11:30:03.8716193Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='989ed47f-c3ef-4194-8dd6-bcccc81ce400', @p74='Created' (Nullable = false), @p75='"User created with role SeniorLawyer"' (DbType = Object), @p76='2025-06-14T11:30:03.9808265Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-14T11:30:03.5341489Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-14 15:30:04.076 +04:00 [INF] User added successfully: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 15:30:04.078 +04:00 [INF] User "ebafe5e7-5450-4d33-9568-eb2a874ea6c6" registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-14 15:30:04.081 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "SeniorLawyer"
2025-06-14 15:30:04.084 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-14 15:30:04.105 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 701.168ms
2025-06-14 15:30:04.107 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-14 15:30:04.108 +04:00 [INF] Request POST /api/auth/register completed in 715ms with status 201 (Correlation ID: de0dcdf2-cba7-4e0e-9f13-1cca40314c9e)
2025-06-14 15:30:04.111 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/register - 201 null application/json; charset=utf-8 728.0743ms
2025-06-14 15:30:10.352 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-14 15:30:10.360 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 2ebdcb99-8022-46e4-864a-944e8b3085e0
2025-06-14 15:30:10.366 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:10.369 +04:00 [INF] Request OPTIONS /api/auth/login completed in 3ms with status 204 (Correlation ID: 2ebdcb99-8022-46e4-864a-944e8b3085e0)
2025-06-14 15:30:10.376 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 23.9412ms
2025-06-14 15:30:10.377 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 52
2025-06-14 15:30:10.384 +04:00 [INF] Request POST /api/auth/login started with correlation ID 3df1561c-4fbf-41bf-873e-ea7ccbc0e1f6
2025-06-14 15:30:10.385 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:10.386 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 15:30:10.389 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 15:30:10.395 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-14 15:30:10.400 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-14 15:30:10.407 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 15:30:10.409 +04:00 [WRN] Login attempt with non-<NAME_EMAIL>
2025-06-14 15:30:10.500 +04:00 [WRN] Login failed <NAME_EMAIL>
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid email or password
   at LexAI.Identity.Application.Commands.LoginCommandHandler.Handle(LoginCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 83
   at LexAI.Identity.API.Controllers.AuthController.Login(LoginDto loginDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 63
2025-06-14 15:30:10.504 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-14 15:30:10.506 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 115.3557ms
2025-06-14 15:30:10.510 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 15:30:10.512 +04:00 [INF] Request POST /api/auth/login completed in 127ms with status 401 (Correlation ID: 3df1561c-4fbf-41bf-873e-ea7ccbc0e1f6)
2025-06-14 15:30:10.515 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 401 null application/json; charset=utf-8 137.6456ms
2025-06-14 15:30:20.938 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-14 15:30:20.950 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID cb062dbd-cc28-4c2f-b499-bb87876beab2
2025-06-14 15:30:20.952 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:20.954 +04:00 [INF] Request OPTIONS /api/auth/login completed in 2ms with status 204 (Correlation ID: cb062dbd-cc28-4c2f-b499-bb87876beab2)
2025-06-14 15:30:20.966 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 27.9976ms
2025-06-14 15:30:20.971 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 52
2025-06-14 15:30:20.993 +04:00 [INF] Request POST /api/auth/login started with correlation ID db9711e8-07db-4abd-b5ca-31463e3aa718
2025-06-14 15:30:20.996 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:20.998 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 15:30:21.001 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-14 15:30:21.005 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-14 15:30:21.007 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-14 15:30:21.011 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-14 15:30:21.208 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='2eddaf95-66b1-4a81-bd8f-691a9dc2cc5d', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-14T11:30:21.1958174Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-14T11:30:21.1958190Z' (DbType = DateTime), @p12='2025-06-14T11:30:21.1991088Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6' (Nullable = false), @p37='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p16='2025-06-14T11:30:03.9807700Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-14T11:30:21.1956891Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Lawyer' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$cpH6Hikt4rom0NJXjjvc0.xcq/CZQPsXKpDSrriQO9vFE/c6CYWci' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='SeniorLawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-14T11:30:21.1991071Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='793' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-14 15:30:21.214 +04:00 [INF] User updated successfully: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 15:30:21.247 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='c332a55e-5586-498f-a271-b25534445f6f', @p1='2025-06-14T11:30:21.2440454Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-21T11:30:21.2247119Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='LKcKfClaH5+i162EjK+AlrY7eTPniEXtwB4xp9bGommtm4ho+AoK7xGKuhH10RnLhvNX9rdiWUdCfDxneVfqsg==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-14 15:30:21.261 +04:00 [INF] Refresh token added successfully: "c332a55e-5586-498f-a271-b25534445f6f"
2025-06-14 15:30:21.262 +04:00 [INF] User "ebafe5e7-5450-4d33-9568-eb2a874ea6c6" logged in successfully
2025-06-14 15:30:21.264 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-14 15:30:21.266 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-14 15:30:21.269 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 265.0007ms
2025-06-14 15:30:21.271 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-14 15:30:21.273 +04:00 [INF] Request POST /api/auth/login completed in 276ms with status 200 (Correlation ID: db9711e8-07db-4abd-b5ca-31463e3aa718)
2025-06-14 15:30:21.275 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 200 null application/json; charset=utf-8 304.7878ms
