2025-06-14 15:53:26.948 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 15:53:26.979 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-14 15:53:26.984 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 15:53:27.432 +04:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-14 15:53:27.443 +04:00 [INF] LexAI Identity Service started successfully
2025-06-14 15:53:27.472 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:53:27.711 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-14 15:53:27.713 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-14 15:53:27.751 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:53:27.754 +04:00 [INF] Hosting environment: Development
2025-06-14 15:53:27.756 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-14 15:53:28.263 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-14 15:53:28.503 +04:00 [INF] Request GET / started with correlation ID f5a2b6bd-f85f-4b45-8388-dd772f5f245d
2025-06-14 15:53:28.594 +04:00 [INF] Request GET / completed in 87ms with status 404 (Correlation ID: f5a2b6bd-f85f-4b45-8388-dd772f5f245d)
2025-06-14 15:53:28.608 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 370.6465ms
2025-06-14 15:53:28.628 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
