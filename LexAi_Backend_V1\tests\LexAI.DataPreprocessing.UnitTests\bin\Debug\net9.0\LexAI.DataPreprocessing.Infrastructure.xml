<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.DataPreprocessing.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent">
            <summary>
            Document classification agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IClassificationService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent})">
            <summary>
            Initializes a new instance of the ClassificationAgent
            </summary>
            <param name="classificationService">Classification service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.ClassifyDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Classifies a document into legal domains
            </summary>
            <param name="document">Document to classify</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Classification result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.GetDomainConfidenceAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets classification confidence for a document
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Domain confidence scores</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.ExtractKeywordsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from document text
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ClassificationAgent.ExtractNamedEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts named entities from document text
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Named entities</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent">
            <summary>
            Chunking agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.SupportedStrategies">
            <summary>
            Supported chunking strategies
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IChunkingService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent})">
            <summary>
            Initializes a new instance of the ChunkingAgent
            </summary>
            <param name="chunkingService">Chunking service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.ChunkDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration,System.Threading.CancellationToken)">
            <summary>
            Chunks a document into smaller pieces
            </summary>
            <param name="document">Document to chunk</param>
            <param name="configuration">Chunking configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chunking result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.EstimateChunkCount(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Estimates the number of chunks for a document
            </summary>
            <param name="text">Document text</param>
            <param name="configuration">Chunking configuration</param>
            <returns>Estimated chunk count</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ChunkingAgent.ValidateConfiguration(LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Validates chunking configuration
            </summary>
            <param name="configuration">Configuration to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent">
            <summary>
            Text extraction agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.SupportedMimeTypes">
            <summary>
            Supported MIME types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.ITextExtractionService,LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent})">
            <summary>
            Initializes a new instance of the ExtractionAgent
            </summary>
            <param name="textExtractionService">Text extraction service</param>
            <param name="fileStorageService">File storage service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.ExtractTextAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Extracts text from a document
            </summary>
            <param name="document">Document to extract text from</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extraction result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.CanHandle(System.String)">
            <summary>
            Checks if the agent can handle the document
            </summary>
            <param name="mimeType">Document MIME type</param>
            <returns>True if agent can handle the document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.ExtractionAgent.GetExtractionConfidenceAsync(LexAI.DataPreprocessing.Domain.Entities.Document)">
            <summary>
            Gets extraction confidence for a document
            </summary>
            <param name="document">Document to evaluate</param>
            <returns>Confidence score (0-1)</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent">
            <summary>
            Orchestration agent that coordinates the entire document processing pipeline
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent,LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent,LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent,LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent,LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent,LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent,LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository,LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent})">
            <summary>
            Initializes a new instance of the OrchestrationAgent
            </summary>
            <param name="extractionAgent">Extraction agent</param>
            <param name="classificationAgent">Classification agent</param>
            <param name="chunkingAgent">Chunking agent</param>
            <param name="vectorizationAgent">Vectorization agent</param>
            <param name="routingAgent">Routing agent</param>
            <param name="qualityAssuranceAgent">Quality assurance agent</param>
            <param name="documentRepository">Document repository</param>
            <param name="vectorStorageService">Vector storage service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.ProcessDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto,System.Threading.CancellationToken)">
            <summary>
            Orchestrates the complete document processing pipeline
            </summary>
            <param name="document">Document to process</param>
            <param name="configuration">Processing configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.RetryProcessingAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.String,System.Threading.CancellationToken)">
            <summary>
            Retries failed processing steps
            </summary>
            <param name="document">Document to retry</param>
            <param name="fromStep">Step to retry from</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Retry result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.GetProcessingStatusAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets processing status for a document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing status</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.OrchestrationAgent.CancelProcessingAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Cancels document processing
            </summary>
            <param name="documentId">Document ID</param>
            <param name="reason">Cancellation reason</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Cancellation result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent">
            <summary>
            Quality assurance agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent})">
            <summary>
            Initializes a new instance of the QualityAssuranceAgent
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.AssessDocumentQualityAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Validates document quality
            </summary>
            <param name="document">Document to validate</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Quality assessment</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.AssessChunkQualityAsync(LexAI.DataPreprocessing.Domain.Entities.DocumentChunk,System.Threading.CancellationToken)">
            <summary>
            Validates chunk quality
            </summary>
            <param name="chunk">Chunk to validate</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Quality assessment</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.ValidatePipelineResultsAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Validates processing pipeline results
            </summary>
            <param name="document">Processed document</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Pipeline validation result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.QualityAssuranceAgent.SuggestImprovementsAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Suggests improvements for document processing
            </summary>
            <param name="document">Document to analyze</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Improvement suggestions</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent">
            <summary>
            Routing agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.SupportedDatabases">
            <summary>
            Supported vector databases
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent})">
            <summary>
            Initializes a new instance of the RoutingAgent
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.RouteChunksAsync(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.DocumentChunk},System.Threading.CancellationToken)">
            <summary>
            Routes chunks to appropriate vector databases
            </summary>
            <param name="chunks">Chunks to route</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Routing result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.GetRecommendedDatabase(LexAI.Shared.Application.DTOs.LegalDomain)">
            <summary>
            Determines the best vector database for a domain
            </summary>
            <param name="domain">Legal domain</param>
            <returns>Recommended vector database</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.RoutingAgent.GetCollectionName(LexAI.Shared.Application.DTOs.LegalDomain,LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType)">
            <summary>
            Gets the collection name for a domain
            </summary>
            <param name="domain">Legal domain</param>
            <param name="databaseType">Database type</param>
            <returns>Collection name</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent">
            <summary>
            Vectorization agent implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.SupportedModels">
            <summary>
            Supported embedding models
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IEmbeddingService,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent})">
            <summary>
            Initializes a new instance of the VectorizationAgent
            </summary>
            <param name="embeddingService">Embedding service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.VectorizeChunksAsync(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.DocumentChunk},LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Vectorizes document chunks
            </summary>
            <param name="chunks">Chunks to vectorize</param>
            <param name="modelType">Embedding model to use</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Vectorization result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.GenerateEmbeddingAsync(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Generates embedding for a single text
            </summary>
            <param name="text">Text to embed</param>
            <param name="modelType">Embedding model</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vector</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.GetEmbeddingDimension(LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Gets the dimension of the embedding model
            </summary>
            <param name="modelType">Embedding model</param>
            <returns>Vector dimension</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Agents.VectorizationAgent.EstimateVectorizationCost(System.Int32,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Estimates the cost of vectorization
            </summary>
            <param name="tokenCount">Number of tokens</param>
            <param name="modelType">Embedding model</param>
            <returns>Estimated cost</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext">
            <summary>
            Database context for data preprocessing service
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.Documents">
            <summary>
            Documents
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.DocumentChunks">
            <summary>
            Document chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.ProcessingSteps">
            <summary>
            Processing steps
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.ProcessingErrors">
            <summary>
            Processing errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.NamedEntities">
            <summary>
            Named entities
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext})">
            <summary>
            Initializes a new instance of the DataPreprocessingDbContext
            </summary>
            <param name="options">Database context options</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the model
            </summary>
            <param name="modelBuilder">Model builder</param>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Data.Migrations.InitialMigration">
            <inheritdoc />
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Data.Migrations.InitialMigration.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Data.Migrations.InitialMigration.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Data.Migrations.InitialMigration.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository">
            <summary>
            Document repository implementation
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.#ctor(LexAI.DataPreprocessing.Infrastructure.Data.DataPreprocessingDbContext,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository},Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Initializes a new instance of the DocumentRepository
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger</param>
            <param name="configuration">Configuration for connection string</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a document by ID
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByFileHashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a document by file hash
            </summary>
            <param name="fileHash">File hash</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByStatusAsync(LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets documents by status
            </summary>
            <param name="status">Document status</param>
            <param name="limit">Maximum number of documents</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetByUserAsync(System.Guid,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets documents by user
            </summary>
            <param name="userId">User ID</param>
            <param name="limit">Maximum number of documents</param>
            <param name="offset">Offset for pagination</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.AddAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Adds a new document
            </summary>
            <param name="document">Document to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Updates a document
            </summary>
            <param name="document">Document to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.UpdateAsyncWithNewContext(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Updates a document using a new context (for background operations)
            </summary>
            <param name="document">Document to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a document
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Repositories.DocumentRepository.GetProcessingStatisticsAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets processing statistics
            </summary>
            <param name="fromDate">From date</param>
            <param name="toDate">To date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing statistics</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.ChunkingService">
            <summary>
            Chunking service implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Services.ChunkingService.SupportedStrategies">
            <summary>
            Supported chunking strategies
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ChunkingService.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.ChunkingService})">
            <summary>
            Initializes a new instance of the ChunkingService
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ChunkingService.ChunkTextAsync(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration,System.Threading.CancellationToken)">
            <summary>
            Chunks text into smaller pieces
            </summary>
            <param name="text">Text to chunk</param>
            <param name="configuration">Chunking configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Text chunks</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ChunkingService.EstimateChunkCount(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Estimates the number of chunks
            </summary>
            <param name="text">Text to analyze</param>
            <param name="configuration">Chunking configuration</param>
            <returns>Estimated chunk count</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.ClassificationService">
            <summary>
            Classification service implementation
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ClassificationService.#ctor(System.Net.Http.HttpClient,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.ClassificationService})">
            <summary>
            Initializes a new instance of the ClassificationService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ClassificationService.ClassifyTextAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Classifies text into legal domains
            </summary>
            <param name="text">Text to classify</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Classification result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ClassificationService.ExtractKeywordsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from text
            </summary>
            <param name="text">Text to analyze</param>
            <param name="maxKeywords">Maximum keywords to extract</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.ClassificationService.ExtractNamedEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts named entities from text
            </summary>
            <param name="text">Text to analyze</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Named entities</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService">
            <summary>
            OpenAI embedding service implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService.SupportedModels">
            <summary>
            Supported embedding models
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService.#ctor(System.Net.Http.HttpClient,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService})">
            <summary>
            Initializes a new instance of the EmbeddingService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService.GenerateEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Generates embeddings for texts
            </summary>
            <param name="texts">Texts to embed</param>
            <param name="modelType">Embedding model</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embeddings</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService.GetEmbeddingDimension(LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Gets the dimension of an embedding model
            </summary>
            <param name="modelType">Embedding model</param>
            <returns>Vector dimension</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.EmbeddingService.EstimateCost(System.Int32,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Estimates the cost of generating embeddings
            </summary>
            <param name="tokenCount">Number of tokens</param>
            <param name="modelType">Embedding model</param>
            <returns>Estimated cost</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService">
            <summary>
            File storage service implementation
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService})">
            <summary>
            Initializes a new instance of the FileStorageService
            </summary>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService.StoreFileAsync(System.String,System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            Stores a file
            </summary>
            <param name="fileName">File name</param>
            <param name="fileContent">File content</param>
            <param name="mimeType">MIME type</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Storage path</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService.RetrieveFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves a file
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>File content</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService.DeleteFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a file
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService.FileExistsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Checks if a file exists
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if file exists</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.FileStorageService.GetFileMetadataAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets file metadata
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>File metadata</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.LLMClassificationService">
            <summary>
            Service de classification utilisant le service LLM unifié
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMClassificationService.ClassifyTextAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Classifie un texte en domaines juridiques
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMClassificationService.ExtractKeywordsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extrait les mots-clés d'un texte
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMClassificationService.ExtractKeywordsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Extrait les mots-clés d'un texte avec limite
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMClassificationService.ExtractNamedEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extrait les entités nommées d'un texte
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.LLMQualityAssuranceService">
            <summary>
            Service d'assurance qualité utilisant le service LLM unifié
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMQualityAssuranceService.AssessDocumentQualityAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Évalue la qualité d'un document avec l'IA
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMQualityAssuranceService.AssessChunkQualityAsync(LexAI.DataPreprocessing.Domain.Entities.DocumentChunk,System.Threading.CancellationToken)">
            <summary>
            Évalue la qualité d'un chunk avec l'IA
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LLMQualityAssuranceService.ValidatePipelineResultsAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Valide les résultats du pipeline avec l'IA
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService">
            <summary>
            Service d'embedding local utilisant Sentence Transformers
            Alternative gratuite à OpenAI pour le développement
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.SupportedModels">
            <summary>
            Modèles supportés par le service local
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.#ctor(System.Net.Http.IHttpClientFactory,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService})">
            <summary>
            Initialise le service d'embedding local
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.GenerateEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Génère des embeddings pour une liste de textes
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.GetEmbeddingDimension(LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Obtient la dimension des embeddings pour un modèle
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.EstimateCost(System.Int32,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Estime le coût (gratuit pour le service local)
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.CheckServiceHealthAsync(System.Threading.CancellationToken)">
            <summary>
            Vérifie la santé du service local
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.GetModelName(LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Obtient le nom du modèle pour le service local
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.EstimateTokenCount(System.String)">
            <summary>
            Estime le nombre de tokens
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.LocalEmbeddingResponse">
            <summary>
            Réponse du service local d'embedding
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.EmbeddingData">
            <summary>
            Données d'embedding
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.LocalEmbeddingService.Usage">
            <summary>
            Informations d'utilisation
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.TextExtractionService">
            <summary>
            Text extraction service implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Services.TextExtractionService.SupportedMimeTypes">
            <summary>
            Supported MIME types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.TextExtractionService.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.TextExtractionService})">
            <summary>
            Initializes a new instance of the TextExtractionService
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.TextExtractionService.ExtractTextAsync(System.Byte[],System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts text from a file
            </summary>
            <param name="fileContent">File content</param>
            <param name="mimeType">MIME type</param>
            <param name="fileName">File name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted text and metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.TextExtractionService.CanHandle(System.String)">
            <summary>
            Checks if the service can handle the MIME type
            </summary>
            <param name="mimeType">MIME type</param>
            <returns>True if supported</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService">
            <summary>
            Vector storage service implementation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService.SupportedDatabases">
            <summary>
            Supported database types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService},MongoDB.Driver.IMongoClient)">
            <summary>
            Initializes a new instance of the VectorStorageService
            </summary>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
            <param name="mongoClient">MongoDB client</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService.StoreVectorsAsync(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.DocumentChunk},LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Threading.CancellationToken)">
            <summary>
            Stores vectors in the database
            </summary>
            <param name="chunks">Chunks with vectors to store</param>
            <param name="databaseType">Target database type</param>
            <param name="collection">Collection name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Storage result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService.SearchSimilarAsync(System.Single[],LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Searches for similar vectors
            </summary>
            <param name="queryVector">Query vector</param>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="limit">Maximum results</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar vectors</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService.DeleteVectorsAsync(System.Collections.Generic.IEnumerable{System.String},LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes vectors from the database
            </summary>
            <param name="vectorIds">Vector IDs to delete</param>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Infrastructure.Services.VectorStorageService.EnsureCollectionExistsAsync(LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Creates a collection if it doesn't exist
            </summary>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="dimension">Vector dimension</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
    </members>
</doc>
