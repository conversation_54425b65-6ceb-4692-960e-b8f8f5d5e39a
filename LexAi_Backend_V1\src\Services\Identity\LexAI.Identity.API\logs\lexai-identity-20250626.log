2025-06-26 22:08:40.500 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 22:08:40.570 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 22:08:40.592 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 22:08:42.379 +04:00 [INF] Executed DbCommand (283ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-26 22:08:42.404 +04:00 [INF] LexAI Identity Service started successfully
2025-06-26 22:08:42.482 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:08:42.985 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-26 22:08:42.990 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-26 22:08:43.578 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:08:43.582 +04:00 [INF] Hosting environment: Development
2025-06-26 22:08:43.584 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-26 22:08:43.654 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-26 22:08:43.932 +04:00 [INF] Request GET / started with correlation ID c73faa67-4ab7-4e44-82a9-45139d839388
2025-06-26 22:08:45.716 +04:00 [INF] Request GET / completed in 1775ms with status 404 (Correlation ID: c73faa67-4ab7-4e44-82a9-45139d839388)
2025-06-26 22:08:45.725 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 2098.224ms
2025-06-26 22:08:45.733 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-26 22:10:19.170 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-26 22:10:19.188 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 21931870-365d-429c-9fda-e9133e4dae1e
2025-06-26 22:10:19.192 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:19.195 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 4ms with status 204 (Correlation ID: 21931870-365d-429c-9fda-e9133e4dae1e)
2025-06-26 22:10:19.199 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 29.6148ms
2025-06-26 22:10:19.202 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-26 22:10:19.210 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 94704fd7-e895-45c5-a81e-94f34c8ca249
2025-06-26 22:10:19.213 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:19.217 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-26 22:10:19.243 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-26 22:10:19.298 +04:00 [INF] Token refresh attempt
2025-06-26 22:10:19.358 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-26 22:10:20.002 +04:00 [INF] Executed DbCommand (26ms) [Parameters=[@__token_0='i+Kb4FEFWz7jW5S0ILzS+zk2oDOgqrQPbHuB8gnLyGu+luGVJrlrDxnRtSPf9AwFxaFc69m8i6CQ/PcxMqK0Pw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-26 22:10:20.176 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-26 22:10:20.201 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-26 22:10:20.224 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='i+Kb4FEFWz7jW5S0ILzS+zk2oDOgqrQPbHuB8gnLyGu+luGVJrlrDxnRtSPf9AwFxaFc69m8i6CQ/PcxMqK0Pw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-26 22:10:20.357 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@p16='15731585-281f-498c-84ab-c83d8d13ffc0', @p0='2025-06-23T17:12:22.3204380Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-30T17:12:22.2925220Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-26T18:10:20.2278251Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='i+Kb4FEFWz7jW5S0ILzS+zk2oDOgqrQPbHuB8gnLyGu+luGVJrlrDxnRtSPf9AwFxaFc69m8i6CQ/PcxMqK0Pw==' (Nullable = false), @p12='2025-06-26T18:10:20.2695255Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='829' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-26 22:10:20.374 +04:00 [INF] Refresh token updated successfully: "15731585-281f-498c-84ab-c83d8d13ffc0"
2025-06-26 22:10:20.502 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='48bcad80-51a4-4909-bc3c-eeb733101e08', @p1='2025-06-26T18:10:20.4950591Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-07-03T18:10:20.4853994Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='HIQBJT+/fdRLsP2s5wZHCKapFAoaI8C4drA7GIUtpZH78XCj9FpkCEBCjwnRiel9wHsJt41/rUT5wuusyaAh2Q==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-26 22:10:20.507 +04:00 [INF] Refresh token added successfully: "48bcad80-51a4-4909-bc3c-eeb733101e08"
2025-06-26 22:10:20.510 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-26 22:10:20.515 +04:00 [INF] Token refresh successful
2025-06-26 22:10:20.523 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-26 22:10:20.553 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 1301.9622ms
2025-06-26 22:10:20.555 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-26 22:10:20.557 +04:00 [INF] Request POST /api/auth/refresh completed in 1344ms with status 200 (Correlation ID: 94704fd7-e895-45c5-a81e-94f34c8ca249)
2025-06-26 22:10:20.565 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 1363.6162ms
2025-06-26 22:22:08.499 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 22:22:08.552 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 22:22:08.561 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 22:22:09.559 +04:00 [INF] Executed DbCommand (107ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-26 22:22:09.593 +04:00 [INF] LexAI Identity Service started successfully
2025-06-26 22:22:09.709 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:22:10.160 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-26 22:22:10.162 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-26 22:22:10.209 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:22:10.211 +04:00 [INF] Hosting environment: Development
2025-06-26 22:22:10.214 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-26 22:22:11.972 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-26 22:22:12.444 +04:00 [INF] Request GET / started with correlation ID 0b2818cf-4ee1-4cc8-93ae-987db13ded88
2025-06-26 22:22:12.684 +04:00 [INF] Request GET / completed in 217ms with status 404 (Correlation ID: 0b2818cf-4ee1-4cc8-93ae-987db13ded88)
2025-06-26 22:22:12.702 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 769.7866ms
2025-06-26 22:22:12.721 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-26 22:26:04.586 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 22:26:04.684 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 22:26:04.701 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 22:26:05.658 +04:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-26 22:26:05.683 +04:00 [INF] LexAI Identity Service started successfully
2025-06-26 22:26:05.774 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:26:06.167 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-26 22:26:06.170 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-26 22:26:06.215 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:26:06.218 +04:00 [INF] Hosting environment: Development
2025-06-26 22:26:06.233 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-26 22:26:07.157 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-26 22:26:07.391 +04:00 [INF] Request GET / started with correlation ID 5feb9aad-efd1-49c7-9828-8a5b3b81963b
2025-06-26 22:26:07.623 +04:00 [INF] Request GET / completed in 222ms with status 404 (Correlation ID: 5feb9aad-efd1-49c7-9828-8a5b3b81963b)
2025-06-26 22:26:07.644 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 508.8178ms
2025-06-26 22:26:07.672 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-26 23:28:42.002 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 23:28:42.099 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-26 23:28:42.114 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-26 23:28:44.743 +04:00 [INF] Executed DbCommand (119ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-26 23:28:44.769 +04:00 [INF] LexAI Identity Service started successfully
2025-06-26 23:28:44.836 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 23:28:45.453 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-26 23:28:45.461 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-26 23:28:46.106 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 23:28:46.112 +04:00 [INF] Hosting environment: Development
2025-06-26 23:28:46.116 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-26 23:28:46.199 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-26 23:28:46.424 +04:00 [INF] Request GET / started with correlation ID 9aaa1afa-597f-44b1-baf7-44638add32c1
2025-06-26 23:28:46.602 +04:00 [INF] Request GET / completed in 171ms with status 404 (Correlation ID: 9aaa1afa-597f-44b1-baf7-44638add32c1)
2025-06-26 23:28:46.613 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 424.7565ms
2025-06-26 23:28:46.622 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-26 23:29:11.550 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-26 23:29:11.629 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 9ff3965b-6177-4827-9d82-c1ec07c59a92
2025-06-26 23:29:11.652 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:11.660 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 9ms with status 204 (Correlation ID: 9ff3965b-6177-4827-9d82-c1ec07c59a92)
2025-06-26 23:29:11.677 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 125.3712ms
2025-06-26 23:29:11.694 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-26 23:29:11.722 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 1dc5135a-0b3d-429d-a5cf-79b5ccde0571
2025-06-26 23:29:11.731 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:11.740 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-26 23:29:11.835 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-26 23:29:11.944 +04:00 [INF] Token refresh attempt
2025-06-26 23:29:11.974 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-26 23:29:13.102 +04:00 [INF] Executed DbCommand (61ms) [Parameters=[@__token_0='HIQBJT+/fdRLsP2s5wZHCKapFAoaI8C4drA7GIUtpZH78XCj9FpkCEBCjwnRiel9wHsJt41/rUT5wuusyaAh2Q=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-26 23:29:13.564 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-26 23:29:13.638 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-26 23:29:13.711 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='HIQBJT+/fdRLsP2s5wZHCKapFAoaI8C4drA7GIUtpZH78XCj9FpkCEBCjwnRiel9wHsJt41/rUT5wuusyaAh2Q=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-26 23:29:14.025 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@p16='48bcad80-51a4-4909-bc3c-eeb733101e08', @p0='2025-06-26T18:10:20.4950590Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-07-03T18:10:20.4853990Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-26T19:29:13.7193171Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='HIQBJT+/fdRLsP2s5wZHCKapFAoaI8C4drA7GIUtpZH78XCj9FpkCEBCjwnRiel9wHsJt41/rUT5wuusyaAh2Q==' (Nullable = false), @p12='2025-06-26T19:29:13.8502820Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='831' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-26 23:29:14.062 +04:00 [INF] Refresh token updated successfully: "48bcad80-51a4-4909-bc3c-eeb733101e08"
2025-06-26 23:29:14.222 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='61f002af-94ba-410d-b598-08bdc74b3f31', @p1='2025-06-26T19:29:14.2037117Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-07-03T19:29:14.1746933Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='K9CzJjDQPd3amRl7YlpkP2R1P9cjiuc7EP7oH4fLwgOmUqZsyp4s8dLxW8h1BPb1RbUhBkoCSElf/OPa8QbXmQ==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-26 23:29:14.232 +04:00 [INF] Refresh token added successfully: "61f002af-94ba-410d-b598-08bdc74b3f31"
2025-06-26 23:29:14.235 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-26 23:29:14.243 +04:00 [INF] Token refresh successful
2025-06-26 23:29:14.263 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-26 23:29:14.334 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2481.5475ms
2025-06-26 23:29:14.342 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-26 23:29:14.353 +04:00 [INF] Request POST /api/auth/refresh completed in 2625ms with status 200 (Correlation ID: 1dc5135a-0b3d-429d-a5cf-79b5ccde0571)
2025-06-26 23:29:14.392 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 2698.6731ms
