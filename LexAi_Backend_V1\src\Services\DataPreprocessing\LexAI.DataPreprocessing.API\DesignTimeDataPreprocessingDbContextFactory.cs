﻿namespace LexAI.DataPreprocessing.API;

using LexAI.DataPreprocessing.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;

/// <summary>
/// Design-time factory for creating DataPreprocessingDbContext instances during migrations
/// </summary>
public class DesignTimeDataPreprocessingDbContextFactory : IDesignTimeDbContextFactory<DataPreprocessingDbContext>
{
    /// <summary>
    /// Creates a new instance of DataPreprocessingDbContext for design-time operations
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Configured DataPreprocessingDbContext instance</returns>
    public DataPreprocessingDbContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<DataPreprocessingDbContext>();
        optionsBuilder.UseNpgsql(configuration.GetConnectionString("PostgreSql"));

        return new DataPreprocessingDbContext(optionsBuilder.Options);
    }
}

