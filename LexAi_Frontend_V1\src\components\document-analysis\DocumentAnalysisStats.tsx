import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { 
  Activity, 
  Clock, 
  DollarSign, 
  FileText, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Server,
  Database,
  Zap
} from 'lucide-react'

interface DocumentAnalysisStatsProps {
  className?: string
}

interface AnalysisStats {
  totalAnalyses: number
  successfulAnalyses: number
  failedAnalyses: number
  averageProcessingTime: number
  totalTokensUsed: number
  totalCostEstimated: number
  cacheHitRatio: number
  topDocumentTypes: Array<{ type: string; count: number }>
  recentAnalyses: Array<{
    id: string
    documentName: string
    status: string
    processingTime: number
    analyzedAt: string
  }>
}

interface ServiceHealth {
  status: string
  services: Record<string, {
    status: string
    message: string
    responseTime?: number
  }>
  uptime: string
  version: string
}

export const DocumentAnalysisStats: React.FC<DocumentAnalysisStatsProps> = ({ className }) => {
  const [stats, setStats] = useState<AnalysisStats | null>(null)
  const [health, setHealth] = useState<ServiceHealth | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  useEffect(() => {
    loadStats()
    loadHealth()
    
    // Rafraîchir toutes les 30 secondes
    const interval = setInterval(() => {
      loadStats()
      loadHealth()
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const loadStats = async () => {
    try {
      // Simulation des données - à remplacer par un vrai appel API
      const mockStats: AnalysisStats = {
        totalAnalyses: 1247,
        successfulAnalyses: 1198,
        failedAnalyses: 49,
        averageProcessingTime: 4250,
        totalTokensUsed: 2847392,
        totalCostEstimated: 142.37,
        cacheHitRatio: 0.73,
        topDocumentTypes: [
          { type: 'Contrat', count: 456 },
          { type: 'Bail', count: 234 },
          { type: 'NDA', count: 189 },
          { type: 'Emploi', count: 156 },
          { type: 'Autre', count: 212 }
        ],
        recentAnalyses: [
          {
            id: '1',
            documentName: 'contrat_location.pdf',
            status: 'Completed',
            processingTime: 3200,
            analyzedAt: new Date(Date.now() - 300000).toISOString()
          },
          {
            id: '2',
            documentName: 'accord_confidentialite.docx',
            status: 'Completed',
            processingTime: 2800,
            analyzedAt: new Date(Date.now() - 600000).toISOString()
          },
          {
            id: '3',
            documentName: 'contrat_travail.pdf',
            status: 'Failed',
            processingTime: 0,
            analyzedAt: new Date(Date.now() - 900000).toISOString()
          }
        ]
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error)
    }
  }

  const loadHealth = async () => {
    try {
      // Simulation des données de santé - à remplacer par un vrai appel API
      const mockHealth: ServiceHealth = {
        status: 'Healthy',
        services: {
          'AzureDocumentIntelligence': {
            status: 'Healthy',
            message: 'Service opérationnel',
            responseTime: 245
          },
          'Cache': {
            status: 'Healthy',
            message: 'Cache opérationnel avec 156 entrées',
            responseTime: 2
          },
          'LLMService': {
            status: 'Healthy',
            message: 'Azure OpenAI GPT-4 disponible',
            responseTime: 1200
          }
        },
        uptime: '2d 14h 32m',
        version: '1.0.0'
      }
      setHealth(mockHealth)
      setLastRefresh(new Date())
    } catch (error) {
      console.error('Erreur lors du chargement de la santé du service:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = () => {
    setIsLoading(true)
    loadStats()
    loadHealth()
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return 'text-green-600'
      case 'degraded':
        return 'text-yellow-600'
      case 'unhealthy':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'unhealthy':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  if (isLoading && !stats) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Chargement des statistiques...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* En-tête avec bouton de rafraîchissement */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Statistiques d'analyse</h2>
          <p className="text-gray-600">
            Dernière mise à jour: {lastRefresh.toLocaleTimeString('fr-FR')}
          </p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Actualiser
        </Button>
      </div>

      {/* Santé du service */}
      {health && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              Santé du service
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${getStatusColor(health.status)}`}>
                  {health.status}
                </div>
                <div className="text-sm text-gray-600">Statut global</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{health.uptime}</div>
                <div className="text-sm text-gray-600">Temps de fonctionnement</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{health.version}</div>
                <div className="text-sm text-gray-600">Version</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {Object.values(health.services).filter(s => s.status === 'Healthy').length}
                </div>
                <div className="text-sm text-gray-600">Services sains</div>
              </div>
            </div>

            <div className="space-y-2">
              {Object.entries(health.services).map(([serviceName, serviceInfo]) => (
                <div key={serviceName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(serviceInfo.status)}
                    <div>
                      <div className="font-medium">{serviceName}</div>
                      <div className="text-sm text-gray-600">{serviceInfo.message}</div>
                    </div>
                  </div>
                  {serviceInfo.responseTime && (
                    <Badge variant="outline">
                      {serviceInfo.responseTime}ms
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Métriques principales */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total analyses</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAnalyses.toLocaleString()}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux de succès</p>
                  <p className="text-2xl font-bold text-green-600">
                    {((stats.successfulAnalyses / stats.totalAnalyses) * 100).toFixed(1)}%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Temps moyen</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.averageProcessingTime}ms</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Coût total</p>
                  <p className="text-2xl font-bold text-purple-600">${stats.totalCostEstimated.toFixed(2)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Métriques détaillées */}
      {stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance du cache */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Performance du cache
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Taux de hit</span>
                  <span className="text-lg font-bold text-blue-600">
                    {(stats.cacheHitRatio * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${stats.cacheHitRatio * 100}%` }}
                  ></div>
                </div>
                <div className="text-sm text-gray-600">
                  Un taux élevé indique une bonne optimisation des performances
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Utilisation des tokens */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Utilisation des tokens
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Total tokens</span>
                  <span className="text-lg font-bold text-green-600">
                    {stats.totalTokensUsed.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Moyenne par analyse</span>
                  <span className="text-lg font-bold text-green-600">
                    {Math.round(stats.totalTokensUsed / stats.totalAnalyses).toLocaleString()}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  Coût moyen par analyse: ${(stats.totalCostEstimated / stats.totalAnalyses).toFixed(4)}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Types de documents et analyses récentes */}
      {stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Types de documents populaires */}
          <Card>
            <CardHeader>
              <CardTitle>Types de documents populaires</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.topDocumentTypes.map((docType, index) => (
                  <div key={docType.type} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        {index + 1}
                      </div>
                      <span className="font-medium">{docType.type}</span>
                    </div>
                    <Badge variant="secondary">{docType.count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Analyses récentes */}
          <Card>
            <CardHeader>
              <CardTitle>Analyses récentes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.recentAnalyses.map((analysis) => (
                  <div key={analysis.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium text-sm truncate">{analysis.documentName}</div>
                      <div className="text-xs text-gray-600">
                        {new Date(analysis.analyzedAt).toLocaleString('fr-FR')}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {analysis.status === 'Completed' ? (
                        <Badge variant="success">{analysis.processingTime}ms</Badge>
                      ) : (
                        <Badge variant="destructive">Échec</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default DocumentAnalysisStats
