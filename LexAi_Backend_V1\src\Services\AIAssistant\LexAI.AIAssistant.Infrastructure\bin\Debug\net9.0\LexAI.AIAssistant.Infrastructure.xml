<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.AIAssistant.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext">
            <summary>
            DbContext pour le service AI Assistant
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Data.Migrations.InitialMigration">
            <inheritdoc />
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Data.Migrations.InitialMigration.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Data.Migrations.InitialMigration.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Data.Migrations.InitialMigration.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository">
            <summary>
            Repository pour la gestion des conversations avec EF Core
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtient une conversation par son ID
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.GetByMessageIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtient une conversation par l'ID d'un message
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.AddAsync(LexAI.AIAssistant.Domain.Entities.Conversation,System.Threading.CancellationToken)">
            <summary>
            Ajoute une nouvelle conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.UpdateAsync(LexAI.AIAssistant.Domain.Entities.Conversation,System.Threading.CancellationToken)">
            <summary>
            Met à jour une conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(LexAI.AIAssistant.Domain.Entities.Conversation,System.Threading.CancellationToken)">
            <summary>
            Sauvegarde une conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            Sauvegarde les changements dans le contexte
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.UpdateMessageRatingAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Met à jour le rating d'un message
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.GetByUserIdAsync(System.Guid,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Obtient les conversations d'un utilisateur
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Supprime une conversation
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService">
            <summary>
            Service de gestion des conversations - Implémentation minimale
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService.CreateConversationAsync(LexAI.AIAssistant.Application.DTOs.CreateConversationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Crée une nouvelle conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService.GetConversationAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtient une conversation par ID
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService.GetUserConversationsAsync(System.Guid,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Obtient les conversations d'un utilisateur (retourne ConversationSummaryDto)
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService.UpdateConversationAsync(System.Guid,LexAI.AIAssistant.Application.DTOs.UpdateConversationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Met à jour une conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService.DeleteConversationAsync(System.Guid,System.Guid,System.Threading.CancellationToken)">
            <summary>
            Supprime une conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.ConversationManagementService.ArchiveConversationAsync(System.Guid,System.Guid,System.Threading.CancellationToken)">
            <summary>
            Archive une conversation
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService">
            <summary>
            Service d'assistant IA amélioré utilisant le service LLM unifié
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.SendMessageAsync(LexAI.AIAssistant.Application.DTOs.ChatRequestDto,System.Threading.CancellationToken)">
            <summary>
            Envoie un message à l'assistant IA et obtient une réponse
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.ContinueConversationAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Continue une conversation existante
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.AnalyzeDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto,System.Threading.CancellationToken)">
            <summary>
            Analyse un document juridique
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.PerformLegalResearchAsync(LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Effectue une recherche juridique et fournit des insights
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.GenerateDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Génère un document juridique basé sur les exigences de l'utilisateur
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.SummarizeConversationAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Résume une conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.EnhancedAIAssistantService.AnalyzeMessageWithLLMAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyse l'intention du message avec le LLM
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.LegalResearchIntegrationService">
            <summary>
            Service d'intégration avec le service Legal Research
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.LegalResearchIntegrationService.SearchLegalDocumentsAsync(System.String,System.Nullable{LexAI.Shared.Application.DTOs.LegalDomain},System.Int32,System.Threading.CancellationToken)">
            <summary>
            Recherche des documents juridiques pertinents
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.LegalResearchIntegrationService.GetLegalDocumentAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtient un document juridique par son ID
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.LegalResearchIntegrationService.FindSimilarDocumentsAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Trouve des documents similaires
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.LegalResearchIntegrationService.GetDocumentDetailsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Obtient des détails sur un document juridique spécifique
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.LegalResearchIntegrationService.AnalyzeQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyse une requête juridique
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.SearchResponseDto">
            <summary>
            DTO pour les réponses de recherche du service Legal Research
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.QueryAnalysisDto">
            <summary>
            DTO pour l'analyse de requête
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService">
            <summary>
            OpenAI-based AI assistant service implementation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.#ctor(System.Net.Http.HttpClient,LexAI.AIAssistant.Application.Interfaces.ILegalResearchIntegrationService,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService})">
            <summary>
            Initializes a new instance of the OpenAIAssistantService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="legalResearchService">Legal research service</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.SendMessageAsync(LexAI.AIAssistant.Application.DTOs.ChatRequestDto,System.Threading.CancellationToken)">
            <summary>
            Sends a message to the AI assistant and gets a response
            </summary>
            <param name="request">Chat request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.ContinueConversationAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Continues an existing conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="message">User message</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>AI response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.AnalyzeDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto,System.Threading.CancellationToken)">
            <summary>
            Analyzes a legal document
            </summary>
            <param name="request">Document analysis request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document analysis response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.PerformLegalResearchAsync(LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs legal research and provides insights
            </summary>
            <param name="request">Research request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Research response</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.GenerateDocumentAsync(LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto,System.Threading.CancellationToken)">
            <summary>
            Generates a legal document based on user requirements
            </summary>
            <param name="request">Document generation request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Generated document</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.SummarizeConversationAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Summarizes a conversation
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Conversation summary</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.AnalyzeMessageAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyzes message intent and extracts entities
            </summary>
            <param name="message">Message to analyze</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Message analysis</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.GenerateFollowUpQuestionsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Generates follow-up questions based on conversation context
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Follow-up questions</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIChatResponse">
            <summary>
            OpenAI chat response model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIChoice">
            <summary>
            OpenAI choice model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIMessage">
            <summary>
            OpenAI message model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Infrastructure.Services.OpenAIAssistantService.OpenAIUsage">
            <summary>
            OpenAI usage model
            </summary>
        </member>
    </members>
</doc>
