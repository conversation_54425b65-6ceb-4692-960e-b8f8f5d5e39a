using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using LexAI.DataPreprocessing.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DataPreprocessing.Infrastructure.Repositories;

/// <summary>
/// Document repository implementation
/// </summary>
public class DocumentRepository : IDocumentRepository
{
    private readonly DataPreprocessingDbContext _context;
    private readonly ILogger<DocumentRepository> _logger;
    private readonly string _connectionString;

    /// <summary>
    /// Initializes a new instance of the DocumentRepository
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger</param>
    /// <param name="configuration">Configuration for connection string</param>
    public DocumentRepository(
        DataPreprocessingDbContext context,
        ILogger<DocumentRepository> logger,
        IConfiguration configuration)
    {
        _context = context;
        _logger = logger;
        _connectionString = configuration.GetConnectionString("PostgreSql")
            ?? "Host=localhost;Database=data_preprocessing_db;Username=lexai_user;Password=lexai_password_2024!";
    }

    /// <summary>
    /// Gets a document by ID
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document</returns>
    public async Task<Document?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Documents
                .Include(d => d.Chunks)
                .Include(d => d.ProcessingSteps)
                .Include(d => d.Errors)
                .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document by ID {DocumentId}", id);
            throw;
        }
    }

    /// <summary>
    /// Gets a document by file hash
    /// </summary>
    /// <param name="fileHash">File hash</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document</returns>
    public async Task<Document?> GetByFileHashAsync(string fileHash, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Documents
                .Include(d => d.Chunks)
                .Include(d => d.ProcessingSteps)
                .Include(d => d.Errors)
                .FirstOrDefaultAsync(d => d.FileHash == fileHash, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document by file hash {FileHash}", fileHash);
            throw;
        }
    }

    /// <summary>
    /// Gets documents by status
    /// </summary>
    /// <param name="status">Document status</param>
    /// <param name="limit">Maximum number of documents</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents</returns>
    public async Task<IEnumerable<Document>> GetByStatusAsync(
        DocumentStatus status,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Documents
                .Include(d => d.Chunks)
                .Include(d => d.ProcessingSteps)
                .Include(d => d.Errors)
                .Where(d => d.Status == status)
                .OrderBy(d => d.CreatedAt)
                .Take(limit)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by status {Status}", status);
            throw;
        }
    }

    /// <summary>
    /// Gets documents by user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="limit">Maximum number of documents</param>
    /// <param name="offset">Offset for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Documents</returns>
    public async Task<IEnumerable<Document>> GetByUserAsync(
        Guid userId,
        int limit = 20,
        int offset = 0,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Documents
                .Include(d => d.Chunks)
                .Include(d => d.ProcessingSteps)
                .Include(d => d.Errors)
                .Where(d => d.CreatedBy == userId.ToString())
                .OrderByDescending(d => d.CreatedAt)
                .Skip(offset)
                .Take(limit)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting documents by user {UserId}", userId);
            throw;
        }
    }

    /// <summary>
    /// Adds a new document
    /// </summary>
    /// <param name="document">Document to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task AddAsync(Document document, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Documents.Add(document);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Document added successfully: {DocumentId}", document.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding document {DocumentId}", document.Id);
            throw;
        }
    }

    /// <summary>
    /// Updates a document
    /// </summary>
    /// <param name="document">Document to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task UpdateAsync(Document document, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Documents.Update(document);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("Document updated successfully: {DocumentId}", document.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document {DocumentId}", document.Id);
            throw;
        }
    }

    /// <summary>
    /// Updates a document using a new context (for background operations)
    /// </summary>
    /// <param name="document">Document to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task UpdateAsyncWithNewContext(Document document, CancellationToken cancellationToken = default)
    {
        try
        {
            var options = new DbContextOptionsBuilder<DataPreprocessingDbContext>()
                .UseNpgsql(_connectionString)
                .Options;

            using var context = new DataPreprocessingDbContext(options);

            // Recharger l'entité depuis la base pour éviter les conflits de concurrence
            var existingDocument = await context.Documents
                .Include(d => d.ProcessingSteps)
                .Include(d => d.Errors)
                .FirstOrDefaultAsync(d => d.Id == document.Id, cancellationToken);

            if (existingDocument != null)
            {
                // Copier les propriétés modifiées
                existingDocument.GetType().GetProperty("Status")?.SetValue(existingDocument, document.Status);
                existingDocument.GetType().GetProperty("ExtractedText")?.SetValue(existingDocument, document.ExtractedText);
                existingDocument.GetType().GetProperty("ProcessingTime")?.SetValue(existingDocument, document.ProcessingTime);
                existingDocument.GetType().GetProperty("ChunkCount")?.SetValue(existingDocument, document.ChunkCount);
                existingDocument.GetType().GetProperty("TotalTokens")?.SetValue(existingDocument, document.TotalTokens);
                existingDocument.GetType().GetProperty("EstimatedCost")?.SetValue(existingDocument, document.EstimatedCost);
                existingDocument.GetType().GetProperty("IsVectorized")?.SetValue(existingDocument, document.IsVectorized);
                existingDocument.GetType().GetProperty("VectorDatabase")?.SetValue(existingDocument, document.VectorDatabase);
                existingDocument.GetType().GetProperty("VectorCollection")?.SetValue(existingDocument, document.VectorCollection);
                existingDocument.GetType().GetProperty("DetectedDomain")?.SetValue(existingDocument, document.DetectedDomain);
                existingDocument.GetType().GetProperty("ClassificationConfidence")?.SetValue(existingDocument, document.ClassificationConfidence);
                existingDocument.GetType().GetProperty("UpdatedAt")?.SetValue(existingDocument, DateTime.UtcNow);

                // Ajouter les nouveaux ProcessingSteps
                foreach (var step in document.ProcessingSteps)
                {
                    if (!existingDocument.ProcessingSteps.Any(s => s.Id == step.Id))
                    {
                        context.Entry(step).State = EntityState.Added;
                        existingDocument.ProcessingSteps.Add(step);
                    }
                }

                // Ajouter les nouvelles erreurs
                foreach (var error in document.Errors)
                {
                    if (!existingDocument.Errors.Any(e => e.Id == error.Id))
                    {
                        context.Entry(error).State = EntityState.Added;
                        existingDocument.Errors.Add(error);
                    }
                }

                await context.SaveChangesAsync(cancellationToken);
                _logger.LogDebug("Document updated successfully with new context: {DocumentId}", document.Id);
            }
            else
            {
                _logger.LogWarning("Document {DocumentId} not found for update", document.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating document with new context {DocumentId}", document.Id);
            throw;
        }
    }

    /// <summary>
    /// Deletes a document
    /// </summary>
    /// <param name="id">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var document = await _context.Documents.FindAsync(new object[] { id }, cancellationToken);
            if (document != null)
            {
                _context.Documents.Remove(document);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Document deleted successfully: {DocumentId}", id);
            }
            else
            {
                _logger.LogWarning("Document not found for deletion: {DocumentId}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document {DocumentId}", id);
            throw;
        }
    }

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing statistics</returns>
    public async Task<Application.Interfaces.ProcessingStatistics> GetProcessingStatisticsAsync(
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var documents = await _context.Documents
                .Where(d => d.CreatedAt >= fromDate && d.CreatedAt <= toDate)
                .ToListAsync(cancellationToken);

            var statistics = new Application.Interfaces.ProcessingStatistics
            {
                DocumentId = Guid.Empty, // This is aggregate statistics
                FileName = "Aggregate Statistics",
                FileSize = documents.Sum(d => d.FileSize),
                Status = DocumentStatus.Completed, // Default status for aggregate
                ChunkCount = documents.Sum(d => d.ChunkCount),
                TotalTokens = documents.Sum(d => d.TotalTokens),
                ProcessingTime = documents.Where(d => d.ProcessingTime.HasValue)
                    .Aggregate(TimeSpan.Zero, (sum, d) => sum + d.ProcessingTime!.Value),
                EstimatedCost = documents.Sum(d => d.EstimatedCost),
                StepCount = documents.Sum(d => d.ProcessingSteps.Count),
                ErrorCount = documents.Sum(d => d.Errors.Count),
                IsVectorized = documents.All(d => d.IsVectorized)
            };

            _logger.LogDebug("Generated processing statistics for period {FromDate} to {ToDate}: {DocumentCount} documents",
                fromDate, toDate, documents.Count);

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting processing statistics for period {FromDate} to {ToDate}", fromDate, toDate);
            throw;
        }
    }
}
