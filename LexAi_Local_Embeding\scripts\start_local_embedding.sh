#!/bin/bash

echo "========================================"
echo "  Service d'Embedding Local - LexAI"
echo "========================================"
echo

# Vérifier si Python est installé
if ! command -v python3 &> /dev/null; then
    echo "ERREUR: Python 3 n'est pas installé"
    echo "Veuillez installer Python 3.8+ depuis https://python.org"
    exit 1
fi

echo "✅ Python détecté"

# Créer un environnement virtuel s'il n'existe pas
if [ ! -d "venv" ]; then
    echo "📦 Création de l'environnement virtuel..."
    python3 -m venv venv
fi

# Activer l'environnement virtuel
echo "🔄 Activation de l'environnement virtuel..."
source venv/bin/activate

# Installer les dépendances
echo "📥 Installation des dépendances..."
pip install -r requirements.txt

echo
echo "🚀 Démarrage du service d'embedding local..."
echo "📍 URL: http://localhost:8000"
echo "📍 Health Check: http://localhost:8000/health"
echo
echo "Appuyez sur Ctrl+C pour arrêter le service"
echo

# Démarrer le service
python local_embedding_service.py
