{"openapi": "3.0.1", "info": {"title": "LexAI Data Preprocessing Service API", "description": "Service de préprocessing de données avec agents spécialisés pour LexAI", "contact": {"name": "LexAI Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Documents/upload": {"post": {"tags": ["Documents"], "summary": "Uploads a document for processing", "requestBody": {"description": "Document upload request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DocumentUploadRequestDto"}}}}, "responses": {"200": {"description": "Document uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadResponseDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "413": {"description": "File too large", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Documents/{documentId}/process": {"post": {"tags": ["Documents"], "summary": "Processes a document through the pipeline", "parameters": [{"name": "documentId", "in": "path", "description": "Document ID to process", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Processing configuration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessingConfigurationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessingConfigurationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcessingConfigurationDto"}}}}, "responses": {"200": {"description": "Document processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessingResultDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Documents/{documentId}/retry": {"post": {"tags": ["Documents"], "summary": "Retries failed document processing", "parameters": [{"name": "documentId", "in": "path", "description": "Document ID to retry", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Retry request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetryProcessingRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetryProcessingRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RetryProcessingRequestDto"}}}}, "responses": {"200": {"description": "Document retry completed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessingResultDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Documents/{documentId}/status": {"get": {"tags": ["Documents"], "summary": "Gets processing status for a document", "parameters": [{"name": "documentId", "in": "path", "description": "Document ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessingStatusDto"}}}}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Documents/{documentId}/cancel": {"post": {"tags": ["Documents"], "summary": "Cancels document processing", "parameters": [{"name": "documentId", "in": "path", "description": "Document ID to cancel", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Cancellation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelProcessingRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancelProcessingRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancelProcessingRequestDto"}}}}, "responses": {"200": {"description": "Processing cancelled successfully", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Documents": {"get": {"tags": ["Documents"], "summary": "Gets user's documents", "parameters": [{"name": "limit", "in": "query", "description": "Maximum number of documents", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "offset", "in": "query", "description": "Offset for pagination", "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "Documents retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentSummaryDto"}}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}}, "components": {"schemas": {"CancelProcessingRequestDto": {"type": "object", "properties": {"reason": {"type": "string", "description": "Cancellation reason", "nullable": true}}, "additionalProperties": false, "description": "Cancel processing request DTO"}, "ChunkingConfigurationDto": {"type": "object", "properties": {"strategy": {"$ref": "#/components/schemas/ChunkingStrategy"}, "maxChunkSize": {"type": "integer", "format": "int32"}, "overlapSize": {"type": "integer", "format": "int32"}, "minChunkSize": {"type": "integer", "format": "int32"}, "preserveSentences": {"type": "boolean"}, "preserveParagraphs": {"type": "boolean"}, "customSeparators": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ChunkingStrategy": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "DocumentStatus": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "type": "integer", "format": "int32"}, "DocumentSummaryDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Document ID", "format": "uuid"}, "fileName": {"type": "string", "description": "File name", "nullable": true}, "fileSize": {"type": "integer", "description": "File size", "format": "int64"}, "mimeType": {"type": "string", "description": "MIME type", "nullable": true}, "status": {"$ref": "#/components/schemas/DocumentStatus"}, "detectedDomain": {"$ref": "#/components/schemas/LegalDomain"}, "classificationConfidence": {"type": "number", "description": "Classification confidence", "format": "double", "nullable": true}, "chunkCount": {"type": "integer", "description": "Number of chunks", "format": "int32"}, "totalTokens": {"type": "integer", "description": "Total tokens", "format": "int32"}, "estimatedCost": {"type": "number", "description": "Estimated cost", "format": "double"}, "isVectorized": {"type": "boolean", "description": "Whether vectorized"}, "vectorDatabase": {"type": "string", "description": "Vector database", "nullable": true}, "processingTime": {"type": "string", "description": "Processing time", "format": "date-span", "nullable": true}, "createdAt": {"type": "string", "description": "Created timestamp", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Updated timestamp", "format": "date-time", "nullable": true}}, "additionalProperties": false, "description": "Document summary DTO"}, "DocumentUploadRequestDto": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "fileContent": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "configuration": {"$ref": "#/components/schemas/ProcessingConfigurationDto"}, "userId": {"type": "string", "format": "uuid"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "DocumentUploadResponseDto": {"type": "object", "properties": {"documentId": {"type": "string", "format": "uuid"}, "fileName": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "status": {"type": "string", "nullable": true}, "storagePath": {"type": "string", "nullable": true}, "processingStarted": {"type": "boolean"}, "estimatedProcessingTime": {"type": "string", "format": "date-span", "nullable": true}, "uploadedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "EmbeddingModelType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "ErrorSeverity": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "LegalDomain": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], "type": "integer", "format": "int32"}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProcessingConfigurationDto": {"type": "object", "properties": {"chunking": {"$ref": "#/components/schemas/ChunkingConfigurationDto"}, "embeddingModel": {"$ref": "#/components/schemas/EmbeddingModelType"}, "targetDatabases": {"type": "array", "items": {"$ref": "#/components/schemas/VectorDatabaseType"}, "nullable": true}, "performQualityAssurance": {"type": "boolean"}, "extractNamedEntities": {"type": "boolean"}, "extractKeywords": {"type": "boolean"}, "priority": {"$ref": "#/components/schemas/ProcessingPriority"}, "customOptions": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ProcessingPriority": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "ProcessingResultDto": {"type": "object", "properties": {"documentId": {"type": "string", "format": "uuid"}, "success": {"type": "boolean"}, "finalStatus": {"$ref": "#/components/schemas/DocumentStatus"}, "totalProcessingTime": {"type": "string", "format": "date-span"}, "completedSteps": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessingStepResultDto"}, "nullable": true}, "totalChunks": {"type": "integer", "format": "int32"}, "totalTokens": {"type": "integer", "format": "int32"}, "estimatedCost": {"type": "number", "format": "double"}, "vectorDatabasesUsed": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "qualityAssessment": {"$ref": "#/components/schemas/QualityAssessmentDto"}}, "additionalProperties": false}, "ProcessingStatusDto": {"type": "object", "properties": {"documentId": {"type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/DocumentStatus"}, "currentStep": {"type": "string", "nullable": true}, "progressPercentage": {"type": "integer", "format": "int32"}, "estimatedTimeRemaining": {"type": "string", "format": "date-span", "nullable": true}, "startedAt": {"type": "string", "format": "date-time"}, "lastUpdatedAt": {"type": "string", "format": "date-time"}, "completedSteps": {"type": "array", "items": {"type": "string"}, "nullable": true}, "remainingSteps": {"type": "array", "items": {"type": "string"}, "nullable": true}, "currentAgent": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProcessingStepResultDto": {"type": "object", "properties": {"stepName": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "duration": {"type": "string", "format": "date-span"}, "agentName": {"type": "string", "nullable": true}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "QualityAssessmentDto": {"type": "object", "properties": {"overallScore": {"type": "number", "format": "double"}, "qualityPassed": {"type": "boolean"}, "assessmentTime": {"type": "string", "format": "date-span"}, "agentName": {"type": "string", "nullable": true}, "qualityMetrics": {"type": "object", "additionalProperties": {"type": "number", "format": "double"}, "nullable": true}, "issues": {"type": "array", "items": {"$ref": "#/components/schemas/QualityIssueDto"}, "nullable": true}, "recommendations": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "QualityIssueDto": {"type": "object", "properties": {"issueType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "severity": {"$ref": "#/components/schemas/ErrorSeverity"}, "suggestedFix": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RetryProcessingRequestDto": {"type": "object", "properties": {"fromStep": {"type": "string", "description": "Step to retry from", "nullable": true}}, "additionalProperties": false, "description": "Retry processing request DTO"}, "VectorDatabaseType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}