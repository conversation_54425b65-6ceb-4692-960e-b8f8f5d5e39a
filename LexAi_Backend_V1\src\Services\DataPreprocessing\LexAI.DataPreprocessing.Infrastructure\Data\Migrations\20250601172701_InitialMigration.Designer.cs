﻿// <auto-generated />
using System;
using LexAI.DataPreprocessing.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LexAI.DataPreprocessing.Infrastructure.Data.Migrations
{
    [DbContext(typeof(DataPreprocessingDbContext))]
    [Migration("20250601172701_InitialMigration")]
    partial class InitialMigration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<int>("ChunkCount")
                        .HasColumnType("integer");

                    b.Property<double?>("ClassificationConfidence")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<string>("DetectedDomain")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("EstimatedCost")
                        .HasColumnType("numeric");

                    b.Property<string>("ExtractedText")
                        .HasColumnType("text");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVectorized")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<TimeSpan?>("ProcessingTime")
                        .HasColumnType("interval");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("TotalTokens")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("VectorCollection")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("VectorDatabase")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DetectedDomain");

                    b.HasIndex("FileHash")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.DocumentChunk", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<int>("CharacterCount")
                        .HasColumnType("integer");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("DomainRelevance")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("EmbeddingModel")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EmbeddingVector")
                        .HasColumnType("jsonb");

                    b.Property<int>("EndPosition")
                        .HasColumnType("integer");

                    b.Property<double>("ImportanceScore")
                        .HasColumnType("double precision");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVectorized")
                        .HasColumnType("boolean");

                    b.Property<string>("Keywords")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<double>("QualityScore")
                        .HasColumnType("double precision");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("integer");

                    b.Property<int>("StartPosition")
                        .HasColumnType("integer");

                    b.Property<int>("TokenCount")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int?>("VectorDimension")
                        .HasColumnType("integer");

                    b.Property<string>("VectorId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<uint>("xmin")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("xid")
                        .HasColumnName("xmin");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("IsVectorized");

                    b.HasIndex("SequenceNumber");

                    b.HasIndex("Type");

                    b.HasIndex("VectorId");

                    b.ToTable("DocumentChunks");
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.NamedEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ChunkId")
                        .HasColumnType("uuid");

                    b.Property<double>("Confidence")
                        .HasColumnType("double precision");

                    b.Property<int>("EndPosition")
                        .HasColumnType("integer");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<int>("StartPosition")
                        .HasColumnType("integer");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ChunkId");

                    b.HasIndex("Text");

                    b.HasIndex("Type");

                    b.ToTable("NamedEntities");
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.ProcessingError", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AgentName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("ErrorCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsResolved")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("OccurredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ResolutionNotes")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("StepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("ErrorCode");

                    b.HasIndex("OccurredAt");

                    b.HasIndex("Severity");

                    b.ToTable("ProcessingErrors");
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.ProcessingStep", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AgentName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("StepName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("StartedAt");

                    b.HasIndex("StepName");

                    b.ToTable("ProcessingSteps");
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.DocumentChunk", b =>
                {
                    b.HasOne("LexAI.DataPreprocessing.Domain.Entities.Document", null)
                        .WithMany("Chunks")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.NamedEntity", b =>
                {
                    b.HasOne("LexAI.DataPreprocessing.Domain.Entities.DocumentChunk", null)
                        .WithMany("NamedEntities")
                        .HasForeignKey("ChunkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.ProcessingError", b =>
                {
                    b.HasOne("LexAI.DataPreprocessing.Domain.Entities.Document", null)
                        .WithMany("Errors")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.ProcessingStep", b =>
                {
                    b.HasOne("LexAI.DataPreprocessing.Domain.Entities.Document", null)
                        .WithMany("ProcessingSteps")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.Document", b =>
                {
                    b.Navigation("Chunks");

                    b.Navigation("Errors");

                    b.Navigation("ProcessingSteps");
                });

            modelBuilder.Entity("LexAI.DataPreprocessing.Domain.Entities.DocumentChunk", b =>
                {
                    b.Navigation("NamedEntities");
                });
#pragma warning restore 612, 618
        }
    }
}
