﻿
namespace LexAI.Shared.Application.DTOs.Embedding
{
    /// <summary>
    /// Résultat de génération d'embeddings en lot
    /// </summary>
    public class BatchEmbeddingResult
    {
        public bool Success { get; set; }
        public List<float[]>? Embeddings { get; set; }
        public string? Error { get; set; }
        public string Model { get; set; } = string.Empty;
        public int TokensUsed { get; set; }
        public decimal EstimatedCost { get; set; }
        public int ProcessingTimeMs { get; set; }
        public bool UsedFallback { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

}
