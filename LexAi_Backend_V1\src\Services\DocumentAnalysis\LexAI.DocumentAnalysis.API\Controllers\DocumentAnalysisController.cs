using LexAI.DocumentAnalysis.Application.Commands;
using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.DocumentAnalysis.Application.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.Security.Claims;

namespace LexAI.DocumentAnalysis.API.Controllers;

/// <summary>
/// Contrôleur pour l'analyse de documents juridiques
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
[EnableRateLimiting("DocumentAnalysisPolicy")]
public class DocumentAnalysisController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IDocumentAnalysisService _documentAnalysisService;
    private readonly ILogger<DocumentAnalysisController> _logger;

    public DocumentAnalysisController(
        IMediator mediator,
        IDocumentAnalysisService documentAnalysisService,
        ILogger<DocumentAnalysisController> logger)
    {
        _mediator = mediator;
        _documentAnalysisService = documentAnalysisService;
        _logger = logger;
    }

    /// <summary>
    /// Analyse un document juridique complet
    /// </summary>
    /// <param name="request">Données du document à analyser</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'analyse complète</returns>
    [HttpPost("analyze")]
    [ProducesResponseType(typeof(DocumentAnalysisResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status429TooManyRequests)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DocumentAnalysisResponseDto>> AnalyzeDocument(
        [FromForm] DocumentAnalysisUploadRequest request,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("Starting document analysis for user {UserId}, document: {DocumentName}", 
                userId, request.DocumentName);

            // Validation du fichier
            if (request.DocumentFile == null || request.DocumentFile.Length == 0)
            {
                return BadRequest("Aucun fichier fourni");
            }

            // Validation de la taille du fichier (max 50MB)
            if (request.DocumentFile.Length > 50 * 1024 * 1024)
            {
                return BadRequest("Le fichier est trop volumineux (maximum 50MB)");
            }

            // Validation du type de fichier
            var allowedTypes = new[] { "application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain" };
            if (!allowedTypes.Contains(request.DocumentFile.ContentType))
            {
                return BadRequest("Type de fichier non supporté. Formats acceptés: PDF, DOCX, TXT");
            }

            // Conversion en bytes
            byte[] documentContent;
            using (var memoryStream = new MemoryStream())
            {
                await request.DocumentFile.CopyToAsync(memoryStream, cancellationToken);
                documentContent = memoryStream.ToArray();
            }

            // Détermination du type de document
            var documentType = GetDocumentType(request.DocumentFile.ContentType);

            // Création de la commande
            var command = new AnalyzeDocumentCommand
            {
                DocumentName = request.DocumentName ?? request.DocumentFile.FileName,
                DocumentType = documentType,
                DocumentContent = documentContent,
                UserId = userId,
                Options = request.Options ?? new AnalysisOptions()
            };

            // Exécution de l'analyse
            var result = await _mediator.Send(command, cancellationToken);

            _logger.LogInformation("Document analysis completed successfully for user {UserId}, analysis ID: {AnalysisId}", 
                userId, result.Id);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during document analysis");
            return StatusCode(500, "Erreur interne lors de l'analyse du document");
        }
    }

    /// <summary>
    /// Obtient le résultat d'une analyse par ID
    /// </summary>
    /// <param name="analysisId">ID de l'analyse</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de l'analyse</returns>
    [HttpGet("{analysisId:guid}")]
    [ProducesResponseType(typeof(DocumentAnalysisResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<DocumentAnalysisResponseDto>> GetAnalysisResult(
        Guid analysisId,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("Retrieving analysis {AnalysisId} for user {UserId}", analysisId, userId);

            // Récupération de l'analyse avec vérification des droits d'accès via MediatR
            var query = new GetAnalysisResultQuery(analysisId, userId);
            var result = await _mediator.Send(query, cancellationToken);

            if (result == null)
            {
                return NotFound("Analyse non trouvée");
            }

            // Vérification que l'utilisateur a accès à cette analyse
            if (result.UserId != userId)
            {
                return Forbid("Accès non autorisé à cette analyse");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analysis {AnalysisId}", analysisId);
            return StatusCode(500, "Erreur lors de la récupération de l'analyse");
        }
    }

    /// <summary>
    /// Obtient la liste des analyses pour l'utilisateur connecté
    /// </summary>
    /// <param name="request">Paramètres de filtrage et pagination</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Liste paginée des analyses</returns>
    [HttpGet]
    [ProducesResponseType(typeof(DocumentAnalysisListResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<DocumentAnalysisListResponseDto>> GetUserAnalyses(
        [FromQuery] DocumentAnalysisListRequestDto request,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = GetCurrentUserId();
            request.UserId = userId;

            _logger.LogInformation("Retrieving analyses list for user {UserId}", userId);

            // Récupération des analyses depuis la base de données via MediatR
            var query = new GetUserAnalysesQuery(request);
            var result = await _mediator.Send(query, cancellationToken);

            _logger.LogInformation("Retrieved {Count} analyses for user {UserId}", result.TotalCount, userId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analyses list");
            return StatusCode(500, "Erreur lors de la récupération des analyses");
        }
    }

    /// <summary>
    /// Régénère une analyse existante avec de nouvelles options
    /// </summary>
    /// <param name="analysisId">ID de l'analyse à régénérer</param>
    /// <param name="options">Nouvelles options d'analyse</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Nouvelle analyse</returns>
    [HttpPost("{analysisId:guid}/regenerate")]
    [ProducesResponseType(typeof(DocumentAnalysisResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<DocumentAnalysisResponseDto>> RegenerateAnalysis(
        Guid analysisId,
        [FromBody] AnalysisOptions? options,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("Regenerating analysis {AnalysisId} for user {UserId}", analysisId, userId);

            var command = new RegenerateAnalysisCommand
            {
                AnalysisId = analysisId,
                UserId = userId,
                NewOptions = options
            };

            var result = await _mediator.Send(command, cancellationToken);

            _logger.LogInformation("Analysis regenerated successfully: {AnalysisId}", analysisId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating analysis {AnalysisId}", analysisId);
            return StatusCode(500, "Erreur lors de la régénération de l'analyse");
        }
    }

    /// <summary>
    /// Supprime une analyse
    /// </summary>
    /// <param name="analysisId">ID de l'analyse à supprimer</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de la suppression</returns>
    [HttpDelete("{analysisId:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> DeleteAnalysis(
        Guid analysisId,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("Deleting analysis {AnalysisId} for user {UserId}", analysisId, userId);

            var command = new DeleteAnalysisCommand
            {
                AnalysisId = analysisId,
                UserId = userId
            };

            var result = await _mediator.Send(command, cancellationToken);

            if (result)
            {
                _logger.LogInformation("Analysis deleted successfully: {AnalysisId}", analysisId);
                return NoContent();
            }
            else
            {
                return NotFound("Analyse non trouvée ou non autorisée");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting analysis {AnalysisId}", analysisId);
            return StatusCode(500, "Erreur lors de la suppression de l'analyse");
        }
    }

    // Méthodes utilitaires privées

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Utilisateur non identifié");
        }
        return userId;
    }

    private static string GetDocumentType(string contentType)
    {
        return contentType switch
        {
            "application/pdf" => "pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => "docx",
            "text/plain" => "txt",
            _ => "unknown"
        };
    }
}

/// <summary>
/// Modèle pour l'upload de document
/// </summary>
public class DocumentAnalysisUploadRequest
{
    /// <summary>
    /// Fichier document à analyser
    /// </summary>
    public IFormFile? DocumentFile { get; set; }

    /// <summary>
    /// Nom du document (optionnel, utilise le nom du fichier par défaut)
    /// </summary>
    public string? DocumentName { get; set; }

    /// <summary>
    /// Options d'analyse
    /// </summary>
    public AnalysisOptions? Options { get; set; }
}
