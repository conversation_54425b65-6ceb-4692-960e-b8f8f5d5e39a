import React, { useState, useEffect } from 'react'
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { Badge } from '../components/ui/Badge'
import { EnhancedDocumentAnalyzer } from '../components/document-analysis/EnhancedDocumentAnalyzer'
import { DocumentAnalysisDetails } from '../components/document-analysis/DocumentAnalysisDetails'
import {
  FileText,
  History,
  Plus,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Trash2,
  Download
} from 'lucide-react'
import { documentAnalysisApi } from '../services/api'
import type {
  DocumentAnalysisResponse,
  DocumentAnalysisSummary,
  DocumentAnalysisListRequest
} from '../services/documentAnalysis/types'

export const DocumentAnalysisPage: React.FC = () => {
  const [currentView, setCurrentView] = useState<'analyzer' | 'history' | 'details'>('analyzer')
  const [selectedAnalysis, setSelectedAnalysis] = useState<DocumentAnalysisResponse | null>(null)
  const [analysisHistory, setAnalysisHistory] = useState<DocumentAnalysisSummary[]>([])
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [riskFilter, setRiskFilter] = useState<string>('all')

  // Charger l'historique des analyses
  useEffect(() => {
    if (currentView === 'history') {
      loadAnalysisHistory()
    }
  }, [currentView])

  const loadAnalysisHistory = async () => {
    setIsLoadingHistory(true)
    try {
      const request: DocumentAnalysisListRequest = {
        page: 1,
        pageSize: 20,
        sortBy: 'analyzedAt',
        sortDescending: true
      }

      if (statusFilter !== 'all') {
        request.status = statusFilter
      }

      const response = await documentAnalysisApi.getUserAnalyses(request)
      setAnalysisHistory(response.items)
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error)
    } finally {
      setIsLoadingHistory(false)
    }
  }

  const handleAnalysisComplete = (analysis: DocumentAnalysisResponse) => {
    setSelectedAnalysis(analysis)
    setCurrentView('details')
    // Recharger l'historique pour inclure la nouvelle analyse
    if (analysisHistory.length > 0) {
      loadAnalysisHistory()
    }
  }

  const handleViewAnalysis = async (analysisId: string) => {
    try {
      const analysis = await documentAnalysisApi.getAnalysisResult(analysisId)
      setSelectedAnalysis(analysis)
      setCurrentView('details')
    } catch (error) {
      console.error('Erreur lors du chargement de l\'analyse:', error)
    }
  }

  const handleRegenerateAnalysis = async (analysisId: string) => {
    try {
      const analysis = await documentAnalysisApi.regenerateAnalysis(analysisId)
      setSelectedAnalysis(analysis)
      loadAnalysisHistory()
    } catch (error) {
      console.error('Erreur lors de la régénération:', error)
    }
  }

  const handleDeleteAnalysis = async (analysisId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette analyse ?')) {
      try {
        await documentAnalysisApi.deleteAnalysis(analysisId)
        loadAnalysisHistory()
        if (selectedAnalysis?.id === analysisId) {
          setSelectedAnalysis(null)
          setCurrentView('history')
        }
      } catch (error) {
        console.error('Erreur lors de la suppression:', error)
      }
    }
  }

  const handleExportAnalysis = (analysis: DocumentAnalysisResponse) => {
    // Créer un objet avec les données à exporter
    const exportData = {
      documentName: analysis.documentName,
      analyzedAt: analysis.analyzedAt,
      summary: analysis.summary,
      clauses: analysis.clauses,
      risks: analysis.risks,
      recommendations: analysis.recommendations,
      entities: analysis.entities,
      citations: analysis.citations,
      metadata: {
        processingTime: analysis.processingTimeMs,
        tokensUsed: analysis.tokensUsed,
        modelUsed: analysis.modelUsed,
        confidenceScore: analysis.confidenceScore
      }
    }

    // Créer et télécharger le fichier JSON
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `analyse-${analysis.documentName}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const filteredHistory = analysisHistory.filter(analysis => {
    const matchesSearch = analysis.documentName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || analysis.status.toLowerCase() === statusFilter.toLowerCase()
    const matchesRisk = riskFilter === 'all' || analysis.overallRiskLevel.toLowerCase() === riskFilter.toLowerCase()
    return matchesSearch && matchesStatus && matchesRisk
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* En-tête */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Analyse de Documents Juridiques</h1>
          <p className="text-gray-600 mt-2">
            Analysez vos documents avec Azure Document Intelligence et l'IA pour détecter les risques et obtenir des recommandations
          </p>
        </div>

        {/* Navigation */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
            <button
              onClick={() => setCurrentView('analyzer')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                currentView === 'analyzer'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Plus className="h-4 w-4 inline mr-2" />
              Nouvelle analyse
            </button>
            <button
              onClick={() => setCurrentView('history')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                currentView === 'history'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <History className="h-4 w-4 inline mr-2" />
              Historique
            </button>
          </div>
        </div>

        {/* Contenu principal */}
        {currentView === 'analyzer' && (
          <EnhancedDocumentAnalyzer onAnalysisComplete={handleAnalysisComplete} />
        )}

        {currentView === 'history' && (
          <div className="space-y-6">
            {/* Filtres et recherche */}
            <Card>
              <CardHeader>
                <CardTitle>Historique des analyses</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Rechercher par nom de document..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">Tous les statuts</option>
                      <option value="completed">Terminé</option>
                      <option value="processing">En cours</option>
                      <option value="failed">Échoué</option>
                    </select>
                    <select
                      value={riskFilter}
                      onChange={(e) => setRiskFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">Tous les niveaux</option>
                      <option value="critical">Critique</option>
                      <option value="high">Élevé</option>
                      <option value="medium">Moyen</option>
                      <option value="low">Faible</option>
                    </select>
                  </div>
                </div>

                {/* Liste des analyses */}
                {isLoadingHistory ? (
                  <div className="text-center py-8">
                    <Clock className="h-8 w-8 animate-spin mx-auto text-gray-400" />
                    <p className="text-gray-600 mt-2">Chargement de l'historique...</p>
                  </div>
                ) : filteredHistory.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto text-gray-400" />
                    <p className="text-gray-600 mt-2">Aucune analyse trouvée</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredHistory.map((analysis) => (
                      <div
                        key={analysis.id}
                        className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center gap-4 flex-1">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(analysis.status)}
                            <FileText className="h-5 w-5 text-gray-400" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">{analysis.documentName}</h3>
                            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                              <span>{new Date(analysis.analyzedAt).toLocaleDateString('fr-FR')}</span>
                              <span>{analysis.riskCount} risques</span>
                              <span>{analysis.recommendationCount} recommandations</span>
                              <span>{analysis.processingTimeMs}ms</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getRiskColor(analysis.overallRiskLevel)}>
                              {analysis.overallRiskLevel}
                            </Badge>
                            <Badge variant="outline">{analysis.status}</Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewAnalysis(analysis.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteAnalysis(analysis.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {currentView === 'details' && selectedAnalysis && (
          <div>
            <div className="mb-4">
              <Button
                variant="outline"
                onClick={() => setCurrentView('history')}
                className="mb-4"
              >
                ← Retour à l'historique
              </Button>
            </div>
            <DocumentAnalysisDetails
              analysis={selectedAnalysis}
              onRegenerate={handleRegenerateAnalysis}
              onDelete={handleDeleteAnalysis}
              onExport={handleExportAnalysis}
            />
          </div>
        )}
      </div>
    </div>
  )
}
