// Types pour le service AI Assistant

export interface Conversation {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  userId: string
  messages: Message[]
}

export interface Message {
  id: string
  conversationId: string
  content: string
  role: MessageRole
  timestamp: string
  metadata?: MessageMetadata
}

export enum MessageRole {
  User = 'user',
  Assistant = 'assistant',
  System = 'system'
}

export interface MessageMetadata {
  sources?: string[]
  confidence?: number
  processingTime?: number
  tokens?: number
  cost?: number
}

export interface SendMessageRequest {
  message: string
  conversationId?: string
  context?: any
}

export interface SendMessageResponse {
  message: Message
  conversation: Conversation
}

export interface DocumentAnalysisRequest {
  documentName: string
  documentContent: string
  documentType?: string
  focusAreas?: string[]
  context?: string
}

export interface DocumentGenerationRequest {
  documentType: string
  requirements: string
  template?: string
  jurisdiction?: string
  parameters?: Record<string, any>
}

export interface LegalResearchRequest {
  query: string
  domain?: string
  jurisdiction?: string
  maxResults?: number
}

export interface MessageRatingRequest {
  messageId: string
  rating: number
  feedback?: string
}

export interface ConversationListRequest {
  page?: number
  pageSize?: number
  sortBy?: string
  sortDescending?: boolean
}

export interface ConversationListResponse {
  items: Conversation[]
  totalCount: number
  page: number
  pageSize: number
  totalPages: number
}
