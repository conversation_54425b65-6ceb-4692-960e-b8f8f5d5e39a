using LexAI.DocumentAnalysis.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace LexAI.DocumentAnalysis.Infrastructure.Data;

/// <summary>
/// Contexte de base de données pour le service d'analyse de documents
/// </summary>
public class DocumentAnalysisDbContext : DbContext
{
    public DocumentAnalysisDbContext(DbContextOptions<DocumentAnalysisDbContext> options)
        : base(options)
    {
    }

    // DbSets
    public DbSet<DocumentAnalysisResult> DocumentAnalysisResults { get; set; }
    public DbSet<ClauseAnalysis> ClauseAnalyses { get; set; }
    public DbSet<RiskAssessment> RiskAssessments { get; set; }
    public DbSet<DocumentRecommendation> DocumentRecommendations { get; set; }
    public DbSet<ExtractedEntity> ExtractedEntities { get; set; }
    public DbSet<DocumentCitation> DocumentCitations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration pour DocumentAnalysisResult
        modelBuilder.Entity<DocumentAnalysisResult>(entity =>
        {
            entity.ToTable("document_analysis_results");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.DocumentName)
                .IsRequired()
                .HasMaxLength(500);
                
            entity.Property(e => e.DocumentType)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.OriginalContent)
                .IsRequired();
                
            entity.Property(e => e.ExtractedText)
                .IsRequired();
                
            entity.Property(e => e.AnalysisContent)
                .IsRequired();
                
            entity.Property(e => e.Status)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.ConfidenceScore)
                .HasPrecision(5, 4);
                
            entity.Property(e => e.EstimatedCost)
                .HasPrecision(10, 4);
                
            entity.Property(e => e.ModelUsed)
                .HasMaxLength(100);
                
            entity.Property(e => e.UserId)
                .IsRequired();
                
            entity.Property(e => e.AnalyzedAt)
                .IsRequired();

            // Index pour les requêtes fréquentes
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.AnalyzedAt);
            entity.HasIndex(e => new { e.UserId, e.Status });
        });

        // Configuration pour ClauseAnalysis
        modelBuilder.Entity<ClauseAnalysis>(entity =>
        {
            entity.ToTable("clause_analyses");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.ClauseText)
                .IsRequired();
                
            entity.Property(e => e.ClauseType)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.Analysis)
                .IsRequired();
                
            entity.Property(e => e.RiskLevel)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.ConfidenceScore)
                .HasPrecision(5, 4);
                
            entity.Property(e => e.SuggestedRevision);
            
            entity.Property(e => e.Tags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>());

            // Relation avec DocumentAnalysisResult
            entity.HasOne(e => e.DocumentAnalysisResult)
                .WithMany(d => d.Clauses)
                .HasForeignKey(e => e.DocumentAnalysisResultId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasIndex(e => e.DocumentAnalysisResultId);
            entity.HasIndex(e => e.RiskLevel);
        });

        // Configuration pour RiskAssessment
        modelBuilder.Entity<RiskAssessment>(entity =>
        {
            entity.ToTable("risk_assessments");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.RiskType)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.Description)
                .IsRequired();
                
            entity.Property(e => e.Severity)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.Probability)
                .HasPrecision(5, 4);
                
            entity.Property(e => e.Impact)
                .IsRequired();
                
            entity.Property(e => e.Mitigation)
                .IsRequired();
            
            entity.Property(e => e.AffectedClauses)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>());

            // Relation avec DocumentAnalysisResult
            entity.HasOne(e => e.DocumentAnalysisResult)
                .WithMany(d => d.Risks)
                .HasForeignKey(e => e.DocumentAnalysisResultId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasIndex(e => e.DocumentAnalysisResultId);
            entity.HasIndex(e => e.Severity);
        });

        // Configuration pour DocumentRecommendation
        modelBuilder.Entity<DocumentRecommendation>(entity =>
        {
            entity.ToTable("document_recommendations");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Type)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(500);
                
            entity.Property(e => e.Description)
                .IsRequired();
                
            entity.Property(e => e.Priority)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.SuggestedAction);
            entity.Property(e => e.LegalBasis);
            
            entity.Property(e => e.RelatedClauses)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>());

            // Relation avec DocumentAnalysisResult
            entity.HasOne(e => e.DocumentAnalysisResult)
                .WithMany(d => d.Recommendations)
                .HasForeignKey(e => e.DocumentAnalysisResultId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasIndex(e => e.DocumentAnalysisResultId);
            entity.HasIndex(e => e.Priority);
        });

        // Configuration pour ExtractedEntity
        modelBuilder.Entity<ExtractedEntity>(entity =>
        {
            entity.ToTable("extracted_entities");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Text)
                .IsRequired()
                .HasMaxLength(1000);
                
            entity.Property(e => e.Type)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.ConfidenceScore)
                .HasPrecision(5, 4);
                
            entity.Property(e => e.NormalizedValue)
                .HasMaxLength(1000);
            
            entity.Property(e => e.Metadata)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

            // Relation avec DocumentAnalysisResult
            entity.HasOne(e => e.DocumentAnalysisResult)
                .WithMany(d => d.Entities)
                .HasForeignKey(e => e.DocumentAnalysisResultId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasIndex(e => e.DocumentAnalysisResultId);
            entity.HasIndex(e => e.Type);
        });

        // Configuration pour DocumentCitation
        modelBuilder.Entity<DocumentCitation>(entity =>
        {
            entity.ToTable("document_citations");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Type)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(500);
                
            entity.Property(e => e.Source)
                .IsRequired()
                .HasMaxLength(500);
                
            entity.Property(e => e.Url)
                .HasMaxLength(1000);
                
            entity.Property(e => e.Reference)
                .HasMaxLength(200);
                
            entity.Property(e => e.RelevanceScore)
                .HasPrecision(5, 4);
                
            entity.Property(e => e.Context)
                .IsRequired();

            // Relation avec DocumentAnalysisResult
            entity.HasOne(e => e.DocumentAnalysisResult)
                .WithMany(d => d.Citations)
                .HasForeignKey(e => e.DocumentAnalysisResultId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasIndex(e => e.DocumentAnalysisResultId);
            entity.HasIndex(e => e.Type);
        });
    }
}
