# 🚀 Service d'Embedding Local - LexAI

Alternative **gratuite** et **locale** à OpenAI pour le développement.

## 🎯 Avantages

- ✅ **100% Gratuit** - Pas de coûts d'API
- ✅ **Local** - Fonctionne sans connexion internet
- ✅ **Rapide** - Pas de latence réseau
- ✅ **Privé** - Vos données restent locales
- ✅ **Compatible** - API compatible OpenAI

## 📋 Prérequis

- **Python 3.8+** installé
- **4GB RAM** minimum
- **2GB espace disque** pour le modèle

## 🛠️ Installation Rapide

### Windows
```bash
# Double-cliquer sur le fichier
start_local_embedding.bat
```

### Linux/Mac
```bash
# Rendre le script exécutable
chmod +x start_local_embedding.sh

# Démarrer le service
./start_local_embedding.sh
```

### Installation Manuelle
```bash
# Créer un environnement virtuel
python -m venv venv

# Activer l'environnement (Windows)
venv\Scripts\activate

# Activer l'environnement (Linux/Mac)
source venv/bin/activate

# Installer les dépendances
pip install -r requirements.txt

# Démarrer le service
python local_embedding_service.py
```

## 🔧 Configuration Backend

Le backend est déjà configuré pour utiliser le service local :

```json
{
  "Processing": {
    "DefaultEmbeddingModel": "HuggingFace"
  },
  "LocalEmbedding": {
    "BaseUrl": "http://localhost:8000"
  }
}
```

## 🧪 Test du Service

### 1. Vérification de Santé
```bash
curl http://localhost:8000/health
```

### 2. Test d'Embedding
```bash
curl -X POST http://localhost:8000/embeddings \
  -H "Content-Type: application/json" \
  -d '{"input": ["Hello world", "Test embedding"]}'
```

## 📊 Modèle Utilisé

- **Nom** : `all-MiniLM-L6-v2`
- **Taille** : ~90MB
- **Dimension** : 384
- **Langues** : Multilingue (français inclus)
- **Performance** : Excellente pour les documents juridiques

## 🔄 Basculer vers OpenAI

Pour revenir à OpenAI quand vous avez des crédits :

### 1. Modifier la Configuration
```json
{
  "Processing": {
    "DefaultEmbeddingModel": "OpenAISmall"
  }
}
```

### 2. Redémarrer le Service
```bash
dotnet run --project LexAI.DataPreprocessing.API
```

## 🐛 Dépannage

### Service ne démarre pas
```bash
# Vérifier Python
python --version

# Vérifier les dépendances
pip list
```

### Port 8000 occupé
```bash
# Windows
netstat -ano | findstr :8000

# Linux/Mac
lsof -i :8000
```

### Erreur de mémoire
- Fermer d'autres applications
- Minimum 4GB RAM requis

## 📈 Performance

| Métrique | Local | OpenAI |
|----------|-------|--------|
| **Coût** | Gratuit | $0.02/1M tokens |
| **Latence** | ~100ms | ~500ms |
| **Débit** | 50 req/s | 60 req/s |
| **Offline** | ✅ | ❌ |

## 🔒 Sécurité

- Aucune donnée envoyée à l'extérieur
- Traitement 100% local
- Pas de logs externes
- Conforme RGPD

## 🚀 Prêt à Tester !

1. **Démarrer le service local** : `start_local_embedding.bat`
2. **Vérifier** : http://localhost:8000/health
3. **Uploader un document** dans LexAI
4. **Observer** : Plus d'erreurs OpenAI !

Le service est maintenant **gratuit** et **local** ! 🎉
