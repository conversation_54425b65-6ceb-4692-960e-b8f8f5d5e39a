<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\LexAI.Shared.Application\LexAI.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\LexAI.Shared.Domain\LexAI.Shared.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
  </ItemGroup>

</Project>
