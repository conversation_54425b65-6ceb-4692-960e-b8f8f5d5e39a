using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using LexAI.Shared.Domain.Common;

namespace LexAI.LegalResearch.Domain.Entities;

/// <summary>
/// Represents a legal research search query
/// </summary>
public class SearchQuery : BaseEntity
{
    /// <summary>
    /// Original query text from user
    /// </summary>
    public string OriginalQuery { get; private set; } = string.Empty;

    /// <summary>
    /// Processed/normalized query text
    /// </summary>
    public string ProcessedQuery { get; private set; } = string.Empty;

    /// <summary>
    /// Query intent classification
    /// </summary>
    public QueryIntent Intent { get; private set; }

    /// <summary>
    /// Legal domain filter
    /// </summary>
    public LegalDomain? DomainFilter { get; private set; }

    /// <summary>
    /// Document type filter
    /// </summary>
    public DocumentType? TypeFilter { get; private set; }

    /// <summary>
    /// Date range filter
    /// </summary>
    public DateRange? DateFilter { get; private set; }

    /// <summary>
    /// Language filter
    /// </summary>
    public string? LanguageFilter { get; private set; }

    /// <summary>
    /// Search parameters
    /// </summary>
    public SearchParameters Parameters { get; private set; } = null!;

    /// <summary>
    /// User who performed the search
    /// </summary>
    public Guid UserId { get; private set; }

    /// <summary>
    /// Session identifier for grouping related searches
    /// </summary>
    public string SessionId { get; private set; } = string.Empty;

    /// <summary>
    /// Search execution time in milliseconds
    /// </summary>
    public long ExecutionTimeMs { get; private set; }

    /// <summary>
    /// Number of results found
    /// </summary>
    public int ResultCount { get; private set; }

    /// <summary>
    /// Search results
    /// </summary>
    public List<SearchResult> Results { get; private set; } = new();

    /// <summary>
    /// Query embedding vector for similarity search
    /// </summary>
    public float[]? EmbeddingVector { get; private set; }

    /// <summary>
    /// Search status
    /// </summary>
    public SearchStatus Status { get; private set; }

    /// <summary>
    /// Error message if search failed
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Search quality score (0-1)
    /// </summary>
    public double QualityScore { get; private set; }

    /// <summary>
    /// Whether the search was cached
    /// </summary>
    public bool IsCached { get; private set; }

    /// <summary>
    /// Cache key for the search
    /// </summary>
    public string? CacheKey { get; private set; }

    /// <summary>
    /// User feedback on search quality
    /// </summary>
    public UserFeedback? Feedback { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private SearchQuery() { }

    /// <summary>
    /// Creates a new search query
    /// </summary>
    /// <param name="originalQuery">Original query text</param>
    /// <param name="userId">User performing the search</param>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>New search query instance</returns>
    public static SearchQuery Create(string originalQuery, Guid userId, string sessionId)
    {
        if (string.IsNullOrWhiteSpace(originalQuery))
            throw new ArgumentException("Query cannot be empty", nameof(originalQuery));

        if (userId == Guid.Empty)
            throw new ArgumentException("UserId cannot be empty", nameof(userId));

        if (string.IsNullOrWhiteSpace(sessionId))
            throw new ArgumentException("SessionId cannot be empty", nameof(sessionId));

        var query = new SearchQuery
        {
            Id = Guid.NewGuid(),
            OriginalQuery = originalQuery.Trim(),
            ProcessedQuery = originalQuery.Trim(),
            UserId = userId,
            SessionId = sessionId,
            Status = SearchStatus.Pending,
            Intent = QueryIntent.Unknown,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId.ToString()
        };

        // Initialize default search parameters
        query.Parameters = SearchParameters.CreateDefault();

        return query;
    }

    /// <summary>
    /// Sets the processed query text
    /// </summary>
    /// <param name="processedQuery">Processed query text</param>
    public void SetProcessedQuery(string processedQuery)
    {
        if (string.IsNullOrWhiteSpace(processedQuery))
            throw new ArgumentException("Processed query cannot be empty", nameof(processedQuery));

        ProcessedQuery = processedQuery.Trim();
    }

    /// <summary>
    /// Sets the query intent
    /// </summary>
    /// <param name="intent">Query intent</param>
    public void SetIntent(QueryIntent intent)
    {
        Intent = intent;
    }

    /// <summary>
    /// Sets search filters
    /// </summary>
    /// <param name="domainFilter">Legal domain filter</param>
    /// <param name="typeFilter">Document type filter</param>
    /// <param name="dateFilter">Date range filter</param>
    /// <param name="languageFilter">Language filter</param>
    public void SetFilters(
        LegalDomain? domainFilter = null,
        DocumentType? typeFilter = null,
        DateRange? dateFilter = null,
        string? languageFilter = null)
    {
        DomainFilter = domainFilter;
        TypeFilter = typeFilter;
        DateFilter = dateFilter;
        LanguageFilter = languageFilter;
    }

    /// <summary>
    /// Sets search parameters
    /// </summary>
    /// <param name="parameters">Search parameters</param>
    public void SetParameters(SearchParameters parameters)
    {
        Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
    }

    /// <summary>
    /// Sets the query embedding vector
    /// </summary>
    /// <param name="embeddingVector">Embedding vector</param>
    public void SetEmbeddingVector(float[] embeddingVector)
    {
        EmbeddingVector = embeddingVector ?? throw new ArgumentNullException(nameof(embeddingVector));
    }

    /// <summary>
    /// Marks the search as started
    /// </summary>
    public void MarkAsStarted()
    {
        Status = SearchStatus.InProgress;
    }

    /// <summary>
    /// Marks the search as completed with results
    /// </summary>
    /// <param name="results">Search results</param>
    /// <param name="executionTimeMs">Execution time in milliseconds</param>
    /// <param name="qualityScore">Search quality score</param>
    /// <param name="isCached">Whether results were cached</param>
    /// <param name="cacheKey">Cache key if applicable</param>
    public void MarkAsCompleted(
        IEnumerable<SearchResult> results,
        long executionTimeMs,
        double qualityScore = 0.0,
        bool isCached = false,
        string? cacheKey = null)
    {
        if (results == null)
            throw new ArgumentNullException(nameof(results));

        Results.Clear();
        Results.AddRange(results);
        ResultCount = Results.Count;
        ExecutionTimeMs = executionTimeMs;
        QualityScore = Math.Max(0.0, Math.Min(1.0, qualityScore));
        IsCached = isCached;
        CacheKey = cacheKey;
        Status = SearchStatus.Completed;
    }

    /// <summary>
    /// Marks the search as failed
    /// </summary>
    /// <param name="errorMessage">Error message</param>
    /// <param name="executionTimeMs">Execution time in milliseconds</param>
    public void MarkAsFailed(string errorMessage, long executionTimeMs = 0)
    {
        if (string.IsNullOrWhiteSpace(errorMessage))
            throw new ArgumentException("Error message cannot be empty", nameof(errorMessage));

        Status = SearchStatus.Failed;
        ErrorMessage = errorMessage;
        ExecutionTimeMs = executionTimeMs;
    }

    /// <summary>
    /// Adds user feedback for the search
    /// </summary>
    /// <param name="feedback">User feedback</param>
    public void AddFeedback(UserFeedback feedback)
    {
        Feedback = feedback ?? throw new ArgumentNullException(nameof(feedback));
    }

    /// <summary>
    /// Checks if the search was successful
    /// </summary>
    /// <returns>True if search completed successfully</returns>
    public bool IsSuccessful()
    {
        return Status == SearchStatus.Completed && ResultCount > 0;
    }

    /// <summary>
    /// Gets the search effectiveness score based on results and feedback
    /// </summary>
    /// <returns>Effectiveness score (0-1)</returns>
    public double GetEffectivenessScore()
    {
        if (Status != SearchStatus.Completed)
            return 0.0;

        var baseScore = QualityScore;

        // Adjust based on result count
        if (ResultCount == 0)
            baseScore *= 0.1;
        else if (ResultCount < 5)
            baseScore *= 0.8;
        else if (ResultCount > 100)
            baseScore *= 0.9;

        // Adjust based on user feedback
        if (Feedback != null)
        {
            baseScore = (baseScore + Feedback.GetOverallRating() / 5.0) / 2.0;
        }

        return Math.Max(0.0, Math.Min(1.0, baseScore));
    }

    /// <summary>
    /// Gets the top N search results
    /// </summary>
    /// <param name="count">Number of results to return</param>
    /// <returns>Top search results</returns>
    public IEnumerable<SearchResult> GetTopResults(int count = 10)
    {
        return Results
            .OrderByDescending(r => r.RelevanceScore)
            .Take(count);
    }
}
