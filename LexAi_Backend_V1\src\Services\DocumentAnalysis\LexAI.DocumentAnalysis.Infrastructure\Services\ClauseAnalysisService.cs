using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service d'analyse de clauses juridiques utilisant l'IA
/// </summary>
public class ClauseAnalysisService : IClauseAnalysisService
{
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<ClauseAnalysisService> _logger;
    private readonly IConfiguration _configuration;

    public ClauseAnalysisService(
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<ClauseAnalysisService> logger)
    {
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Analyse les clauses d'un document juridique
    /// </summary>
    public async Task<List<ClauseAnalysisDto>> AnalyzeClausesAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting clause analysis for document type: {DocumentType}", documentType);

        try
        {
            // 1. Identifier et extraire les clauses
            var clauses = await IdentifyClausesAsync(documentText, documentType, cancellationToken);

            // 2. Analyser chaque clause individuellement
            var analyzedClauses = new List<ClauseAnalysisDto>();
            
            foreach (var clause in clauses)
            {
                var analysis = await AnalyzeIndividualClauseAsync(clause, documentType, cancellationToken);
                analyzedClauses.Add(analysis);
            }

            _logger.LogInformation("Clause analysis completed. Found {ClauseCount} clauses", analyzedClauses.Count);
            return analyzedClauses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during clause analysis");
            throw;
        }
    }

    /// <summary>
    /// Identifie les clauses critiques dans un document
    /// </summary>
    public async Task<List<ClauseAnalysisDto>> IdentifyCriticalClausesAsync(
        string documentText, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Identifying critical clauses in document");

        var prompt = $@"Identifiez les clauses les plus critiques et à risque dans ce document juridique.
Concentrez-vous sur les clauses qui pourraient présenter des risques juridiques, financiers ou opérationnels significatifs.

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans texte avant ou après. N'utilisez pas de retours à la ligne dans les valeurs JSON.

Répondez en JSON avec ce format:
{{
  ""criticalClauses"": [
    {{
      ""clauseText"": ""texte de la clause"",
      ""clauseType"": ""type de clause"",
      ""riskLevel"": ""High|Critical"",
      ""riskDescription"": ""description du risque"",
      ""startPosition"": 0,
      ""endPosition"": 100,
      ""confidence"": 0.95
    }}
  ]
}}

Document: {documentText.Substring(0, Math.Min(documentText.Length, 8000))}";

        var options = LLMServiceExtensions.ForLegalAnalysis(4000);
        var result = await _llmService.AnalyzeStructuredAsync<CriticalClausesResponse>(
            documentText, prompt, options, cancellationToken);

        if (result?.CriticalClauses == null)
        {
            _logger.LogWarning("No critical clauses identified or parsing failed");
            return new List<ClauseAnalysisDto>();
        }

        var criticalClauses = result.CriticalClauses.Select(c => new ClauseAnalysisDto
        {
            Id = Guid.NewGuid(),
            ClauseText = c.ClauseText,
            ClauseType = c.ClauseType,
            Analysis = c.RiskDescription,
            RiskLevel = c.RiskLevel,
            ConfidenceScore = c.Confidence,
            StartPosition = c.StartPosition,
            EndPosition = c.EndPosition,
            Tags = new List<string> { "Critical", "High-Risk" }
        }).ToList();

        _logger.LogInformation("Identified {CriticalCount} critical clauses", criticalClauses.Count);
        return criticalClauses;
    }

    /// <summary>
    /// Suggère des révisions pour une clause
    /// </summary>
    public async Task<string> SuggestClauseRevisionAsync(
        string clauseText, 
        string riskLevel, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating clause revision suggestion for risk level: {RiskLevel}", riskLevel);

        var prompt = $@"Analysez cette clause juridique et proposez une révision améliorée qui réduit les risques identifiés.
Expliquez les changements proposés et pourquoi ils améliorent la clause.

Clause originale: {clauseText}
Niveau de risque actuel: {riskLevel}

Fournissez:
1. La clause révisée
2. Explication des changements
3. Justification juridique
4. Réduction de risque attendue";

        var options = LLMServiceExtensions.ForLegalAnalysis(3000);
        var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to generate clause revision: {Error}", result.Error);
            return "Impossible de générer une révision pour cette clause.";
        }

        return result.Content;
    }

    // Méthodes privées

    private async Task<List<IdentifiedClause>> IdentifyClausesAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Identifiez et extrayez toutes les clauses importantes de ce document juridique de type {documentType}.
Segmentez le document en clauses distinctes avec leur type et position.

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans retours à la ligne dans les valeurs.

Répondez en JSON avec ce format:
{{
  ""clauses"": [
    {{
      ""clauseText"": ""texte complet de la clause"",
      ""clauseType"": ""type de clause (ex: paiement, résiliation, responsabilité, etc.)"",
      ""startPosition"": 0,
      ""endPosition"": 100,
      ""importance"": ""High|Medium|Low""
    }}
  ]
}}

Document: {documentText.Substring(0, Math.Min(documentText.Length, 10000))}";

        var options = LLMServiceExtensions.ForLegalAnalysis(6000);
        var result = await _llmService.AnalyzeStructuredAsync<ClauseIdentificationResponse>(
            documentText, prompt, options, cancellationToken);

        return result?.Clauses ?? new List<IdentifiedClause>();
    }

    private async Task<ClauseAnalysisDto> AnalyzeIndividualClauseAsync(
        IdentifiedClause clause, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Analysez en détail cette clause juridique d'un document de type {documentType}.
Évaluez les risques, la clarté, l'équité et la conformité légale.

Clause: {clause.ClauseText}
Type: {clause.ClauseType}

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans retours à la ligne dans les valeurs.

Répondez en JSON avec ce format:
{{
  ""analysis"": ""analyse détaillée de la clause"",
  ""riskLevel"": ""Low|Medium|High|Critical"",
  ""riskFactors"": [""facteur1"", ""facteur2""],
  ""suggestedRevision"": ""révision suggérée ou null"",
  ""legalConcerns"": [""préoccupation1"", ""préoccupation2""],
  ""confidence"": 0.95,
  ""tags"": [""tag1"", ""tag2""]
}}";

        var options = LLMServiceExtensions.ForLegalAnalysis(3000);
        var result = await _llmService.AnalyzeStructuredAsync<ClauseAnalysisResponse>(
            clause.ClauseText, prompt, options, cancellationToken);

        if (result == null)
        {
            // Fallback en cas d'échec de parsing
            return new ClauseAnalysisDto
            {
                Id = Guid.NewGuid(),
                ClauseText = clause.ClauseText,
                ClauseType = clause.ClauseType,
                Analysis = "Analyse non disponible",
                RiskLevel = "Medium",
                ConfidenceScore = 0.5,
                StartPosition = clause.StartPosition,
                EndPosition = clause.EndPosition
            };
        }

        return new ClauseAnalysisDto
        {
            Id = Guid.NewGuid(),
            ClauseText = clause.ClauseText,
            ClauseType = clause.ClauseType,
            Analysis = result.Analysis,
            RiskLevel = result.RiskLevel,
            ConfidenceScore = result.Confidence,
            StartPosition = clause.StartPosition,
            EndPosition = clause.EndPosition,
            SuggestedRevision = result.SuggestedRevision,
            Tags = result.Tags ?? new List<string>()
        };
    }

    // Classes pour la désérialisation JSON

    private class ClauseIdentificationResponse
    {
        public List<IdentifiedClause> Clauses { get; set; } = new();
    }

    private class IdentifiedClause
    {
        public string ClauseText { get; set; } = string.Empty;
        public string ClauseType { get; set; } = string.Empty;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public string Importance { get; set; } = string.Empty;
    }

    private class ClauseAnalysisResponse
    {
        public string Analysis { get; set; } = string.Empty;
        public string RiskLevel { get; set; } = string.Empty;
        public List<string>? RiskFactors { get; set; }
        public string? SuggestedRevision { get; set; }
        public List<string>? LegalConcerns { get; set; }
        public double Confidence { get; set; }
        public List<string>? Tags { get; set; }
    }

    private class CriticalClausesResponse
    {
        public List<CriticalClauseInfo> CriticalClauses { get; set; } = new();
    }

    private class CriticalClauseInfo
    {
        public string ClauseText { get; set; } = string.Empty;
        public string ClauseType { get; set; } = string.Empty;
        public string RiskLevel { get; set; } = string.Empty;
        public string RiskDescription { get; set; } = string.Empty;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public double Confidence { get; set; }
    }
}
