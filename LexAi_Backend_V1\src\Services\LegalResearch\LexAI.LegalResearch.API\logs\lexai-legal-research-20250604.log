2025-06-04 13:21:20.059 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-04 13:21:20.104 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 13:21:20.217 +04:00 [INF] Now listening on: http://localhost:5002
2025-06-04 13:21:20.220 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 13:21:20.222 +04:00 [INF] Hosting environment: Development
2025-06-04 13:21:20.224 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-04 13:21:46.134 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger - null null
2025-06-04 13:21:46.227 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger - 301 0 null 98.0903ms
2025-06-04 13:21:46.256 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/index.html - null null
2025-06-04 13:21:46.298 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/index.html - 200 null text/html;charset=utf-8 41.9346ms
2025-06-04 13:21:46.511 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui.css - null null
2025-06-04 13:21:46.511 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/index.css - null null
2025-06-04 13:21:46.512 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-bundle.js - null null
2025-06-04 13:21:46.512 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-standalone-preset.js - null null
2025-06-04 13:21:46.513 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/index.js - null null
2025-06-04 13:21:46.527 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/index.js - 200 null application/javascript;charset=utf-8 13.9137ms
2025-06-04 13:21:46.548 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-04 13:21:46.554 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/index.css - 200 202 text/css 42.6948ms
2025-06-04 13:21:46.565 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-04 13:21:46.571 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-04 13:21:46.577 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui.css - 200 152035 text/css 66.1387ms
2025-06-04 13:21:46.578 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 65.3703ms
2025-06-04 13:21:46.635 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-04 13:21:46.639 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 127.2001ms
2025-06-04 13:21:47.104 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/favicon-32x32.png - null null
2025-06-04 13:21:47.104 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/swagger/v1/swagger.json - null null
2025-06-04 13:21:47.109 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-04 13:21:47.115 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/favicon-32x32.png - 200 628 image/png 11.0764ms
2025-06-04 13:21:47.250 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 146.7397ms
2025-06-04 13:30:48.691 +04:00 [INF] Application is shutting down...
2025-06-04 13:34:41.277 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-04 13:34:41.319 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 13:34:41.398 +04:00 [INF] Now listening on: http://localhost:5002
2025-06-04 13:34:41.401 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 13:34:41.402 +04:00 [INF] Hosting environment: Development
2025-06-04 13:34:41.403 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-04 13:36:10.133 +04:00 [INF] Request starting HTTP/1.1 POST http://localhost:5002/api/search/test - application/json 25
2025-06-04 13:36:10.165 +04:00 [INF] Request POST /api/search/test started with correlation ID 37fa9f98-fd6a-410c-8b99-dc61611b07a9
2025-06-04 13:36:10.168 +04:00 [WRN] Failed to determine the https port for redirect.
2025-06-04 13:36:10.205 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.TestSearch (LexAI.LegalResearch.API)'
2025-06-04 13:36:10.226 +04:00 [INF] Route matched with {action = "TestSearch", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.LegalResearch.Application.DTOs.SearchResponseDto]] TestSearch(LexAI.LegalResearch.Application.DTOs.SearchRequestDto) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-04 13:36:10.292 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-04 13:36:10.313 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.TestSearch (LexAI.LegalResearch.API) in 82.053ms
2025-06-04 13:36:10.325 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.TestSearch (LexAI.LegalResearch.API)'
2025-06-04 13:36:10.329 +04:00 [INF] Request POST /api/search/test completed in 161ms with status 400 (Correlation ID: 37fa9f98-fd6a-410c-8b99-dc61611b07a9)
2025-06-04 13:36:10.333 +04:00 [INF] Request finished HTTP/1.1 POST http://localhost:5002/api/search/test - 400 null application/json; charset=utf-8 201.5758ms
2025-06-04 13:41:43.785 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/api/search/suggestions?query=tes - null null
2025-06-04 13:41:43.805 +04:00 [INF] Request GET /api/search/suggestions started with correlation ID 7b169192-f1ed-44f0-a109-09f055e4d27b
2025-06-04 13:41:43.819 +04:00 [INF] CORS policy execution successful.
2025-06-04 13:41:43.837 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 13:41:43.848 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 13:41:43.850 +04:00 [INF] Request GET /api/search/suggestions completed in 36ms with status 401 (Correlation ID: 7b169192-f1ed-44f0-a109-09f055e4d27b)
2025-06-04 13:41:43.858 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/api/search/suggestions?query=tes - 401 0 null 73.5302ms
2025-06-04 13:42:23.723 +04:00 [INF] Request starting HTTP/1.1 GET http://localhost:5002/api/search/suggestions?query=tes - null null
2025-06-04 13:42:23.730 +04:00 [INF] Request GET /api/search/suggestions started with correlation ID 287e1d6a-e86c-471a-904b-0f54c06b02a6
2025-06-04 13:42:23.738 +04:00 [INF] CORS policy execution successful.
2025-06-04 13:42:23.746 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 13:42:23.750 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 13:42:23.753 +04:00 [INF] Request GET /api/search/suggestions completed in 16ms with status 401 (Correlation ID: 287e1d6a-e86c-471a-904b-0f54c06b02a6)
2025-06-04 13:42:23.760 +04:00 [INF] Request finished HTTP/1.1 GET http://localhost:5002/api/search/suggestions?query=tes - 401 0 null 37.1224ms
2025-06-04 13:44:13.222 +04:00 [INF] Application is shutting down...
2025-06-04 14:00:15.075 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-04 14:00:15.169 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 14:00:15.543 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-04 14:00:15.547 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-04 14:00:16.467 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 14:00:16.472 +04:00 [INF] Hosting environment: Development
2025-06-04 14:00:16.473 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-04 14:00:18.404 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-04 14:00:18.790 +04:00 [INF] Request GET / started with correlation ID 9d198995-25a2-41b4-b938-c6429cc15495
2025-06-04 14:00:18.955 +04:00 [INF] Request GET / completed in 160ms with status 404 (Correlation ID: 9d198995-25a2-41b4-b938-c6429cc15495)
2025-06-04 14:00:19.114 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 711.1633ms
2025-06-04 14:00:19.249 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-04 14:02:44.671 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/search/suggestions?query=tes - null null
2025-06-04 14:02:44.766 +04:00 [INF] Request GET /api/search/suggestions started with correlation ID 5c80999c-bf3e-47d2-850d-11d3a2dbdcd2
2025-06-04 14:02:44.776 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:02:44.793 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 14:02:44.804 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 14:02:44.805 +04:00 [INF] Request GET /api/search/suggestions completed in 33ms with status 401 (Correlation ID: 5c80999c-bf3e-47d2-850d-11d3a2dbdcd2)
2025-06-04 14:02:44.815 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/search/suggestions?query=tes - 401 0 null 144.3228ms
2025-06-04 14:03:56.152 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/search/suggestions?query=tes - null null
2025-06-04 14:03:56.170 +04:00 [INF] Request GET /api/search/suggestions started with correlation ID d49c577f-d6af-483b-8cef-43a28f936b30
2025-06-04 14:03:56.225 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:56.241 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 14:03:56.248 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 14:03:56.253 +04:00 [INF] Request GET /api/search/suggestions completed in 29ms with status 401 (Correlation ID: d49c577f-d6af-483b-8cef-43a28f936b30)
2025-06-04 14:03:56.258 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/search/suggestions?query=tes - 401 0 null 106.1613ms
2025-06-04 14:04:34.239 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/search/suggestions?query=tes - null null
2025-06-04 14:04:34.243 +04:00 [INF] Request GET /api/search/suggestions started with correlation ID 7457036b-9005-4633-984a-c0cc3d56bb64
2025-06-04 14:04:34.245 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:04:34.248 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 14:04:34.249 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 14:04:34.251 +04:00 [INF] Request GET /api/search/suggestions completed in 5ms with status 401 (Correlation ID: 7457036b-9005-4633-984a-c0cc3d56bb64)
2025-06-04 14:04:34.253 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/search/suggestions?query=tes - 401 0 null 14.2996ms
2025-06-04 14:16:43.876 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/search/test - null null
2025-06-04 14:16:43.886 +04:00 [INF] Request OPTIONS /api/search/test started with correlation ID 06643bce-4863-44f0-8635-edd4d7154a76
2025-06-04 14:16:43.890 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:16:43.897 +04:00 [INF] Request OPTIONS /api/search/test completed in 6ms with status 204 (Correlation ID: 06643bce-4863-44f0-8635-edd4d7154a76)
2025-06-04 14:16:43.908 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/search/test - 204 null null 31.5931ms
2025-06-04 14:16:43.913 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/search/test - application/json 152
2025-06-04 14:16:43.931 +04:00 [INF] Request POST /api/search/test started with correlation ID f2b5e91d-a72f-402a-a42c-ba68e06d059e
2025-06-04 14:16:43.938 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:16:43.945 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.TestSearch (LexAI.LegalResearch.API)'
2025-06-04 14:16:44.070 +04:00 [INF] Route matched with {action = "TestSearch", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.LegalResearch.Application.DTOs.SearchResponseDto]] TestSearch(LexAI.LegalResearch.Application.DTOs.SearchRequestDto) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-04 14:16:44.354 +04:00 [INF] Test search request: test
2025-06-04 14:16:44.367 +04:00 [INF] Test search completed. Results: 2, Time: 150ms
2025-06-04 14:16:44.400 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.LegalResearch.Application.DTOs.SearchResponseDto'.
2025-06-04 14:16:44.585 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.TestSearch (LexAI.LegalResearch.API) in 504.0835ms
2025-06-04 14:16:44.590 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.TestSearch (LexAI.LegalResearch.API)'
2025-06-04 14:16:44.595 +04:00 [INF] Request POST /api/search/test completed in 657ms with status 200 (Correlation ID: f2b5e91d-a72f-402a-a42c-ba68e06d059e)
2025-06-04 14:16:44.607 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/search/test - 200 null application/json; charset=utf-8 694.4101ms
2025-06-04 14:38:01.851 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-04 14:38:01.942 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 14:38:02.301 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-04 14:38:02.303 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-04 14:38:02.377 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 14:38:02.379 +04:00 [INF] Hosting environment: Development
2025-06-04 14:38:02.382 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-04 14:38:06.161 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-04 14:38:06.627 +04:00 [INF] Request GET / started with correlation ID 7cc3981d-92ad-445b-861e-fcdfc61457eb
2025-06-04 14:38:06.843 +04:00 [INF] Request GET / completed in 210ms with status 404 (Correlation ID: 7cc3981d-92ad-445b-861e-fcdfc61457eb)
2025-06-04 14:38:06.860 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 703.4857ms
2025-06-04 14:38:06.884 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-04 20:20:25.110 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-04 20:20:25.234 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 20:20:25.635 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-04 20:20:25.637 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-04 20:20:25.733 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 20:20:25.735 +04:00 [INF] Hosting environment: Development
2025-06-04 20:20:25.737 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-04 20:20:30.843 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-04 20:20:31.363 +04:00 [INF] Request GET / started with correlation ID a4a66dee-870c-424c-8ab2-14cd6abd809d
2025-06-04 20:20:31.736 +04:00 [INF] Request GET / completed in 336ms with status 404 (Correlation ID: a4a66dee-870c-424c-8ab2-14cd6abd809d)
2025-06-04 20:20:31.777 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 936.7565ms
2025-06-04 20:20:31.866 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
