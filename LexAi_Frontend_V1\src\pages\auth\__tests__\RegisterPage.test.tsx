import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { RegisterPage } from '../RegisterPage'
import { useAuthStore } from '../../../store/authStore'

// Mock du store d'authentification
jest.mock('../../../store/authStore')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock de react-router-dom
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}))

// Wrapper pour les tests avec Router
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

describe('RegisterPage', () => {
  const mockRegister = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseAuthStore.mockReturnValue({
      register: mockRegister,
      isLoading: false,
      user: null,
      isAuthenticated: false,
      accessToken: null,
      refreshToken: null,
      login: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      setUser: jest.fn(),
      setLoading: jest.fn(),
      setTokens: jest.fn(),
      clearTokens: jest.fn(),
    })
  })

  it('should render registration form', () => {
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    expect(screen.getByText('Créer un compte LexAi')).toBeInTheDocument()
    expect(screen.getByLabelText(/prénom/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/nom/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/mot de passe/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirmer le mot de passe/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/rôle/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /créer mon compte/i })).toBeInTheDocument()
  })

  it('should show validation errors for empty required fields', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    const submitButton = screen.getByRole('button', { name: /créer mon compte/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/le prénom doit contenir au moins 2 caractères/i)).toBeInTheDocument()
      expect(screen.getByText(/le nom doit contenir au moins 2 caractères/i)).toBeInTheDocument()
      expect(screen.getByText(/email invalide/i)).toBeInTheDocument()
    })
  })

  it('should show password validation error for weak password', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    const passwordInput = screen.getByLabelText(/^mot de passe/i)
    await user.type(passwordInput, 'weak')

    const submitButton = screen.getByRole('button', { name: /créer mon compte/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/le mot de passe doit contenir au moins une minuscule/i)).toBeInTheDocument()
    })
  })

  it('should show error when passwords do not match', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    const passwordInput = screen.getByLabelText(/^mot de passe/i)
    const confirmPasswordInput = screen.getByLabelText(/confirmer le mot de passe/i)

    await user.type(passwordInput, 'Password123!')
    await user.type(confirmPasswordInput, 'DifferentPassword123!')

    const submitButton = screen.getByRole('button', { name: /créer mon compte/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/les mots de passe ne correspondent pas/i)).toBeInTheDocument()
    })
  })

  it('should show error when terms are not accepted', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    // Remplir tous les champs requis sauf les conditions
    await user.type(screen.getByLabelText(/prénom/i), 'John')
    await user.type(screen.getByLabelText(/nom/i), 'Doe')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^mot de passe/i), 'Password123!')
    await user.type(screen.getByLabelText(/confirmer le mot de passe/i), 'Password123!')
    await user.selectOptions(screen.getByLabelText(/rôle/i), 'Client')

    // Accepter seulement la politique de confidentialité
    await user.click(screen.getByLabelText(/politique de confidentialité/i))

    const submitButton = screen.getByRole('button', { name: /créer mon compte/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/vous devez accepter les conditions d'utilisation/i)).toBeInTheDocument()
    })
  })

  it('should submit form with valid data', async () => {
    const user = userEvent.setup()
    mockRegister.mockResolvedValue(undefined)
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    // Remplir le formulaire
    await user.type(screen.getByLabelText(/prénom/i), 'John')
    await user.type(screen.getByLabelText(/nom/i), 'Doe')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/téléphone/i), '+33123456789')
    await user.type(screen.getByLabelText(/^mot de passe/i), 'Password123!')
    await user.type(screen.getByLabelText(/confirmer le mot de passe/i), 'Password123!')
    await user.selectOptions(screen.getByLabelText(/rôle/i), 'Client')
    await user.click(screen.getByLabelText(/conditions d'utilisation/i))
    await user.click(screen.getByLabelText(/politique de confidentialité/i))

    const submitButton = screen.getByRole('button', { name: /créer mon compte/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+33123456789',
        password: 'Password123!',
        confirmPassword: 'Password123!',
        role: 'Client',
        preferredLanguage: 'fr-FR',
        timeZone: 'Europe/Paris',
        acceptTerms: true,
        acceptPrivacyPolicy: true,
      })
    })

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard')
  })

  it('should show loading state during registration', async () => {
    const user = userEvent.setup()
    mockUseAuthStore.mockReturnValue({
      register: mockRegister,
      isLoading: true,
      user: null,
      isAuthenticated: false,
      accessToken: null,
      refreshToken: null,
      login: jest.fn(),
      logout: jest.fn(),
      updateProfile: jest.fn(),
      setUser: jest.fn(),
      setLoading: jest.fn(),
      setTokens: jest.fn(),
      clearTokens: jest.fn(),
    })
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    expect(screen.getByText(/création du compte.../i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /création du compte.../i })).toBeDisabled()
  })

  it('should show error message when registration fails', async () => {
    const user = userEvent.setup()
    const errorMessage = 'Email already exists'
    mockRegister.mockRejectedValue(new Error(errorMessage))
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    // Remplir le formulaire avec des données valides
    await user.type(screen.getByLabelText(/prénom/i), 'John')
    await user.type(screen.getByLabelText(/nom/i), 'Doe')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^mot de passe/i), 'Password123!')
    await user.type(screen.getByLabelText(/confirmer le mot de passe/i), 'Password123!')
    await user.selectOptions(screen.getByLabelText(/rôle/i), 'Client')
    await user.click(screen.getByLabelText(/conditions d'utilisation/i))
    await user.click(screen.getByLabelText(/politique de confidentialité/i))

    const submitButton = screen.getByRole('button', { name: /créer mon compte/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  it('should toggle password visibility', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    const passwordInput = screen.getByLabelText(/^mot de passe/i)
    const toggleButton = passwordInput.parentElement?.querySelector('button')

    expect(passwordInput).toHaveAttribute('type', 'password')

    if (toggleButton) {
      await user.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'text')

      await user.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'password')
    }
  })

  it('should show role descriptions when role is selected', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    const roleSelect = screen.getByLabelText(/rôle/i)
    await user.selectOptions(roleSelect, 'SeniorLawyer')

    await waitFor(() => {
      expect(screen.getByText(/avocat expérimenté avec responsabilités de gestion/i)).toBeInTheDocument()
    })
  })

  it('should not allow Administrator role selection', () => {
    render(
      <TestWrapper>
        <RegisterPage />
      </TestWrapper>
    )

    const roleSelect = screen.getByLabelText(/rôle/i)
    const adminOption = screen.queryByRole('option', { name: /administrateur/i })

    expect(adminOption).not.toBeInTheDocument()
  })
})
