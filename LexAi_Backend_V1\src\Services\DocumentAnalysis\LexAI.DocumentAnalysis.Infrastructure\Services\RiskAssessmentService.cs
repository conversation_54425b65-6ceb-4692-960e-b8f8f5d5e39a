using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service d'évaluation des risques juridiques utilisant l'IA
/// </summary>
public class RiskAssessmentService : IRiskAssessmentService
{
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<RiskAssessmentService> _logger;
    private readonly IConfiguration _configuration;

    public RiskAssessmentService(
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<RiskAssessmentService> logger)
    {
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Évalue les risques d'un document juridique
    /// </summary>
    public async Task<List<RiskAssessmentDto>> AssessDocumentRisksAsync(
        string documentText, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting risk assessment for document type: {DocumentType}", documentType);

        try
        {
            // 1. Évaluation des risques globaux du document
            var globalRisks = await AssessGlobalRisksAsync(documentText, documentType, cancellationToken);

            // 2. Évaluation des risques spécifiques aux clauses
            var clauseRisks = await AssessClauseSpecificRisksAsync(clauses, cancellationToken);

            // 3. Évaluation des risques de conformité
            var complianceRisks = await AssessComplianceRisksAsync(documentText, documentType, cancellationToken);

            // 4. Consolidation et priorisation des risques
            var allRisks = new List<RiskAssessmentDto>();
            allRisks.AddRange(globalRisks);
            allRisks.AddRange(clauseRisks);
            allRisks.AddRange(complianceRisks);

            var prioritizedRisks = await PrioritizeRisksAsync(allRisks, cancellationToken);

            _logger.LogInformation("Risk assessment completed. Identified {RiskCount} risks", prioritizedRisks.Count);
            return prioritizedRisks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during risk assessment");
            throw;
        }
    }

    /// <summary>
    /// Calcule le niveau de risque global du document
    /// </summary>
    public async Task<string> CalculateOverallRiskLevelAsync(
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken = default)
    {
        if (!risks.Any())
            return "Low";

        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        // Logique de calcul du risque global
        var criticalCount = risks.Count(r => r.Severity == "Critical");
        var highCount = risks.Count(r => r.Severity == "High");
        var mediumCount = risks.Count(r => r.Severity == "Medium");

        if (criticalCount > 0)
            return "Critical";
        if (highCount >= 3)
            return "Critical";
        if (highCount >= 1)
            return "High";
        if (mediumCount >= 3)
            return "High";
        if (mediumCount >= 1)
            return "Medium";

        return "Low";
    }

    /// <summary>
    /// Génère des stratégies de mitigation pour un risque
    /// </summary>
    public async Task<List<string>> GenerateMitigationStrategiesAsync(
        RiskAssessmentDto risk, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating mitigation strategies for risk: {RiskType}", risk.RiskType);

        var prompt = $@"Générez des stratégies concrètes de mitigation pour ce risque juridique.
Proposez des actions pratiques et réalisables pour réduire ou éliminer ce risque.

Risque: {risk.RiskType}
Description: {risk.Description}
Sévérité: {risk.Severity}
Impact: {risk.Impact}

Fournissez 3-5 stratégies de mitigation spécifiques et actionnables:";

        var options = LLMServiceExtensions.ForLegalAnalysis(2000);
        var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

        if (!result.Success)
        {
            _logger.LogWarning("Failed to generate mitigation strategies: {Error}", result.Error);
            return new List<string> { "Consulter un avocat spécialisé pour ce type de risque." };
        }

        // Parser les stratégies de la réponse
        var strategies = result.Content
            .Split('\n', StringSplitOptions.RemoveEmptyEntries)
            .Where(line => line.Trim().Length > 10)
            .Select(line => line.Trim().TrimStart('-', '*', '•').Trim())
            .Take(5)
            .ToList();

        return strategies.Any() ? strategies : new List<string> { result.Content };
    }

    // Méthodes privées

    private async Task<List<RiskAssessmentDto>> AssessGlobalRisksAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Analysez ce document juridique de type {documentType} et identifiez les risques globaux.
Concentrez-vous sur les risques structurels, de conformité, et opérationnels.

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans retours à la ligne dans les valeurs.

Répondez en JSON avec ce format:
{{
  ""risks"": [
    {{
      ""riskType"": ""type de risque"",
      ""description"": ""description détaillée"",
      ""severity"": ""Low|Medium|High|Critical"",
      ""probability"": 0.7,
      ""impact"": ""description de l'impact"",
      ""affectedClauses"": [""clause1"", ""clause2""]
    }}
  ]
}}

Document: {documentText.Substring(0, Math.Min(documentText.Length, 8000))}";

        var options = LLMServiceExtensions.ForLegalAnalysis(4000);
        var result = await _llmService.AnalyzeStructuredAsync<RiskAssessmentResponse>(
            documentText, prompt, options, cancellationToken);

        return ConvertToRiskAssessmentDtos(result?.Risks ?? new List<RiskInfo>(), "Global");
    }

    private async Task<List<RiskAssessmentDto>> AssessClauseSpecificRisksAsync(
        List<ClauseAnalysisDto> clauses, 
        CancellationToken cancellationToken)
    {
        var risks = new List<RiskAssessmentDto>();

        foreach (var clause in clauses.Where(c => c.RiskLevel == "High" || c.RiskLevel == "Critical"))
        {
            var prompt = $@"Analysez les risques spécifiques de cette clause juridique.
Identifiez les risques potentiels liés à cette clause particulière.

Clause: {clause.ClauseText}
Type: {clause.ClauseType}
Niveau de risque détecté: {clause.RiskLevel}

Répondez en JSON avec le format standard des risques.";

            var options = LLMServiceExtensions.ForLegalAnalysis(2000);
            var result = await _llmService.AnalyzeStructuredAsync<RiskAssessmentResponse>(
                clause.ClauseText, prompt, options, cancellationToken);

            var clauseRisks = ConvertToRiskAssessmentDtos(result?.Risks ?? new List<RiskInfo>(), "Clause");
            risks.AddRange(clauseRisks);
        }

        return risks;
    }

    private async Task<List<RiskAssessmentDto>> AssessComplianceRisksAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Analysez ce document de type {documentType} pour identifier les risques de conformité légale.
Vérifiez la conformité aux lois et réglementations applicables.

Répondez en JSON avec le format standard des risques, en vous concentrant sur:
- Conformité RGPD
- Conformité au droit du travail
- Conformité au droit commercial
- Conformité aux réglementations sectorielles

Document: {documentText.Substring(0, Math.Min(documentText.Length, 6000))}";

        var options = LLMServiceExtensions.ForLegalAnalysis(3000);
        var result = await _llmService.AnalyzeStructuredAsync<RiskAssessmentResponse>(
            documentText, prompt, options, cancellationToken);

        return ConvertToRiskAssessmentDtos(result?.Risks ?? new List<RiskInfo>(), "Compliance");
    }

    private async Task<List<RiskAssessmentDto>> PrioritizeRisksAsync(
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        // Tri par sévérité puis par probabilité
        var severityOrder = new Dictionary<string, int>
        {
            { "Critical", 4 },
            { "High", 3 },
            { "Medium", 2 },
            { "Low", 1 }
        };

        return risks
            .OrderByDescending(r => severityOrder.GetValueOrDefault(r.Severity, 0))
            .ThenByDescending(r => r.Probability)
            .ToList();
    }

    private List<RiskAssessmentDto> ConvertToRiskAssessmentDtos(List<RiskInfo> risks, string category)
    {
        return risks.Select(r => new RiskAssessmentDto
        {
            Id = Guid.NewGuid(),
            RiskType = $"{category}: {r.RiskType}",
            Description = r.Description,
            Severity = r.Severity,
            Probability = r.Probability,
            Impact = r.Impact,
            Mitigation = "À définir", // Sera généré séparément
            AffectedClauses = r.AffectedClauses ?? new List<string>()
        }).ToList();
    }

    // Classes pour la désérialisation JSON

    private class RiskAssessmentResponse
    {
        public List<RiskInfo> Risks { get; set; } = new();
    }

    private class RiskInfo
    {
        public string RiskType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public double Probability { get; set; }
        public string Impact { get; set; } = string.Empty;
        public List<string>? AffectedClauses { get; set; }
    }
}
