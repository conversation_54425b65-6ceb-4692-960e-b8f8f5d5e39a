import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../ui/Tabs'
import {
  FileText,
  AlertTriangle,
  CheckCircle,
  Users,
  Calendar,
  DollarSign,
  MapPin,
  ExternalLink,
  Download,
  RefreshCw,
  Trash2,
  Eye,
  Shield
} from 'lucide-react'
import type { DocumentAnalysisResponse } from '../../services/documentAnalysis/types'

interface DocumentAnalysisDetailsProps {
  analysis: DocumentAnalysisResponse
  onRegenerate?: (analysisId: string) => void
  onDelete?: (analysisId: string) => void
  onExport?: (analysis: DocumentAnalysisResponse) => void
}

export const DocumentAnalysisDetails: React.FC<DocumentAnalysisDetailsProps> = ({
  analysis,
  onRegenerate,
  onDelete,
  onExport
}) => {
  const [activeTab, setActiveTab] = useState('overview')

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical':
        return 'destructive'
      case 'high':
        return 'destructive'
      case 'medium':
        return 'warning'
      case 'low':
        return 'success'
      default:
        return 'secondary'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'destructive'
      case 'high':
        return 'destructive'
      case 'medium':
        return 'warning'
      case 'low':
        return 'success'
      default:
        return 'secondary'
    }
  }

  const getEntityTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'person':
        return <Users className="h-4 w-4" />
      case 'organization':
        return <Users className="h-4 w-4" />
      case 'date':
        return <Calendar className="h-4 w-4" />
      case 'amount':
        return <DollarSign className="h-4 w-4" />
      case 'location':
        return <MapPin className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* En-tête */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {analysis.documentName}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Analysé le {new Date(analysis.analyzedAt).toLocaleDateString('fr-FR')} à {new Date(analysis.analyzedAt).toLocaleTimeString('fr-FR')}
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => onExport?.(analysis)}>
                <Download className="h-4 w-4 mr-2" />
                Exporter
              </Button>
              <Button variant="outline" size="sm" onClick={() => onRegenerate?.(analysis.id)}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Régénérer
              </Button>
              <Button variant="outline" size="sm" onClick={() => onDelete?.(analysis.id)}>
                <Trash2 className="h-4 w-4 mr-2" />
                Supprimer
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{analysis.clauses.length}</div>
              <div className="text-sm text-gray-600">Clauses</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{analysis.risks.length}</div>
              <div className="text-sm text-gray-600">Risques</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{analysis.recommendations.length}</div>
              <div className="text-sm text-gray-600">Recommandations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{analysis.entities.length}</div>
              <div className="text-sm text-gray-600">Entités</div>
            </div>
            <div className="text-center">
              <Badge variant={getRiskColor(analysis.summary.overallRiskLevel)} className="text-lg px-3 py-1">
                {analysis.summary.overallRiskLevel}
              </Badge>
              <div className="text-sm text-gray-600 mt-1">Risque global</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contenu principal avec onglets */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="clauses">Clauses</TabsTrigger>
          <TabsTrigger value="risks">Risques</TabsTrigger>
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
          <TabsTrigger value="entities">Entités</TabsTrigger>
          <TabsTrigger value="citations">Citations</TabsTrigger>
        </TabsList>

        {/* Vue d'ensemble */}
        <TabsContent value="overview" className="space-y-6">
          {/* Résumé exécutif */}
          <Card>
            <CardHeader>
              <CardTitle>Résumé exécutif</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">{analysis.summary.executiveSummary}</p>
            </CardContent>
          </Card>

          {/* Points clés */}
          {analysis.summary.keyPoints.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Points clés</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {analysis.summary.keyPoints.map((point, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{point}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Parties principales */}
          {analysis.summary.mainParties.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Parties principales</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {analysis.summary.mainParties.map((party, index) => (
                    <Badge key={index} variant="outline">
                      <Users className="h-3 w-3 mr-1" />
                      {party}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Analyse complète */}
          <Card>
            <CardHeader>
              <CardTitle>Analyse détaillée</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{analysis.analysisContent}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Clauses */}
        <TabsContent value="clauses" className="space-y-4">
          {analysis.clauses.map((clause) => (
            <Card key={clause.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{clause.clauseType}</CardTitle>
                  <Badge variant={getRiskColor(clause.riskLevel)}>
                    {clause.riskLevel}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Texte de la clause</h4>
                  <p className="text-gray-700 bg-gray-50 p-3 rounded-md">{clause.clauseText}</p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Analyse</h4>
                  <p className="text-gray-700">{clause.analysis}</p>
                </div>

                {clause.suggestedRevision && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Révision suggérée</h4>
                    <p className="text-gray-700 bg-blue-50 p-3 rounded-md border border-blue-200">
                      {clause.suggestedRevision}
                    </p>
                  </div>
                )}

                {clause.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {clause.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="text-sm text-gray-500">
                  Confiance: {(clause.confidenceScore * 100).toFixed(1)}%
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Risques */}
        <TabsContent value="risks" className="space-y-4">
          {analysis.risks.map((risk) => (
            <Card key={risk.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    {risk.riskType}
                  </CardTitle>
                  <Badge variant={getRiskColor(risk.severity)}>
                    {risk.severity}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700">{risk.description}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Impact</h4>
                  <p className="text-gray-700">{risk.impact}</p>
                </div>

                {risk.mitigation && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Stratégie de mitigation</h4>
                    <p className="text-gray-700 bg-green-50 p-3 rounded-md border border-green-200">
                      {risk.mitigation}
                    </p>
                  </div>
                )}

                {risk.affectedClauses.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Clauses affectées</h4>
                    <div className="flex flex-wrap gap-2">
                      {risk.affectedClauses.map((clause, index) => (
                        <Badge key={index} variant="outline">{clause}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="text-sm text-gray-500">
                  Probabilité: {(risk.probability * 100).toFixed(1)}%
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Recommandations */}
        <TabsContent value="recommendations" className="space-y-4">
          {analysis.recommendations.map((rec) => (
            <Card key={rec.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{rec.title}</CardTitle>
                  <Badge variant={getPriorityColor(rec.priority)}>
                    {rec.priority}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700">{rec.description}</p>
                </div>

                {rec.suggestedAction && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Action suggérée</h4>
                    <p className="text-gray-700 bg-blue-50 p-3 rounded-md border border-blue-200">
                      {rec.suggestedAction}
                    </p>
                  </div>
                )}

                {rec.legalBasis && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Base légale</h4>
                    <p className="text-gray-700">{rec.legalBasis}</p>
                  </div>
                )}

                {rec.relatedClauses.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Clauses liées</h4>
                    <div className="flex flex-wrap gap-2">
                      {rec.relatedClauses.map((clause, index) => (
                        <Badge key={index} variant="outline">{clause}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="text-sm text-gray-500">
                  Type: {rec.type}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Entités */}
        <TabsContent value="entities" className="space-y-4">
          <div className="grid gap-4">
            {analysis.entities.map((entity) => (
              <Card key={entity.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getEntityTypeIcon(entity.type)}
                      <div>
                        <div className="font-medium text-gray-900">{entity.text}</div>
                        <div className="text-sm text-gray-600">
                          {entity.type} • Confiance: {(entity.confidenceScore * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                    {entity.normalizedValue && (
                      <Badge variant="secondary">{entity.normalizedValue}</Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Citations */}
        <TabsContent value="citations" className="space-y-4">
          {analysis.citations.map((citation) => (
            <Card key={citation.id}>
              <CardHeader>
                <CardTitle className="text-lg">{citation.title}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Source</h4>
                  <p className="text-gray-700">{citation.source}</p>
                </div>

                {citation.context && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Contexte</h4>
                    <p className="text-gray-700 italic">"{citation.context}"</p>
                  </div>
                )}

                {citation.reference && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Référence</h4>
                    <p className="text-gray-700">{citation.reference}</p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    Pertinence: {(citation.relevanceScore * 100).toFixed(1)}%
                  </div>
                  {citation.url && (
                    <a
                      href={citation.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                    >
                      Voir la source
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>

      {/* Métadonnées */}
      <Card>
        <CardHeader>
          <CardTitle>Métadonnées de l'analyse</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-900">Statut</div>
              <div className="text-gray-600">{analysis.status}</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Temps de traitement</div>
              <div className="text-gray-600">{analysis.processingTimeMs}ms</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Tokens utilisés</div>
              <div className="text-gray-600">{analysis.tokensUsed}</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Coût estimé</div>
              <div className="text-gray-600">${analysis.estimatedCost.toFixed(4)}</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Modèle utilisé</div>
              <div className="text-gray-600">{analysis.modelUsed}</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Score de confiance</div>
              <div className="text-gray-600">{(analysis.confidenceScore * 100).toFixed(1)}%</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">Type de document</div>
              <div className="text-gray-600">{analysis.documentType}</div>
            </div>
            <div>
              <div className="font-medium text-gray-900">ID d'analyse</div>
              <div className="text-gray-600 font-mono text-xs">{analysis.id}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DocumentAnalysisDetails
