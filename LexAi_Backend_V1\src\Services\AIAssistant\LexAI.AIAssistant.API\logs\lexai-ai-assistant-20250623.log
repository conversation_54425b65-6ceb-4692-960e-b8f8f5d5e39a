2025-06-23 21:10:32.557 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-23 21:10:32.780 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 21:10:33.675 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-23 21:10:33.680 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-23 21:10:34.628 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 21:10:34.661 +04:00 [INF] Hosting environment: Development
2025-06-23 21:10:34.665 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-23 21:10:35.039 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-23 21:10:35.477 +04:00 [INF] Request GET / started with correlation ID 280598bb-029d-46d4-941d-3bfa3ffb9ecc
2025-06-23 21:10:38.015 +04:00 [INF] Request GET / completed in 2531ms with status 404 (Correlation ID: 280598bb-029d-46d4-941d-3bfa3ffb9ecc)
2025-06-23 21:10:38.034 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 3000.3308ms
2025-06-23 21:10:38.064 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-23 21:25:36.666 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-23 21:25:36.666 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-23 21:25:36.750 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 4470be13-1fb4-4772-bc7a-42af777d9c4d
2025-06-23 21:25:36.773 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID d9582a65-d029-4298-9a42-d37180c99c2f
2025-06-23 21:25:36.799 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:25:36.800 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:25:36.815 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 30ms with status 204 (Correlation ID: 4470be13-1fb4-4772-bc7a-42af777d9c4d)
2025-06-23 21:25:36.815 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 16ms with status 204 (Correlation ID: d9582a65-d029-4298-9a42-d37180c99c2f)
2025-06-23 21:25:36.834 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 167.5754ms
2025-06-23 21:25:36.839 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-23 21:25:36.842 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 175.5926ms
2025-06-23 21:25:36.864 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 562a63f4-b95c-4111-95dd-3ff498415a08
2025-06-23 21:25:36.872 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:25:37.124 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:25:37.139 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:25:37.184 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:25:38.620 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:25:43.562 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:25:43.647 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:25:45.885 +04:00 [INF] Executed DbCommand (167ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:25:46.222 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:25:46.247 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-23 21:25:46.300 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-23 21:25:46.302 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 9104.8901ms
2025-06-23 21:25:46.308 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 6c02273d-1d5b-4f22-9b71-48b8c84607df
2025-06-23 21:25:46.310 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:25:46.311 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:25:46.313 +04:00 [INF] Request GET /api/chat/conversations completed in 9441ms with status 200 (Correlation ID: 562a63f4-b95c-4111-95dd-3ff498415a08)
2025-06-23 21:25:46.318 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:25:46.323 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:25:46.326 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:25:46.330 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:25:46.332 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 9493.2308ms
2025-06-23 21:25:46.342 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:25:46.352 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:25:46.363 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-23 21:25:46.366 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 36.169ms
2025-06-23 21:25:46.370 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:25:46.376 +04:00 [INF] Request GET /api/chat/conversations completed in 65ms with status 200 (Correlation ID: 6c02273d-1d5b-4f22-9b71-48b8c84607df)
2025-06-23 21:25:46.381 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 81.0999ms
2025-06-23 21:26:04.504 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - null null
2025-06-23 21:26:04.516 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 73d37c18-6321-47bf-ab7f-7149c19a2f65
2025-06-23 21:26:04.520 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:04.522 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 2ms with status 204 (Correlation ID: 73d37c18-6321-47bf-ab7f-7149c19a2f65)
2025-06-23 21:26:04.535 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 204 null null 31.2812ms
2025-06-23 21:26:04.546 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-23 21:26:04.581 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID d93dcffa-3fe0-4ae8-8f07-dc412c3edbde
2025-06-23 21:26:04.588 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:04.593 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:04.599 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:26:04.615 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:26:04.629 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:04.854 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:26:04.879 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:26:04.960 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 335.2808ms
2025-06-23 21:26:04.966 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:26:04.971 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 382ms with status 200 (Correlation ID: d93dcffa-3fe0-4ae8-8f07-dc412c3edbde)
2025-06-23 21:26:04.978 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 431.6226ms
2025-06-23 21:26:05.979 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - null null
2025-06-23 21:26:05.987 +04:00 [INF] Request OPTIONS /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 started with correlation ID 32c82276-5c0f-427b-8f0d-015ba9db1061
2025-06-23 21:26:05.991 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:05.993 +04:00 [INF] Request OPTIONS /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 completed in 1ms with status 204 (Correlation ID: 32c82276-5c0f-427b-8f0d-015ba9db1061)
2025-06-23 21:26:05.999 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - 204 null null 19.7855ms
2025-06-23 21:26:06.001 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - application/json null
2025-06-23 21:26:06.010 +04:00 [INF] Request GET /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 started with correlation ID a8efac07-8735-4277-9246-fe0b542d156b
2025-06-23 21:26:06.013 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:06.015 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:06.020 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:26:06.023 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:26:06.026 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:06.035 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:26:06.051 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:26:06.054 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 28.0661ms
2025-06-23 21:26:06.059 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:26:06.061 +04:00 [INF] Request GET /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 completed in 48ms with status 200 (Correlation ID: a8efac07-8735-4277-9246-fe0b542d156b)
2025-06-23 21:26:06.072 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - 200 null application/json; charset=utf-8 70.45ms
2025-06-23 21:26:07.662 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-23 21:26:07.670 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 261358b7-1ff3-428f-8409-7e75fae121bd
2025-06-23 21:26:07.674 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:07.677 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:07.680 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:26:07.682 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:26:07.686 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:07.702 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:26:07.713 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:26:07.721 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 34.8227ms
2025-06-23 21:26:07.730 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:26:07.733 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 58ms with status 200 (Correlation ID: 261358b7-1ff3-428f-8409-7e75fae121bd)
2025-06-23 21:26:07.738 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 75.6476ms
2025-06-23 21:26:44.096 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-23 21:26:44.097 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-23 21:26:44.127 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 9d2555fc-95bf-4971-902f-52fb3b07cb32
2025-06-23 21:26:44.145 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID ee9b7b4c-8b1c-4cb9-a7de-bc603e5da43a
2025-06-23 21:26:44.155 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:44.165 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:44.170 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 15ms with status 204 (Correlation ID: 9d2555fc-95bf-4971-902f-52fb3b07cb32)
2025-06-23 21:26:44.174 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 9ms with status 204 (Correlation ID: ee9b7b4c-8b1c-4cb9-a7de-bc603e5da43a)
2025-06-23 21:26:44.182 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 85.5117ms
2025-06-23 21:26:44.193 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 96.2307ms
2025-06-23 21:26:44.184 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-23 21:26:44.245 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 0a1a13b6-da61-4e55-a74c-d6d012309ae6
2025-06-23 21:26:44.257 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:44.261 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:44.266 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:26:44.273 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:26:44.279 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:44.300 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:26:44.316 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:26:44.324 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-23 21:26:44.327 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 48.3196ms
2025-06-23 21:26:44.329 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-23 21:26:44.329 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:26:44.337 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 69c78959-1e8c-4958-95a4-785c17e63ae7
2025-06-23 21:26:44.339 +04:00 [INF] Request GET /api/chat/conversations completed in 81ms with status 200 (Correlation ID: 0a1a13b6-da61-4e55-a74c-d6d012309ae6)
2025-06-23 21:26:44.343 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:44.349 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 164.4748ms
2025-06-23 21:26:44.354 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:44.363 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:26:44.366 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:26:44.371 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:44.399 +04:00 [INF] Executed DbCommand (21ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:26:44.410 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:26:44.416 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-23 21:26:44.421 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 50.2393ms
2025-06-23 21:26:44.425 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:26:44.428 +04:00 [INF] Request GET /api/chat/conversations completed in 85ms with status 200 (Correlation ID: 69c78959-1e8c-4958-95a4-785c17e63ae7)
2025-06-23 21:26:44.436 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 106.4806ms
2025-06-23 21:27:23.199 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-23 21:27:23.210 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 3c802f49-5ba4-44f8-92f4-f0f3c289c333
2025-06-23 21:27:23.212 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:27:23.215 +04:00 [INF] Request OPTIONS /api/chat/message completed in 2ms with status 204 (Correlation ID: 3c802f49-5ba4-44f8-92f4-f0f3c289c333)
2025-06-23 21:27:23.225 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 26.0115ms
2025-06-23 21:27:23.228 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 83
2025-06-23 21:27:23.242 +04:00 [INF] Request POST /api/chat/message started with correlation ID a8c281a9-3434-46e5-8efe-6db99617bba4
2025-06-23 21:27:23.246 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:27:23.248 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:27:23.253 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-23 21:27:23.263 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:27:23.270 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:27:23.359 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit francais
2025-06-23 21:27:23.381 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit francais
2025-06-23 21:27:23.705 +04:00 [INF] Executed DbCommand (23ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-23 21:27:23.772 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit francais
2025-06-23 21:27:27.669 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 175, Cost: 0, Time: 1099ms
2025-06-23 21:27:27.680 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "27687691-e6b0-4922-9a8b-fb768f006fd3", Tokens: 175, Cost: 0
2025-06-23 21:27:27.686 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "27687691-e6b0-4922-9a8b-fb768f006fd3", Tokens: 175, Cost: 0
2025-06-23 21:27:27.690 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-23 21:27:27.716 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 4446.4217ms
2025-06-23 21:27:27.722 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-23 21:27:27.725 +04:00 [INF] Request POST /api/chat/message completed in 4479ms with status 200 (Correlation ID: a8c281a9-3434-46e5-8efe-6db99617bba4)
2025-06-23 21:27:27.731 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 4502.4253ms
2025-06-23 21:40:44.282 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages - null null
2025-06-23 21:40:44.297 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages started with correlation ID 9fa52541-c8d2-449a-9d2d-1b5d910c70dc
2025-06-23 21:40:44.300 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:40:44.301 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages completed in 1ms with status 204 (Correlation ID: 9fa52541-c8d2-449a-9d2d-1b5d910c70dc)
2025-06-23 21:40:44.305 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages - 204 null null 23.1638ms
2025-06-23 21:40:44.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages - application/json 33
2025-06-23 21:40:44.313 +04:00 [INF] Request POST /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages started with correlation ID 5091080a-4043-4c50-8537-42de4bd591c5
2025-06-23 21:40:44.315 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:40:44.316 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:40:44.317 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation (LexAI.AIAssistant.API)'
2025-06-23 21:40:44.324 +04:00 [INF] Route matched with {action = "ContinueConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] ContinueConversation(System.Guid, LexAI.AIAssistant.API.Controllers.ContinueConversationRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:40:44.328 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:40:44.336 +04:00 [INF] Continue conversation "27687691-e6b0-4922-9a8b-fb768f006fd3" from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit du travail
2025-06-23 21:40:44.343 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit du travail
2025-06-23 21:40:44.377 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:40:44.382 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit du travail
2025-06-23 21:40:50.068 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 815, Cost: 0, Time: 4116ms
2025-06-23 21:40:50.078 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "27687691-e6b0-4922-9a8b-fb768f006fd3", Tokens: 815, Cost: 0
2025-06-23 21:40:50.083 +04:00 [INF] Conversation continued successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "27687691-e6b0-4922-9a8b-fb768f006fd3", Tokens: 815
2025-06-23 21:40:50.088 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-23 21:40:50.094 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation (LexAI.AIAssistant.API) in 5765.9241ms
2025-06-23 21:40:50.097 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation (LexAI.AIAssistant.API)'
2025-06-23 21:40:50.099 +04:00 [INF] Request POST /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages completed in 5784ms with status 200 (Correlation ID: 5091080a-4043-4c50-8537-42de4bd591c5)
2025-06-23 21:40:50.105 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3/messages - 200 null application/json; charset=utf-8 5796.6018ms
2025-06-23 21:42:29.415 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-23 21:42:29.431 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 9c2d6eb6-b94f-44ed-9d19-26a29ee43bc5
2025-06-23 21:42:29.434 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:29.436 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 2ms with status 204 (Correlation ID: 9c2d6eb6-b94f-44ed-9d19-26a29ee43bc5)
2025-06-23 21:42:29.441 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 26.1875ms
2025-06-23 21:42:29.443 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-23 21:42:29.448 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 5973f18a-92ba-4e68-a9a6-8c512c233cfa
2025-06-23 21:42:29.450 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:29.452 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:29.454 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:42:29.455 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:29.458 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:29.466 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:42:29.474 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-23 21:42:29.478 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-23 21:42:29.480 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 22.279ms
2025-06-23 21:42:29.482 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-23 21:42:29.484 +04:00 [INF] Request GET /api/chat/conversations completed in 34ms with status 200 (Correlation ID: 5973f18a-92ba-4e68-a9a6-8c512c233cfa)
2025-06-23 21:42:29.490 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 46.754ms
2025-06-23 21:42:40.590 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - null null
2025-06-23 21:42:40.598 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 3c792403-48e8-48d6-a593-6e22786d0eef
2025-06-23 21:42:40.603 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:40.605 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 2ms with status 204 (Correlation ID: 3c792403-48e8-48d6-a593-6e22786d0eef)
2025-06-23 21:42:40.609 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 204 null null 19.7898ms
2025-06-23 21:42:40.612 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-23 21:42:40.623 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 3f9b6cc6-3ebb-469f-9add-ccb781ec1119
2025-06-23 21:42:40.625 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:40.627 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:40.629 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:40.632 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:40.636 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:40.644 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:42:40.650 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:42:40.653 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 18.0378ms
2025-06-23 21:42:40.655 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:40.657 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 31ms with status 200 (Correlation ID: 3f9b6cc6-3ebb-469f-9add-ccb781ec1119)
2025-06-23 21:42:40.660 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 47.5493ms
2025-06-23 21:42:41.788 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - null null
2025-06-23 21:42:41.800 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 started with correlation ID 7bfe7099-a113-428d-8220-39da9d77ff6c
2025-06-23 21:42:41.808 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:41.811 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 completed in 3ms with status 204 (Correlation ID: 7bfe7099-a113-428d-8220-39da9d77ff6c)
2025-06-23 21:42:41.817 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - 204 null null 29.4935ms
2025-06-23 21:42:41.824 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - application/json null
2025-06-23 21:42:41.837 +04:00 [INF] Request GET /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 started with correlation ID a0fbfef8-e70a-4a39-ad0b-a7fce62b5d5f
2025-06-23 21:42:41.842 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:41.847 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:41.850 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:41.855 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:41.864 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:41.875 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:42:41.883 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:42:41.888 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 24.6227ms
2025-06-23 21:42:41.892 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:41.895 +04:00 [INF] Request GET /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 completed in 53ms with status 200 (Correlation ID: a0fbfef8-e70a-4a39-ad0b-a7fce62b5d5f)
2025-06-23 21:42:41.902 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - 200 null application/json; charset=utf-8 78.5022ms
2025-06-23 21:42:49.899 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - null null
2025-06-23 21:42:49.908 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID e6af2eb8-51e2-43a9-a766-234ccb39dc9b
2025-06-23 21:42:49.912 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:49.915 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 2ms with status 204 (Correlation ID: e6af2eb8-51e2-43a9-a766-234ccb39dc9b)
2025-06-23 21:42:49.921 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 204 null null 22.0481ms
2025-06-23 21:42:49.925 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-23 21:42:49.938 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 6bba03aa-3e97-448e-b2e9-66b79df433c5
2025-06-23 21:42:49.943 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:49.946 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:49.951 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:49.958 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:49.965 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:49.983 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:42:50.002 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:42:50.011 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 46.5564ms
2025-06-23 21:42:50.020 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:50.027 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 84ms with status 200 (Correlation ID: 6bba03aa-3e97-448e-b2e9-66b79df433c5)
2025-06-23 21:42:50.041 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 116.8321ms
2025-06-23 21:42:51.275 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - null null
2025-06-23 21:42:51.285 +04:00 [INF] Request OPTIONS /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 started with correlation ID 0b301977-86a8-4d82-9d59-cc7c1b82eebc
2025-06-23 21:42:51.292 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:51.294 +04:00 [INF] Request OPTIONS /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 completed in 2ms with status 204 (Correlation ID: 0b301977-86a8-4d82-9d59-cc7c1b82eebc)
2025-06-23 21:42:51.300 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - 204 null null 24.6302ms
2025-06-23 21:42:51.308 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - application/json null
2025-06-23 21:42:51.324 +04:00 [INF] Request GET /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 started with correlation ID 13434c34-23af-4a2a-98b5-2a4ff21deff0
2025-06-23 21:42:51.335 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:51.341 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:51.347 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:51.354 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:51.364 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:51.373 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:42:51.378 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:42:51.383 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 19.186ms
2025-06-23 21:42:51.387 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:51.389 +04:00 [INF] Request GET /api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 completed in 54ms with status 200 (Correlation ID: 13434c34-23af-4a2a-98b5-2a4ff21deff0)
2025-06-23 21:42:51.392 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/e3fc9152-89ab-4512-96cb-ce3f284229e8 - 200 null application/json; charset=utf-8 84.5835ms
2025-06-23 21:42:53.610 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - null null
2025-06-23 21:42:53.625 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 started with correlation ID 90ec8806-6cad-4601-8ea9-c9ba4cb9c1f0
2025-06-23 21:42:53.628 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:53.630 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 completed in 1ms with status 204 (Correlation ID: 90ec8806-6cad-4601-8ea9-c9ba4cb9c1f0)
2025-06-23 21:42:53.635 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - 204 null null 24.7029ms
2025-06-23 21:42:53.642 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - application/json null
2025-06-23 21:42:53.732 +04:00 [INF] Request GET /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 started with correlation ID e89a2111-49e0-4b87-8083-b6245295b829
2025-06-23 21:42:53.743 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:53.751 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:53.760 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:53.770 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:53.785 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:53.801 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:42:53.816 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:42:53.824 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 39.1723ms
2025-06-23 21:42:53.830 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:53.835 +04:00 [INF] Request GET /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 completed in 92ms with status 200 (Correlation ID: e89a2111-49e0-4b87-8083-b6245295b829)
2025-06-23 21:42:53.851 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - 200 null application/json; charset=utf-8 209.332ms
2025-06-23 21:42:55.032 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - null null
2025-06-23 21:42:55.046 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID 239e7585-fb8a-4ada-b709-f66d23371d56
2025-06-23 21:42:55.054 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:55.060 +04:00 [INF] Request OPTIONS /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 6ms with status 204 (Correlation ID: 239e7585-fb8a-4ada-b709-f66d23371d56)
2025-06-23 21:42:55.077 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 204 null null 43.6969ms
2025-06-23 21:42:55.083 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - application/json null
2025-06-23 21:42:55.101 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb started with correlation ID e511e3c7-2c1d-43ad-b4fa-bc576c16ba23
2025-06-23 21:42:55.106 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:42:55.109 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:42:55.111 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:55.114 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:42:55.118 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:42:55.131 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:42:55.139 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:42:55.144 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 26.3181ms
2025-06-23 21:42:55.148 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:42:55.150 +04:00 [INF] Request GET /api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb completed in 45ms with status 200 (Correlation ID: e511e3c7-2c1d-43ad-b4fa-bc576c16ba23)
2025-06-23 21:42:55.159 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/2608bd44-f21a-42a4-a663-f85e2ebb1cbb - 200 null application/json; charset=utf-8 76.1153ms
2025-06-23 21:45:25.622 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - null null
2025-06-23 21:45:25.653 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 started with correlation ID 7394d325-df50-4d88-a20d-7f7a80494dbc
2025-06-23 21:45:25.659 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:45:25.661 +04:00 [INF] Request OPTIONS /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 completed in 2ms with status 204 (Correlation ID: 7394d325-df50-4d88-a20d-7f7a80494dbc)
2025-06-23 21:45:25.672 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - 204 null null 49.8848ms
2025-06-23 21:45:25.674 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - application/json null
2025-06-23 21:45:25.692 +04:00 [INF] Request GET /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 started with correlation ID a67702b3-5c84-4e33-b0b0-6a0fdab0184c
2025-06-23 21:45:25.699 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:45:25.705 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:45:25.708 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:45:25.710 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-23 21:45:25.717 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:45:25.726 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-23 21:45:25.736 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-23 21:45:25.741 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 23.9439ms
2025-06-23 21:45:25.745 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-23 21:45:25.751 +04:00 [INF] Request GET /api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 completed in 51ms with status 200 (Correlation ID: a67702b3-5c84-4e33-b0b0-6a0fdab0184c)
2025-06-23 21:45:25.756 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/27687691-e6b0-4922-9a8b-fb768f006fd3 - 200 null application/json; charset=utf-8 81.8974ms
