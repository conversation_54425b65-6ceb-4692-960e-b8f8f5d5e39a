using LexAI.DataPreprocessing.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using LexAI.Shared.Domain.Common;

namespace LexAI.DataPreprocessing.Domain.Entities;

/// <summary>
/// Represents a chunk of a document created during processing
/// </summary>
public class DocumentChunk : BaseEntity
{
    /// <summary>
    /// Document ID this chunk belongs to
    /// </summary>
    public Guid DocumentId { get; private set; }

    /// <summary>
    /// Chunk sequence number within the document
    /// </summary>
    public int SequenceNumber { get; private set; }

    /// <summary>
    /// Chunk content text
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// Chunk type
    /// </summary>
    public ChunkType Type { get; private set; }

    /// <summary>
    /// Start position in the original document
    /// </summary>
    public int StartPosition { get; private set; }

    /// <summary>
    /// End position in the original document
    /// </summary>
    public int EndPosition { get; private set; }

    /// <summary>
    /// Number of tokens in this chunk
    /// </summary>
    public int TokenCount { get; private set; }

    /// <summary>
    /// Character count in this chunk
    /// </summary>
    public int CharacterCount { get; private set; }

    /// <summary>
    /// Chunk metadata
    /// </summary>
    public ChunkMetadata Metadata { get; private set; } = null!;

    /// <summary>
    /// Vector embedding for this chunk
    /// </summary>
    public float[]? EmbeddingVector { get; private set; }

    /// <summary>
    /// Embedding model used
    /// </summary>
    public string? EmbeddingModel { get; private set; }

    /// <summary>
    /// Vector dimension
    /// </summary>
    public int? VectorDimension { get; private set; }

    /// <summary>
    /// Whether this chunk is vectorized
    /// </summary>
    public bool IsVectorized { get; private set; }

    /// <summary>
    /// Vector ID in the vector database
    /// </summary>
    public string? VectorId { get; private set; }

    /// <summary>
    /// Keywords extracted from this chunk
    /// </summary>
    public List<string> Keywords { get; private set; } = new();

    /// <summary>
    /// Named entities found in this chunk
    /// </summary>
    public List<NamedEntity> NamedEntities { get; private set; } = new();

    /// <summary>
    /// Chunk quality score (0-1)
    /// </summary>
    public double QualityScore { get; private set; }

    /// <summary>
    /// Chunk importance score (0-1)
    /// </summary>
    public double ImportanceScore { get; private set; }

    /// <summary>
    /// Legal domain relevance scores
    /// </summary>
    public Dictionary<LegalDomain, double> DomainRelevance { get; private set; } = new();

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentChunk() { }

    /// <summary>
    /// Creates a new document chunk
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="sequenceNumber">Sequence number</param>
    /// <param name="content">Chunk content</param>
    /// <param name="type">Chunk type</param>
    /// <param name="startPosition">Start position</param>
    /// <param name="endPosition">End position</param>
    /// <param name="createdBy">Agent that created the chunk</param>
    /// <returns>New document chunk</returns>
    public static DocumentChunk Create(
        Guid documentId,
        int sequenceNumber,
        string content,
        ChunkType type,
        int startPosition,
        int endPosition,
        string createdBy)
    {
        if (documentId == Guid.Empty)
            throw new ArgumentException("Document ID cannot be empty", nameof(documentId));

        if (sequenceNumber < 0)
            throw new ArgumentException("Sequence number cannot be negative", nameof(sequenceNumber));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (startPosition < 0)
            throw new ArgumentException("Start position cannot be negative", nameof(startPosition));

        if (endPosition <= startPosition)
            throw new ArgumentException("End position must be greater than start position", nameof(endPosition));

        var chunk = new DocumentChunk
        {
            Id = Guid.NewGuid(),
            DocumentId = documentId,
            SequenceNumber = sequenceNumber,
            Content = content.Trim(),
            Type = type,
            StartPosition = startPosition,
            EndPosition = endPosition,
            TokenCount = EstimateTokenCount(content),
            CharacterCount = content.Length,
            QualityScore = CalculateQualityScore(content),
            ImportanceScore = CalculateImportanceScore(content, type),
            IsVectorized = false,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = createdBy
        };

        chunk.Metadata = ChunkMetadata.Create(chunk.Id, type, chunk.TokenCount, chunk.CharacterCount);

        return chunk;
    }

    /// <summary>
    /// Sets the embedding vector for this chunk
    /// </summary>
    /// <param name="vector">Embedding vector</param>
    /// <param name="model">Embedding model used</param>
    /// <param name="vectorId">Vector ID in the database</param>
    public void SetEmbedding(float[] vector, string model, string? vectorId = null)
    {
        if (vector == null || vector.Length == 0)
            throw new ArgumentException("Vector cannot be null or empty", nameof(vector));

        if (string.IsNullOrWhiteSpace(model))
            throw new ArgumentException("Model cannot be empty", nameof(model));

        EmbeddingVector = vector;
        EmbeddingModel = model.Trim();
        VectorDimension = vector.Length;
        VectorId = vectorId?.Trim();
        IsVectorized = true;
        UpdatedAt = DateTime.UtcNow;

        // Update metadata
        Metadata = Metadata.WithEmbedding(model, vector.Length);
    }

    /// <summary>
    /// Adds keywords to this chunk
    /// </summary>
    /// <param name="keywords">Keywords to add</param>
    public void AddKeywords(IEnumerable<string> keywords)
    {
        if (keywords == null)
            throw new ArgumentNullException(nameof(keywords));

        foreach (var keyword in keywords.Where(k => !string.IsNullOrWhiteSpace(k)))
        {
            var normalizedKeyword = keyword.Trim().ToLowerInvariant();
            if (!Keywords.Contains(normalizedKeyword))
            {
                Keywords.Add(normalizedKeyword);
            }
        }

        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Adds named entities to this chunk
    /// </summary>
    /// <param name="entities">Named entities to add</param>
    public void AddNamedEntities(IEnumerable<NamedEntity> entities)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        NamedEntities.AddRange(entities);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets domain relevance scores
    /// </summary>
    /// <param name="relevanceScores">Domain relevance scores</param>
    public void SetDomainRelevance(Dictionary<LegalDomain, double> relevanceScores)
    {
        if (relevanceScores == null)
            throw new ArgumentNullException(nameof(relevanceScores));

        // Validate scores are between 0 and 1
        foreach (var score in relevanceScores.Values)
        {
            if (score < 0.0 || score > 1.0)
                throw new ArgumentException("Relevance scores must be between 0 and 1");
        }

        DomainRelevance = new Dictionary<LegalDomain, double>(relevanceScores);
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Gets the most relevant legal domain
    /// </summary>
    /// <returns>Most relevant domain and its score</returns>
    public (LegalDomain Domain, double Score)? GetMostRelevantDomain()
    {
        if (!DomainRelevance.Any())
            return null;

        var best = DomainRelevance.OrderByDescending(kvp => kvp.Value).First();
        return (best.Key, best.Value);
    }

    /// <summary>
    /// Updates quality and importance scores
    /// </summary>
    /// <param name="qualityScore">Quality score (0-1)</param>
    /// <param name="importanceScore">Importance score (0-1)</param>
    public void UpdateScores(double qualityScore, double importanceScore)
    {
        if (qualityScore < 0.0 || qualityScore > 1.0)
            throw new ArgumentException("Quality score must be between 0 and 1", nameof(qualityScore));

        if (importanceScore < 0.0 || importanceScore > 1.0)
            throw new ArgumentException("Importance score must be between 0 and 1", nameof(importanceScore));

        QualityScore = qualityScore;
        ImportanceScore = importanceScore;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if this chunk overlaps with another chunk
    /// </summary>
    /// <param name="other">Other chunk to check</param>
    /// <returns>True if chunks overlap</returns>
    public bool OverlapsWith(DocumentChunk other)
    {
        if (other == null || other.DocumentId != DocumentId)
            return false;

        return StartPosition < other.EndPosition && EndPosition > other.StartPosition;
    }

    /// <summary>
    /// Gets the overlap percentage with another chunk
    /// </summary>
    /// <param name="other">Other chunk</param>
    /// <returns>Overlap percentage (0-1)</returns>
    public double GetOverlapPercentage(DocumentChunk other)
    {
        if (!OverlapsWith(other))
            return 0.0;

        var overlapStart = Math.Max(StartPosition, other.StartPosition);
        var overlapEnd = Math.Min(EndPosition, other.EndPosition);
        var overlapLength = overlapEnd - overlapStart;

        var thisLength = EndPosition - StartPosition;
        var otherLength = other.EndPosition - other.StartPosition;
        var minLength = Math.Min(thisLength, otherLength);

        return (double)overlapLength / minLength;
    }

    /// <summary>
    /// Gets chunk statistics
    /// </summary>
    /// <returns>Chunk statistics</returns>
    public ChunkStatistics GetStatistics()
    {
        return new ChunkStatistics
        {
            ChunkId = Id,
            DocumentId = DocumentId,
            SequenceNumber = SequenceNumber,
            TokenCount = TokenCount,
            CharacterCount = CharacterCount,
            QualityScore = QualityScore,
            ImportanceScore = ImportanceScore,
            KeywordCount = Keywords.Count,
            EntityCount = NamedEntities.Count,
            IsVectorized = IsVectorized,
            VectorDimension = VectorDimension,
            DomainRelevanceCount = DomainRelevance.Count
        };
    }

    /// <summary>
    /// Creates a preview of the chunk content
    /// </summary>
    /// <param name="maxLength">Maximum length of preview</param>
    /// <returns>Content preview</returns>
    public string GetPreview(int maxLength = 200)
    {
        if (Content.Length <= maxLength)
            return Content;

        return Content.Substring(0, maxLength - 3) + "...";
    }

    private static int EstimateTokenCount(string text)
    {
        // Simple estimation: ~4 characters per token
        return Math.Max(1, text.Length / 4);
    }

    private static double CalculateQualityScore(string content)
    {
        // Simple quality scoring based on content characteristics
        var score = 0.5; // Base score

        // Longer content generally has higher quality
        if (content.Length > 100) score += 0.1;
        if (content.Length > 500) score += 0.1;

        // Check for complete sentences
        var sentences = content.Split('.', '!', '?').Where(s => !string.IsNullOrWhiteSpace(s)).Count();
        if (sentences > 1) score += 0.1;

        // Check for proper capitalization
        if (char.IsUpper(content.FirstOrDefault())) score += 0.1;

        // Penalize very short content
        if (content.Length < 50) score -= 0.2;

        return Math.Max(0.0, Math.Min(1.0, score));
    }

    private static double CalculateImportanceScore(string content, ChunkType type)
    {
        // Base importance by chunk type
        var score = type switch
        {
            ChunkType.Title => 0.9,
            ChunkType.Header => 0.8,
            ChunkType.Summary => 0.8,
            ChunkType.Conclusion => 0.7,
            ChunkType.Paragraph => 0.5,
            ChunkType.List => 0.4,
            ChunkType.Table => 0.6,
            ChunkType.Footer => 0.2,
            _ => 0.5
        };

        // Adjust based on content characteristics
        var lowerContent = content.ToLowerInvariant();

        // Legal keywords increase importance
        var legalKeywords = new[] { "article", "loi", "code", "jurisprudence", "tribunal", "cour", "arrêt", "décision" };
        var keywordCount = legalKeywords.Count(keyword => lowerContent.Contains(keyword));
        score += keywordCount * 0.05;

        // Numbers and dates might indicate important references
        if (System.Text.RegularExpressions.Regex.IsMatch(content, @"\d{4}")) score += 0.05; // Years
        if (System.Text.RegularExpressions.Regex.IsMatch(content, @"L\.\d+")) score += 0.1; // Legal references

        return Math.Max(0.0, Math.Min(1.0, score));
    }
}
