import React from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { Shield, ArrowLeft, Home } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent } from '../components/ui/Card'
import { useAuthStore } from '../store/authStore'

export function UnauthorizedPage() {
  const navigate = useNavigate()
  const { user } = useAuthStore()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <Shield className="mx-auto h-16 w-16 text-red-500" />
              <h1 className="mt-6 text-3xl font-extrabold text-gray-900">
                Accès non autorisé
              </h1>
              <p className="mt-4 text-lg text-gray-600">
                Vous n'avez pas les permissions nécessaires pour accéder à cette page.
              </p>
              
              {user && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Utilisateur connecté :</span> {user.firstName} {user.lastName}
                  </p>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Rôle actuel :</span> {user.role}
                  </p>
                </div>
              )}

              <div className="mt-8 space-y-3">
                <Button 
                  onClick={() => navigate(-1)}
                  className="w-full"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Retour à la page précédente
                </Button>
                
                <Link to="/dashboard">
                  <Button variant="outline" className="w-full">
                    <Home className="h-4 w-4 mr-2" />
                    Aller au tableau de bord
                  </Button>
                </Link>
              </div>

              <div className="mt-6 text-sm text-gray-500">
                <p>
                  Si vous pensez qu'il s'agit d'une erreur, contactez votre administrateur système.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
