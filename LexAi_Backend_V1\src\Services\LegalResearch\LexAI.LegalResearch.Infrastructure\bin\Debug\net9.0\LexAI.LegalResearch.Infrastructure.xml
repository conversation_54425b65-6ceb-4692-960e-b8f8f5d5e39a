<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.LegalResearch.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository">
            <summary>
            Legal document repository implementation using DataProcessing service
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository.#ctor(System.Net.Http.HttpClient,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository})">
            <summary>
            Initializes a new instance of the LegalDocumentRepository
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a document by ID
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document or null if not found</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository.AddAsync(LexAI.LegalResearch.Domain.Entities.LegalDocument,System.Threading.CancellationToken)">
            <summary>
            Adds a new document
            </summary>
            <param name="document">Document to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository.UpdateAsync(LexAI.LegalResearch.Domain.Entities.LegalDocument,System.Threading.CancellationToken)">
            <summary>
            Updates a document
            </summary>
            <param name="document">Document to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a document
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.LegalDocumentRepository.GetByDomainAsync(LexAI.Shared.Application.DTOs.LegalDomain,System.Threading.CancellationToken)">
            <summary>
            Gets documents by legal domain
            </summary>
            <param name="domain">Legal domain</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents in the domain</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Repositories.DocumentDto">
            <summary>
            Simple DTO for document data transfer
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository">
            <summary>
            Search query repository implementation using in-memory storage
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository})">
            <summary>
            Initializes a new instance of the SearchQueryRepository
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a search query by ID
            </summary>
            <param name="id">Query ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search query or null if not found</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.AddAsync(LexAI.LegalResearch.Domain.Entities.SearchQuery,System.Threading.CancellationToken)">
            <summary>
            Adds a new search query
            </summary>
            <param name="query">Query to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.UpdateAsync(LexAI.LegalResearch.Domain.Entities.SearchQuery,System.Threading.CancellationToken)">
            <summary>
            Updates a search query
            </summary>
            <param name="query">Query to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a search query
            </summary>
            <param name="id">Query ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetByUserAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search queries by user
            </summary>
            <param name="userId">User ID</param>
            <param name="limit">Maximum number of queries</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User's search queries</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetBySessionAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets search queries by session
            </summary>
            <param name="sessionId">Session ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Session's search queries</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetByDateRangeAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets search queries within a date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search queries in the date range</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetPopularQueriesAsync(System.Int32,System.Nullable{System.TimeSpan},System.Threading.CancellationToken)">
            <summary>
            Gets most popular search queries
            </summary>
            <param name="limit">Maximum number of queries</param>
            <param name="timeRange">Time range for popularity calculation</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Popular search queries</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetQueriesWithFeedbackAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search queries with feedback
            </summary>
            <param name="minRating">Minimum feedback rating</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search queries with feedback</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Repositories.SearchQueryRepository.GetAnalyticsAsync(System.Nullable{System.Guid},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Threading.CancellationToken)">
            <summary>
            Gets search analytics
            </summary>
            <param name="userId">Optional user ID filter</param>
            <param name="startDate">Start date for analytics</param>
            <param name="endDate">End date for analytics</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search analytics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.DocumentChunkingService">
            <summary>
            Document chunking service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.DocumentChunkingService.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.DocumentChunkingService})">
            <summary>
            Initializes a new instance of the DocumentChunkingService
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.DocumentChunkingService.ChunkDocumentAsync(System.Guid,System.String,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Splits document content into chunks for vector search
            </summary>
            <param name="documentId">Document ID</param>
            <param name="content">Document content</param>
            <param name="chunkSize">Maximum chunk size in characters</param>
            <param name="overlap">Overlap between chunks in characters</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document chunks</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.DocumentChunkingService.ExtractKeywordsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from text
            </summary>
            <param name="text">Text to analyze</param>
            <param name="maxKeywords">Maximum number of keywords</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.DocumentChunkingService.IdentifyChunkTypeAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Identifies the type of text chunk
            </summary>
            <param name="text">Text to analyze</param>
            <param name="context">Surrounding context</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chunk type</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.EnhancedQueryProcessingService">
            <summary>
            Service de traitement de requêtes amélioré avec LLM unifié
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.EnhancedQueryProcessingService.ProcessQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Traite et normalise la requête de recherche
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.EnhancedQueryProcessingService.DetectIntentAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Détecte l'intention de la requête
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.EnhancedQueryProcessingService.ExtractEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extrait les entités juridiques de la requête
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.EnhancedQueryProcessingService.ExpandQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Expanse la requête avec des termes associés
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.EnhancedQueryProcessingService.GenerateSearchSuggestionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Génère des suggestions de recherche
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService">
            <summary>
            Legal search service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.#ctor(LexAI.LegalResearch.Application.Interfaces.IEmbeddingService,LexAI.LegalResearch.Application.Interfaces.IVectorDatabaseService,LexAI.LegalResearch.Application.Interfaces.IQueryProcessingService,LexAI.LegalResearch.Application.Interfaces.ILegalDocumentRepository,Microsoft.Extensions.Caching.Memory.IMemoryCache,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.LegalSearchService})">
            <summary>
            Initializes a new instance of the LegalSearchService
            </summary>
            <param name="embeddingService">Embedding service</param>
            <param name="vectorService">Vector database service</param>
            <param name="queryProcessor">Query processing service</param>
            <param name="documentRepository">Document repository</param>
            <param name="cache">Memory cache</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(LexAI.LegalResearch.Application.DTOs.SearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs a semantic search for legal documents
            </summary>
            <param name="request">Search request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response with results</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(LexAI.LegalResearch.Application.DTOs.SearchRequestDto,System.Threading.CancellationToken)">
            <summary>
            Performs a hybrid search combining keyword and semantic search
            </summary>
            <param name="request">Search request</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search response with results</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.FindSimilarDocumentsAsync(System.Guid,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Finds similar documents to a given document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="limit">Maximum number of similar documents</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar documents</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.GetSearchSuggestionsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets search suggestions based on partial query
            </summary>
            <param name="partialQuery">Partial query text</param>
            <param name="limit">Maximum number of suggestions</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search suggestions</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.AnalyzeQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Analyzes query intent and extracts entities
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Query analysis result</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.GetSearchAnalyticsAsync(System.Nullable{System.Guid},System.String,System.Threading.CancellationToken)">
            <summary>
            Gets search analytics for a user or session
            </summary>
            <param name="userId">User ID</param>
            <param name="sessionId">Session ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Search analytics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService">
            <summary>
            OpenAI embedding service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.#ctor(System.Net.Http.HttpClient,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService})">
            <summary>
            Initializes a new instance of the OpenAIEmbeddingService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Generates embedding vector for text
            </summary>
            <param name="text">Text to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vector</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            Generates embeddings for multiple texts
            </summary>
            <param name="texts">Texts to embed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vectors</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.CalculateSimilarity(System.Single[],System.Single[])">
            <summary>
            Calculates cosine similarity between two embedding vectors
            </summary>
            <param name="vector1">First vector</param>
            <param name="vector2">Second vector</param>
            <returns>Cosine similarity score</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GetEmbeddingDimension">
            <summary>
            Gets the embedding model dimension
            </summary>
            <returns>Embedding dimension</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateDefaultEmbedding">
            <summary>
            Generates a default embedding vector when the deployment is not available
            </summary>
            <returns>Default embedding vector</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.OpenAIEmbeddingResponse">
            <summary>
            OpenAI embedding response model
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.OpenAIEmbeddingData">
            <summary>
            OpenAI embedding data model
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.OpenAIUsage">
            <summary>
            OpenAI usage model
            </summary>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService">
            <summary>
            Qdrant vector database service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.#ctor(System.Net.Http.HttpClient,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService})">
            <summary>
            Initializes a new instance of the QdrantVectorDatabaseService
            </summary>
            <param name="httpClient">HTTP client</param>
            <param name="configuration">Configuration</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.StoreChunksAsync(System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Application.DTOs.DocumentChunkDto},System.Threading.CancellationToken)">
            <summary>
            Stores document chunks with embeddings in vector database
            </summary>
            <param name="chunks">Document chunks with embeddings</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.SearchSimilarAsync(System.Single[],System.Int32,System.Double,System.Collections.Generic.Dictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Performs vector similarity search
            </summary>
            <param name="queryVector">Query embedding vector</param>
            <param name="limit">Maximum number of results</param>
            <param name="threshold">Minimum similarity threshold</param>
            <param name="filters">Optional filters</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar chunks with scores</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.UpdateDocumentEmbeddingsAsync(System.Guid,System.Collections.Generic.IEnumerable{LexAI.LegalResearch.Application.DTOs.DocumentChunkDto},System.Threading.CancellationToken)">
            <summary>
            Updates embeddings for a document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="chunks">Updated chunks</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.DeleteDocumentEmbeddingsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes document embeddings from vector database
            </summary>
            <param name="documentId">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.GetStatsAsync(System.Threading.CancellationToken)">
            <summary>
            Gets vector database statistics
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Database statistics</returns>
        </member>
        <member name="T:LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService">
            <summary>
            Query processing service implementation
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService})">
            <summary>
            Initializes a new instance of the QueryProcessingService
            </summary>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService.ProcessQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Processes and normalizes search query
            </summary>
            <param name="query">Raw query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processed query</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService.ExpandQueryAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Expands query with synonyms and related terms
            </summary>
            <param name="query">Original query</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Expanded query terms</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService.DetectIntentAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Detects query intent
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Query intent</returns>
        </member>
        <member name="M:LexAI.LegalResearch.Infrastructure.Services.QueryProcessingService.ExtractEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts legal entities from query
            </summary>
            <param name="query">Query text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted entities</returns>
        </member>
    </members>
</doc>
