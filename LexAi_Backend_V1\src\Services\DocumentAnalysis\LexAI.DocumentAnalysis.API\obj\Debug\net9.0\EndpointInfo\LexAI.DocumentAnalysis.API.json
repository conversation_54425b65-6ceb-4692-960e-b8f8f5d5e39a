{"openapi": "3.0.1", "info": {"title": "LexAI Document Analysis Service API", "description": "Service d'analyse de documents juridiques avec IA pour LexAI", "contact": {"name": "LexAI Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/v1/DocumentAnalysis/analyze": {"post": {"tags": ["DocumentAnalysis"], "summary": "Analyse un document juridique complet", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"DocumentFile": {"type": "string", "description": "Fichier document à analyser", "format": "binary"}, "DocumentName": {"type": "string", "description": "Nom du document (optionnel, utilise le nom du fichier par défaut)"}, "Options.ExtractClauses": {"type": "boolean"}, "Options.PerformRiskAssessment": {"type": "boolean"}, "Options.GenerateRecommendations": {"type": "boolean"}, "Options.ExtractEntities": {"type": "boolean"}, "Options.FindCitations": {"type": "boolean"}, "Options.UseAzureDocumentIntelligence": {"type": "boolean"}, "Options.SpecificAnalysisType": {"type": "string"}, "Options.FocusAreas": {"type": "array", "items": {"type": "string"}}, "Options.Language": {"type": "string"}}}, "encoding": {"DocumentFile": {"style": "form"}, "DocumentName": {"style": "form"}, "Options.ExtractClauses": {"style": "form"}, "Options.PerformRiskAssessment": {"style": "form"}, "Options.GenerateRecommendations": {"style": "form"}, "Options.ExtractEntities": {"style": "form"}, "Options.FindCitations": {"style": "form"}, "Options.UseAzureDocumentIntelligence": {"style": "form"}, "Options.SpecificAnalysisType": {"style": "form"}, "Options.FocusAreas": {"style": "form"}, "Options.Language": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "429": {"description": "Too Many Requests", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/v1/DocumentAnalysis/{analysisId}": {"get": {"tags": ["DocumentAnalysis"], "summary": "Obtient le résultat d'une analyse par ID", "parameters": [{"name": "analysisId", "in": "path", "description": "ID de l'analyse", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["DocumentAnalysis"], "summary": "Supprime une analyse", "parameters": [{"name": "analysisId", "in": "path", "description": "ID de l'analyse à supprimer", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/DocumentAnalysis": {"get": {"tags": ["DocumentAnalysis"], "summary": "Obtient la liste des analyses pour l'utilisateur connecté", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "DocumentType", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisListResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisListResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisListResponseDto"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/DocumentAnalysis/{analysisId}/regenerate": {"post": {"tags": ["DocumentAnalysis"], "summary": "Régénère une analyse existante avec de nouvelles options", "parameters": [{"name": "analysisId", "in": "path", "description": "ID de l'analyse à régénérer", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Nouvelles options d'analyse", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisOptions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AnalysisOptions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AnalysisOptions"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Health": {"get": {"tags": ["Health"], "summary": "Vérification de santé basique", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HealthStatus"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HealthStatus"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthStatus"}}}}}}}, "/api/v1/Health/detailed": {"get": {"tags": ["Health"], "summary": "Vérification de santé détail<PERSON>e", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DetailedHealthStatus"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DetailedHealthStatus"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DetailedHealthStatus"}}}}}}}, "/api/v1/Health/cache/stats": {"get": {"tags": ["Health"], "summary": "Statistiques du cache", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CacheStatistics"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CacheStatistics"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CacheStatistics"}}}}}}}, "/api/v1/Health/cache/clear": {"post": {"tags": ["Health"], "summary": "Vider le cache", "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AnalysisOptions": {"type": "object", "properties": {"extractClauses": {"type": "boolean"}, "performRiskAssessment": {"type": "boolean"}, "generateRecommendations": {"type": "boolean"}, "extractEntities": {"type": "boolean"}, "findCitations": {"type": "boolean"}, "useAzureDocumentIntelligence": {"type": "boolean"}, "specificAnalysisType": {"type": "string", "nullable": true}, "focusAreas": {"type": "array", "items": {"type": "string"}, "nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AzureConfigurationInfo": {"type": "object", "properties": {"documentIntelligenceConfigured": {"type": "boolean"}, "endpoint": {"type": "string", "nullable": true}, "maxFileSizeMB": {"type": "integer", "format": "int32"}, "supportedFileTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "cachingEnabled": {"type": "boolean"}, "retryEnabled": {"type": "boolean"}}, "additionalProperties": false}, "CacheStatistics": {"type": "object", "properties": {"totalEntries": {"type": "integer", "format": "int32"}, "totalSizeBytes": {"type": "integer", "format": "int64"}, "hitCount": {"type": "integer", "format": "int32"}, "missCount": {"type": "integer", "format": "int32"}, "hitRatio": {"type": "number", "format": "double", "readOnly": true}, "totalRequests": {"type": "integer", "format": "int32", "readOnly": true}, "lastUpdated": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ClauseAnalysisDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "clauseText": {"type": "string", "nullable": true}, "clauseType": {"type": "string", "nullable": true}, "analysis": {"type": "string", "nullable": true}, "riskLevel": {"type": "string", "nullable": true}, "confidenceScore": {"type": "number", "format": "double"}, "startPosition": {"type": "integer", "format": "int32"}, "endPosition": {"type": "integer", "format": "int32"}, "suggestedRevision": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DetailedHealthStatus": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "version": {"type": "string", "nullable": true}, "uptime": {"type": "string", "format": "date-span"}, "environment": {"type": "string", "nullable": true}, "services": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ServiceHealthInfo"}, "nullable": true}, "systemInfo": {"$ref": "#/components/schemas/SystemInfo"}, "azureConfiguration": {"$ref": "#/components/schemas/AzureConfigurationInfo"}, "cacheStatistics": {"$ref": "#/components/schemas/CacheStatistics"}}, "additionalProperties": false}, "DocumentAnalysisListResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentAnalysisSummaryDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DocumentAnalysisResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "documentName": {"type": "string", "nullable": true}, "documentType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "analysisContent": {"type": "string", "nullable": true}, "confidenceScore": {"type": "number", "format": "double"}, "processingTimeMs": {"type": "integer", "format": "int32"}, "tokensUsed": {"type": "integer", "format": "int32"}, "estimatedCost": {"type": "number", "format": "double"}, "modelUsed": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "analyzedAt": {"type": "string", "format": "date-time"}, "clauses": {"type": "array", "items": {"$ref": "#/components/schemas/ClauseAnalysisDto"}, "nullable": true}, "risks": {"type": "array", "items": {"$ref": "#/components/schemas/RiskAssessmentDto"}, "nullable": true}, "recommendations": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentRecommendationDto"}, "nullable": true}, "entities": {"type": "array", "items": {"$ref": "#/components/schemas/ExtractedEntityDto"}, "nullable": true}, "citations": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentCitationDto"}, "nullable": true}, "summary": {"$ref": "#/components/schemas/DocumentSummaryDto"}}, "additionalProperties": false}, "DocumentAnalysisSummaryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "documentName": {"type": "string", "nullable": true}, "documentType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "confidenceScore": {"type": "number", "format": "double"}, "riskCount": {"type": "integer", "format": "int32"}, "recommendationCount": {"type": "integer", "format": "int32"}, "overallRiskLevel": {"type": "string", "nullable": true}, "analyzedAt": {"type": "string", "format": "date-time"}, "processingTimeMs": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DocumentCitationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "relevanceScore": {"type": "number", "format": "double"}, "context": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DocumentRecommendationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "priority": {"type": "string", "nullable": true}, "suggestedAction": {"type": "string", "nullable": true}, "legalBasis": {"type": "string", "nullable": true}, "relatedClauses": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DocumentSummaryDto": {"type": "object", "properties": {"executiveSummary": {"type": "string", "nullable": true}, "keyPoints": {"type": "array", "items": {"type": "string"}, "nullable": true}, "mainParties": {"type": "array", "items": {"type": "string"}, "nullable": true}, "importantDates": {"type": "array", "items": {"type": "string"}, "nullable": true}, "financialTerms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "documentPurpose": {"type": "string", "nullable": true}, "overallRiskLevel": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExtractedEntityDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "text": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "confidenceScore": {"type": "number", "format": "double"}, "startPosition": {"type": "integer", "format": "int32"}, "endPosition": {"type": "integer", "format": "int32"}, "normalizedValue": {"type": "string", "nullable": true}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "HealthStatus": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "version": {"type": "string", "nullable": true}, "uptime": {"type": "string", "format": "date-span"}, "environment": {"type": "string", "nullable": true}, "services": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ServiceHealthInfo"}, "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "RiskAssessmentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "riskType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "severity": {"type": "string", "nullable": true}, "probability": {"type": "number", "format": "double"}, "impact": {"type": "string", "nullable": true}, "mitigation": {"type": "string", "nullable": true}, "affectedClauses": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ServiceHealthInfo": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "lastChecked": {"type": "string", "format": "date-time"}, "responseTime": {"type": "string", "format": "date-span", "nullable": true}}, "additionalProperties": false}, "SystemInfo": {"type": "object", "properties": {"machineName": {"type": "string", "nullable": true}, "processorCount": {"type": "integer", "format": "int32"}, "workingSet": {"type": "integer", "format": "int64"}, "totalMemory": {"type": "integer", "format": "int64"}, "osVersion": {"type": "string", "nullable": true}, "clrVersion": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}