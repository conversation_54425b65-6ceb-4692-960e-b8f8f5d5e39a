{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Jwt": {"SecretKey": "test-secret-key-that-is-at-least-32-characters-long-for-testing", "Issuer": "LexAI-Test", "Audience": "LexAI-Test-Users", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": false, "ValidateAudience": false, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "AllowedHosts": "*"}