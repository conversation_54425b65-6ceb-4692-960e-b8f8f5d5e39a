2025-06-26 22:08:35.476 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-26 22:08:35.620 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:08:36.249 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-26 22:08:36.257 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-26 22:08:36.361 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:08:36.371 +04:00 [INF] Hosting environment: Development
2025-06-26 22:08:36.374 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-26 22:08:38.420 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-26 22:08:38.825 +04:00 [INF] Request GET / started with correlation ID aa588d62-47c1-473e-b971-1a3ab168771f
2025-06-26 22:08:40.864 +04:00 [INF] Request GET / completed in 2030ms with status 404 (Correlation ID: aa588d62-47c1-473e-b971-1a3ab168771f)
2025-06-26 22:08:40.881 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2465.1013ms
2025-06-26 22:08:40.932 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-26 22:22:07.572 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-26 22:22:07.699 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:22:08.179 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-26 22:22:08.183 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-26 22:22:08.255 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:22:08.257 +04:00 [INF] Hosting environment: Development
2025-06-26 22:22:08.258 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-26 22:22:09.111 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-26 22:22:09.315 +04:00 [INF] Request GET / started with correlation ID 8465dc59-9ef9-4efa-823d-696fd1967f84
2025-06-26 22:22:09.491 +04:00 [INF] Request GET / completed in 172ms with status 404 (Correlation ID: 8465dc59-9ef9-4efa-823d-696fd1967f84)
2025-06-26 22:22:09.506 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 402.9232ms
2025-06-26 22:22:09.526 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-26 22:26:03.266 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-26 22:26:03.475 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:26:03.930 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-26 22:26:03.939 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-26 22:26:04.001 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:26:04.004 +04:00 [INF] Hosting environment: Development
2025-06-26 22:26:04.006 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-26 22:26:04.681 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-26 22:26:04.972 +04:00 [INF] Request GET / started with correlation ID f24e7389-c7d8-4c2b-b41d-7754a9cdd0b0
2025-06-26 22:26:05.231 +04:00 [INF] Request GET / completed in 244ms with status 404 (Correlation ID: f24e7389-c7d8-4c2b-b41d-7754a9cdd0b0)
2025-06-26 22:26:05.244 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 564.7234ms
2025-06-26 22:26:05.285 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-26 23:28:40.045 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-26 23:28:40.222 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 23:28:40.763 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-26 23:28:40.765 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-26 23:28:40.916 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 23:28:40.930 +04:00 [INF] Hosting environment: Development
2025-06-26 23:28:41.003 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-26 23:28:44.205 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-26 23:28:44.469 +04:00 [INF] Request GET / started with correlation ID 45c66d25-d2b0-4ed0-87b3-df8d0aabcd7f
2025-06-26 23:28:44.645 +04:00 [INF] Request GET / completed in 172ms with status 404 (Correlation ID: 45c66d25-d2b0-4ed0-87b3-df8d0aabcd7f)
2025-06-26 23:28:44.659 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 455.9872ms
2025-06-26 23:28:44.682 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
