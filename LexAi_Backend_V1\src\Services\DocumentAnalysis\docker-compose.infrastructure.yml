version: '3.8'

services:
  # PostgreSQL Database
  documentanalysis-postgres:
    image: postgres:16-alpine
    container_name: lexai-documentanalysis-postgres
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres_admin_2024!
    ports:
      - "5435:5432"  # Port différent pour éviter les conflits
    volumes:
      - documentanalysis_postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - lexai-documentanalysis-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache for DocumentAnalysis
  documentanalysis-redis:
    image: redis:7-alpine
    container_name: lexai-documentanalysis-redis
    ports:
      - "6381:6379"  # Port différent pour éviter les conflits
    volumes:
      - documentanalysis_redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - lexai-documentanalysis-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database for DocumentAnalysis
  # documentanalysis-qdrant:
  #   image: qdrant/qdrant:v1.7.4
  #   container_name: lexai-documentanalysis-qdrant
  #   ports:
  #     - "6335:6333"  # Port différent pour éviter les conflits
  #     - "6336:6334"  # gRPC port
  #   volumes:
  #     - documentanalysis_qdrant_data:/qdrant/storage
  #     - ./config/qdrant-config.yaml:/qdrant/config/production.yaml:ro
  #   environment:
  #     QDRANT__SERVICE__HTTP_PORT: 6333
  #     QDRANT__SERVICE__GRPC_PORT: 6334
  #     QDRANT__LOG_LEVEL: INFO
  #   networks:
  #     - lexai-documentanalysis-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Ollama for Local LLM (optional)
  # documentanalysis-ollama:
  #   image: ollama/ollama:latest
  #   container_name: lexai-documentanalysis-ollama
  #   ports:
  #     - "11435:11434"  # Port différent pour éviter les conflits
  #   volumes:
  #     - documentanalysis_ollama_data:/root/.ollama
  #   environment:
  #     OLLAMA_HOST: 0.0.0.0
  #     OLLAMA_ORIGINS: "*"
  #   networks:
  #     - lexai-documentanalysis-network
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:11434/api/version"]
  #     interval: 60s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s

volumes:
  documentanalysis_postgres_data:
    driver: local
  documentanalysis_redis_data:
    driver: local
  # documentanalysis_qdrant_data:
  #   driver: local
  # documentanalysis_ollama_data:
  #   driver: local

networks:
  lexai-documentanalysis-network:
    driver: bridge
    name: lexai-documentanalysis-network
