-- Script d'initialisation pour la base de données DocumentAnalysis
-- Ce script est exécuté automatiquement lors de la création du conteneur PostgreSQL

-- Création des extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Création d'un utilisateur spécifique pour l'application (si nécessaire)
-- DO $$
-- BEGIN
--     IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'lexai_app_user') THEN
--         CREATE ROLE lexai_app_user WITH LOGIN PASSWORD 'app_password_2024';
--     END IF;
-- END
-- $$;

-- Octroi des permissions sur la base de données
-- GRANT CONNECT ON DATABASE lexai_document_analysis TO lexai_app_user;
-- GRANT USAGE ON SCHEMA public TO lexai_app_user;
-- GRANT CREATE ON SCHEMA public TO lexai_app_user;

-- Configuration des paramètres de performance
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Configuration pour les recherches textuelles
ALTER SYSTEM SET default_text_search_config = 'pg_catalog.french';

-- Message de confirmation
SELECT 'Base de données DocumentAnalysis initialisée avec succès!' as message;
