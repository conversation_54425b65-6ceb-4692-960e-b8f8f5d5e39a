<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.AIAssistant.Domain</name>
    </assembly>
    <members>
        <member name="T:LexAI.AIAssistant.Domain.Entities.Conversation">
            <summary>
            Represents a conversation between a user and the AI assistant
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.Title">
            <summary>
            Conversation title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.UserId">
            <summary>
            User ID who owns this conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.SessionId">
            <summary>
            Session ID for grouping related conversations
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.Context">
            <summary>
            Conversation context and metadata
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.Messages">
            <summary>
            List of messages in the conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.Status">
            <summary>
            Conversation status
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.PrimaryDomain">
            <summary>
            Legal domain focus of the conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.Tags">
            <summary>
            Conversation tags for categorization
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.Summary">
            <summary>
            Conversation summary (auto-generated)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.LastActivityAt">
            <summary>
            Last activity timestamp
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.MessageCount">
            <summary>
            Total number of messages
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.TotalTokensUsed">
            <summary>
            Total tokens used in this conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.EstimatedCost">
            <summary>
            Estimated cost of the conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.IsArchived">
            <summary>
            Whether the conversation is archived
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.UserRating">
            <summary>
            Conversation rating by user (1-5)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Conversation.UserFeedback">
            <summary>
            User feedback on the conversation
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.Create(System.String,System.Guid,System.String,LexAI.AIAssistant.Domain.ValueObjects.ConversationContext)">
            <summary>
            Creates a new conversation
            </summary>
            <param name="title">Conversation title</param>
            <param name="userId">User ID</param>
            <param name="sessionId">Session ID</param>
            <param name="context">Conversation context</param>
            <returns>New conversation instance</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.AddMessage(LexAI.AIAssistant.Domain.Entities.Message)">
            <summary>
            Adds a message to the conversation
            </summary>
            <param name="message">Message to add</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.UpdateTitle(System.String,System.String)">
            <summary>
            Updates the conversation title
            </summary>
            <param name="title">New title</param>
            <param name="updatedBy">User who updated the title</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.UpdateContext(LexAI.AIAssistant.Domain.ValueObjects.ConversationContext)">
            <summary>
            Updates the conversation context
            </summary>
            <param name="context">New context</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.AddTags(System.String[])">
            <summary>
            Adds tags to the conversation
            </summary>
            <param name="tags">Tags to add</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.RemoveTags(System.String[])">
            <summary>
            Removes tags from the conversation
            </summary>
            <param name="tags">Tags to remove</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.SetSummary(System.String)">
            <summary>
            Sets the conversation summary
            </summary>
            <param name="summary">Conversation summary</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.Close(System.String)">
            <summary>
            Closes the conversation
            </summary>
            <param name="closedBy">User who closed the conversation</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.Reopen(System.String)">
            <summary>
            Reopens the conversation
            </summary>
            <param name="reopenedBy">User who reopened the conversation</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.Archive(System.String)">
            <summary>
            Archives the conversation
            </summary>
            <param name="archivedBy">User who archived the conversation</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.Unarchive(System.String)">
            <summary>
            Unarchives the conversation
            </summary>
            <param name="unarchivedBy">User who unarchived the conversation</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.SetUserRating(System.Int32,System.String,System.String)">
            <summary>
            Sets user rating and feedback
            </summary>
            <param name="rating">Rating (1-5)</param>
            <param name="feedback">Optional feedback text</param>
            <param name="ratedBy">User who provided the rating</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.GetLastMessages(System.Int32)">
            <summary>
            Gets the last N messages from the conversation
            </summary>
            <param name="count">Number of messages to retrieve</param>
            <returns>Last N messages</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.GetMessagesByRole(LexAI.AIAssistant.Domain.ValueObjects.MessageRole)">
            <summary>
            Gets messages by role
            </summary>
            <param name="role">Message role</param>
            <returns>Messages with the specified role</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.IsActive">
            <summary>
            Checks if the conversation is active
            </summary>
            <returns>True if conversation is active</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.IsIdle(System.TimeSpan)">
            <summary>
            Checks if the conversation has been idle for too long
            </summary>
            <param name="idleThreshold">Idle threshold timespan</param>
            <returns>True if conversation is idle</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.GetDuration">
            <summary>
            Gets the conversation duration
            </summary>
            <returns>Duration of the conversation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Conversation.GetAverageResponseTime">
            <summary>
            Gets the average response time for AI messages
            </summary>
            <returns>Average response time</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.Entities.Message">
            <summary>
            Represents a message in a conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.ConversationId">
            <summary>
            Conversation ID this message belongs to
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Content">
            <summary>
            Message content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Role">
            <summary>
            Message role (User, Assistant, System)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Type">
            <summary>
            Message type for categorization
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Metadata">
            <summary>
            Message metadata
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.TokensUsed">
            <summary>
            Number of tokens used for this message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.EstimatedCost">
            <summary>
            Estimated cost for this message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.ProcessingTime">
            <summary>
            Processing time for AI responses
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.DetectedDomain">
            <summary>
            Detected legal domain from message content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.DetectedIntent">
            <summary>
            Detected intent from message content
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.ConfidenceScore">
            <summary>
            Confidence score for AI responses (0-1)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Citations">
            <summary>
            Citations and references used in the response
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Attachments">
            <summary>
            Attachments associated with the message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.Status">
            <summary>
            Message status
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.ErrorMessage">
            <summary>
            Error message if processing failed
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.IsEdited">
            <summary>
            Whether the message has been edited
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.OriginalContent">
            <summary>
            Original content before editing
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.UserRating">
            <summary>
            User rating for AI responses (1-5)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.Entities.Message.UserFeedback">
            <summary>
            User feedback on the message
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.CreateUserMessage(System.Guid,System.String,System.Guid,LexAI.Shared.Application.DTOs.MessageType)">
            <summary>
            Creates a new user message
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="content">Message content</param>
            <param name="userId">User ID</param>
            <param name="type">Message type</param>
            <returns>New user message</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.CreateAssistantMessage(System.Guid,System.String,LexAI.Shared.Application.DTOs.MessageType,System.Nullable{System.TimeSpan},System.Nullable{System.Int32},System.Decimal)">
            <summary>
            Creates a new assistant message
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="content">Message content</param>
            <param name="type">Message type</param>
            <param name="processingTime">Processing time</param>
            <param name="tokensUsed">Tokens used</param>
            <param name="estimatedCost">Estimated cost</param>
            <returns>New assistant message</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.CreateSystemMessage(System.Guid,System.String,LexAI.Shared.Application.DTOs.MessageType)">
            <summary>
            Creates a system message
            </summary>
            <param name="conversationId">Conversation ID</param>
            <param name="content">Message content</param>
            <param name="type">Message type</param>
            <returns>New system message</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.UpdateContent(System.String,System.String)">
            <summary>
            Updates the message content
            </summary>
            <param name="content">New content</param>
            <param name="updatedBy">User who updated the message</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.SetDetectedClassification(System.Nullable{LexAI.Shared.Application.DTOs.LegalDomain},System.Nullable{LexAI.Shared.Application.DTOs.MessageIntent},System.Nullable{System.Double})">
            <summary>
            Sets the detected domain and intent
            </summary>
            <param name="domain">Detected legal domain</param>
            <param name="intent">Detected message intent</param>
            <param name="confidenceScore">Confidence score</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.AddCitations(System.Collections.Generic.IEnumerable{LexAI.AIAssistant.Domain.ValueObjects.Citation})">
            <summary>
            Adds citations to the message
            </summary>
            <param name="citations">Citations to add</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.AddAttachment(LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment)">
            <summary>
            Adds an attachment to the message
            </summary>
            <param name="attachment">Attachment to add</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.MarkAsProcessing">
            <summary>
            Marks the message as processing
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.MarkAsSent(System.Nullable{System.TimeSpan})">
            <summary>
            Marks the message as sent
            </summary>
            <param name="processingTime">Processing time for AI messages</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.MarkAsFailed(System.String)">
            <summary>
            Marks the message as failed
            </summary>
            <param name="errorMessage">Error message</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.SetUserRating(System.Int32,System.String,System.String)">
            <summary>
            Sets user rating and feedback
            </summary>
            <param name="rating">Rating (1-5)</param>
            <param name="feedback">Optional feedback text</param>
            <param name="ratedBy">User who provided the rating</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.UpdateTokenUsage(System.Int32,System.Decimal)">
            <summary>
            Updates token usage and cost
            </summary>
            <param name="tokensUsed">Tokens used</param>
            <param name="estimatedCost">Estimated cost</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.IsUserMessage">
            <summary>
            Checks if the message is from a user
            </summary>
            <returns>True if message is from user</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.IsAssistantMessage">
            <summary>
            Checks if the message is from the assistant
            </summary>
            <returns>True if message is from assistant</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.IsSystemMessage">
            <summary>
            Checks if the message is a system message
            </summary>
            <returns>True if message is system message</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.GetPreview">
            <summary>
            Gets the message preview (first 100 characters)
            </summary>
            <returns>Message preview</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.Entities.Message.EstimateTokenCount(System.String)">
            <summary>
            Estimates token count for a text
            </summary>
            <param name="text">Text to estimate</param>
            <returns>Estimated token count</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.MessageRole">
            <summary>
            Message role enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageRole.User">
            <summary>
            Message from user
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageRole.Assistant">
            <summary>
            Message from AI assistant
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageRole.System">
            <summary>
            System message
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.MessageStatus">
            <summary>
            Message status enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageStatus.Processing">
            <summary>
            Message is being processed
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageStatus.Sent">
            <summary>
            Message has been sent
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageStatus.Failed">
            <summary>
            Message processing failed
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.MessageStatus.Cancelled">
            <summary>
            Message was cancelled
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.ConversationStatus">
            <summary>
            Conversation status enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationStatus.Active">
            <summary>
            Conversation is active
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationStatus.Closed">
            <summary>
            Conversation is closed
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationStatus.Archived">
            <summary>
            Conversation is archived
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationStatus.Deleted">
            <summary>
            Conversation is deleted
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.AIModelType">
            <summary>
            AI model type enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AIModelType.GPT4">
            <summary>
            GPT-4 model
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AIModelType.GPT4Turbo">
            <summary>
            GPT-4 Turbo model
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AIModelType.GPT35Turbo">
            <summary>
            GPT-3.5 Turbo model
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AIModelType.Claude">
            <summary>
            Claude model
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AIModelType.CustomLegal">
            <summary>
            Custom legal model
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.CitationType">
            <summary>
            Citation type enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.LegalDocument">
            <summary>
            Legal document citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.CaseLaw">
            <summary>
            Case law citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.Statute">
            <summary>
            Statute citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.Regulation">
            <summary>
            Regulation citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.Academic">
            <summary>
            Academic source citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.News">
            <summary>
            News article citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.Website">
            <summary>
            Website citation
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.CitationType.Other">
            <summary>
            Other citation type
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType">
            <summary>
            Attachment type enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType.PDF">
            <summary>
            PDF document
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType.Word">
            <summary>
            Word document
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType.Text">
            <summary>
            Text file
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType.Image">
            <summary>
            Image file
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType.Spreadsheet">
            <summary>
            Spreadsheet
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.AttachmentType.Other">
            <summary>
            Other file type
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.ConversationMode">
            <summary>
            Conversation mode enumeration
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationMode.Standard">
            <summary>
            Standard conversation mode
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationMode.Research">
            <summary>
            Research mode with enhanced search
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationMode.DocumentAnalysis">
            <summary>
            Document analysis mode
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationMode.LegalAdvice">
            <summary>
            Legal advice mode
            </summary>
        </member>
        <member name="F:LexAI.AIAssistant.Domain.ValueObjects.ConversationMode.QuickQuestion">
            <summary>
            Quick question mode
            </summary>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.Citation">
            <summary>
            Represents a citation or reference used in a message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Id">
            <summary>
            Citation ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Type">
            <summary>
            Citation type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Title">
            <summary>
            Document or source title
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Url">
            <summary>
            Document URL or reference
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Source">
            <summary>
            Source name or publisher
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Authors">
            <summary>
            Authors of the document
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.PublicationDate">
            <summary>
            Publication date
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.RelevanceScore">
            <summary>
            Relevance score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Excerpt">
            <summary>
            Excerpt or summary from the source
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.PageReference">
            <summary>
            Page number or section reference
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Jurisdiction">
            <summary>
            Legal jurisdiction (for legal documents)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Court">
            <summary>
            Court name (for case law)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.CaseNumber">
            <summary>
            Case number (for case law)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.CitationFormat">
            <summary>
            Citation format (e.g., APA, MLA, Bluebook)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.Citation.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.Create(LexAI.AIAssistant.Domain.ValueObjects.CitationType,System.String,System.String,System.String,System.Double)">
            <summary>
            Creates a new citation
            </summary>
            <param name="type">Citation type</param>
            <param name="title">Document title</param>
            <param name="url">Document URL</param>
            <param name="source">Source name</param>
            <param name="relevanceScore">Relevance score</param>
            <returns>New citation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.CreateLegalDocument(System.String,System.String,System.String,System.String,System.Double)">
            <summary>
            Creates a legal document citation
            </summary>
            <param name="title">Document title</param>
            <param name="url">Document URL</param>
            <param name="source">Source name</param>
            <param name="jurisdiction">Legal jurisdiction</param>
            <param name="relevanceScore">Relevance score</param>
            <returns>New legal document citation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.CreateCaseLaw(System.String,System.String,System.String,System.String,System.String,System.Double)">
            <summary>
            Creates a case law citation
            </summary>
            <param name="title">Case title</param>
            <param name="url">Case URL</param>
            <param name="court">Court name</param>
            <param name="caseNumber">Case number</param>
            <param name="jurisdiction">Jurisdiction</param>
            <param name="relevanceScore">Relevance score</param>
            <returns>New case law citation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.SetPublicationInfo(System.Nullable{System.DateTime},System.String,System.String)">
            <summary>
            Sets publication information
            </summary>
            <param name="publicationDate">Publication date</param>
            <param name="authors">Authors</param>
            <param name="pageReference">Page reference</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.SetExcerpt(System.String,System.Int32)">
            <summary>
            Sets the excerpt from the source
            </summary>
            <param name="excerpt">Excerpt text</param>
            <param name="maxLength">Maximum excerpt length</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.SetCitationFormat(System.String)">
            <summary>
            Sets the citation format
            </summary>
            <param name="format">Citation format</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.AddMetadata(System.String,System.Object)">
            <summary>
            Adds metadata
            </summary>
            <param name="key">Metadata key</param>
            <param name="value">Metadata value</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.GetMetadata``1(System.String)">
            <summary>
            Gets metadata value
            </summary>
            <typeparam name="T">Value type</typeparam>
            <param name="key">Metadata key</param>
            <returns>Metadata value or default</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.IsLegalCitation">
            <summary>
            Checks if this is a legal citation
            </summary>
            <returns>True if legal citation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.GetFormattedCitation">
            <summary>
            Gets formatted citation string
            </summary>
            <returns>Formatted citation</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.Citation.GetAtomicValues">
            <summary>
            Gets the atomic values for value object equality
            </summary>
            <returns>Atomic values</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext">
            <summary>
            Represents the context and configuration for a conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.ModelType">
            <summary>
            AI model to use for this conversation
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.Mode">
            <summary>
            Conversation mode
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.SystemPrompt">
            <summary>
            System prompt for the AI
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.MaxTokens">
            <summary>
            Maximum tokens per response
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.Temperature">
            <summary>
            Temperature for AI responses (0-1)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.IncludeLegalResearch">
            <summary>
            Whether to include legal research in responses
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.IncludeCitations">
            <summary>
            Whether to include citations in responses
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.Language">
            <summary>
            Preferred language for responses
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.Jurisdiction">
            <summary>
            User's legal jurisdiction
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.UserRole">
            <summary>
            User's role/profession
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.Preferences">
            <summary>
            Conversation preferences
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.CreatedAt">
            <summary>
            Context creation timestamp
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.Create(LexAI.AIAssistant.Domain.ValueObjects.AIModelType,LexAI.AIAssistant.Domain.ValueObjects.ConversationMode,System.String,System.Int32,System.Double,System.Boolean,System.Boolean,System.String,System.String,System.String)">
            <summary>
            Creates a new conversation context
            </summary>
            <param name="modelType">AI model type</param>
            <param name="mode">Conversation mode</param>
            <param name="systemPrompt">System prompt</param>
            <param name="maxTokens">Maximum tokens</param>
            <param name="temperature">Temperature</param>
            <param name="includeLegalResearch">Include legal research</param>
            <param name="includeCitations">Include citations</param>
            <param name="language">Language</param>
            <param name="jurisdiction">Jurisdiction</param>
            <param name="userRole">User role</param>
            <returns>New conversation context</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.CreateDefault">
            <summary>
            Creates default conversation context
            </summary>
            <returns>Default conversation context</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.CreateForResearch(System.String,System.String)">
            <summary>
            Creates context for legal research mode
            </summary>
            <param name="jurisdiction">Legal jurisdiction</param>
            <param name="userRole">User role</param>
            <returns>Research mode context</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.CreateForDocumentAnalysis(System.String,System.String)">
            <summary>
            Creates context for document analysis mode
            </summary>
            <param name="jurisdiction">Legal jurisdiction</param>
            <param name="userRole">User role</param>
            <returns>Document analysis mode context</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.CreateForLegalAdvice(System.String,System.String)">
            <summary>
            Creates context for legal advice mode
            </summary>
            <param name="jurisdiction">Legal jurisdiction</param>
            <param name="userRole">User role</param>
            <returns>Legal advice mode context</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.WithSystemPrompt(System.String)">
            <summary>
            Updates the system prompt
            </summary>
            <param name="systemPrompt">New system prompt</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.WithModelType(LexAI.AIAssistant.Domain.ValueObjects.AIModelType)">
            <summary>
            Updates the model type
            </summary>
            <param name="modelType">New model type</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.WithMode(LexAI.AIAssistant.Domain.ValueObjects.ConversationMode)">
            <summary>
            Updates the conversation mode
            </summary>
            <param name="mode">New conversation mode</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.WithGenerationParams(System.Int32,System.Double)">
            <summary>
            Updates generation parameters
            </summary>
            <param name="maxTokens">Maximum tokens</param>
            <param name="temperature">Temperature</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.WithPreference(System.String,System.Object)">
            <summary>
            Adds or updates a preference
            </summary>
            <param name="key">Preference key</param>
            <param name="value">Preference value</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.GetPreference``1(System.String,``0)">
            <summary>
            Gets a preference value
            </summary>
            <typeparam name="T">Preference type</typeparam>
            <param name="key">Preference key</param>
            <param name="defaultValue">Default value if not found</param>
            <returns>Preference value</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.GetEffectiveSystemPrompt">
            <summary>
            Gets the effective system prompt with context
            </summary>
            <returns>System prompt with context</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.ConversationContext.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment">
            <summary>
            Represents an attachment associated with a message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Id">
            <summary>
            Attachment ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.FileName">
            <summary>
            Original file name
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.FileSize">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.MimeType">
            <summary>
            MIME type of the file
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Type">
            <summary>
            Attachment type
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Url">
            <summary>
            File URL or storage path
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Content">
            <summary>
            File content (for small files or text content)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.FileHash">
            <summary>
            File hash for integrity verification
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.ThumbnailUrl">
            <summary>
            Thumbnail URL for images/videos
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Description">
            <summary>
            File description or alt text
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.IsProcessed">
            <summary>
            Whether the file has been processed
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.ProcessingStatus">
            <summary>
            Processing status message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.ExtractedText">
            <summary>
            Extracted text content (for documents)
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Metadata">
            <summary>
            File metadata
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.UploadedAt">
            <summary>
            Upload timestamp
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.UploadedBy">
            <summary>
            User who uploaded the file
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.Create(System.String,System.Int64,System.String,System.String,System.String)">
            <summary>
            Creates a new message attachment
            </summary>
            <param name="fileName">Original file name</param>
            <param name="fileSize">File size in bytes</param>
            <param name="mimeType">MIME type</param>
            <param name="url">File URL or path</param>
            <param name="uploadedBy">User who uploaded the file</param>
            <returns>New message attachment</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.CreateWithContent(System.String,System.String,System.String,System.String)">
            <summary>
            Creates an attachment with content
            </summary>
            <param name="fileName">Original file name</param>
            <param name="content">File content</param>
            <param name="mimeType">MIME type</param>
            <param name="uploadedBy">User who uploaded the file</param>
            <returns>New message attachment</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.SetFileHash(System.String)">
            <summary>
            Sets the file hash
            </summary>
            <param name="hash">File hash</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.SetThumbnailUrl(System.String)">
            <summary>
            Sets the thumbnail URL
            </summary>
            <param name="thumbnailUrl">Thumbnail URL</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.SetDescription(System.String)">
            <summary>
            Sets the description
            </summary>
            <param name="description">File description</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.MarkAsProcessed(System.String,System.String)">
            <summary>
            Marks the file as processed
            </summary>
            <param name="status">Processing status</param>
            <param name="extractedText">Extracted text content</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.MarkProcessingFailed(System.String)">
            <summary>
            Marks the file processing as failed
            </summary>
            <param name="errorMessage">Error message</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.AddMetadata(System.String,System.Object)">
            <summary>
            Adds metadata
            </summary>
            <param name="key">Metadata key</param>
            <param name="value">Metadata value</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.GetMetadata``1(System.String)">
            <summary>
            Gets metadata value
            </summary>
            <typeparam name="T">Value type</typeparam>
            <param name="key">Metadata key</param>
            <returns>Metadata value or default</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.IsImage">
            <summary>
            Checks if the attachment is an image
            </summary>
            <returns>True if image</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.IsDocument">
            <summary>
            Checks if the attachment is a document
            </summary>
            <returns>True if document</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.GetFileExtension">
            <summary>
            Gets the file extension
            </summary>
            <returns>File extension</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.GetFormattedFileSize">
            <summary>
            Gets human-readable file size
            </summary>
            <returns>Formatted file size</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.DetermineAttachmentType(System.String)">
            <summary>
            Determines attachment type from MIME type
            </summary>
            <param name="mimeType">MIME type</param>
            <returns>Attachment type</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageAttachment.GetAtomicValues">
            <summary>
            Gets the atomic values for value object equality
            </summary>
            <returns>Atomic values</returns>
        </member>
        <member name="T:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata">
            <summary>
            Represents metadata associated with a message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.MessageId">
            <summary>
            Message ID this metadata belongs to
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.UserId">
            <summary>
            User ID who created the message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.IpAddress">
            <summary>
            IP address from which the message was sent
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.UserAgent">
            <summary>
            User agent string
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.DeviceType">
            <summary>
            Device type used to send the message
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.Platform">
            <summary>
            Platform information
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.Browser">
            <summary>
            Browser information
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.SessionId">
            <summary>
            Session ID
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.RequestId">
            <summary>
            Request ID for tracing
            </summary>
        </member>
        <member name="P:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.Properties">
            <summary>
            Additional custom properties
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.Create(System.Guid,System.Guid,System.String,System.String,System.String,System.String)">
            <summary>
            Creates message metadata for a user message
            </summary>
            <param name="messageId">Message ID</param>
            <param name="userId">User ID</param>
            <param name="ipAddress">IP address</param>
            <param name="userAgent">User agent</param>
            <param name="sessionId">Session ID</param>
            <param name="requestId">Request ID</param>
            <returns>New message metadata</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.Create(System.Guid,System.String)">
            <summary>
            Creates message metadata for a system message
            </summary>
            <param name="messageId">Message ID</param>
            <param name="requestId">Request ID</param>
            <returns>New message metadata</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.AddProperty(System.String,System.Object)">
            <summary>
            Adds a custom property
            </summary>
            <param name="key">Property key</param>
            <param name="value">Property value</param>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.GetProperty``1(System.String)">
            <summary>
            Gets a custom property value
            </summary>
            <typeparam name="T">Property type</typeparam>
            <param name="key">Property key</param>
            <returns>Property value or default</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.ExtractDeviceType(System.String)">
            <summary>
            Extracts device type from user agent
            </summary>
            <param name="userAgent">User agent string</param>
            <returns>Device type</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.ExtractPlatform(System.String)">
            <summary>
            Extracts platform from user agent
            </summary>
            <param name="userAgent">User agent string</param>
            <returns>Platform</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.ExtractBrowser(System.String)">
            <summary>
            Extracts browser from user agent
            </summary>
            <param name="userAgent">User agent string</param>
            <returns>Browser</returns>
        </member>
        <member name="M:LexAI.AIAssistant.Domain.ValueObjects.MessageMetadata.GetAtomicValues">
            <summary>
            Gets the atomic values for value object equality
            </summary>
            <returns>Atomic values</returns>
        </member>
    </members>
</doc>
