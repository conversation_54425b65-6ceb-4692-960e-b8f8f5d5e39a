2025-06-15 00:00:13.484 +04:00 [INF] Generating processing statistics
2025-06-15 01:21:42.159 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-15 01:21:42.325 +04:00 [INF] Hangfire SQL objects installed.
2025-06-15 01:21:42.339 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-15 01:21:43.106 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-15 01:21:43.121 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-15 01:21:43.296 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-15 01:21:43.298 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-15 01:21:43.356 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-15 01:21:43.360 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-15 01:21:43.362 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-15 01:21:43.364 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-15 01:21:43.365 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-15 01:21:43.401 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-15 01:21:43.441 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 01:21:43.445 +04:00 [INF] Hosting environment: Development
2025-06-15 01:21:43.494 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-15 01:21:43.562 +04:00 [INF] Server datapreprocessing-kevin11:30712:cd31538a successfully announced in 65.5502 ms
2025-06-15 01:21:43.597 +04:00 [INF] Server datapreprocessing-kevin11:30712:cd31538a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-15 01:21:43.742 +04:00 [INF] 1 servers were removed due to timeout
2025-06-15 01:21:43.908 +04:00 [INF] Server datapreprocessing-kevin11:30712:cd31538a all the dispatchers started
2025-06-15 01:21:44.241 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-15 01:21:44.486 +04:00 [INF] Request GET / started with correlation ID c829a1d2-a8f0-4be4-ac4b-1d8478016623
2025-06-15 01:21:44.546 +04:00 [INF] Generating processing statistics
2025-06-15 01:21:46.305 +04:00 [INF] Request GET / completed in 1814ms with status 404 (Correlation ID: c829a1d2-a8f0-4be4-ac4b-1d8478016623)
2025-06-15 01:21:46.314 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 2076.18ms
2025-06-15 01:21:46.322 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
