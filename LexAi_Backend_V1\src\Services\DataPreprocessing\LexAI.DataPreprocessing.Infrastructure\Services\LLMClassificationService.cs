using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using System.Text.Json;
using LexAI.Shared.Application.DTOs;
using LexAI.Shared.Application.Interfaces;
using LexAI.Shared.Application.Extensions;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Service de classification utilisant le service LLM unifié
/// </summary>
public class LLMClassificationService : IClassificationService
{
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<LLMClassificationService> _logger;
    private readonly IConfiguration _configuration;

    public LLMClassificationService(
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<LLMClassificationService> logger)
    {
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Classifie un texte en domaines juridiques
    /// </summary>
    public async Task<ClassificationResult> ClassifyTextAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Classifying text using unified LLM service. Text length: {Length}", text.Length);

            var prompt = CreateClassificationPrompt(text);
            var options = LLMServiceExtensions.ForClassification(1000);

            var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

            if (!result.Success)
            {
                _logger.LogWarning("LLM classification failed: {Error}", result.Error);
                return new ClassificationResult
                {
                    Success = false,
                    ErrorMessage = result.Error ?? "LLM classification failed"
                };
            }

            return ParseClassificationResult(result.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during LLM classification");
            return new ClassificationResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <summary>
    /// Extrait les mots-clés d'un texte
    /// </summary>
    public async Task<IEnumerable<string>> ExtractKeywordsAsync(string text, CancellationToken cancellationToken = default)
    {
        return await ExtractKeywordsAsync(text, 15, cancellationToken);
    }

    /// <summary>
    /// Extrait les mots-clés d'un texte avec limite
    /// </summary>
    public async Task<IEnumerable<string>> ExtractKeywordsAsync(string text, int maxKeywords, CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = CreateKeywordExtractionPrompt(text, maxKeywords);
            var options = LLMServiceExtensions.ForClassification(800);

            var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

            if (!result.Success)
            {
                _logger.LogWarning("LLM keyword extraction failed: {Error}", result.Error);
                return new List<string>();
            }

            return ParseKeywords(result.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting keywords with LLM");
            return new List<string>();
        }
    }

    /// <summary>
    /// Extrait les entités nommées d'un texte
    /// </summary>
    public async Task<IEnumerable<NamedEntity>> ExtractNamedEntitiesAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            var prompt = CreateEntityExtractionPrompt(text);
            var options = LLMServiceExtensions.ForClassification(1200);

            var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

            if (!result.Success)
            {
                _logger.LogWarning("LLM entity extraction failed: {Error}", result.Error);
                return new List<NamedEntity>();
            }

            return ParseNamedEntities(result.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting entities with LLM");
            return new List<NamedEntity>();
        }
    }



    private string CreateClassificationPrompt(string text)
    {
        return $$"""
        Tu es un expert en classification de documents juridiques français.

        Analyse le texte suivant et détermine le domaine juridique principal parmi ces options :
        - Civil: Droit civil, contrats, responsabilité civile
        - Criminal: Droit pénal, infractions, procédure pénale
        - Administrative: Droit administratif, fonction publique
        - Commercial: Droit des affaires, sociétés, commerce
        - Labor: Droit du travail, relations sociales
        - Constitutional: Droit constitutionnel, libertés fondamentales
        - Tax: Droit fiscal, impôts
        - Environmental: Droit de l'environnement
        - Intellectual: Propriété intellectuelle
        - International: Droit international
        - Family: Droit de la famille
        - Real: Droit immobilier
        - Insurance: Droit des assurances
        - Banking: Droit bancaire
        - Competition: Droit de la concurrence
        - Consumer: Droit de la consommation
        - Health: Droit de la santé
        - Education: Droit de l'éducation
        - Media: Droit des médias
        - Other: Autre domaine juridique

        Texte à analyser :
        {{text.Substring(0, Math.Min(text.Length, 2000))}}

        Réponds UNIQUEMENT au format JSON suivant :
        {
            "domain": "NomDuDomaine",
            "confidence": 0.85,
            "reasoning": "Explication courte de ton choix"
        }
        """;
    }

    private string CreateKeywordExtractionPrompt(string text, int maxKeywords = 15)
    {
        return $$"""
        Extrait les mots-clés juridiques les plus importants du texte suivant.
        Concentre-toi sur les termes juridiques, les concepts de droit, les références légales.

        Texte :
        {{text.Substring(0, Math.Min(text.Length, 1500))}}

        Réponds avec une liste de mots-clés séparés par des virgules, maximum {{maxKeywords}} mots-clés.
        """;
    }

    private string CreateEntityExtractionPrompt(string text)
    {
        return $$"""
        Extrait les entités nommées juridiques du texte suivant :
        - PERSON: Noms de personnes (juges, avocats, parties)
        - ORGANIZATION: Tribunaux, entreprises, institutions
        - LOCATION: Lieux, juridictions
        - LAW: Références à des lois, codes, articles
        - DATE: Dates importantes
        - CASE: Numéros d'affaires, références de jurisprudence

        Texte :
        {{text.Substring(0, Math.Min(text.Length, 1500))}}

        Réponds au format JSON :
        [
            {"text": "entité", "type": "TYPE", "confidence": 0.9}
        ]
        """;
    }

    private ClassificationResult ParseClassificationResult(string response)
    {
        try
        {
            var jsonResponse = JsonSerializer.Deserialize<JsonElement>(response);

            var domainStr = jsonResponse.GetProperty("domain").GetString();
            var confidence = jsonResponse.GetProperty("confidence").GetDouble();

            if (Enum.TryParse<LegalDomain>(domainStr, out var domain))
            {
                return new ClassificationResult
                {
                    DetectedDomain = domain,
                    Confidence = confidence,
                    DomainScores = new Dictionary<LegalDomain, double> { [domain] = confidence },
                    Success = true
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse LLM classification response: {Response}", response);
        }

        return new ClassificationResult
        {
            Success = false,
            ErrorMessage = "Failed to parse LLM response"
        };
    }

    private IEnumerable<string> ParseKeywords(string response)
    {
        try
        {
            return response
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(k => k.Trim())
                .Where(k => !string.IsNullOrEmpty(k))
                .Take(15);
        }
        catch
        {
            return new List<string>();
        }
    }

    private IEnumerable<NamedEntity> ParseNamedEntities(string response)
    {
        try
        {
            var entities = JsonSerializer.Deserialize<JsonElement[]>(response);
            return entities.Select(e => NamedEntity.Create(
                e.GetProperty("text").GetString() ?? "",
                Enum.TryParse<EntityType>(e.GetProperty("type").GetString(), out var entityType) ? entityType : EntityType.Other,
                0, // Start position - à améliorer
                0, // End position - à améliorer
                e.TryGetProperty("confidence", out var conf) ? conf.GetDouble() : 0.8
            ));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse entities response: {Response}", response);
            return new List<NamedEntity>();
        }
    }
}
