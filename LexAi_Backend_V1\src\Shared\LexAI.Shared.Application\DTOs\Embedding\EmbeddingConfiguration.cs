﻿
namespace LexAI.Shared.Application.DTOs.Embedding
{
    /// <summary>
    /// Configuration pour les embeddings
    /// </summary>
    public class EmbeddingConfiguration
    {
        public EmbeddingProvider Provider { get; set; } = EmbeddingProvider.Local;
        public string Model { get; set; } = string.Empty;
        public int MaxTokens { get; set; } = 512;
        public bool EnableFallback { get; set; } = true;
        public EmbeddingProvider FallbackProvider { get; set; } = EmbeddingProvider.Local;
        public Dictionary<string, object>? ProviderSettings { get; set; }
    }

}
