namespace LexAI.Shared.Infrastructure.Configuration;

/// <summary>
/// Configuration settings for JWT authentication
/// </summary>
public class JwtSettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "JwtSettings";

    /// <summary>
    /// Secret key for signing JWT tokens
    /// </summary>
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// Token issuer
    /// </summary>
    public string Issuer { get; set; } = string.Empty;

    /// <summary>
    /// Token audience
    /// </summary>
    public string Audience { get; set; } = string.Empty;

    /// <summary>
    /// Access token expiration time in minutes
    /// </summary>
    public int AccessTokenExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Refresh token expiration time in days
    /// </summary>
    public int RefreshTokenExpirationDays { get; set; } = 7;

    /// <summary>
    /// Clock skew tolerance in minutes
    /// </summary>
    public int ClockSkewMinutes { get; set; } = 5;

    /// <summary>
    /// Validate token issuer
    /// </summary>
    public bool ValidateIssuer { get; set; } = true;

    /// <summary>
    /// Validate token audience
    /// </summary>
    public bool ValidateAudience { get; set; } = true;

    /// <summary>
    /// Validate token lifetime
    /// </summary>
    public bool ValidateLifetime { get; set; } = true;

    /// <summary>
    /// Validate issuer signing key
    /// </summary>
    public bool ValidateIssuerSigningKey { get; set; } = true;

    /// <summary>
    /// Require HTTPS for token validation
    /// </summary>
    public bool RequireHttpsMetadata { get; set; } = true;
}

/// <summary>
/// Configuration settings for OpenAI integration
/// </summary>
public class OpenAISettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "OpenAI";

    /// <summary>
    /// OpenAI API key
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// OpenAI organization ID (optional)
    /// </summary>
    public string? OrganizationId { get; set; }

    /// <summary>
    /// Default model for text completion
    /// </summary>
    public string DefaultModel { get; set; } = "gpt-4";

    /// <summary>
    /// Default model for embeddings
    /// </summary>
    public string EmbeddingModel { get; set; } = "text-embedding-ada-002";

    /// <summary>
    /// Maximum tokens for completion requests
    /// </summary>
    public int MaxTokens { get; set; } = 4000;

    /// <summary>
    /// Temperature for text generation (0.0 to 2.0)
    /// </summary>
    public double Temperature { get; set; } = 0.7;

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// Maximum retry attempts for failed requests
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base URL for OpenAI API (for Azure OpenAI or custom endpoints)
    /// </summary>
    public string? BaseUrl { get; set; }
}

/// <summary>
/// Configuration settings for file storage
/// </summary>
public class FileStorageSettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "FileStorage";

    /// <summary>
    /// Storage provider type (Local, AzureBlob, S3)
    /// </summary>
    public string Provider { get; set; } = "Local";

    /// <summary>
    /// Base path for local file storage
    /// </summary>
    public string LocalPath { get; set; } = "./uploads";

    /// <summary>
    /// Azure Blob Storage connection string
    /// </summary>
    public string? AzureBlobConnectionString { get; set; }

    /// <summary>
    /// Azure Blob Storage container name
    /// </summary>
    public string? AzureBlobContainerName { get; set; }

    /// <summary>
    /// AWS S3 access key
    /// </summary>
    public string? S3AccessKey { get; set; }

    /// <summary>
    /// AWS S3 secret key
    /// </summary>
    public string? S3SecretKey { get; set; }

    /// <summary>
    /// AWS S3 bucket name
    /// </summary>
    public string? S3BucketName { get; set; }

    /// <summary>
    /// AWS S3 region
    /// </summary>
    public string? S3Region { get; set; }

    /// <summary>
    /// Maximum file size in bytes
    /// </summary>
    public long MaxFileSizeBytes { get; set; } = 50 * 1024 * 1024; // 50 MB

    /// <summary>
    /// Allowed file extensions
    /// </summary>
    public string[] AllowedExtensions { get; set; } = { ".pdf", ".docx", ".doc", ".txt" };

    /// <summary>
    /// Enable virus scanning
    /// </summary>
    public bool EnableVirusScanning { get; set; } = true;

    /// <summary>
    /// Enable file encryption at rest
    /// </summary>
    public bool EnableEncryption { get; set; } = true;
}
