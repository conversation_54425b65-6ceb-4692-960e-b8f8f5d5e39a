2025-06-04 11:40:06.391 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:40:06.470 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:40:06.486 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 11:40:08.893 +04:00 [INF] Executed DbCommand (212ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 11:40:08.919 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 11:40:09.001 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 11:40:10.333 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-04 11:40:10.335 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 11:40:10.557 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 11:40:10.560 +04:00 [INF] Hosting environment: Development
2025-06-04 11:40:10.563 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 11:40:12.795 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-04 11:40:13.446 +04:00 [INF] Request GET / started with correlation ID 45dbcea3-906d-49e5-9ac1-f4abc4d9d968
2025-06-04 11:40:15.242 +04:00 [INF] Request GET / completed in 1784ms with status 404 (Correlation ID: 45dbcea3-906d-49e5-9ac1-f4abc4d9d968)
2025-06-04 11:40:15.256 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 2491.2218ms
2025-06-04 11:40:15.267 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-04 11:46:13.611 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:46:13.642 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 11:46:13.647 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 11:46:14.097 +04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 11:46:14.115 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 11:46:14.151 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 11:46:14.378 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-04 11:46:14.380 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 11:46:14.420 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 11:46:14.423 +04:00 [INF] Hosting environment: Development
2025-06-04 11:46:14.425 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 11:46:15.624 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-04 11:46:15.800 +04:00 [INF] Request GET / started with correlation ID 5a9fc9fd-bd11-41bb-8582-1450b6670ac9
2025-06-04 11:46:15.911 +04:00 [INF] Request GET / completed in 100ms with status 404 (Correlation ID: 5a9fc9fd-bd11-41bb-8582-1450b6670ac9)
2025-06-04 11:46:15.922 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 313.8492ms
2025-06-04 11:46:15.929 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-04 11:47:13.871 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - null null
2025-06-04 11:47:13.886 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 8f7dad7a-3bb9-488a-adff-110517a0feca
2025-06-04 11:47:13.894 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.900 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 8ms with status 204 (Correlation ID: 8f7dad7a-3bb9-488a-adff-110517a0feca)
2025-06-04 11:47:13.906 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:59998/api/auth/refresh - 204 null null 35.3861ms
2025-06-04 11:47:13.909 +04:00 [INF] Request starting HTTP/2 POST https://localhost:59998/api/auth/refresh - application/json 107
2025-06-04 11:47:13.938 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 1a5afe43-2c3b-4ca3-803e-e1b2b7f04bd7
2025-06-04 11:47:13.943 +04:00 [INF] CORS policy execution successful.
2025-06-04 11:47:13.948 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 11:47:14.017 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 11:47:14.106 +04:00 [INF] Token refresh attempt
2025-06-04 11:47:14.226 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-04 11:47:15.344 +04:00 [INF] Executed DbCommand (67ms) [Parameters=[@__token_0='XTpCuQd2hdAxzZ5Ts3zGGZv9wzDnNlm8PbaIKXAiZiJNOl4NHGdfZakyo3m15Ss+lMUnVZsb8scDN4OG+SaDww=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 11:47:15.821 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 11:47:15.887 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 11:47:15.936 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__token_0='XTpCuQd2hdAxzZ5Ts3zGGZv9wzDnNlm8PbaIKXAiZiJNOl4NHGdfZakyo3m15Ss+lMUnVZsb8scDN4OG+SaDww=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 11:47:16.230 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@p16='77722b4f-e2fe-4dfd-b8e7-7f1b8b99702c', @p0='2025-06-02T20:16:51.6657010Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-09T20:16:51.6637010Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-04T07:47:15.9437245Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='XTpCuQd2hdAxzZ5Ts3zGGZv9wzDnNlm8PbaIKXAiZiJNOl4NHGdfZakyo3m15Ss+lMUnVZsb8scDN4OG+SaDww==' (Nullable = false), @p12='2025-06-04T07:47:16.0590941Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='819' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-04 11:47:16.269 +04:00 [INF] Refresh token updated successfully: "77722b4f-e2fe-4dfd-b8e7-7f1b8b99702c"
2025-06-04 11:47:16.392 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='9fe0ce38-b537-446f-b2c1-467bd37eef01', @p1='2025-06-04T07:47:16.3750259Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-11T07:47:16.3474434Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='/9qzp1tbZeCGiEjKV/tMITBYd/U+Ltym+FTgfA7/EBT0irof3QYlMZyOBGsil6PAqLoln7/iFAsGiX+kua5Kpw==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-04 11:47:16.401 +04:00 [INF] Refresh token added successfully: "9fe0ce38-b537-446f-b2c1-467bd37eef01"
2025-06-04 11:47:16.403 +04:00 [INF] Token refreshed successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 11:47:16.409 +04:00 [INF] Token refresh successful
2025-06-04 11:47:16.425 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-04 11:47:16.499 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2467.6502ms
2025-06-04 11:47:16.505 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 11:47:16.508 +04:00 [INF] Request POST /api/auth/refresh completed in 2566ms with status 200 (Correlation ID: 1a5afe43-2c3b-4ca3-803e-e1b2b7f04bd7)
2025-06-04 11:47:16.522 +04:00 [INF] Request finished HTTP/2 POST https://localhost:59998/api/auth/refresh - 200 null application/json; charset=utf-8 2612.609ms
2025-06-04 13:27:51.504 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 13:27:51.554 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 13:27:51.565 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 13:27:58.097 +04:00 [INF] Executed DbCommand (179ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 13:27:58.139 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 13:27:58.231 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 13:27:58.891 +04:00 [INF] Now listening on: https://localhost:59998
2025-06-04 13:27:58.893 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 13:27:59.037 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 13:27:59.042 +04:00 [INF] Hosting environment: Development
2025-06-04 13:27:59.044 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 13:28:02.672 +04:00 [INF] Request starting HTTP/2 GET https://localhost:59998/ - null null
2025-06-04 13:28:03.011 +04:00 [INF] Request GET / started with correlation ID 2f1ef664-7879-42fc-b528-9b5cbdfc0776
2025-06-04 13:28:03.379 +04:00 [INF] Request GET / completed in 357ms with status 404 (Correlation ID: 2f1ef664-7879-42fc-b528-9b5cbdfc0776)
2025-06-04 13:28:03.396 +04:00 [INF] Request finished HTTP/2 GET https://localhost:59998/ - 404 0 null 741.8278ms
2025-06-04 13:28:03.406 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:59998/, Response status code: 404
2025-06-04 14:00:16.749 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 14:00:16.811 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 14:00:16.822 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 14:00:18.437 +04:00 [INF] Executed DbCommand (154ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 14:00:18.460 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 14:00:18.529 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 14:00:19.272 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-04 14:00:19.301 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 14:00:19.753 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 14:00:19.756 +04:00 [INF] Hosting environment: Development
2025-06-04 14:00:19.758 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 14:00:20.153 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-04 14:00:20.722 +04:00 [INF] Request GET / started with correlation ID 8ae3b3ef-ab11-41bf-b30a-986dc4c2a02c
2025-06-04 14:00:21.042 +04:00 [INF] Request GET / completed in 309ms with status 404 (Correlation ID: 8ae3b3ef-ab11-41bf-b30a-986dc4c2a02c)
2025-06-04 14:00:21.066 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 930.4573ms
2025-06-04 14:00:21.078 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-04 14:03:03.108 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-04 14:03:03.161 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 5e4f7fee-9ba4-4dd9-b3dd-c3f812ee195c
2025-06-04 14:03:03.168 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:03.178 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 12ms with status 204 (Correlation ID: 5e4f7fee-9ba4-4dd9-b3dd-c3f812ee195c)
2025-06-04 14:03:03.182 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 77.5746ms
2025-06-04 14:03:03.184 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-04 14:03:03.192 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 2aedf6eb-30ee-4ee5-9e1b-6b43a192d61c
2025-06-04 14:03:03.194 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:03.253 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 8:47:16 AM', Current time (UTC): '6/4/2025 10:03:03 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-04 14:03:03.288 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 8:47:16 AM', Current time (UTC): '6/4/2025 10:03:03 AM'.
2025-06-04 14:03:03.290 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 8:47:16 AM', Current time (UTC): '6/4/2025 10:03:03 AM'.
2025-06-04 14:03:03.295 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 14:03:03.300 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 14:03:03.301 +04:00 [INF] Request POST /api/auth/logout completed in 107ms with status 401 (Correlation ID: 2aedf6eb-30ee-4ee5-9e1b-6b43a192d61c)
2025-06-04 14:03:03.304 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 119.7867ms
2025-06-04 14:03:03.310 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-04 14:03:03.313 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 3f992a05-a502-46b3-8c54-7f9c1a8559f0
2025-06-04 14:03:03.316 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:03.317 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: 3f992a05-a502-46b3-8c54-7f9c1a8559f0)
2025-06-04 14:03:03.321 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 10.859ms
2025-06-04 14:03:03.324 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-04 14:03:03.329 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 372ca00a-d445-4ab4-82e2-8923c8cbdaf3
2025-06-04 14:03:03.332 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:03.339 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 14:03:03.396 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 14:03:03.445 +04:00 [INF] Token refresh attempt
2025-06-04 14:03:03.574 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-04 14:03:04.014 +04:00 [INF] Executed DbCommand (43ms) [Parameters=[@__token_0='/9qzp1tbZeCGiEjKV/tMITBYd/U+Ltym+FTgfA7/EBT0irof3QYlMZyOBGsil6PAqLoln7/iFAsGiX+kua5Kpw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 14:03:04.144 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 14:03:04.172 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 14:03:04.192 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='/9qzp1tbZeCGiEjKV/tMITBYd/U+Ltym+FTgfA7/EBT0irof3QYlMZyOBGsil6PAqLoln7/iFAsGiX+kua5Kpw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 14:03:04.290 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p16='9fe0ce38-b537-446f-b2c1-467bd37eef01', @p0='2025-06-04T07:47:16.3750250Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-11T07:47:16.3474430Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-04T10:03:04.1962072Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='/9qzp1tbZeCGiEjKV/tMITBYd/U+Ltym+FTgfA7/EBT0irof3QYlMZyOBGsil6PAqLoln7/iFAsGiX+kua5Kpw==' (Nullable = false), @p12='2025-06-04T10:03:04.2325474Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='821' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-04 14:03:04.306 +04:00 [INF] Refresh token updated successfully: "9fe0ce38-b537-446f-b2c1-467bd37eef01"
2025-06-04 14:03:04.332 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='b1e02255-b1b0-4340-82f5-bb5caeba0651', @p1='2025-06-04T10:03:04.3248154Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-11T10:03:04.3165077Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='f5Gxj5mNGd/Oq9Oi4KbeomzzXAKVW9KG+G6U4by0ZVTZ/VunrUxorb/niFlc7/ULxNZM42soDbsjznW4JKTTpQ==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-04 14:03:04.335 +04:00 [INF] Refresh token added successfully: "b1e02255-b1b0-4340-82f5-bb5caeba0651"
2025-06-04 14:03:04.336 +04:00 [INF] Token refreshed successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 14:03:04.338 +04:00 [INF] Token refresh successful
2025-06-04 14:03:04.344 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-04 14:03:04.362 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 960.2537ms
2025-06-04 14:03:04.364 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 14:03:04.366 +04:00 [INF] Request POST /api/auth/refresh completed in 1033ms with status 200 (Correlation ID: 372ca00a-d445-4ab4-82e2-8923c8cbdaf3)
2025-06-04 14:03:04.375 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 1050.8632ms
2025-06-04 14:03:04.431 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-04 14:03:04.436 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 6b6816d5-cda5-43dd-8696-aff5d369ba6f
2025-06-04 14:03:04.438 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:04.444 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 14:03:04.447 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API)'
2025-06-04 14:03:04.455 +04:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout(Boolean) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 14:03:04.461 +04:00 [INF] Logout attempt for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 14:03:04.504 +04:00 [INF] Logout completed successfully
2025-06-04 14:03:04.506 +04:00 [INF] Logout successful for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 14:03:04.507 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-04 14:03:04.511 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API) in 52.9403ms
2025-06-04 14:03:04.514 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API)'
2025-06-04 14:03:04.516 +04:00 [INF] Request POST /api/auth/logout completed in 78ms with status 200 (Correlation ID: 6b6816d5-cda5-43dd-8696-aff5d369ba6f)
2025-06-04 14:03:04.519 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 200 null application/json; charset=utf-8 87.9436ms
2025-06-04 14:03:34.385 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-04 14:03:34.404 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID fd29bd70-7fd4-45da-96da-ef703867ae6b
2025-06-04 14:03:34.416 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:34.422 +04:00 [INF] Request OPTIONS /api/auth/login completed in 6ms with status 204 (Correlation ID: fd29bd70-7fd4-45da-96da-ef703867ae6b)
2025-06-04 14:03:34.434 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 49.6708ms
2025-06-04 14:03:34.438 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 52
2025-06-04 14:03:34.457 +04:00 [INF] Request POST /api/auth/login started with correlation ID 4869aed2-b001-46ed-877e-25f8b414c4c0
2025-06-04 14:03:34.460 +04:00 [INF] CORS policy execution successful.
2025-06-04 14:03:34.461 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-04 14:03:34.473 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 14:03:34.482 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-04 14:03:34.489 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-04 14:03:34.504 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 14:03:34.523 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 14:03:34.832 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='eb8fa713-068f-4d29-9578-66d584caa98e', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-04T10:03:34.7839303Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-04T10:03:34.7840604Z' (DbType = DateTime), @p12='2025-06-04T10:03:34.7999189Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5' (Nullable = false), @p37='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p16='2025-06-01T17:33:42.5262650Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Kevin' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-04T10:03:34.7836130Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Jules' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$eWMQSH57bq.BO5srOIQ1AutHq3dday.oDdj4wcF798QSqVq1WX93S' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='Administrator' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-04T10:03:34.7999177Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='818' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-04 14:03:34.849 +04:00 [INF] User updated successfully: "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 14:03:34.855 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='9942c32a-99d9-4d01-832d-d8ca335b777d', @p1='2025-06-04T10:03:34.8522293Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-11T10:03:34.8516174Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='+4YAPnDUuCZ+Qv87p4P9E4yJpa79A2kDlnNDnme0YIG56Tg2vRX16/lK1Pm4YM0XHRYzoegKuXPa++1qb4/+0A==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-04 14:03:34.857 +04:00 [INF] Refresh token added successfully: "9942c32a-99d9-4d01-832d-d8ca335b777d"
2025-06-04 14:03:34.859 +04:00 [INF] User "62e0a975-363b-47ac-abf8-68fcf260cbb5" logged in successfully
2025-06-04 14:03:34.861 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-04 14:03:34.862 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-04 14:03:34.864 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 387.617ms
2025-06-04 14:03:34.865 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-04 14:03:34.867 +04:00 [INF] Request POST /api/auth/login completed in 407ms with status 200 (Correlation ID: 4869aed2-b001-46ed-877e-25f8b414c4c0)
2025-06-04 14:03:34.871 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 200 null application/json; charset=utf-8 432.8681ms
2025-06-04 14:38:02.010 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 14:38:02.062 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 14:38:02.074 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 14:38:02.800 +04:00 [INF] Executed DbCommand (78ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 14:38:02.810 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 14:38:02.861 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 14:38:04.131 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-04 14:38:04.135 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 14:38:04.180 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 14:38:04.184 +04:00 [INF] Hosting environment: Development
2025-06-04 14:38:04.187 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 14:38:06.154 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-04 14:38:06.487 +04:00 [INF] Request GET / started with correlation ID 68768f71-24bc-4499-9aaf-512592a98a88
2025-06-04 14:38:06.846 +04:00 [INF] Request GET / completed in 309ms with status 404 (Correlation ID: 68768f71-24bc-4499-9aaf-512592a98a88)
2025-06-04 14:38:06.864 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 715.0961ms
2025-06-04 14:38:06.876 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-04 20:20:26.323 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 20:20:26.367 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-04 20:20:26.377 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-04 20:20:26.986 +04:00 [INF] Executed DbCommand (88ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 20:20:26.999 +04:00 [INF] LexAI Identity Service started successfully
2025-06-04 20:20:27.034 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 20:20:27.472 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-04 20:20:27.474 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-04 20:20:27.894 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 20:20:27.900 +04:00 [INF] Hosting environment: Development
2025-06-04 20:20:27.905 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-04 20:20:33.371 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-04 20:20:33.596 +04:00 [INF] Request GET / started with correlation ID 932e6eba-9654-4e1e-8e4c-e6cb573e107f
2025-06-04 20:20:33.746 +04:00 [INF] Request GET / completed in 139ms with status 404 (Correlation ID: 932e6eba-9654-4e1e-8e4c-e6cb573e107f)
2025-06-04 20:20:33.756 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 396.6101ms
2025-06-04 20:20:33.768 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-04 20:27:49.496 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-04 20:27:49.540 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID a6fd64b7-d299-41a6-87c7-d2b52bd2b808
2025-06-04 20:27:49.548 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:27:49.553 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 8ms with status 204 (Correlation ID: a6fd64b7-d299-41a6-87c7-d2b52bd2b808)
2025-06-04 20:27:49.558 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 61.994ms
2025-06-04 20:27:49.560 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-04 20:27:49.569 +04:00 [INF] Request POST /api/auth/logout started with correlation ID aea17628-96be-400a-ba5f-5fa585210b92
2025-06-04 20:27:49.572 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:27:49.702 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 11:03:34 AM', Current time (UTC): '6/4/2025 4:27:49 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-04 20:27:49.750 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 11:03:34 AM', Current time (UTC): '6/4/2025 4:27:49 PM'.
2025-06-04 20:27:49.754 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 11:03:34 AM', Current time (UTC): '6/4/2025 4:27:49 PM'.
2025-06-04 20:27:49.769 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-04 20:27:49.783 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-04 20:27:49.787 +04:00 [INF] Request POST /api/auth/logout completed in 216ms with status 401 (Correlation ID: aea17628-96be-400a-ba5f-5fa585210b92)
2025-06-04 20:27:49.794 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 234.1081ms
2025-06-04 20:27:49.800 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-04 20:27:49.804 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID cf286e5c-f5c3-4cf2-a724-d9ed04ab9198
2025-06-04 20:27:49.806 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:27:49.808 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: cf286e5c-f5c3-4cf2-a724-d9ed04ab9198)
2025-06-04 20:27:49.812 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 11.4453ms
2025-06-04 20:27:49.813 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-04 20:27:49.820 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 7fc8bac6-cc34-4edb-8fc4-c4b795361cde
2025-06-04 20:27:49.822 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:27:49.827 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 20:27:49.877 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 20:27:49.968 +04:00 [INF] Token refresh attempt
2025-06-04 20:27:50.059 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-04 20:27:51.184 +04:00 [INF] Executed DbCommand (37ms) [Parameters=[@__token_0='+4YAPnDUuCZ+Qv87p4P9E4yJpa79A2kDlnNDnme0YIG56Tg2vRX16/lK1Pm4YM0XHRYzoegKuXPa++1qb4/+0A=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 20:27:51.589 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 20:27:51.645 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 20:27:51.694 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='+4YAPnDUuCZ+Qv87p4P9E4yJpa79A2kDlnNDnme0YIG56Tg2vRX16/lK1Pm4YM0XHRYzoegKuXPa++1qb4/+0A=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-04 20:27:52.012 +04:00 [INF] Executed DbCommand (25ms) [Parameters=[@p16='9942c32a-99d9-4d01-832d-d8ca335b777d', @p0='2025-06-04T10:03:34.8522290Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-11T10:03:34.8516170Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-04T16:27:51.7026777Z' (Nullable = true) (DbType = DateTime), @p10='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p11='+4YAPnDUuCZ+Qv87p4P9E4yJpa79A2kDlnNDnme0YIG56Tg2vRX16/lK1Pm4YM0XHRYzoegKuXPa++1qb4/+0A==' (Nullable = false), @p12='2025-06-04T16:27:51.8274774Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='62e0a975-363b-47ac-abf8-68fcf260cbb5', @p17='825' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-04 20:27:52.077 +04:00 [INF] Refresh token updated successfully: "9942c32a-99d9-4d01-832d-d8ca335b777d"
2025-06-04 20:27:52.150 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='9931e58e-e68f-4290-b387-3a049915b167', @p1='2025-06-04T16:27:52.1357580Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-11T16:27:52.1082317Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='D2BOabMymowb46Cse4AxKrG52Jp7ca9AUQt+aNbZMhWEu60Hv90mtbHd2RBNx6iv3CvrZLSAyCV8T1LMWkO+nA==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='62e0a975-363b-47ac-abf8-68fcf260cbb5'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-04 20:27:52.155 +04:00 [INF] Refresh token added successfully: "9931e58e-e68f-4290-b387-3a049915b167"
2025-06-04 20:27:52.157 +04:00 [INF] Token refreshed successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 20:27:52.162 +04:00 [INF] Token refresh successful
2025-06-04 20:27:52.183 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-04 20:27:52.256 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2364.0881ms
2025-06-04 20:27:52.261 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-04 20:27:52.269 +04:00 [INF] Request POST /api/auth/refresh completed in 2446ms with status 200 (Correlation ID: 7fc8bac6-cc34-4edb-8fc4-c4b795361cde)
2025-06-04 20:27:52.295 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 2481.0026ms
2025-06-04 20:27:52.345 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-04 20:27:52.353 +04:00 [INF] Request POST /api/auth/logout started with correlation ID fcbf6d3c-ded1-4f08-b231-b2b368c33745
2025-06-04 20:27:52.357 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:27:52.380 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-04 20:27:52.388 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API)'
2025-06-04 20:27:52.404 +04:00 [INF] Route matched with {action = "Logout", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] Logout(Boolean) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 20:27:52.418 +04:00 [INF] Logout attempt for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 20:27:52.591 +04:00 [INF] Logout completed successfully
2025-06-04 20:27:52.594 +04:00 [INF] Logout successful for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-04 20:27:52.599 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-04 20:27:52.612 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API) in 200.7718ms
2025-06-04 20:27:52.620 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Logout (LexAI.Identity.API)'
2025-06-04 20:27:52.623 +04:00 [INF] Request POST /api/auth/logout completed in 266ms with status 200 (Correlation ID: fcbf6d3c-ded1-4f08-b231-b2b368c33745)
2025-06-04 20:27:52.629 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 200 null application/json; charset=utf-8 283.8529ms
2025-06-04 20:36:28.352 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/register - null null
2025-06-04 20:36:28.370 +04:00 [INF] Request OPTIONS /api/auth/register started with correlation ID 2ab4e932-24b3-4aae-ba4c-e470ad687616
2025-06-04 20:36:28.377 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:36:28.382 +04:00 [INF] Request OPTIONS /api/auth/register completed in 5ms with status 204 (Correlation ID: 2ab4e932-24b3-4aae-ba4c-e470ad687616)
2025-06-04 20:36:28.395 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/register - 204 null null 43.2814ms
2025-06-04 20:36:28.399 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/register - application/json 262
2025-06-04 20:36:28.422 +04:00 [INF] Request POST /api/auth/register started with correlation ID 3ae84f40-371d-42ab-ac88-f815c0c796cc
2025-06-04 20:36:28.425 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:36:28.427 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-04 20:36:28.443 +04:00 [INF] Route matched with {action = "Register", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.UserDto]] Register(LexAI.Identity.Application.DTOs.RegisterUserDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 20:36:28.486 +04:00 [INF] Registration attempt <NAME_EMAIL>
2025-06-04 20:36:28.504 +04:00 [INF] Public registration attempt <NAME_EMAIL>
2025-06-04 20:36:28.556 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-04 20:36:28.698 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 20:36:29.542 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p0='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p1='2025-06-04T16:36:29.4616845Z' (DbType = DateTime), @p2='system', @p3=NULL (DbType = DateTime), @p4=NULL, @p5='0', @p6='Senior' (Nullable = false), @p7='True', @p8='False', @p9='False', @p10='False', @p11=NULL (DbType = DateTime), @p12=NULL, @p13='Lawyer' (Nullable = false), @p14=NULL (DbType = DateTime), @p15='$2a$11$IwkLcL8eN5wkkUnfgHtMZeW8Amjcv2Ot9F4VxIZ3WFp9cAD.cqVjy' (Nullable = false), @p16='fr-FR' (Nullable = false), @p17=NULL, @p18='Lawyer' (Nullable = false), @p19='Europe/Paris' (Nullable = false), @p20='2025-06-04T16:36:29.3645385Z' (Nullable = true) (DbType = DateTime), @p21='system', @p22='<EMAIL>' (Nullable = false), @p23='+230', @p24='+23054878091', @p25='1e529a56-9165-4c2d-a7bb-a53dfa0c1fbf', @p26='PasswordChanged' (Nullable = false), @p27='null' (DbType = Object), @p28='2025-06-04T16:36:29.4616881Z' (DbType = DateTime), @p29=NULL, @p30=NULL (DbType = DateTime), @p31=NULL, @p32='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p33='User' (Nullable = false), @p34=NULL, @p35='False', @p36='2025-06-04T16:36:29.3590036Z' (DbType = DateTime), @p37=NULL (DbType = DateTime), @p38=NULL, @p39=NULL, @p40='system' (Nullable = false), @p41='3adebd03-506c-4db3-9395-dc2d6595c7ee', @p42='Created' (Nullable = false), @p43='"User created with role Lawyer"' (DbType = Object), @p44='2025-06-04T16:36:29.4616873Z' (DbType = DateTime), @p45=NULL, @p46=NULL (DbType = DateTime), @p47=NULL, @p48='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p49='User' (Nullable = false), @p50=NULL, @p51='False', @p52='2025-06-04T16:36:28.7660206Z' (DbType = DateTime), @p53=NULL (DbType = DateTime), @p54=NULL, @p55=NULL, @p56='system' (Nullable = false), @p57='63193e8b-0d5d-4087-8ef7-683a5e3729d5', @p58='ProfileUpdated' (Nullable = false), @p59='null' (DbType = Object), @p60='2025-06-04T16:36:29.4616886Z' (DbType = DateTime), @p61=NULL, @p62=NULL (DbType = DateTime), @p63=NULL, @p64='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p65='User' (Nullable = false), @p66=NULL, @p67='False', @p68='2025-06-04T16:36:29.3630191Z' (DbType = DateTime), @p69=NULL (DbType = DateTime), @p70=NULL, @p71=NULL, @p72='system' (Nullable = false), @p73='e69db4fb-782a-452f-9236-d001ddb94aed', @p74='PreferencesUpdated' (Nullable = false), @p75='"Language: fr-FR, TimeZone: Europe/Paris"' (DbType = Object), @p76='2025-06-04T16:36:29.4616891Z' (DbType = DateTime), @p77=NULL, @p78=NULL (DbType = DateTime), @p79=NULL, @p80='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p81='User' (Nullable = false), @p82=NULL, @p83='False', @p84='2025-06-04T16:36:29.3645494Z' (DbType = DateTime), @p85=NULL (DbType = DateTime), @p86=NULL, @p87=NULL, @p88='system' (Nullable = false)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Users" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "FailedLoginAttempts", "FirstName", "IsActive", "IsDeleted", "IsEmailVerified", "IsLocked", "LastLoginAt", "LastLoginIpAddress", "LastName", "LockedAt", "PasswordHash", "PreferredLanguage", "ProfilePictureUrl", "Role", "TimeZone", "UpdatedAt", "UpdatedBy", "Email", "PhoneCountryCode", "PhoneNumber")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72)
RETURNING xmin;
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88)
RETURNING xmin;
2025-06-04 20:36:29.593 +04:00 [INF] User added successfully: "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-04 20:36:29.598 +04:00 [INF] User "44e9958d-1537-4614-bdbb-f4fe57509fd9" registered successfully <NAME_EMAIL> and role "Lawyer"
2025-06-04 20:36:29.601 +04:00 [INF] User registered successfully <NAME_EMAIL> and role "Lawyer"
2025-06-04 20:36:29.610 +04:00 [INF] Executing CreatedAtActionResult, writing value of type 'LexAI.Identity.Application.DTOs.UserDto'.
2025-06-04 20:36:29.695 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API) in 1247.1424ms
2025-06-04 20:36:29.701 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Register (LexAI.Identity.API)'
2025-06-04 20:36:29.707 +04:00 [INF] Request POST /api/auth/register completed in 1282ms with status 201 (Correlation ID: 3ae84f40-371d-42ab-ac88-f815c0c796cc)
2025-06-04 20:36:29.714 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/register - 201 null application/json; charset=utf-8 1314.8386ms
2025-06-04 20:36:35.028 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-04 20:36:35.042 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID 9f29b815-6535-4c0f-946a-ce2d77028a99
2025-06-04 20:36:35.048 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:36:35.052 +04:00 [INF] Request OPTIONS /api/auth/login completed in 3ms with status 204 (Correlation ID: 9f29b815-6535-4c0f-946a-ce2d77028a99)
2025-06-04 20:36:35.058 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 29.5604ms
2025-06-04 20:36:35.060 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 52
2025-06-04 20:36:35.095 +04:00 [INF] Request POST /api/auth/login started with correlation ID 44cd225d-efaa-49b4-a806-bd275a038e04
2025-06-04 20:36:35.114 +04:00 [INF] CORS policy execution successful.
2025-06-04 20:36:35.120 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-04 20:36:35.133 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-04 20:36:35.142 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-04 20:36:35.155 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-04 20:36:35.163 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-04 20:36:35.814 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='8f43d1bf-cb37-4324-bd8c-e7ce4c262fde', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-04T16:36:35.7393277Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-04T16:36:35.7393307Z' (DbType = DateTime), @p12='2025-06-04T16:36:35.7472191Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='44e9958d-1537-4614-bdbb-f4fe57509fd9' (Nullable = false), @p37='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p16='2025-06-04T16:36:29.4616840Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Senior' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-04T16:36:35.7388770Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Lawyer' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$IwkLcL8eN5wkkUnfgHtMZeW8Amjcv2Ot9F4VxIZ3WFp9cAD.cqVjy' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='Lawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-04T16:36:35.7472158Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='828' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-04 20:36:35.881 +04:00 [INF] User updated successfully: "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-04 20:36:35.957 +04:00 [INF] Executed DbCommand (65ms) [Parameters=[@p0='aefbd879-ccc7-4ac7-8a58-593d15dd96e3', @p1='2025-06-04T16:36:35.8913857Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-11T16:36:35.8888150Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='VuDO7YKOn0nws9afSHkGXPbpe6JSfmb8/kwsJr5h2fdbDeHihepqZucu0MEuTeNII3FAvC1aBhacoPJOe+2nYw==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='44e9958d-1537-4614-bdbb-f4fe57509fd9'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-04 20:36:35.968 +04:00 [INF] Refresh token added successfully: "aefbd879-ccc7-4ac7-8a58-593d15dd96e3"
2025-06-04 20:36:35.974 +04:00 [INF] User "44e9958d-1537-4614-bdbb-f4fe57509fd9" logged in successfully
2025-06-04 20:36:35.980 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-04 20:36:35.987 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-04 20:36:35.996 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 858.9848ms
2025-06-04 20:36:36.004 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-04 20:36:36.010 +04:00 [INF] Request POST /api/auth/login completed in 896ms with status 200 (Correlation ID: 44cd225d-efaa-49b4-a806-bd275a038e04)
2025-06-04 20:36:36.019 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 200 null application/json; charset=utf-8 958.0899ms
