using LexAI.Shared.Domain.Common;

namespace LexAI.AIAssistant.Domain.ValueObjects;

/// <summary>
/// Represents metadata associated with a message
/// </summary>
public class MessageMetadata : ValueObject
{
    /// <summary>
    /// Message ID this metadata belongs to
    /// </summary>
    public Guid MessageId { get; private set; }

    /// <summary>
    /// User ID who created the message
    /// </summary>
    public Guid? UserId { get; private set; }

    /// <summary>
    /// IP address from which the message was sent
    /// </summary>
    public string? IpAddress { get; private set; }

    /// <summary>
    /// User agent string
    /// </summary>
    public string? UserAgent { get; private set; }

    /// <summary>
    /// Device type used to send the message
    /// </summary>
    public string? DeviceType { get; private set; }

    /// <summary>
    /// Platform information
    /// </summary>
    public string? Platform { get; private set; }

    /// <summary>
    /// Browser information
    /// </summary>
    public string? Browser { get; private set; }

    /// <summary>
    /// Session ID
    /// </summary>
    public string? SessionId { get; private set; }

    /// <summary>
    /// Request ID for tracing
    /// </summary>
    public string? RequestId { get; private set; }

    /// <summary>
    /// Additional custom properties
    /// </summary>
    public Dictionary<string, object> Properties { get; private set; } = new();

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private MessageMetadata() { }

    /// <summary>
    /// Creates message metadata for a user message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="ipAddress">IP address</param>
    /// <param name="userAgent">User agent</param>
    /// <param name="sessionId">Session ID</param>
    /// <param name="requestId">Request ID</param>
    /// <returns>New message metadata</returns>
    public static MessageMetadata Create(
        Guid messageId,
        Guid userId,
        string? ipAddress = null,
        string? userAgent = null,
        string? sessionId = null,
        string? requestId = null)
    {
        if (messageId == Guid.Empty)
            throw new ArgumentException("Message ID cannot be empty", nameof(messageId));

        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        return new MessageMetadata
        {
            MessageId = messageId,
            UserId = userId,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            SessionId = sessionId,
            RequestId = requestId,
            DeviceType = ExtractDeviceType(userAgent),
            Platform = ExtractPlatform(userAgent),
            Browser = ExtractBrowser(userAgent),
            Properties = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Creates message metadata for a system message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="requestId">Request ID</param>
    /// <returns>New message metadata</returns>
    public static MessageMetadata Create(
        Guid messageId,
        string? requestId = null)
    {
        if (messageId == Guid.Empty)
            throw new ArgumentException("Message ID cannot be empty", nameof(messageId));

        return new MessageMetadata
        {
            MessageId = messageId,
            RequestId = requestId,
            Properties = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// Adds a custom property
    /// </summary>
    /// <param name="key">Property key</param>
    /// <param name="value">Property value</param>
    public void AddProperty(string key, object value)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Property key cannot be empty", nameof(key));

        Properties[key] = value;
    }

    /// <summary>
    /// Gets a custom property value
    /// </summary>
    /// <typeparam name="T">Property type</typeparam>
    /// <param name="key">Property key</param>
    /// <returns>Property value or default</returns>
    public T? GetProperty<T>(string key)
    {
        if (string.IsNullOrWhiteSpace(key) || !Properties.ContainsKey(key))
            return default;

        try
        {
            return (T)Properties[key];
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// Extracts device type from user agent
    /// </summary>
    /// <param name="userAgent">User agent string</param>
    /// <returns>Device type</returns>
    private static string? ExtractDeviceType(string? userAgent)
    {
        if (string.IsNullOrWhiteSpace(userAgent))
            return null;

        var ua = userAgent.ToLowerInvariant();

        if (ua.Contains("mobile") || ua.Contains("android") || ua.Contains("iphone"))
            return "Mobile";

        if (ua.Contains("tablet") || ua.Contains("ipad"))
            return "Tablet";

        return "Desktop";
    }

    /// <summary>
    /// Extracts platform from user agent
    /// </summary>
    /// <param name="userAgent">User agent string</param>
    /// <returns>Platform</returns>
    private static string? ExtractPlatform(string? userAgent)
    {
        if (string.IsNullOrWhiteSpace(userAgent))
            return null;

        var ua = userAgent.ToLowerInvariant();

        if (ua.Contains("windows"))
            return "Windows";

        if (ua.Contains("mac"))
            return "macOS";

        if (ua.Contains("linux"))
            return "Linux";

        if (ua.Contains("android"))
            return "Android";

        if (ua.Contains("ios") || ua.Contains("iphone") || ua.Contains("ipad"))
            return "iOS";

        return "Unknown";
    }

    /// <summary>
    /// Extracts browser from user agent
    /// </summary>
    /// <param name="userAgent">User agent string</param>
    /// <returns>Browser</returns>
    private static string? ExtractBrowser(string? userAgent)
    {
        if (string.IsNullOrWhiteSpace(userAgent))
            return null;

        var ua = userAgent.ToLowerInvariant();

        if (ua.Contains("chrome") && !ua.Contains("edge"))
            return "Chrome";

        if (ua.Contains("firefox"))
            return "Firefox";

        if (ua.Contains("safari") && !ua.Contains("chrome"))
            return "Safari";

        if (ua.Contains("edge"))
            return "Edge";

        return "Unknown";
    }

    /// <summary>
    /// Gets the atomic values for value object equality
    /// </summary>
    /// <returns>Atomic values</returns>
    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return MessageId;
        yield return UserId ?? Guid.Empty;
        yield return IpAddress ?? string.Empty;
        yield return UserAgent ?? string.Empty;
        yield return SessionId ?? string.Empty;
        yield return RequestId ?? string.Empty;
    }
}
