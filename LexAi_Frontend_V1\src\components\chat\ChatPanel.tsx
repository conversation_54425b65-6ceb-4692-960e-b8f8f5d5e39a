import React, { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, FileText, Search, Wand2, ThumbsUp, ThumbsDown, Copy, MoreVertical } from 'lucide-react'
import { chatApi } from '../../services/aiAssistant/chat.api'
import { Button } from '../ui/Button'

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  type?: 'text' | 'document_analysis' | 'legal_research' | 'document_generation'
  metadata?: any
}

interface ChatPanelProps {
  conversationId?: string
  onNewConversation?: () => void
}

export const ChatPanel: React.FC<ChatPanelProps> = ({ conversationId, onNewConversation }) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user',
      timestamp: new Date(),
      type: 'text'
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)
    setError(null)

    try {
      const response = await chatApi.sendMessage({ message: inputValue, conversationId })

      const assistantMessage: Message = {
        id: response.messageId || (Date.now() + 1).toString(),
        content: response.response || 'Réponse reçue',
        role: 'assistant',
        timestamp: new Date(),
        type: 'text',
        metadata: {
          tokensUsed: response.tokensUsed,
          processingTime: response.processingTimeMs,
          citations: response.citations,
          confidenceScore: response.confidenceScore,
          detectedDomain: response.detectedDomain,
          estimatedCost: response.estimatedCost
        }
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (err: any) {
      setError(err.message || 'Erreur lors de l\'envoi du message')
      console.error('Erreur chat:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  const rateMessage = async (messageId: string, rating: number) => {
    try {
      await chatApi.rateMessage({ messageId, rating })
    } catch (err) {
      console.error('Erreur lors de l\'évaluation:', err)
    }
  }

  const quickActions = [
    {
      icon: FileText,
      label: 'Analyser un document',
      action: () => setInputValue('Je voudrais analyser un document juridique. Pouvez-vous m\'aider ?')
    },
    {
      icon: Search,
      label: 'Recherche juridique',
      action: () => setInputValue('J\'ai besoin d\'aide pour une recherche juridique sur ')
    },
    {
      icon: Wand2,
      label: 'Générer un document',
      action: () => setInputValue('Je voudrais générer un document juridique. Quel type de document souhaitez-vous créer ?')
    }
  ]

  return (
    <section className="flex flex-col h-full w-full bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="p-4 border-b border-gray-200 dark:border-gray-800 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-blue-500" />
          <div className="font-bold text-lg">Assistant Juridique IA</div>
        </div>
        <div className="flex items-center gap-2">
          {onNewConversation && (
            <Button variant="outline" size="sm" onClick={onNewConversation}>
              Nouvelle conversation
            </Button>
          )}
          <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </header>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Bot className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Bonjour ! Je suis votre assistant juridique IA
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
              Je peux vous aider avec l'analyse de documents, la recherche juridique,
              la génération de documents et répondre à vos questions juridiques.
            </p>

            {/* Actions rapides */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 w-full max-w-2xl">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className="flex items-center gap-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
                >
                  <action.icon className="h-5 w-5 text-blue-500" />
                  <span className="text-sm font-medium">{action.label}</span>
                </button>
              ))}
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="flex items-start gap-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                message.role === 'user'
                  ? 'bg-blue-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`}>
                {message.role === 'user' ? (
                  <User className="h-4 w-4 text-white" />
                ) : (
                  <Bot className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                )}
              </div>

              <div className="flex-1 space-y-2">
                <div className={`p-3 rounded-lg max-w-4xl ${
                  message.role === 'user'
                    ? 'bg-blue-500 text-white ml-auto'
                    : 'bg-gray-100 dark:bg-gray-800'
                }`}>
                  <div className="text-sm whitespace-pre-wrap">{message.content}</div>

                  {/* Métadonnées pour les réponses de l'assistant */}
                  {message.role === 'assistant' && message.metadata && (
                    <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500">
                      {message.metadata.tokensUsed && (
                        <span>Tokens: {message.metadata.tokensUsed} • </span>
                      )}
                      {message.metadata.processingTime && (
                        <span>Temps: {message.metadata.processingTime}ms</span>
                      )}
                    </div>
                  )}
                </div>

                {/* Actions pour les messages de l'assistant */}
                {message.role === 'assistant' && (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => copyMessage(message.content)}
                      className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-gray-500 hover:text-gray-700"
                      title="Copier"
                    >
                      <Copy className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => rateMessage(message.id, 1)}
                      className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-gray-500 hover:text-green-600"
                      title="Utile"
                    >
                      <ThumbsUp className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => rateMessage(message.id, -1)}
                      className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-gray-500 hover:text-red-600"
                      title="Pas utile"
                    >
                      <ThumbsDown className="h-3 w-3" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))
        )}

        {/* Indicateur de chargement */}
        {isLoading && (
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <Bot className="h-4 w-4 text-gray-600 dark:text-gray-300" />
            </div>
            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">L'assistant réfléchit...</span>
              </div>
            </div>
          </div>
        )}

        {/* Erreur */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
            {error}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <footer className="p-4 border-t border-gray-200 dark:border-gray-800">
        <div className="flex items-end gap-2">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Posez votre question juridique..."
              className="w-full resize-none rounded-lg border border-gray-300 dark:border-gray-600 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-4 py-2"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </footer>
    </section>
  )
}
