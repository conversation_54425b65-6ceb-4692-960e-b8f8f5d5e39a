// Types pour le service DataProcessing

export interface DocumentUploadRequest {
  file: File
  configuration?: ProcessingConfiguration
}

export interface ProcessingConfiguration {
  extractText?: boolean
  performOCR?: boolean
  detectLanguage?: boolean
  extractMetadata?: boolean
  generateThumbnail?: boolean
  compressionLevel?: number
}

export interface Document {
  id: string
  fileName: string
  originalFileName: string
  fileSize: number
  mimeType: string
  uploadedAt: string
  status: DocumentStatus
  processingSteps: ProcessingStep[]
  metadata?: DocumentMetadata
  userId: string
}

export enum DocumentStatus {
  Uploaded = 'Uploaded',
  Processing = 'Processing',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled'
}

export interface ProcessingStep {
  id: string
  name: string
  status: StepStatus
  startedAt?: string
  completedAt?: string
  errorMessage?: string
  progress?: number
}

export enum StepStatus {
  Pending = 'Pending',
  Running = 'Running',
  Completed = 'Completed',
  Failed = 'Failed',
  Skipped = 'Skipped'
}

export interface DocumentMetadata {
  title?: string
  author?: string
  subject?: string
  keywords?: string[]
  createdDate?: string
  modifiedDate?: string
  pageCount?: number
  wordCount?: number
  language?: string
  extractedText?: string
}

export interface DocumentListRequest {
  status?: DocumentStatus
  fromDate?: string
  toDate?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortDescending?: boolean
}

export interface DocumentListResponse {
  items: Document[]
  totalCount: number
  page: number
  pageSize: number
  totalPages: number
}

export interface ProcessingStatusResponse {
  documentId: string
  status: DocumentStatus
  currentStep?: string
  progress: number
  estimatedTimeRemaining?: number
  steps: ProcessingStep[]
}
