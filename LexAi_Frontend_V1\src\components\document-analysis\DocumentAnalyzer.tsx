import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { FileText, AlertTriangle, CheckCircle, Clock, X } from 'lucide-react'
import { documentAnalysisApi, type DocumentAnalysisResponse, type AnalysisOptions } from '../../services/documentAnalysisApi'
import type { DocumentAnalysis } from '../../types'

interface DocumentAnalyzerProps {
  onAnalysisComplete?: (analysis: DocumentAnalysis) => void
}

export const DocumentAnalyzer: React.FC<DocumentAnalyzerProps> = ({ onAnalysisComplete }) => {
  const [file, setFile] = useState<File | null>(null)
  const [documentContent, setDocumentContent] = useState('')
  const [documentType, setDocumentType] = useState('')
  const [focusAreas, setFocusAreas] = useState<string[]>([])
  const [context, setContext] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<DocumentAnalysisResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Fonctions utilitaires pour l'affichage des risques
  const getRiskColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'high':
      case 'élevé':
        return 'border-red-200 bg-red-50'
      case 'medium':
      case 'moyen':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
      case 'faible':
        return 'border-green-200 bg-green-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getRiskIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'high':
      case 'élevé':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'medium':
      case 'moyen':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'low':
      case 'faible':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      // Lire le contenu du fichier si c'est un fichier texte
      if (selectedFile.type === 'text/plain') {
        const reader = new FileReader()
        reader.onload = (e) => {
          setDocumentContent(e.target?.result as string)
        }
        reader.readAsText(selectedFile)
      }
    }
  }

  const handleAnalyze = async () => {
    if (!file) {
      setError('Veuillez sélectionner un fichier à analyser')
      return
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const options: AnalysisOptions = {
        ...documentAnalysisApi.getDefaultOptions(),
        focusAreas: focusAreas.length > 0 ? focusAreas : undefined,
        language: 'fr'
      }

      const response = await documentAnalysisApi.analyzeDocument({
        documentFile: file,
        documentName: file.name,
        options
      })

      setAnalysis(response)
      onAnalysisComplete?.(response as any) // Cast temporaire pour compatibilité
    } catch (err: any) {
      setError(err.message || 'Erreur lors de l\'analyse du document')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const addFocusArea = (area: string) => {
    if (area && !focusAreas.includes(area)) {
      setFocusAreas([...focusAreas, area])
    }
  }

  const removeFocusArea = (area: string) => {
    setFocusAreas(focusAreas.filter(a => a !== area))
  }

  // Fonctions dupliquées supprimées - utilisation des fonctions définies plus haut

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Configuration de l'analyse */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Analyse de Document Juridique
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload de fichier */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document à analyser
            </label>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <input
                  type="file"
                  accept=".txt,.pdf,.docx"
                  onChange={handleFileUpload}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
              </div>
              {file && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <FileText className="h-4 w-4" />
                  {file.name}
                </div>
              )}
            </div>
          </div>

          {/* Contenu texte alternatif */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ou saisir le contenu directement
            </label>
            <textarea
              value={documentContent}
              onChange={(e) => setDocumentContent(e.target.value)}
              placeholder="Collez ici le contenu du document à analyser..."
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Type de document */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type de document (optionnel)
            </label>
            <select
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Sélectionner un type</option>
              <option value="contract">Contrat</option>
              <option value="lease">Bail</option>
              <option value="employment">Contrat de travail</option>
              <option value="partnership">Accord de partenariat</option>
              <option value="nda">Accord de confidentialité</option>
              <option value="terms">Conditions générales</option>
              <option value="other">Autre</option>
            </select>
          </div>

          {/* Domaines d'analyse */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Domaines d'analyse spécifiques
            </label>
            <div className="flex gap-2 mb-2">
              <Input
                placeholder="Ajouter un domaine d'analyse..."
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addFocusArea((e.target as HTMLInputElement).value)
                    ;(e.target as HTMLInputElement).value = ''
                  }
                }}
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {focusAreas.map((area) => (
                <span
                  key={area}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                >
                  {area}
                  <button
                    onClick={() => removeFocusArea(area)}
                    className="hover:text-blue-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>

          {/* Contexte */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Contexte additionnel (optionnel)
            </label>
            <textarea
              value={context}
              onChange={(e) => setContext(e.target.value)}
              placeholder="Fournissez du contexte supplémentaire pour l'analyse..."
              className="w-full h-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Bouton d'analyse */}
          <Button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !file}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Analyse en cours...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Analyser le document
              </>
            )}
          </Button>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
              {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Résultats de l'analyse */}
      {analysis && (
        <div className="space-y-6">
          {/* Résumé de l'analyse */}
          <Card>
            <CardHeader>
              <CardTitle>Analyse du document</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed">{analysis.analysisContent}</p>
                {analysis.summary && (
                  <div className="mt-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Résumé exécutif</h4>
                    <p className="text-gray-700">{analysis.summary.executiveSummary}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Points clés */}
          {analysis.summary?.keyPoints && analysis.summary.keyPoints.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Points clés identifiés</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {analysis.summary.keyPoints.map((point: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{point}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Évaluation des risques */}
          {analysis.risks && analysis.risks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Évaluation des risques</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analysis.risks.map((risk, index) => (
                    <div key={index} className={`p-4 rounded-lg border ${getRiskColor(risk.severity)}`}>
                      <div className="flex items-start gap-3">
                        {getRiskIcon(risk.severity)}
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{risk.riskType}</h4>
                          <p className="text-gray-700 mt-1">{risk.description}</p>
                          {risk.mitigation && (
                            <div className="mt-2">
                              <span className="text-sm font-medium text-gray-900">Mitigation: </span>
                              <span className="text-sm text-gray-700">{risk.mitigation}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommandations */}
          {analysis.recommendations && analysis.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recommandations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {analysis.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{recommendation.title}</h4>
                        <p className="text-gray-700">{recommendation.description}</p>
                        {recommendation.suggestedAction && (
                          <p className="text-sm text-blue-600 mt-1">{recommendation.suggestedAction}</p>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Citations */}
          {analysis.citations && analysis.citations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Sources et références</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysis.citations.map((citation, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-md">
                      <div className="font-medium text-gray-900">{citation.title}</div>
                      {citation.source && (
                        <div className="text-sm text-gray-600 mt-1">Source: {citation.source}</div>
                      )}
                      {citation.context && (
                        <div className="text-sm text-gray-700 mt-2 italic">"{citation.context}"</div>
                      )}
                      {citation.url && (
                        <a
                          href={citation.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block"
                        >
                          Voir la source →
                        </a>
                      )}
                      <div className="text-xs text-gray-500 mt-1">
                        Pertinence: {Math.round(citation.relevanceScore * 100)}%
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Métadonnées */}
          <Card>
            <CardHeader>
              <CardTitle>Informations sur l'analyse</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {analysis.processingTimeMs && (
                  <div>
                    <div className="font-medium text-gray-900">Temps de traitement</div>
                    <div className="text-gray-600">{analysis.processingTimeMs}ms</div>
                  </div>
                )}
                {analysis.tokensUsed && (
                  <div>
                    <div className="font-medium text-gray-900">Tokens utilisés</div>
                    <div className="text-gray-600">{analysis.tokensUsed}</div>
                  </div>
                )}
                {analysis.estimatedCost && (
                  <div>
                    <div className="font-medium text-gray-900">Coût estimé</div>
                    <div className="text-gray-600">${analysis.estimatedCost.toFixed(4)}</div>
                  </div>
                )}
                <div>
                  <div className="font-medium text-gray-900">Document</div>
                  <div className="text-gray-600">{analysis.documentName}</div>
                </div>
                {analysis.modelUsed && (
                  <div>
                    <div className="font-medium text-gray-900">Modèle utilisé</div>
                    <div className="text-gray-600">{analysis.modelUsed}</div>
                  </div>
                )}
                {analysis.confidenceScore && (
                  <div>
                    <div className="font-medium text-gray-900">Score de confiance</div>
                    <div className="text-gray-600">{Math.round(analysis.confidenceScore * 100)}%</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
