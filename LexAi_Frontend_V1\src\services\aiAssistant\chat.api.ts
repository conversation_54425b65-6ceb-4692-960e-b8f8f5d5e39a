import { BaseApiService } from '../api/base'
import type { 
  Conversation,
  SendMessageRequest,
  SendMessageResponse,
  ConversationListRequest,
  ConversationListResponse,
  MessageRatingRequest,
  DocumentAnalysisRequest,
  DocumentGenerationRequest,
  LegalResearchRequest
} from './types'

const AI_ASSISTANT_API_BASE_URL = import.meta.env.VITE_AI_ASSISTANT_API_URL || 'http://localhost:51404'

class ChatApiService extends BaseApiService {
  constructor() {
    super(AI_ASSISTANT_API_BASE_URL)
  }

  /**
   * Démarre une nouvelle conversation
   */
  async startConversation(initialMessage?: string): Promise<Conversation> {
    return this.post('/api/chat/conversations', {
      initialMessage: initialMessage || 'Bonjour, comment puis-je vous aider ?'
    })
  }

  /**
   * Envoie un message dans une conversation
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    if (request.conversationId) {
      return this.post(`/api/chat/conversations/${request.conversationId}/messages`, {
        message: request.message,
        context: request.context
      })
    } else {
      return this.post('/api/chat/message', request)
    }
  }

  /**
   * Obtient l'historique d'une conversation
   */
  async getConversation(conversationId: string): Promise<Conversation> {
    return this.get(`/api/chat/conversations/${conversationId}`)
  }

  /**
   * Liste les conversations de l'utilisateur
   */
  async getConversations(request: ConversationListRequest = {}): Promise<ConversationListResponse> {
    const params = new URLSearchParams()

    if (request.page) params.append('page', request.page.toString())
    if (request.pageSize) params.append('pageSize', request.pageSize.toString())
    if (request.sortBy) params.append('sortBy', request.sortBy)
    if (request.sortDescending !== undefined) params.append('sortDescending', request.sortDescending.toString())

    return this.get(`/api/chat/conversations?${params.toString()}`)
  }

  /**
   * Supprime une conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    return this.delete(`/api/chat/conversations/${conversationId}`)
  }

  /**
   * Évalue un message
   */
  async rateMessage(request: MessageRatingRequest): Promise<void> {
    return this.post(`/api/chat/messages/${request.messageId}/rate`, {
      rating: request.rating,
      feedback: request.feedback
    })
  }

  /**
   * Analyse un document via l'assistant IA
   */
  async analyzeDocument(request: DocumentAnalysisRequest): Promise<any> {
    return this.post('/api/analysis/document', request)
  }

  /**
   * Génère un document via l'assistant IA
   */
  async generateDocument(request: DocumentGenerationRequest): Promise<any> {
    return this.post('/api/generation/document', request)
  }

  /**
   * Effectue une recherche juridique via l'assistant IA
   */
  async performLegalResearch(request: LegalResearchRequest): Promise<any> {
    return this.post('/api/research/search', request)
  }

  /**
   * Génère un résumé
   */
  async generateSummary(content: string, type: 'document' | 'conversation' | 'research' = 'document'): Promise<any> {
    return this.post('/api/generation/summary', {
      content,
      type
    })
  }
}

export const chatApi = new ChatApiService()
