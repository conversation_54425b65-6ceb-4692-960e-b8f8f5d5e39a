2025-06-26 22:08:40.698 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-26 22:08:40.848 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:08:41.397 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-26 22:08:41.451 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-26 22:08:41.968 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:08:41.997 +04:00 [INF] Hosting environment: Development
2025-06-26 22:08:42.101 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-26 22:08:42.250 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-26 22:08:42.532 +04:00 [INF] Request GET / started with correlation ID 734d5f65-7156-4e67-89ce-d1c63f00046f
2025-06-26 22:08:44.483 +04:00 [INF] Request GET / completed in 1941ms with status 404 (Correlation ID: 734d5f65-7156-4e67-89ce-d1c63f00046f)
2025-06-26 22:08:44.496 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 2248.6242ms
2025-06-26 22:08:44.517 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-26 22:22:09.519 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-26 22:22:09.755 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:22:10.116 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-26 22:22:10.117 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-26 22:22:10.165 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:22:10.167 +04:00 [INF] Hosting environment: Development
2025-06-26 22:22:10.169 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-26 22:22:12.259 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-26 22:22:12.651 +04:00 [INF] Request GET / started with correlation ID 2531499d-14bd-4036-8fbf-1d6f1f0aefba
2025-06-26 22:22:12.861 +04:00 [INF] Request GET / completed in 203ms with status 404 (Correlation ID: 2531499d-14bd-4036-8fbf-1d6f1f0aefba)
2025-06-26 22:22:12.880 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 628.5457ms
2025-06-26 22:22:12.931 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-26 22:26:03.752 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-26 22:26:03.887 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:26:04.322 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-26 22:26:04.336 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-26 22:26:05.145 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:26:05.148 +04:00 [INF] Hosting environment: Development
2025-06-26 22:26:05.150 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-26 22:26:05.151 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-26 22:26:05.470 +04:00 [INF] Request GET / started with correlation ID 348acdf0-f924-4bd3-96ca-2d5f80e7de52
2025-06-26 22:26:05.644 +04:00 [INF] Request GET / completed in 169ms with status 404 (Correlation ID: 348acdf0-f924-4bd3-96ca-2d5f80e7de52)
2025-06-26 22:26:05.653 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 506.24ms
2025-06-26 22:26:05.677 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
