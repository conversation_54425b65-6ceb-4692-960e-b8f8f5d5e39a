using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using FluentValidation;
using System.Text;

namespace LexAI.DataPreprocessing.Application.Commands;

/// <summary>
/// Command to upload a document for processing
/// </summary>
public class UploadDocumentCommand : IRequest<DocumentUploadResponseDto>
{
    /// <summary>
    /// Upload request
    /// </summary>
    public DocumentUploadRequestDto Request { get; set; } = null!;
}

/// <summary>
/// Handler for UploadDocumentCommand
/// </summary>
public class UploadDocumentCommandHandler : IRequestHandler<UploadDocumentCommand, DocumentUploadResponseDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IFileStorageService _fileStorageService;
    private readonly IOrchestrationAgent _orchestrationAgent;
    private readonly ILogger<UploadDocumentCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the UploadDocumentCommandHandler
    /// </summary>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="fileStorageService">File storage service</param>
    /// <param name="orchestrationAgent">Orchestration agent</param>
    /// <param name="logger">Logger</param>
    public UploadDocumentCommandHandler(
        IDocumentRepository documentRepository,
        IFileStorageService fileStorageService,
        IOrchestrationAgent orchestrationAgent,
        ILogger<UploadDocumentCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _fileStorageService = fileStorageService;
        _orchestrationAgent = orchestrationAgent;
        _logger = logger;
    }

    /// <summary>
    /// Handles the UploadDocumentCommand
    /// </summary>
    /// <param name="request">Upload command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upload response</returns>
    public async Task<DocumentUploadResponseDto> Handle(UploadDocumentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document upload: {FileName} by user {UserId}",
            request.Request.FileName, request.Request.UserId);

        try
        {
            // Validate file content
            var fileBytes = Convert.FromBase64String(request.Request.FileContent);
            var fileHash = CalculateFileHash(fileBytes);

            // Check for duplicate documents
            var existingDocument = await _documentRepository.GetByFileHashAsync(fileHash, cancellationToken);
            if (existingDocument != null)
            {
                _logger.LogWarning("Duplicate document detected: {FileName} (hash: {FileHash})",
                    request.Request.FileName, fileHash);

                return new DocumentUploadResponseDto
                {
                    DocumentId = existingDocument.Id,
                    FileName = existingDocument.FileName,
                    FileSize = existingDocument.FileSize,
                    Status = "Duplicate",
                    StoragePath = existingDocument.StoragePath,
                    ProcessingStarted = false,
                    UploadedAt = existingDocument.CreatedAt
                };
            }

            // Store file
            var storagePath = await _fileStorageService.StoreFileAsync(
                request.Request.FileName,
                fileBytes,
                request.Request.MimeType,
                cancellationToken);

            // Create document entity
            var document = Document.Create(
                request.Request.FileName,
                fileBytes.Length,
                request.Request.MimeType,
                fileHash,
                storagePath,
                request.Request.UserId.ToString());

            // Add custom metadata if provided
            if (request.Request.Metadata.Any())
            {
                var updatedMetadata = document.Metadata;
                foreach (var metadata in request.Request.Metadata)
                {
                    updatedMetadata = updatedMetadata.WithCustomMetadata(metadata.Key, metadata.Value);
                }
                // Note: We need to add a method to update metadata on Document
                // For now, this will be handled in the infrastructure layer
            }

            // Save document
            await _documentRepository.AddAsync(document, cancellationToken);

            _logger.LogInformation("Document uploaded successfully: {DocumentId} - {FileName}",
                document.Id, document.FileName);

            // Start processing if configuration provided
            var processingStarted = false;
            TimeSpan? estimatedTime = null;

            if (request.Request.Configuration != null)
            {
                try
                {
                    var processingConfig = MapToProcessingConfiguration(request.Request.Configuration);

                    // Start async processing (fire and forget)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _orchestrationAgent.ProcessDocumentAsync(document, processingConfig, CancellationToken.None);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error in background processing for document {DocumentId}", document.Id);
                        }
                    }, cancellationToken);

                    processingStarted = true;
                    estimatedTime = EstimateProcessingTime(fileBytes.Length, request.Request.MimeType);

                    _logger.LogInformation("Background processing started for document {DocumentId}", document.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to start processing for document {DocumentId}", document.Id);
                }
            }

            return new DocumentUploadResponseDto
            {
                DocumentId = document.Id,
                FileName = document.FileName,
                FileSize = document.FileSize,
                Status = "Uploaded",
                StoragePath = storagePath,
                ProcessingStarted = processingStarted,
                EstimatedProcessingTime = estimatedTime,
                UploadedAt = document.CreatedAt
            };
        }
        catch (FormatException ex)
        {
            _logger.LogError(ex, "Invalid file content format for {FileName}", request.Request.FileName);
            throw new ValidationException("Invalid file content format");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document {FileName}", request.Request.FileName);
            throw;
        }
    }

    private static string CalculateFileHash(byte[] fileBytes)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(fileBytes);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    private static ProcessingConfigurationDto MapToProcessingConfiguration(ProcessingConfigurationDto config)
    {
        return new ProcessingConfigurationDto
        {
            Chunking = config.Chunking,
            EmbeddingModel = config.EmbeddingModel,
            TargetDatabases = config.TargetDatabases,
            PerformQualityAssurance = config.PerformQualityAssurance,
            ExtractNamedEntities = config.ExtractNamedEntities,
            ExtractKeywords = config.ExtractKeywords,
            Priority = config.Priority,
            CustomOptions = config.CustomOptions
        };
    }

    private static TimeSpan EstimateProcessingTime(long fileSize, string mimeType)
    {
        // Simple estimation based on file size and type
        var baseTime = TimeSpan.FromSeconds(30); // Base processing time
        var sizeMultiplier = Math.Max(1, fileSize / (1024 * 1024)); // Per MB

        var typeMultiplier = mimeType.ToLowerInvariant() switch
        {
            "application/pdf" => 2.0,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => 1.5,
            "text/plain" => 1.0,
            _ => 1.5
        };

        return TimeSpan.FromSeconds(baseTime.TotalSeconds * sizeMultiplier * typeMultiplier);
    }
}

/// <summary>
/// Command to process a document through the pipeline
/// </summary>
public class ProcessDocumentCommand : IRequest<ProcessingResultDto>
{
    /// <summary>
    /// Document ID to process
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Processing configuration
    /// </summary>
    public ProcessingConfigurationDto Configuration { get; set; } = null!;

    /// <summary>
    /// User who initiated processing
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
/// Handler for ProcessDocumentCommand
/// </summary>
public class ProcessDocumentCommandHandler : IRequestHandler<ProcessDocumentCommand, ProcessingResultDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IOrchestrationAgent _orchestrationAgent;
    private readonly ILogger<ProcessDocumentCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the ProcessDocumentCommandHandler
    /// </summary>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="orchestrationAgent">Orchestration agent</param>
    /// <param name="logger">Logger</param>
    public ProcessDocumentCommandHandler(
        IDocumentRepository documentRepository,
        IOrchestrationAgent orchestrationAgent,
        ILogger<ProcessDocumentCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _orchestrationAgent = orchestrationAgent;
        _logger = logger;
    }

    /// <summary>
    /// Handles the ProcessDocumentCommand
    /// </summary>
    /// <param name="request">Process command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    public async Task<ProcessingResultDto> Handle(ProcessDocumentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing document {DocumentId} by user {UserId}",
            request.DocumentId, request.UserId);

        try
        {
            // Get document
            var document = await _documentRepository.GetByIdAsync(request.DocumentId, cancellationToken);
            if (document == null)
            {
                throw new EntityNotFoundException("Document", request.DocumentId);
            }

            // Validate document is ready for processing
            if (document.Status != DocumentStatus.Uploaded && document.Status != DocumentStatus.Failed)
            {
                throw new InvalidOperationException($"Document is not ready for processing. Current status: {document.Status}");
            }

            // Process document
            var result = await _orchestrationAgent.ProcessDocumentAsync(document, request.Configuration, cancellationToken);

            _logger.LogInformation("Document processing completed for {DocumentId}. Success: {Success}",
                request.DocumentId, result.Success);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing document {DocumentId}", request.DocumentId);
            throw;
        }
    }
}

/// <summary>
/// Command to retry failed document processing
/// </summary>
public class RetryDocumentProcessingCommand : IRequest<ProcessingResultDto>
{
    /// <summary>
    /// Document ID to retry
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Step to retry from
    /// </summary>
    public string FromStep { get; set; } = string.Empty;

    /// <summary>
    /// User who initiated retry
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
/// Handler for RetryDocumentProcessingCommand
/// </summary>
public class RetryDocumentProcessingCommandHandler : IRequestHandler<RetryDocumentProcessingCommand, ProcessingResultDto>
{
    private readonly IDocumentRepository _documentRepository;
    private readonly IOrchestrationAgent _orchestrationAgent;
    private readonly ILogger<RetryDocumentProcessingCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the RetryDocumentProcessingCommandHandler
    /// </summary>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="orchestrationAgent">Orchestration agent</param>
    /// <param name="logger">Logger</param>
    public RetryDocumentProcessingCommandHandler(
        IDocumentRepository documentRepository,
        IOrchestrationAgent orchestrationAgent,
        ILogger<RetryDocumentProcessingCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _orchestrationAgent = orchestrationAgent;
        _logger = logger;
    }

    /// <summary>
    /// Handles the RetryDocumentProcessingCommand
    /// </summary>
    /// <param name="request">Retry command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    public async Task<ProcessingResultDto> Handle(RetryDocumentProcessingCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrying document processing {DocumentId} from step {FromStep} by user {UserId}",
            request.DocumentId, request.FromStep, request.UserId);

        try
        {
            // Get document
            var document = await _documentRepository.GetByIdAsync(request.DocumentId, cancellationToken);
            if (document == null)
            {
                throw new EntityNotFoundException("Document", request.DocumentId);
            }

            // Validate document can be retried
            if (document.Status != DocumentStatus.Failed)
            {
                throw new InvalidOperationException($"Document cannot be retried. Current status: {document.Status}");
            }

            // Retry processing from specified step
            document.RetryProcessing(request.FromStep, request.UserId.ToString());
            await _documentRepository.UpdateAsync(document, cancellationToken);

            var result = await _orchestrationAgent.RetryProcessingAsync(document, request.FromStep, cancellationToken);

            _logger.LogInformation("Document retry completed for {DocumentId}. Success: {Success}",
                request.DocumentId, result.Success);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying document processing {DocumentId}", request.DocumentId);
            throw;
        }
    }
}

/// <summary>
/// Command to cancel document processing
/// </summary>
public class CancelDocumentProcessingCommand : IRequest<bool>
{
    /// <summary>
    /// Document ID to cancel
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Cancellation reason
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// User who initiated cancellation
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
/// Handler for CancelDocumentProcessingCommand
/// </summary>
public class CancelDocumentProcessingCommandHandler : IRequestHandler<CancelDocumentProcessingCommand, bool>
{
    private readonly IOrchestrationAgent _orchestrationAgent;
    private readonly ILogger<CancelDocumentProcessingCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the CancelDocumentProcessingCommandHandler
    /// </summary>
    /// <param name="orchestrationAgent">Orchestration agent</param>
    /// <param name="logger">Logger</param>
    public CancelDocumentProcessingCommandHandler(
        IOrchestrationAgent orchestrationAgent,
        ILogger<CancelDocumentProcessingCommandHandler> logger)
    {
        _orchestrationAgent = orchestrationAgent;
        _logger = logger;
    }

    /// <summary>
    /// Handles the CancelDocumentProcessingCommand
    /// </summary>
    /// <param name="request">Cancel command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cancellation was successful</returns>
    public async Task<bool> Handle(CancelDocumentProcessingCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Cancelling document processing {DocumentId} by user {UserId}. Reason: {Reason}",
            request.DocumentId, request.UserId, request.Reason);

        try
        {
            var result = await _orchestrationAgent.CancelProcessingAsync(request.DocumentId, request.Reason, cancellationToken);

            _logger.LogInformation("Document processing cancellation for {DocumentId}. Success: {Success}",
                request.DocumentId, result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling document processing {DocumentId}", request.DocumentId);
            throw;
        }
    }
}
