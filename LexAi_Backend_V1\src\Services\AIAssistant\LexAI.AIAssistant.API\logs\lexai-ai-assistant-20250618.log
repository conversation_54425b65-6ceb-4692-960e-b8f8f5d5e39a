2025-06-18 00:42:45.874 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/v1/chat/send - null null
2025-06-18 00:42:45.897 +04:00 [INF] Request OPTIONS /api/v1/chat/send started with correlation ID 6428dc40-b4f4-4a9d-9cb9-edaea7af002c
2025-06-18 00:42:45.924 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:42:45.931 +04:00 [INF] Request OPTIONS /api/v1/chat/send completed in 21ms with status 204 (Correlation ID: 6428dc40-b4f4-4a9d-9cb9-edaea7af002c)
2025-06-18 00:42:45.938 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/v1/chat/send - 204 null null 64.3083ms
2025-06-18 00:42:45.940 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/v1/chat/send - application/json 21
2025-06-18 00:42:45.948 +04:00 [INF] Request POST /api/v1/chat/send started with correlation ID c1cd8926-7427-42ab-ad4d-dbf3cdd2357f
2025-06-18 00:42:45.956 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:42:46.100 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:42:46.103 +04:00 [INF] Request POST /api/v1/chat/send completed in 148ms with status 404 (Correlation ID: c1cd8926-7427-42ab-ad4d-dbf3cdd2357f)
2025-06-18 00:42:46.107 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/v1/chat/send - 404 0 null 167.4027ms
2025-06-18 00:42:46.117 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5003/api/v1/chat/send, Response status code: 404
2025-06-18 00:50:43.909 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-18 00:50:43.934 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 00558665-dee0-43f2-98c9-89f9a1d4e096
2025-06-18 00:50:43.940 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:50:43.942 +04:00 [INF] Request OPTIONS /api/chat/message completed in 2ms with status 204 (Correlation ID: 00558665-dee0-43f2-98c9-89f9a1d4e096)
2025-06-18 00:50:43.947 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 38.0001ms
2025-06-18 00:50:43.956 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 85
2025-06-18 00:50:43.973 +04:00 [INF] Request POST /api/chat/message started with correlation ID 85af98f8-f5ed-4307-a69e-3ad664b67080
2025-06-18 00:50:43.977 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:50:44.008 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/17/2025 8:41:56 PM', Current time (UTC): '6/17/2025 8:50:44 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-18 00:50:44.078 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/17/2025 8:41:56 PM', Current time (UTC): '6/17/2025 8:50:44 PM'.
2025-06-18 00:50:44.087 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/17/2025 8:41:56 PM', Current time (UTC): '6/17/2025 8:50:44 PM'.
2025-06-18 00:50:44.105 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-18 00:50:44.120 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-18 00:50:44.125 +04:00 [INF] Request POST /api/chat/message completed in 148ms with status 401 (Correlation ID: 85af98f8-f5ed-4307-a69e-3ad664b67080)
2025-06-18 00:50:44.130 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 401 0 null 174.0257ms
2025-06-18 00:50:44.893 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 85
2025-06-18 00:50:44.901 +04:00 [INF] Request POST /api/chat/message started with correlation ID 8b45616a-736b-4264-a978-5ed21c910ed3
2025-06-18 00:50:44.905 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:50:44.908 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:50:44.912 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-18 00:50:45.012 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-18 00:50:47.006 +04:00 [ERR] Error configuring primary LLM model: OpenAI
System.InvalidOperationException: Either Azure OpenAI or OpenAI API configuration is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 357
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 297
2025-06-18 00:51:01.912 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 16890.9835ms
2025-06-18 00:51:01.914 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-18 00:51:03.784 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Either Azure OpenAI or OpenAI API configuration is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 357
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 297
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService..ctor(IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 27
   at InvokeStub_UnifiedLLMService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method10(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_13>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 273
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__12>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 258
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-18 00:51:03.802 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null text/plain; charset=utf-8 18909.2556ms
2025-06-18 00:53:28.939 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-18 00:53:28.956 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 55735e45-6f3c-4044-8330-324ecff030e9
2025-06-18 00:53:28.967 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:53:28.974 +04:00 [INF] Request OPTIONS /api/chat/message completed in 7ms with status 204 (Correlation ID: 55735e45-6f3c-4044-8330-324ecff030e9)
2025-06-18 00:53:28.989 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 49.5674ms
2025-06-18 00:53:28.994 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 19
2025-06-18 00:53:29.017 +04:00 [INF] Request POST /api/chat/message started with correlation ID 7874b0a2-cf00-4125-86b2-a760bd8dad4c
2025-06-18 00:53:29.029 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:53:29.038 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:53:29.052 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-18 00:53:29.060 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-18 00:53:29.137 +04:00 [ERR] Error configuring primary LLM model: OpenAI
System.InvalidOperationException: Either Azure OpenAI or OpenAI API configuration is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 357
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 297
2025-06-18 00:53:48.909 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 19839.3182ms
2025-06-18 00:53:48.911 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-18 00:53:49.794 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Either Azure OpenAI or OpenAI API configuration is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 357
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 297
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService..ctor(IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 27
   at InvokeStub_UnifiedLLMService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method10(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_13>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 273
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__12>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 258
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-18 00:53:49.814 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null text/plain; charset=utf-8 20820.3015ms
2025-06-18 00:58:07.991 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-18 00:58:08.067 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-18 00:58:08.299 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-18 00:58:08.303 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-18 00:58:08.347 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-18 00:58:08.351 +04:00 [INF] Hosting environment: Development
2025-06-18 00:58:08.369 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-18 00:58:09.047 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-18 00:58:09.250 +04:00 [INF] Request GET / started with correlation ID 7e318cf5-f439-4f43-8814-f776f5860509
2025-06-18 00:58:09.324 +04:00 [INF] Request GET / completed in 69ms with status 404 (Correlation ID: 7e318cf5-f439-4f43-8814-f776f5860509)
2025-06-18 00:58:09.334 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 296.9117ms
2025-06-18 00:58:09.368 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-18 00:58:52.096 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-18 00:58:52.160 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 9e5cdf71-f33e-4b50-8b16-05f774c4854e
2025-06-18 00:58:52.170 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:58:52.173 +04:00 [INF] Request OPTIONS /api/chat/message completed in 6ms with status 204 (Correlation ID: 9e5cdf71-f33e-4b50-8b16-05f774c4854e)
2025-06-18 00:58:52.177 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 81.5863ms
2025-06-18 00:58:52.180 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 86
2025-06-18 00:58:52.186 +04:00 [INF] Request POST /api/chat/message started with correlation ID 4f167599-59db-44f4-a717-f63a1caf412a
2025-06-18 00:58:52.189 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:58:52.252 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:58:52.261 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-18 00:58:52.282 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-18 00:58:52.467 +04:00 [ERR] Error configuring primary LLM model: OpenAI
System.InvalidOperationException: Either Azure OpenAI or OpenAI API configuration is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 357
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 297
2025-06-18 00:59:39.181 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 46892.8142ms
2025-06-18 00:59:39.185 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-18 00:59:40.559 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Either Azure OpenAI or OpenAI API configuration is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 357
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 297
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService..ctor(IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 27
   at InvokeStub_UnifiedLLMService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method10(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_13>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 273
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__12>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 258
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-18 00:59:40.588 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null text/plain; charset=utf-8 48407.3562ms
