2025-06-12 19:49:15.061 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-12 19:49:15.300 +04:00 [INF] Hangfire SQL objects installed.
2025-06-12 19:49:15.318 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-12 19:49:16.749 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-12 19:49:16.790 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-12 19:49:17.138 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-12 19:49:17.140 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-12 19:49:17.416 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-12 19:49:17.418 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-12 19:49:17.421 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-12 19:49:17.425 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-12 19:49:17.427 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-12 19:49:17.429 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-12 19:49:17.458 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-12 19:49:17.469 +04:00 [INF] Hosting environment: Development
2025-06-12 19:49:17.475 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-12 19:49:17.551 +04:00 [INF] Server datapreprocessing-kevin11:6460:95420872 successfully announced in 54.8381 ms
2025-06-12 19:49:17.657 +04:00 [INF] Server datapreprocessing-kevin11:6460:95420872 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-12 19:49:17.726 +04:00 [INF] 2 servers were removed due to timeout
2025-06-12 19:49:17.882 +04:00 [INF] Removed 1 outdated record(s) from 'aggregatedcounter' table.
2025-06-12 19:49:18.256 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-12 19:49:18.318 +04:00 [INF] Server datapreprocessing-kevin11:6460:95420872 all the dispatchers started
2025-06-12 19:49:18.640 +04:00 [INF] Request GET / started with correlation ID 7a49d166-f7f1-4793-9604-7673be4d3627
2025-06-12 19:49:18.873 +04:00 [INF] Generating processing statistics
2025-06-12 19:49:18.939 +04:00 [INF] Starting cleanup of failed documents
2025-06-12 19:49:19.201 +04:00 [INF] Removed 2 outdated record(s) from 'job' table.
2025-06-12 19:49:20.543 +04:00 [INF] Request GET / completed in 1888ms with status 404 (Correlation ID: 7a49d166-f7f1-4793-9604-7673be4d3627)
2025-06-12 19:49:20.560 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 2310.9138ms
2025-06-12 19:49:20.568 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-12 19:54:44.150 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5001/api/documents? - null null
2025-06-12 19:54:44.169 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5001/api/documents? - null null
2025-06-12 19:54:44.222 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 385421ea-0387-4f45-867c-9b50acdf54a0
2025-06-12 19:54:44.222 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 7d35bb9c-aebd-43ff-8a9b-8ce264ab2368
2025-06-12 19:54:44.248 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:44.248 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:44.261 +04:00 [INF] Request OPTIONS /api/documents completed in 16ms with status 204 (Correlation ID: 7d35bb9c-aebd-43ff-8a9b-8ce264ab2368)
2025-06-12 19:54:44.261 +04:00 [INF] Request OPTIONS /api/documents completed in 25ms with status 204 (Correlation ID: 385421ea-0387-4f45-867c-9b50acdf54a0)
2025-06-12 19:54:44.273 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5001/api/documents? - 204 null null 103.5421ms
2025-06-12 19:54:44.286 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5001/api/documents? - 204 null null 136.1773ms
2025-06-12 19:54:44.280 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:54:44.339 +04:00 [INF] Request GET /api/documents started with correlation ID 1cb3b92b-18e9-4da0-aba4-caef840fb795
2025-06-12 19:54:44.342 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:44.626 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:54:44.635 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:54:44.640 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:54:44.655 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:54:44.668 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:54:44.673 +04:00 [INF] Request GET /api/documents completed in 330ms with status 401 (Correlation ID: 1cb3b92b-18e9-4da0-aba4-caef840fb795)
2025-06-12 19:54:44.680 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 399.4721ms
2025-06-12 19:54:44.685 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:54:44.702 +04:00 [INF] Request GET /api/documents started with correlation ID 2425588e-4132-42f3-98a1-df7be57bb097
2025-06-12 19:54:44.706 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:44.713 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:54:44.718 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:54:44.721 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:54:44.724 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:54:44.727 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:54:44.730 +04:00 [INF] Request GET /api/documents completed in 23ms with status 401 (Correlation ID: 2425588e-4132-42f3-98a1-df7be57bb097)
2025-06-12 19:54:44.734 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 48.9379ms
2025-06-12 19:55:16.931 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5001/api/documents? - null null
2025-06-12 19:55:16.933 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5001/api/documents? - null null
2025-06-12 19:55:16.937 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 7c035f4e-210d-45d7-b319-0e111b576bb9
2025-06-12 19:55:16.941 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 6caa0b39-f416-40df-b1bb-676535c72019
2025-06-12 19:55:16.943 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:16.946 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:16.947 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 7c035f4e-210d-45d7-b319-0e111b576bb9)
2025-06-12 19:55:16.948 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: 6caa0b39-f416-40df-b1bb-676535c72019)
2025-06-12 19:55:16.952 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5001/api/documents? - 204 null null 20.6191ms
2025-06-12 19:55:16.955 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:55:16.955 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5001/api/documents? - 204 null null 22.7056ms
2025-06-12 19:55:16.977 +04:00 [INF] Request GET /api/documents started with correlation ID 64d27198-c644-42b8-a07d-467ac48684a0
2025-06-12 19:55:16.985 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:16.989 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:55:17.000 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:55:17.004 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:55:17.008 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:55:17.011 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:55:17.013 +04:00 [INF] Request GET /api/documents completed in 28ms with status 401 (Correlation ID: 64d27198-c644-42b8-a07d-467ac48684a0)
2025-06-12 19:55:17.017 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 62.3062ms
2025-06-12 19:55:17.019 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:55:17.032 +04:00 [INF] Request GET /api/documents started with correlation ID 324341a1-da53-4f47-9c0e-51eb345dba39
2025-06-12 19:55:17.035 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.038 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:55:17.045 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:55:17.048 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:55:17.051 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:55:17.053 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:55:17.056 +04:00 [INF] Request GET /api/documents completed in 20ms with status 401 (Correlation ID: 324341a1-da53-4f47-9c0e-51eb345dba39)
2025-06-12 19:55:17.062 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 43.1797ms
2025-06-12 19:56:21.624 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5001/api/documents? - null null
2025-06-12 19:56:21.624 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5001/api/documents? - null null
2025-06-12 19:56:21.639 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 5260b089-d58a-46bc-aff9-5ec33d721816
2025-06-12 19:56:21.645 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID ca2f3253-5269-4e2f-ab9c-f21991483eab
2025-06-12 19:56:21.647 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:21.651 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:21.653 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 5260b089-d58a-46bc-aff9-5ec33d721816)
2025-06-12 19:56:21.656 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: ca2f3253-5269-4e2f-ab9c-f21991483eab)
2025-06-12 19:56:21.660 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5001/api/documents? - 204 null null 35.2866ms
2025-06-12 19:56:21.664 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5001/api/documents? - 204 null null 39.182ms
2025-06-12 19:56:21.665 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:56:21.686 +04:00 [INF] Request GET /api/documents started with correlation ID 65eb2a23-ecbf-4c47-be15-7cfd4a9c0e53
2025-06-12 19:56:21.689 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:21.691 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:56:21.698 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:21.701 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:21.705 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:56:21.707 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:56:21.709 +04:00 [INF] Request GET /api/documents completed in 19ms with status 401 (Correlation ID: 65eb2a23-ecbf-4c47-be15-7cfd4a9c0e53)
2025-06-12 19:56:21.713 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 47.0276ms
2025-06-12 19:56:21.715 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:56:21.734 +04:00 [INF] Request GET /api/documents started with correlation ID df3a51b8-bc73-4a06-bf88-5bb2fa1858df
2025-06-12 19:56:21.740 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:21.745 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:56:21.754 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:21.756 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:21.760 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:56:21.763 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:56:21.766 +04:00 [INF] Request GET /api/documents completed in 25ms with status 401 (Correlation ID: df3a51b8-bc73-4a06-bf88-5bb2fa1858df)
2025-06-12 19:56:21.777 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 61.9165ms
2025-06-12 19:56:22.047 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:56:22.052 +04:00 [INF] Request GET /api/documents started with correlation ID cf67df90-bf20-4dcb-8e22-84a72b4b203e
2025-06-12 19:56:22.054 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:22.056 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:56:22.061 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:22.063 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:22.065 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:56:22.067 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:56:22.070 +04:00 [INF] Request GET /api/documents completed in 15ms with status 401 (Correlation ID: cf67df90-bf20-4dcb-8e22-84a72b4b203e)
2025-06-12 19:56:22.074 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 26.7628ms
2025-06-12 19:56:22.076 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/api/documents? - application/json null
2025-06-12 19:56:22.086 +04:00 [INF] Request GET /api/documents started with correlation ID 3ec96a09-7c28-4d2d-bca8-f9e1998ef06a
2025-06-12 19:56:22.089 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:22.091 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:56:22.094 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:22.096 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'bjP55qhpu6RXBaG6c3AFWAonSCnf8LahbRs93q-Fe08'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-12 19:56:22.098 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:56:22.101 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:56:22.103 +04:00 [INF] Request GET /api/documents completed in 14ms with status 401 (Correlation ID: 3ec96a09-7c28-4d2d-bca8-f9e1998ef06a)
2025-06-12 19:56:22.108 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/api/documents? - 401 0 null 31.9383ms
2025-06-12 20:00:05.152 +04:00 [INF] Generating processing statistics
