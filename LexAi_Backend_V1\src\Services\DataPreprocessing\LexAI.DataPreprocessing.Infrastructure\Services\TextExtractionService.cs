using LexAI.DataPreprocessing.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Text extraction service implementation
/// </summary>
public class TextExtractionService : ITextExtractionService
{
    private readonly ILogger<TextExtractionService> _logger;

    /// <summary>
    /// Supported MIME types
    /// </summary>
    public IEnumerable<string> SupportedMimeTypes => new[]
    {
        "text/plain",
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "text/html",
        "application/rtf"
    };

    /// <summary>
    /// Initializes a new instance of the TextExtractionService
    /// </summary>
    /// <param name="logger">Logger</param>
    public TextExtractionService(ILogger<TextExtractionService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Extracts text from a file
    /// </summary>
    /// <param name="fileContent">File content</param>
    /// <param name="mimeType">MIME type</param>
    /// <param name="fileName">File name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted text and metadata</returns>
    public async Task<TextExtractionResult> ExtractTextAsync(
        byte[] fileContent, 
        string mimeType, 
        string fileName, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Extracting text from {FileName} (MIME: {MimeType})", fileName, mimeType);

        try
        {
            if (!CanHandle(mimeType))
            {
                return new TextExtractionResult
                {
                    Success = false,
                    Errors = new List<string> { $"Unsupported MIME type: {mimeType}" }
                };
            }

            var result = mimeType.ToLowerInvariant() switch
            {
                "text/plain" => await ExtractFromTextAsync(fileContent, cancellationToken),
                "application/pdf" => await ExtractFromPdfAsync(fileContent, fileName, cancellationToken),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => 
                    await ExtractFromDocxAsync(fileContent, fileName, cancellationToken),
                "application/msword" => await ExtractFromDocAsync(fileContent, fileName, cancellationToken),
                "text/html" => await ExtractFromHtmlAsync(fileContent, cancellationToken),
                "application/rtf" => await ExtractFromRtfAsync(fileContent, cancellationToken),
                _ => new TextExtractionResult
                {
                    Success = false,
                    Errors = new List<string> { $"No extraction handler for MIME type: {mimeType}" }
                }
            };

            if (result.Success)
            {
                _logger.LogInformation("Text extraction successful for {FileName}. Length: {Length}", 
                    fileName, result.Text.Length);
            }
            else
            {
                _logger.LogWarning("Text extraction failed for {FileName}: {Errors}", 
                    fileName, string.Join(", ", result.Errors));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting text from {FileName}", fileName);
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"Extraction failed: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// Checks if the service can handle the MIME type
    /// </summary>
    /// <param name="mimeType">MIME type</param>
    /// <returns>True if supported</returns>
    public bool CanHandle(string mimeType)
    {
        return SupportedMimeTypes.Contains(mimeType?.ToLowerInvariant());
    }

    private static async Task<TextExtractionResult> ExtractFromTextAsync(
        byte[] fileContent, 
        CancellationToken cancellationToken)
    {
        try
        {
            // Try different encodings
            var encodings = new[] { Encoding.UTF8, Encoding.UTF32, Encoding.Unicode, Encoding.ASCII };
            
            foreach (var encoding in encodings)
            {
                try
                {
                    var text = encoding.GetString(fileContent);
                    
                    // Check if the text looks valid (no excessive null characters or control characters)
                    if (IsValidText(text))
                    {
                        return new TextExtractionResult
                        {
                            Text = text,
                            Success = true,
                            Metadata = new Dictionary<string, object>
                            {
                                ["encoding"] = encoding.EncodingName,
                                ["characterCount"] = text.Length,
                                ["wordCount"] = CountWords(text)
                            }
                        };
                    }
                }
                catch
                {
                    // Try next encoding
                    continue;
                }
            }

            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { "Could not determine text encoding" }
            };
        }
        catch (Exception ex)
        {
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"Text extraction failed: {ex.Message}" }
            };
        }
    }

    private static async Task<TextExtractionResult> ExtractFromPdfAsync(
        byte[] fileContent, 
        string fileName, 
        CancellationToken cancellationToken)
    {
        try
        {
            // For now, return a placeholder implementation
            // In a real implementation, you would use a library like PdfPig or iTextSharp
            var text = $"[PDF Content from {fileName}]\n\nThis is a placeholder for PDF text extraction.\nIn a real implementation, this would extract actual text from the PDF file.";
            
            return new TextExtractionResult
            {
                Text = text,
                Success = true,
                Metadata = new Dictionary<string, object>
                {
                    ["extractionMethod"] = "Placeholder",
                    ["fileName"] = fileName,
                    ["characterCount"] = text.Length,
                    ["wordCount"] = CountWords(text)
                }
            };
        }
        catch (Exception ex)
        {
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"PDF extraction failed: {ex.Message}" }
            };
        }
    }

    private static async Task<TextExtractionResult> ExtractFromDocxAsync(
        byte[] fileContent, 
        string fileName, 
        CancellationToken cancellationToken)
    {
        try
        {
            // For now, return a placeholder implementation
            // In a real implementation, you would use DocumentFormat.OpenXml
            var text = $"[DOCX Content from {fileName}]\n\nThis is a placeholder for DOCX text extraction.\nIn a real implementation, this would extract actual text from the Word document.";
            
            return new TextExtractionResult
            {
                Text = text,
                Success = true,
                Metadata = new Dictionary<string, object>
                {
                    ["extractionMethod"] = "Placeholder",
                    ["fileName"] = fileName,
                    ["characterCount"] = text.Length,
                    ["wordCount"] = CountWords(text)
                }
            };
        }
        catch (Exception ex)
        {
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"DOCX extraction failed: {ex.Message}" }
            };
        }
    }

    private static async Task<TextExtractionResult> ExtractFromDocAsync(
        byte[] fileContent, 
        string fileName, 
        CancellationToken cancellationToken)
    {
        try
        {
            // For now, return a placeholder implementation
            var text = $"[DOC Content from {fileName}]\n\nThis is a placeholder for DOC text extraction.\nIn a real implementation, this would extract actual text from the legacy Word document.";
            
            return new TextExtractionResult
            {
                Text = text,
                Success = true,
                Metadata = new Dictionary<string, object>
                {
                    ["extractionMethod"] = "Placeholder",
                    ["fileName"] = fileName,
                    ["characterCount"] = text.Length,
                    ["wordCount"] = CountWords(text)
                }
            };
        }
        catch (Exception ex)
        {
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"DOC extraction failed: {ex.Message}" }
            };
        }
    }

    private static async Task<TextExtractionResult> ExtractFromHtmlAsync(
        byte[] fileContent, 
        CancellationToken cancellationToken)
    {
        try
        {
            var html = Encoding.UTF8.GetString(fileContent);
            
            // Simple HTML tag removal (in a real implementation, use HtmlAgilityPack)
            var text = System.Text.RegularExpressions.Regex.Replace(html, "<[^>]*>", " ");
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ").Trim();
            
            return new TextExtractionResult
            {
                Text = text,
                Success = true,
                Metadata = new Dictionary<string, object>
                {
                    ["extractionMethod"] = "SimpleRegex",
                    ["originalLength"] = html.Length,
                    ["characterCount"] = text.Length,
                    ["wordCount"] = CountWords(text)
                }
            };
        }
        catch (Exception ex)
        {
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"HTML extraction failed: {ex.Message}" }
            };
        }
    }

    private static async Task<TextExtractionResult> ExtractFromRtfAsync(
        byte[] fileContent, 
        CancellationToken cancellationToken)
    {
        try
        {
            var rtf = Encoding.UTF8.GetString(fileContent);
            
            // Simple RTF control word removal (in a real implementation, use a proper RTF parser)
            var text = System.Text.RegularExpressions.Regex.Replace(rtf, @"\\[a-z]+\d*\s?", " ");
            text = System.Text.RegularExpressions.Regex.Replace(text, @"[{}]", " ");
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ").Trim();
            
            return new TextExtractionResult
            {
                Text = text,
                Success = true,
                Metadata = new Dictionary<string, object>
                {
                    ["extractionMethod"] = "SimpleRegex",
                    ["originalLength"] = rtf.Length,
                    ["characterCount"] = text.Length,
                    ["wordCount"] = CountWords(text)
                }
            };
        }
        catch (Exception ex)
        {
            return new TextExtractionResult
            {
                Success = false,
                Errors = new List<string> { $"RTF extraction failed: {ex.Message}" }
            };
        }
    }

    private static bool IsValidText(string text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        // Check for excessive null characters or control characters
        var controlCharCount = text.Count(c => char.IsControl(c) && c != '\n' && c != '\r' && c != '\t');
        var controlCharRatio = (double)controlCharCount / text.Length;

        return controlCharRatio < 0.1; // Less than 10% control characters
    }

    private static int CountWords(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return 0;

        return text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
    }
}
