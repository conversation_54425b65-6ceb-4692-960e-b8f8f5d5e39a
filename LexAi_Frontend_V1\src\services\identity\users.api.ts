import { BaseApiService } from '../api/base'
import type { PaginationRequest, PaginationResponse } from '../api/types'
import type { User, UpdateUserRequest } from './types'

const IDENTITY_API_BASE_URL = import.meta.env.VITE_IDENTITY_API_URL || 'http://localhost:5000'

class UsersApiService extends BaseApiService {
  constructor() {
    super(IDENTITY_API_BASE_URL)
  }

  async getUsers(pagination?: PaginationRequest): Promise<PaginationResponse<User>> {
    const params = new URLSearchParams()
    
    if (pagination?.limit) params.append('limit', pagination.limit.toString())
    if (pagination?.offset) params.append('offset', pagination.offset.toString())
    
    const queryString = params.toString()
    return this.get(`/api/users${queryString ? `?${queryString}` : ''}`)
  }

  async getUser(id: string): Promise<User> {
    return this.get(`/api/users/${id}`)
  }

  async createUser(userData: UpdateUserRequest): Promise<User> {
    return this.post('/api/users', userData)
  }

  async updateUser(id: string, userData: UpdateUserRequest): Promise<User> {
    return this.put(`/api/users/${id}`, userData)
  }

  async deleteUser(id: string): Promise<void> {
    return this.delete(`/api/users/${id}`)
  }

  async getCurrentUser(): Promise<User> {
    return this.get('/api/users/me')
  }

  async updateProfile(userData: UpdateUserRequest): Promise<User> {
    return this.put('/api/users/me', userData)
  }

  async uploadAvatar(file: File): Promise<User> {
    const formData = new FormData()
    formData.append('file', file)
    return this.upload('/api/users/avatar', formData)
  }
}

export const usersApi = new UsersApiService()
