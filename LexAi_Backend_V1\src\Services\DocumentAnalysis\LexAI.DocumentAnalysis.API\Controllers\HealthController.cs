using LexAI.DocumentAnalysis.Infrastructure.Configuration;
using LexAI.DocumentAnalysis.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Reflection;

namespace LexAI.DocumentAnalysis.API.Controllers;

/// <summary>
/// Contrôleur pour la santé et les statistiques du service
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
public class HealthController : ControllerBase
{
    private readonly IDocumentAnalysisCacheService _cacheService;
    private readonly AzureDocumentIntelligenceSettings _azureSettings;
    private readonly ILogger<HealthController> _logger;

    public HealthController(
        IDocumentAnalysisCacheService cacheService,
        IOptions<AzureDocumentIntelligenceSettings> azureSettings,
        ILogger<HealthController> logger)
    {
        _cacheService = cacheService;
        _azureSettings = azureSettings.Value;
        _logger = logger;
    }

    /// <summary>
    /// Vérification de santé basique
    /// </summary>
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(HealthStatus), StatusCodes.Status200OK)]
    public async Task<ActionResult<HealthStatus>> GetHealth()
    {
        var health = new HealthStatus
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = GetVersion(),
            Uptime = GetUptime(),
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
        };

        // Vérifier Azure Document Intelligence
        health.Services.Add("AzureDocumentIntelligence", await CheckAzureDocumentIntelligenceAsync());

        // Vérifier le cache
        health.Services.Add("Cache", await CheckCacheAsync());

        // Déterminer le statut global
        var hasUnhealthyServices = health.Services.Values.Any(s => s.Status != "Healthy");
        if (hasUnhealthyServices)
        {
            health.Status = "Degraded";
        }

        return Ok(health);
    }

    /// <summary>
    /// Vérification de santé détaillée
    /// </summary>
    [HttpGet("detailed")]
    [Authorize(Roles = "Administrator")]
    [ProducesResponseType(typeof(DetailedHealthStatus), StatusCodes.Status200OK)]
    public async Task<ActionResult<DetailedHealthStatus>> GetDetailedHealth()
    {
        var health = new DetailedHealthStatus
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = GetVersion(),
            Uptime = GetUptime(),
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
        };

        // Informations système
        health.SystemInfo = new SystemInfo
        {
            MachineName = Environment.MachineName,
            ProcessorCount = Environment.ProcessorCount,
            WorkingSet = Environment.WorkingSet,
            TotalMemory = GC.GetTotalMemory(false),
            OSVersion = Environment.OSVersion.ToString(),
            CLRVersion = Environment.Version.ToString()
        };

        // Configuration Azure
        health.AzureConfiguration = new AzureConfigurationInfo
        {
            DocumentIntelligenceConfigured = _azureSettings.IsValid(),
            Endpoint = _azureSettings.Endpoint,
            MaxFileSizeMB = _azureSettings.MaxFileSizeMB,
            SupportedFileTypes = _azureSettings.SupportedFileTypes,
            CachingEnabled = _azureSettings.EnableCaching,
            RetryEnabled = _azureSettings.EnableRetry
        };

        // Statistiques du cache
        health.CacheStatistics = await _cacheService.GetCacheStatisticsAsync();

        // Vérifications de santé
        health.Services.Add("AzureDocumentIntelligence", await CheckAzureDocumentIntelligenceAsync());
        health.Services.Add("Cache", await CheckCacheAsync());

        // Déterminer le statut global
        var hasUnhealthyServices = health.Services.Values.Any(s => s.Status != "Healthy");
        if (hasUnhealthyServices)
        {
            health.Status = "Degraded";
        }

        return Ok(health);
    }

    /// <summary>
    /// Statistiques du cache
    /// </summary>
    [HttpGet("cache/stats")]
    [Authorize(Roles = "Administrator")]
    [ProducesResponseType(typeof(CacheStatistics), StatusCodes.Status200OK)]
    public async Task<ActionResult<CacheStatistics>> GetCacheStatistics()
    {
        var stats = await _cacheService.GetCacheStatisticsAsync();
        return Ok(stats);
    }

    /// <summary>
    /// Vider le cache
    /// </summary>
    [HttpPost("cache/clear")]
    [Authorize(Roles = "Administrator")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult> ClearCache([FromQuery] string? userId = null)
    {
        if (!string.IsNullOrEmpty(userId))
        {
            await _cacheService.ClearUserCacheAsync(userId);
            _logger.LogInformation("Cache cleared for user: {UserId}", userId);
        }
        else
        {
            // Pour vider tout le cache, on devrait redémarrer le service
            // ou utiliser un cache distribué avec des fonctionnalités avancées
            _logger.LogWarning("Full cache clear requested but not implemented for MemoryCache");
        }

        return Ok(new { Message = "Cache clear operation completed" });
    }

    // Méthodes privées

    private async Task<ServiceHealthInfo> CheckAzureDocumentIntelligenceAsync()
    {
        try
        {
            if (!_azureSettings.IsValid())
            {
                return new ServiceHealthInfo
                {
                    Status = "Unhealthy",
                    Message = "Azure Document Intelligence not configured",
                    LastChecked = DateTime.UtcNow
                };
            }

            // Test basique de connectivité (sans faire d'appel réel)
            await Task.Delay(1); // Simulation d'un test de connectivité

            return new ServiceHealthInfo
            {
                Status = "Healthy",
                Message = "Azure Document Intelligence configured and accessible",
                LastChecked = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(50) // Simulation
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Azure Document Intelligence health check failed");
            return new ServiceHealthInfo
            {
                Status = "Unhealthy",
                Message = $"Azure Document Intelligence error: {ex.Message}",
                LastChecked = DateTime.UtcNow
            };
        }
    }

    private async Task<ServiceHealthInfo> CheckCacheAsync()
    {
        try
        {
            var stats = await _cacheService.GetCacheStatisticsAsync();
            
            return new ServiceHealthInfo
            {
                Status = "Healthy",
                Message = $"Cache operational with {stats.TotalEntries} entries",
                LastChecked = DateTime.UtcNow,
                ResponseTime = TimeSpan.FromMilliseconds(1)
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Cache health check failed");
            return new ServiceHealthInfo
            {
                Status = "Unhealthy",
                Message = $"Cache error: {ex.Message}",
                LastChecked = DateTime.UtcNow
            };
        }
    }

    private string GetVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var version = assembly.GetName().Version;
        return version?.ToString() ?? "Unknown";
    }

    private TimeSpan GetUptime()
    {
        return DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime();
    }
}

// Classes de modèles pour les réponses

public class HealthStatus
{
    public string Status { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Version { get; set; } = string.Empty;
    public TimeSpan Uptime { get; set; }
    public string Environment { get; set; } = string.Empty;
    public Dictionary<string, ServiceHealthInfo> Services { get; set; } = new();
}

public class DetailedHealthStatus : HealthStatus
{
    public SystemInfo SystemInfo { get; set; } = new();
    public AzureConfigurationInfo AzureConfiguration { get; set; } = new();
    public CacheStatistics CacheStatistics { get; set; } = new();
}

public class ServiceHealthInfo
{
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime LastChecked { get; set; }
    public TimeSpan? ResponseTime { get; set; }
}

public class SystemInfo
{
    public string MachineName { get; set; } = string.Empty;
    public int ProcessorCount { get; set; }
    public long WorkingSet { get; set; }
    public long TotalMemory { get; set; }
    public string OSVersion { get; set; } = string.Empty;
    public string CLRVersion { get; set; } = string.Empty;
}

public class AzureConfigurationInfo
{
    public bool DocumentIntelligenceConfigured { get; set; }
    public string Endpoint { get; set; } = string.Empty;
    public int MaxFileSizeMB { get; set; }
    public string[] SupportedFileTypes { get; set; } = Array.Empty<string>();
    public bool CachingEnabled { get; set; }
    public bool RetryEnabled { get; set; }
}
