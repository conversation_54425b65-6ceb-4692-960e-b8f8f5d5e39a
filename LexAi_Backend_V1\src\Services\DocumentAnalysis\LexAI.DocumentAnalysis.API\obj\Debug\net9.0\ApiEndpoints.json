[{"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController", "Method": "GetUserAnalyses", "RelativePath": "api/v1/DocumentAnalysis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DocumentType", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController", "Method": "GetAnalysisResult", "RelativePath": "api/v1/DocumentAnalysis/{analysisId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "analysisId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController", "Method": "DeleteAnalysis", "RelativePath": "api/v1/DocumentAnalysis/{analysisId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "analysisId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController", "Method": "RegenerateAnalysis", "RelativePath": "api/v1/DocumentAnalysis/{analysisId}/regenerate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "analysisId", "Type": "System.Guid", "IsRequired": true}, {"Name": "options", "Type": "LexAI.DocumentAnalysis.Application.DTOs.AnalysisOptions", "IsRequired": false}], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController", "Method": "AnalyzeDocument", "RelativePath": "api/v1/DocumentAnalysis/analyze", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "DocumentFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "DocumentName", "Type": "System.String", "IsRequired": false}, {"Name": "Options.ExtractClauses", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Options.PerformRiskAssessment", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Options.GenerateRecommendations", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Options.ExtractEntities", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Options.FindCitations", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Options.UseAzureDocumentIntelligence", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Options.SpecificAnalysisType", "Type": "System.String", "IsRequired": false}, {"Name": "Options.FocusAreas", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Options.Language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 429}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.HealthController", "Method": "GetHealth", "RelativePath": "api/v1/Health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.API.Controllers.HealthStatus", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.HealthController", "Method": "ClearCache", "RelativePath": "api/v1/Health/cache/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.HealthController", "Method": "GetCacheStatistics", "RelativePath": "api/v1/Health/cache/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.Infrastructure.Services.CacheStatistics", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LexAI.DocumentAnalysis.API.Controllers.HealthController", "Method": "GetDetailedHealth", "RelativePath": "api/v1/Health/detailed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LexAI.DocumentAnalysis.API.Controllers.DetailedHealthStatus", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]