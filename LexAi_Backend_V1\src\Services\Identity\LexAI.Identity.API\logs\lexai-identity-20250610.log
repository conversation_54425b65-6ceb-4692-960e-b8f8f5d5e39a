2025-06-10 20:21:31.658 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-10 20:21:31.714 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-10 20:21:31.725 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-10 20:21:33.800 +04:00 [INF] Executed DbCommand (494ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-10 20:21:33.850 +04:00 [INF] LexAI Identity Service started successfully
2025-06-10 20:21:33.915 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:21:34.686 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-10 20:21:34.690 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-10 20:21:34.972 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:21:35.014 +04:00 [INF] Hosting environment: Development
2025-06-10 20:21:35.018 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-10 20:21:35.952 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-10 20:21:36.262 +04:00 [INF] Request GET / started with correlation ID e3e1cad6-06de-4a5b-b742-f237a54c669a
2025-06-10 20:21:37.971 +04:00 [INF] Request GET / completed in 1703ms with status 404 (Correlation ID: e3e1cad6-06de-4a5b-b742-f237a54c669a)
2025-06-10 20:21:37.984 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 2056.3759ms
2025-06-10 20:21:37.996 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-10 20:22:32.747 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-10 20:22:32.813 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-10 20:22:32.823 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-10 20:22:33.340 +04:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-10 20:22:33.357 +04:00 [INF] LexAI Identity Service started successfully
2025-06-10 20:22:33.394 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-10 20:22:33.623 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-10 20:22:33.625 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-10 20:22:33.686 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 20:22:33.688 +04:00 [INF] Hosting environment: Development
2025-06-10 20:22:33.689 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-10 20:22:35.732 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-10 20:22:36.310 +04:00 [INF] Request GET / started with correlation ID 4f69e6da-3e8d-471d-b235-3f82a8a856d9
2025-06-10 20:22:36.509 +04:00 [INF] Request GET / completed in 189ms with status 404 (Correlation ID: 4f69e6da-3e8d-471d-b235-3f82a8a856d9)
2025-06-10 20:22:36.531 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 810.5119ms
2025-06-10 20:22:36.545 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
