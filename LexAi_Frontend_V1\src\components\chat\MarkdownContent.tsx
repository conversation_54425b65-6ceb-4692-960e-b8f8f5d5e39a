import React from 'react'

interface MarkdownContentProps {
  content: string
}

export const MarkdownContent: React.FC<MarkdownContentProps> = ({ content }) => {
  // Fonction pour parser le markdown simple
  const parseMarkdown = (text: string): React.ReactNode => {
    // Diviser le texte en lignes
    const lines = text.split('\n')
    const elements: React.ReactNode[] = []
    
    lines.forEach((line, index) => {
      // Headers (### ## #)
      if (line.startsWith('### ')) {
        elements.push(
          <h3 key={index} className="text-base font-bold mt-3 mb-1 text-black dark:text-white">
            {line.substring(4)}
          </h3>
        )
      } else if (line.startsWith('## ')) {
        elements.push(
          <h2 key={index} className="text-lg font-bold mt-4 mb-1 text-black dark:text-white">
            {line.substring(3)}
          </h2>
        )
      } else if (line.startsWith('# ')) {
        elements.push(
          <h1 key={index} className="text-xl font-bold mt-4 mb-1 text-black dark:text-white">
            {line.substring(2)}
          </h1>
        )
      }
      // Listes avec puces (-)
      else if (line.trim().startsWith('- ')) {
        const content = line.trim().substring(2)
        elements.push(
          <div key={index} className="flex items-start gap-2 my-1">
            <span className="text-blue-500 mt-1">•</span>
            <span>{parseInlineMarkdown(content)}</span>
          </div>
        )
      }
      // Listes numérotées
      else if (/^\d+\.\s/.test(line.trim())) {
        const content = line.trim().replace(/^\d+\.\s/, '')
        elements.push(
          <div key={index} className="flex items-start gap-2 my-1 ml-4">
            <span className="text-blue-500 font-medium">{line.trim().match(/^\d+/)?.[0]}.</span>
            <span>{parseInlineMarkdown(content)}</span>
          </div>
        )
      }
      // Séparateurs (---)
      else if (line.trim() === '---') {
        elements.push(
          <hr key={index} className="my-4 border-gray-300 dark:border-gray-600" />
        )
      }
      // Lignes vides
      else if (line.trim() === '') {
        elements.push(<br key={index} />)
      }
      // Texte normal
      else {
        elements.push(
          <p key={index} className="my-1 text-gray-800 dark:text-gray-200 leading-relaxed">
            {parseInlineMarkdown(line)}
          </p>
        )
      }
    })
    
    return elements
  }

  // Fonction pour parser le markdown inline (gras, italique, etc.)
  const parseInlineMarkdown = (text: string): React.ReactNode => {
    // Gras (**text**)
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-black dark:text-white">$1</strong>')

    // Italique (*text*)
    text = text.replace(/\*(.*?)\*/g, '<em class="italic text-gray-700 dark:text-gray-300">$1</em>')

    // Code inline (`code`)
    text = text.replace(/`(.*?)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-800 dark:text-gray-200">$1</code>')

    // Liens [text](url)
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>')

    return <span dangerouslySetInnerHTML={{ __html: text }} />
  }

  return (
    <div className="markdown-content">
      {parseMarkdown(content)}
    </div>
  )
}
