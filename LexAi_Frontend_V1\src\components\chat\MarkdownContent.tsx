import React from 'react'

interface MarkdownContentProps {
  content: string
}

export const MarkdownContent: React.FC<MarkdownContentProps> = ({ content }) => {
  // Fonction pour parser le markdown simple
  const parseMarkdown = (text: string): React.ReactNode => {
    // Diviser le texte en lignes
    const lines = text.split('\n')
    const elements: React.ReactNode[] = []

    let inCodeBlock = false
    let codeBlockContent: string[] = []
    let codeBlockLanguage = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      // Code blocks (```)
      if (line.startsWith('```')) {
        if (!inCodeBlock) {
          // Start of code block
          inCodeBlock = true
          codeBlockLanguage = line.substring(3).trim()
          codeBlockContent = []
        } else {
          // End of code block
          inCodeBlock = false
          elements.push(
            <div key={i} className="my-3">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                {codeBlockLanguage && (
                  <div className="bg-gray-200 dark:bg-gray-700 px-3 py-1 text-xs font-mono text-gray-600 dark:text-gray-400">
                    {codeBlockLanguage}
                  </div>
                )}
                <pre className="p-3 overflow-x-auto">
                  <code className="text-sm font-mono text-gray-800 dark:text-gray-200">
                    {codeBlockContent.join('\n')}
                  </code>
                </pre>
              </div>
            </div>
          )
          codeBlockContent = []
          codeBlockLanguage = ''
        }
        continue
      }

      // Inside code block
      if (inCodeBlock) {
        codeBlockContent.push(line)
        continue
      }
      // Headers (### ## #)
      if (line.startsWith('### ')) {
        elements.push(
          <h3 key={i} className="text-base font-bold mt-3 mb-1 text-black dark:text-white">
            {parseInlineMarkdown(line.substring(4))}
          </h3>
        )
      } else if (line.startsWith('## ')) {
        elements.push(
          <h2 key={i} className="text-lg font-bold mt-4 mb-1 text-black dark:text-white">
            {parseInlineMarkdown(line.substring(3))}
          </h2>
        )
      } else if (line.startsWith('# ')) {
        elements.push(
          <h1 key={i} className="text-xl font-bold mt-4 mb-1 text-black dark:text-white">
            {parseInlineMarkdown(line.substring(2))}
          </h1>
        )
      }
      // Lignes qui commencent par ** (titres en gras sans #)
      else if (/^\*\*[^*]+\*\*\s*$/.test(line.trim())) {
        const content = line.trim().replace(/^\*\*([^*]+)\*\*\s*$/, '$1')
        elements.push(
          <h3 key={i} className="text-base font-bold mt-3 mb-1 text-black dark:text-white">
            {content}
          </h3>
        )
      }
      // Listes avec puces (-)
      else if (line.trim().startsWith('- ')) {
        const content = line.trim().substring(2)
        elements.push(
          <div key={i} className="flex items-start gap-2 my-1">
            <span className="text-blue-500 mt-1">•</span>
            <span>{parseInlineMarkdown(content)}</span>
          </div>
        )
      }
      // Listes numérotées
      else if (/^\d+\.\s/.test(line.trim())) {
        const content = line.trim().replace(/^\d+\.\s/, '')
        elements.push(
          <div key={i} className="flex items-start gap-2 my-1 ml-4">
            <span className="text-blue-500 font-medium">{line.trim().match(/^\d+/)?.[0]}.</span>
            <span>{parseInlineMarkdown(content)}</span>
          </div>
        )
      }
      // Séparateurs (---)
      else if (line.trim() === '---') {
        elements.push(
          <hr key={i} className="my-4 border-gray-300 dark:border-gray-600" />
        )
      }
      // Lignes vides
      else if (line.trim() === '') {
        elements.push(<br key={i} />)
      }
      // Texte normal
      else {
        elements.push(
          <p key={i} className="my-1 text-gray-800 dark:text-gray-200 leading-relaxed">
            {parseInlineMarkdown(line)}
          </p>
        )
      }
    }
    
    return elements
  }

  // Fonction pour parser le markdown inline (gras, italique, etc.)
  const parseInlineMarkdown = (text: string): React.ReactNode => {
    if (!text || text.trim() === '') {
      return null
    }

    // Échapper les caractères HTML d'abord
    let processedText = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')

    // Gras (**text**) - utiliser une regex plus robuste
    processedText = processedText.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-bold text-black dark:text-white">$1</strong>')

    // Italique (*text*) - éviter les conflits avec le gras
    processedText = processedText.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em class="italic text-gray-700 dark:text-gray-300">$1</em>')

    // Code inline (`code`)
    processedText = processedText.replace(/`([^`]+)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-800 dark:text-gray-200">$1</code>')

    // Liens [text](url)
    processedText = processedText.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>')

    return <span dangerouslySetInnerHTML={{ __html: processedText }} />
  }

  return (
    <div className="markdown-content">
      {parseMarkdown(content)}
    </div>
  )
}
