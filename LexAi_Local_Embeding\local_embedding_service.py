#!/usr/bin/env python3
"""
Service d'embedding local utilisant Sentence Transformers
Alternative gratuite à OpenAI pour le développement
"""

from flask import Flask, request, jsonify
from sentence_transformers import SentenceTransformer
import numpy as np
import logging
import time
from typing import List, Dict, Any

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Modèle global (chargé une seule fois)
model = None

def load_model():
    """Charge le modèle Sentence Transformers"""
    global model
    if model is None:
        logger.info("Chargement du modèle all-MiniLM-L6-v2...")
        model = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info("Modèle chargé avec succès!")
    return model

@app.route('/health', methods=['GET'])
def health_check():
    """Vérification de santé du service"""
    return jsonify({
        "status": "healthy",
        "model": "all-MiniLM-L6-v2",
        "dimension": 384
    })

@app.route('/embeddings', methods=['POST'])
def generate_embeddings():
    """
    Génère des embeddings pour une liste de textes
    Format compatible avec l'API OpenAI
    """
    try:
        data = request.get_json()
        
        if not data or 'input' not in data:
            return jsonify({"error": "Missing 'input' field"}), 400
        
        texts = data['input']
        if isinstance(texts, str):
            texts = [texts]
        
        if not isinstance(texts, list):
            return jsonify({"error": "'input' must be a string or list of strings"}), 400
        
        # Charger le modèle
        model = load_model()
        
        # Générer les embeddings
        start_time = time.time()
        embeddings = model.encode(texts, convert_to_tensor=False)
        processing_time = time.time() - start_time
        
        # Formater la réponse comme OpenAI
        response_data = []
        for i, embedding in enumerate(embeddings):
            response_data.append({
                "object": "embedding",
                "index": i,
                "embedding": embedding.tolist()
            })
        
        response = {
            "object": "list",
            "data": response_data,
            "model": "all-MiniLM-L6-v2",
            "usage": {
                "prompt_tokens": sum(len(text.split()) for text in texts),
                "total_tokens": sum(len(text.split()) for text in texts)
            }
        }
        
        logger.info(f"Généré {len(texts)} embeddings en {processing_time:.2f}s")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Erreur lors de la génération d'embeddings: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/models', methods=['GET'])
def list_models():
    """Liste les modèles disponibles"""
    return jsonify({
        "data": [
            {
                "id": "all-MiniLM-L6-v2",
                "object": "model",
                "created": 1640995200,
                "owned_by": "sentence-transformers",
                "dimension": 384
            }
        ]
    })

if __name__ == '__main__':
    # Précharger le modèle au démarrage
    load_model()
    
    # Démarrer le serveur
    logger.info("Démarrage du service d'embedding local sur le port 8000")
    app.run(host='0.0.0.0', port=8000, debug=False)
