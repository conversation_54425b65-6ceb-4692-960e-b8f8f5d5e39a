[{"ContainingType": "LexAI.AIAssistant.API.Controllers.ChatController", "Method": "AnalyzeDocument", "RelativePath": "api/Chat/analyze-document", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.AIAssistant.Application.DTOs.DocumentAnalysisRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.AIAssistant.Application.DTOs.DocumentAnalysisResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.AIAssistant.API.Controllers.ChatController", "Method": "ContinueConversation", "RelativePath": "api/Chat/conversations/{conversationId}/messages", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conversationId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "LexAI.AIAssistant.API.Controllers.ContinueConversationRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.AIAssistant.Application.DTOs.ChatResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.AIAssistant.API.Controllers.ChatController", "Method": "GenerateDocument", "RelativePath": "api/Chat/generate-document", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.AIAssistant.Application.DTOs.DocumentGenerationRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.AIAssistant.Application.DTOs.DocumentGenerationResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.AIAssistant.API.Controllers.ChatController", "Method": "PerformLegalResearch", "RelativePath": "api/Chat/legal-research", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.AIAssistant.Application.DTOs.LegalResearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.AIAssistant.Application.DTOs.LegalResearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.AIAssistant.API.Controllers.ChatController", "Method": "SendMessage", "RelativePath": "api/Chat/message", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.AIAssistant.Application.DTOs.ChatRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.AIAssistant.Application.DTOs.ChatResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}, {"ContainingType": "LexAI.AIAssistant.API.Controllers.ChatController", "Method": "RateMessage", "RelativePath": "api/Chat/messages/{messageId}/rate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "messageId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "LexAI.AIAssistant.API.Controllers.RateMessageRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}]