
import { Search, MessageCircle, FileText, Cloud, User, ChevronLeft, ChevronRight } from 'lucide-react';

interface SidebarItem {
  icon: React.ReactNode;
  label: string;
  key: string;
}

const items: SidebarItem[] = [
  { icon: <Search size={20} />, label: 'Search', key: 'search' },
  { icon: <MessageCircle size={20} />, label: 'Chats', key: 'chats' },
  { icon: <FileText size={20} />, label: 'Docs', key: 'docs' },
  { icon: <Cloud size={20} />, label: 'Storage', key: 'storage' },
  { icon: <User size={20} />, label: 'Profile', key: 'profile' },
];

interface MainSidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  selected: string;
  onSelect: (key: string) => void;
}

export const MainSidebar: React.FC<MainSidebarProps> = ({ collapsed, onToggle, selected, onSelect }) => {
  return (
    <aside className={`h-full bg-gray-900 text-white flex flex-col transition-all duration-200 ${collapsed ? 'w-16' : 'w-48'}`}> 
      <div className="flex items-center justify-between p-2 border-b border-border">
        <span className={`font-bold text-lg transition-all duration-200 ${collapsed ? 'hidden' : 'block'}`}>ailawyer</span>
        <button onClick={onToggle} className="p-1 rounded hover:bg-gray-800">
          {collapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>
      <nav className="flex-1 mt-2">
        {items.map(item => (
          <button
            key={item.key}
            className={`flex items-center w-full px-3 py-2 my-1 rounded transition-colors duration-150 ${selected === item.key ? 'bg-gray-800' : 'hover:bg-gray-800'} ${collapsed ? 'justify-center' : ''}`}
            onClick={() => onSelect(item.key)}
            title={item.label}
          >
            {item.icon}
            {!collapsed && <span className="ml-3">{item.label}</span>}
          </button>
        ))}
      </nav>
    </aside>
  );
};
