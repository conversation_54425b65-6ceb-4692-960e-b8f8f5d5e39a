using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace LexAI.Shared.Infrastructure.Middleware;

/// <summary>
/// Middleware pour valider les tokens JWT
/// </summary>
public class JwtValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<JwtValidationMiddleware> _logger;
    private readonly string _secretKey;
    private readonly string _issuer;
    private readonly string _audience;

    public JwtValidationMiddleware(
        RequestDelegate next,
        ILogger<JwtValidationMiddleware> logger,
        string secretKey,
        string issuer,
        string audience)
    {
        _next = next;
        _logger = logger;
        _secretKey = secretKey;
        _issuer = issuer;
        _audience = audience;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Ignorer les endpoints publics
        if (IsPublicEndpoint(context.Request.Path))
        {
            await _next(context);
            return;
        }

        try
        {
            var token = ExtractTokenFromHeader(context.Request);
            
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("No JWT token found in request to {Path}", context.Request.Path);
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized: No token provided");
                return;
            }

            var principal = ValidateToken(token);
            if (principal == null)
            {
                _logger.LogWarning("Invalid JWT token for request to {Path}", context.Request.Path);
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized: Invalid token");
                return;
            }

            // Ajouter les claims au contexte
            context.User = principal;
            
            // Ajouter l'ID utilisateur aux headers pour les services en aval
            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!string.IsNullOrEmpty(userId))
            {
                context.Items["UserId"] = userId;
            }

            _logger.LogDebug("JWT token validated successfully for user {UserId} on {Path}", userId, context.Request.Path);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating JWT token for request to {Path}", context.Request.Path);
            context.Response.StatusCode = 401;
            await context.Response.WriteAsync("Unauthorized: Token validation failed");
            return;
        }

        await _next(context);
    }

    private string? ExtractTokenFromHeader(HttpRequest request)
    {
        var authHeader = request.Headers["Authorization"].FirstOrDefault();
        
        if (string.IsNullOrEmpty(authHeader))
            return null;

        if (authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
        {
            return authHeader.Substring("Bearer ".Length).Trim();
        }

        return null;
    }

    private ClaimsPrincipal? ValidateToken(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _issuer,
                ValidateAudience = true,
                ValidAudience = _audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
            return principal;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Token validation failed");
            return null;
        }
    }

    private static bool IsPublicEndpoint(PathString path)
    {
        var publicPaths = new[]
        {
            "/health",
            "/swagger",
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/refresh",
            "/api/health",
            "/metrics"
        };

        return publicPaths.Any(publicPath => 
            path.StartsWithSegments(publicPath, StringComparison.OrdinalIgnoreCase));
    }
}

/// <summary>
/// Extensions pour configurer le middleware JWT
/// </summary>
public static class JwtValidationMiddlewareExtensions
{
    public static IApplicationBuilder UseJwtValidation(
        this IApplicationBuilder builder,
        string secretKey,
        string issuer,
        string audience)
    {
        return builder.UseMiddleware<JwtValidationMiddleware>(secretKey, issuer, audience);
    }
}
