import { BaseApiService } from '../api/base'
import { 
  DocumentUploadRequest,
  Document,
  DocumentListRequest,
  DocumentListResponse,
  ProcessingStatusResponse,
  ProcessingConfiguration
} from './types'

const DATAPROCESSING_API_BASE_URL = import.meta.env.VITE_DATAPROCESSING_API_URL || 'http://localhost:5001'

class DocumentsApiService extends BaseApiService {
  constructor() {
    super(DATAPROCESSING_API_BASE_URL)
  }

  /**
   * Upload un document pour traitement
   */
  async uploadDocument(request: DocumentUploadRequest): Promise<Document> {
    const formData = new FormData()
    formData.append('file', request.file)
    
    if (request.configuration) {
      formData.append('configuration', JSON.stringify(request.configuration))
    }

    return this.upload('/api/documents/upload', formData)
  }

  /**
   * Obtient la liste des documents de l'utilisateur
   */
  async getUserDocuments(request: DocumentListRequest = {}): Promise<DocumentListResponse> {
    const params = new URLSearchParams()

    if (request.status) params.append('status', request.status)
    if (request.fromDate) params.append('fromDate', request.fromDate)
    if (request.toDate) params.append('toDate', request.toDate)
    if (request.page) params.append('page', request.page.toString())
    if (request.pageSize) params.append('pageSize', request.pageSize.toString())
    if (request.sortBy) params.append('sortBy', request.sortBy)
    if (request.sortDescending !== undefined) params.append('sortDescending', request.sortDescending.toString())

    return this.get(`/api/documents?${params.toString()}`)
  }

  /**
   * Obtient les détails d'un document
   */
  async getDocument(documentId: string): Promise<Document> {
    return this.get(`/api/documents/${documentId}`)
  }

  /**
   * Lance le traitement d'un document
   */
  async processDocument(documentId: string, configuration: ProcessingConfiguration): Promise<void> {
    return this.post(`/api/documents/${documentId}/process`, configuration)
  }

  /**
   * Obtient le statut de traitement d'un document
   */
  async getProcessingStatus(documentId: string): Promise<ProcessingStatusResponse> {
    return this.get(`/api/documents/${documentId}/status`)
  }

  /**
   * Relance le traitement d'un document
   */
  async retryProcessing(documentId: string, fromStep?: string): Promise<void> {
    return this.post(`/api/documents/${documentId}/retry`, { fromStep })
  }

  /**
   * Annule le traitement d'un document
   */
  async cancelProcessing(documentId: string): Promise<void> {
    return this.post(`/api/documents/${documentId}/cancel`)
  }

  /**
   * Supprime un document
   */
  async deleteDocument(documentId: string): Promise<void> {
    return this.delete(`/api/documents/${documentId}`)
  }

  /**
   * Configuration par défaut pour le traitement
   */
  getDefaultConfiguration(): ProcessingConfiguration {
    return {
      extractText: true,
      performOCR: true,
      detectLanguage: true,
      extractMetadata: true,
      generateThumbnail: true,
      compressionLevel: 1
    }
  }

  /**
   * Configuration rapide (traitement minimal)
   */
  getQuickConfiguration(): ProcessingConfiguration {
    return {
      extractText: true,
      performOCR: false,
      detectLanguage: false,
      extractMetadata: true,
      generateThumbnail: false,
      compressionLevel: 0
    }
  }
}

export const documentsApi = new DocumentsApiService()
