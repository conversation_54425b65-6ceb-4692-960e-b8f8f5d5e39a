import { ApiService } from './api'

// Configuration du service DocumentAnalysis
const DOCUMENT_ANALYSIS_API_BASE_URL = import.meta.env.VITE_DOCUMENT_ANALYSIS_API_URL || 'http://localhost:51405'
const documentAnalysisService = new ApiService(DOCUMENT_ANALYSIS_API_BASE_URL)

export interface AnalysisOptions {
  extractClauses?: boolean
  performRiskAssessment?: boolean
  generateRecommendations?: boolean
  extractEntities?: boolean
  findCitations?: boolean
  useAzureDocumentIntelligence?: boolean
  specificAnalysisType?: string
  focusAreas?: string[]
  language?: string
}

export interface DocumentAnalysisRequest {
  documentFile: File
  documentName?: string
  options?: AnalysisOptions
}

export interface ClauseAnalysis {
  id: string
  clauseText: string
  clauseType: string
  analysis: string
  riskLevel: string
  confidenceScore: number
  startPosition: number
  endPosition: number
  suggestedRevision?: string
  tags: string[]
}

export interface RiskAssessment {
  id: string
  riskType: string
  description: string
  severity: string
  probability: number
  impact: string
  mitigation: string
  affectedClauses: string[]
}

export interface DocumentRecommendation {
  id: string
  type: string
  title: string
  description: string
  priority: string
  suggestedAction?: string
  legalBasis?: string
  relatedClauses: string[]
}

export interface ExtractedEntity {
  id: string
  text: string
  type: string
  confidenceScore: number
  startPosition: number
  endPosition: number
  normalizedValue?: string
  metadata: Record<string, any>
}

export interface DocumentCitation {
  id: string
  type: string
  title: string
  source: string
  url?: string
  reference?: string
  relevanceScore: number
  context: string
}

export interface DocumentSummary {
  executiveSummary: string
  keyPoints: string[]
  mainParties: string[]
  importantDates: string[]
  financialTerms: string[]
  documentPurpose: string
  overallRiskLevel: string
}

export interface DocumentAnalysisResponse {
  id: string
  documentName: string
  documentType: string
  status: string
  analysisContent: string
  confidenceScore: number
  processingTimeMs: number
  tokensUsed: number
  estimatedCost: number
  modelUsed: string
  analyzedAt: string
  clauses: ClauseAnalysis[]
  risks: RiskAssessment[]
  recommendations: DocumentRecommendation[]
  entities: ExtractedEntity[]
  citations: DocumentCitation[]
  summary: DocumentSummary
}

export interface DocumentAnalysisListRequest {
  userId?: string
  documentType?: string
  status?: string
  fromDate?: string
  toDate?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortDescending?: boolean
}

export interface DocumentAnalysisSummary {
  id: string
  documentName: string
  documentType: string
  status: string
  confidenceScore: number
  riskCount: number
  recommendationCount: number
  overallRiskLevel: string
  analyzedAt: string
  processingTimeMs: number
}

export interface DocumentAnalysisListResponse {
  items: DocumentAnalysisSummary[]
  totalCount: number
  page: number
  pageSize: number
  totalPages: number
}

class DocumentAnalysisApi {
  /**
   * Analyse un document juridique complet
   */
  async analyzeDocument(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResponse> {
    const formData = new FormData()
    formData.append('file', request.documentFile)

    if (request.documentName) {
      formData.append('documentName', request.documentName)
    }

    if (request.options) {
      formData.append('options', JSON.stringify(request.options))
    }

    return await documentAnalysisService.post('/api/v1/DocumentAnalysis/analyze', formData)
  }

  /**
   * Obtient le résultat d'une analyse par ID
   */
  async getAnalysisResult(analysisId: string): Promise<DocumentAnalysisResponse> {
    return await documentAnalysisService.get(`/api/v1/DocumentAnalysis/${analysisId}`)
  }

  /**
   * Obtient la liste des analyses pour l'utilisateur connecté
   */
  async getUserAnalyses(request: DocumentAnalysisListRequest = {}): Promise<DocumentAnalysisListResponse> {
    const params = new URLSearchParams()

    if (request.documentType) params.append('documentType', request.documentType)
    if (request.status) params.append('status', request.status)
    if (request.fromDate) params.append('fromDate', request.fromDate)
    if (request.toDate) params.append('toDate', request.toDate)
    if (request.page) params.append('page', request.page.toString())
    if (request.pageSize) params.append('pageSize', request.pageSize.toString())
    if (request.sortBy) params.append('sortBy', request.sortBy)
    if (request.sortDescending !== undefined) params.append('sortDescending', request.sortDescending.toString())

    return await documentAnalysisService.get(`/api/v1/DocumentAnalysis?${params.toString()}`)
  }

  /**
   * Régénère une analyse existante avec de nouvelles options
   */
  async regenerateAnalysis(analysisId: string, options?: AnalysisOptions): Promise<DocumentAnalysisResponse> {
    return await documentAnalysisService.post(`/api/v1/DocumentAnalysis/${analysisId}/regenerate`, options)
  }

  /**
   * Supprime une analyse
   */
  async deleteAnalysis(analysisId: string): Promise<void> {
    await documentAnalysisService.delete(`/api/v1/DocumentAnalysis/${analysisId}`)
  }

  /**
   * Options d'analyse prédéfinies
   */
  getDefaultOptions(): AnalysisOptions {
    return {
      extractClauses: true,
      performRiskAssessment: true,
      generateRecommendations: true,
      extractEntities: true,
      findCitations: true,
      useAzureDocumentIntelligence: true,
      language: 'fr'
    }
  }

  /**
   * Options d'analyse rapide (moins de fonctionnalités)
   */
  getQuickAnalysisOptions(): AnalysisOptions {
    return {
      extractClauses: true,
      performRiskAssessment: true,
      generateRecommendations: false,
      extractEntities: false,
      findCitations: false,
      useAzureDocumentIntelligence: true,
      language: 'fr'
    }
  }

  /**
   * Options d'analyse complète (toutes les fonctionnalités)
   */
  getComprehensiveAnalysisOptions(): AnalysisOptions {
    return {
      extractClauses: true,
      performRiskAssessment: true,
      generateRecommendations: true,
      extractEntities: true,
      findCitations: true,
      useAzureDocumentIntelligence: true,
      language: 'fr'
    }
  }
}

export const documentAnalysisApi = new DocumentAnalysisApi()
