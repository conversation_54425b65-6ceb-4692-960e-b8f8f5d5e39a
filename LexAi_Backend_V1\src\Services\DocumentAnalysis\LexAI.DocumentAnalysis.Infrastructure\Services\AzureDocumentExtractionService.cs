using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using LexAI.DocumentAnalysis.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service d'extraction de texte utilisant Azure Document Intelligence et fallback local
/// </summary>
public class AzureDocumentExtractionService : IDocumentExtractionService
{
    private readonly DocumentAnalysisClient? _documentAnalysisClient;
    private readonly ILogger<AzureDocumentExtractionService> _logger;
    private readonly IConfiguration _configuration;
    private readonly bool _azureEnabled;

    public AzureDocumentExtractionService(
        IConfiguration configuration,
        ILogger<AzureDocumentExtractionService> logger)
    {
        _configuration = configuration;
        _logger = logger;

        var endpoint = _configuration["Azure:DocumentIntelligence:Endpoint"];
        var apiKey = _configuration["Azure:DocumentIntelligence:ApiKey"];

        if (!string.IsNullOrEmpty(endpoint) && !string.IsNullOrEmpty(apiKey))
        {
            try
            {
                _documentAnalysisClient = new DocumentAnalysisClient(
                    new Uri(endpoint), 
                    new AzureKeyCredential(apiKey));
                _azureEnabled = true;
                _logger.LogInformation("Azure Document Intelligence client initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Azure Document Intelligence client, will use local extraction");
                _azureEnabled = false;
            }
        }
        else
        {
            _logger.LogInformation("Azure Document Intelligence not configured, using local extraction only");
            _azureEnabled = false;
        }
    }

    /// <summary>
    /// Extrait le texte d'un document avec fallback automatique
    /// </summary>
    public async Task<DocumentExtractionResult> ExtractTextAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        if (_azureEnabled)
        {
            try
            {
                return await ExtractTextWithAzureAsync(documentContent, documentType, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Azure extraction failed, falling back to local extraction");
                return await ExtractTextLocallyAsync(documentContent, documentType, cancellationToken);
            }
        }
        else
        {
            return await ExtractTextLocallyAsync(documentContent, documentType, cancellationToken);
        }
    }

    /// <summary>
    /// Extrait le texte avec Azure Document Intelligence
    /// </summary>
    public async Task<DocumentExtractionResult> ExtractTextWithAzureAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        if (!_azureEnabled || _documentAnalysisClient == null)
        {
            throw new InvalidOperationException("Azure Document Intelligence is not available");
        }

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting Azure Document Intelligence extraction for document type: {DocumentType}", documentType);

        try
        {
            using var stream = new MemoryStream(documentContent);
            
            // Utiliser le modèle de lecture général
            var operation = await _documentAnalysisClient.AnalyzeDocumentAsync(
                WaitUntil.Completed,
                "prebuilt-read",
                stream,
                cancellationToken: cancellationToken);

            var result = operation.Value;
            stopwatch.Stop();

            var extractionResult = new DocumentExtractionResult
            {
                ExtractedText = result.Content,
                ConfidenceScore = CalculateAverageConfidence(result),
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ExtractionMethod = "Azure Document Intelligence",
                Pages = ConvertPages(result.Pages),
                Tables = ConvertTables(result.Tables),
                Fields = ConvertKeyValuePairs(result.KeyValuePairs)
            };

            _logger.LogInformation("Azure extraction completed successfully. Processing time: {ProcessingTime}ms, Pages: {PageCount}", 
                stopwatch.ElapsedMilliseconds, result.Pages.Count);

            return extractionResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Azure Document Intelligence extraction");
            throw;
        }
    }

    /// <summary>
    /// Extrait les métadonnées du document
    /// </summary>
    public async Task<DocumentMetadata> ExtractMetadataAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        var metadata = new DocumentMetadata
        {
            FileSizeBytes = documentContent.Length,
            Language = "fr" // Détection automatique à implémenter
        };

        try
        {
            switch (documentType.ToLowerInvariant())
            {
                case "pdf":
                    metadata = ExtractPdfMetadata(documentContent, metadata);
                    break;
                case "docx":
                case "doc":
                    metadata = ExtractWordMetadata(documentContent, metadata);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract metadata for document type: {DocumentType}", documentType);
        }

        return metadata;
    }

    // Méthodes privées pour l'extraction locale

    private async Task<DocumentExtractionResult> ExtractTextLocallyAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting local text extraction for document type: {DocumentType}", documentType);

        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            string extractedText = documentType.ToLowerInvariant() switch
            {
                "pdf" => ExtractTextFromPdf(documentContent),
                "docx" => ExtractTextFromDocx(documentContent),
                "doc" => ExtractTextFromDoc(documentContent),
                "txt" => Encoding.UTF8.GetString(documentContent),
                _ => throw new NotSupportedException($"Document type {documentType} is not supported for local extraction")
            };

            stopwatch.Stop();

            var result = new DocumentExtractionResult
            {
                ExtractedText = extractedText,
                ConfidenceScore = 0.8, // Score fixe pour l'extraction locale
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ExtractionMethod = "Local Extraction",
                Pages = new List<DocumentPage>
                {
                    new DocumentPage
                    {
                        PageNumber = 1,
                        Text = extractedText,
                        ConfidenceScore = 0.8
                    }
                }
            };

            _logger.LogInformation("Local extraction completed successfully. Processing time: {ProcessingTime}ms", 
                stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during local text extraction");
            throw;
        }
    }

    private string ExtractTextFromPdf(byte[] pdfContent)
    {
        var text = new StringBuilder();
        
        using var reader = new PdfReader(pdfContent);
        for (int page = 1; page <= reader.NumberOfPages; page++)
        {
            var pageText = PdfTextExtractor.GetTextFromPage(reader, page);
            text.AppendLine(pageText);
        }
        
        return text.ToString();
    }

    private string ExtractTextFromDocx(byte[] docxContent)
    {
        using var stream = new MemoryStream(docxContent);
        using var document = WordprocessingDocument.Open(stream, false);
        
        var body = document.MainDocumentPart?.Document?.Body;
        if (body == null) return string.Empty;

        var text = new StringBuilder();
        foreach (var paragraph in body.Elements<Paragraph>())
        {
            text.AppendLine(paragraph.InnerText);
        }
        
        return text.ToString();
    }

    private string ExtractTextFromDoc(byte[] docContent)
    {
        // Pour les fichiers .doc, une conversion plus complexe serait nécessaire
        // Pour l'instant, retourner un message d'erreur informatif
        throw new NotSupportedException("Legacy .doc format requires additional libraries. Please convert to .docx format.");
    }

    private DocumentMetadata ExtractPdfMetadata(byte[] pdfContent, DocumentMetadata metadata)
    {
        try
        {
            using var reader = new PdfReader(pdfContent);
            var info = reader.Info;
            
            metadata.Title = info.ContainsKey("Title") ? info["Title"] : string.Empty;
            metadata.Author = info.ContainsKey("Author") ? info["Author"] : string.Empty;
            metadata.Subject = info.ContainsKey("Subject") ? info["Subject"] : string.Empty;
            metadata.PageCount = reader.NumberOfPages;
            
            if (info.ContainsKey("CreationDate"))
            {
                if (DateTime.TryParse(info["CreationDate"], out var creationDate))
                    metadata.CreationDate = creationDate;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract PDF metadata");
        }
        
        return metadata;
    }

    private DocumentMetadata ExtractWordMetadata(byte[] docxContent, DocumentMetadata metadata)
    {
        try
        {
            using var stream = new MemoryStream(docxContent);
            using var document = WordprocessingDocument.Open(stream, false);
            
            var coreProps = document.PackageProperties;
            metadata.Title = coreProps.Title ?? string.Empty;
            metadata.Author = coreProps.Creator ?? string.Empty;
            metadata.Subject = coreProps.Subject ?? string.Empty;
            metadata.CreationDate = coreProps.Created;
            metadata.ModificationDate = coreProps.Modified;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract Word metadata");
        }
        
        return metadata;
    }

    // Méthodes utilitaires pour Azure

    private static double CalculateAverageConfidence(AnalyzeResult result)
    {
        var confidences = new List<double>();
        
        foreach (var page in result.Pages)
        {
            foreach (var line in page.Lines)
            {
                confidences.Add(line.Confidence);
            }
        }
        
        return confidences.Any() ? confidences.Average() : 0.0;
    }

    private static List<DocumentPage> ConvertPages(IReadOnlyList<DocumentPage> azurePages)
    {
        return azurePages.Select((page, index) => new Application.Interfaces.DocumentPage
        {
            PageNumber = index + 1,
            Text = string.Join("\n", page.Lines.Select(l => l.Content)),
            ConfidenceScore = page.Lines.Any() ? page.Lines.Average(l => l.Confidence) : 0.0,
            Lines = page.Lines.Select(l => new DocumentLine
            {
                Text = l.Content,
                ConfidenceScore = l.Confidence,
                BoundingBox = new BoundingBox
                {
                    X = l.BoundingPolygon.FirstOrDefault()?.X ?? 0,
                    Y = l.BoundingPolygon.FirstOrDefault()?.Y ?? 0,
                    Width = 0, // Calcul approximatif nécessaire
                    Height = 0
                }
            }).ToList()
        }).ToList();
    }

    private static List<DocumentTable> ConvertTables(IReadOnlyList<DocumentTable> azureTables)
    {
        return azureTables.Select(table => new Application.Interfaces.DocumentTable
        {
            RowCount = table.RowCount,
            ColumnCount = table.ColumnCount,
            Cells = table.Cells.Select(cell => new DocumentTableCell
            {
                RowIndex = cell.RowIndex,
                ColumnIndex = cell.ColumnIndex,
                Text = cell.Content,
                ConfidenceScore = cell.Confidence
            }).ToList()
        }).ToList();
    }

    private static List<DocumentField> ConvertKeyValuePairs(IReadOnlyDictionary<string, DocumentField> azureFields)
    {
        return azureFields.Select(kvp => new Application.Interfaces.DocumentField
        {
            Name = kvp.Key,
            Value = kvp.Value.Content,
            ConfidenceScore = kvp.Value.Confidence,
            Type = kvp.Value.FieldType.ToString()
        }).ToList();
    }
}
