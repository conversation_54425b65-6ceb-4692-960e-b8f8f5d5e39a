using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using LexAI.DocumentAnalysis.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using iText.Kernel.Pdf.Canvas.Parser.Listener;
using static LexAI.DocumentAnalysis.Application.Interfaces.IDocumentExtractionService;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service d'extraction de texte utilisant Azure Document Intelligence et fallback local
/// </summary>
public class AzureDocumentExtractionService : IDocumentExtractionService
{
    private readonly DocumentAnalysisClient? _documentAnalysisClient;
    private readonly ILogger<AzureDocumentExtractionService> _logger;
    private readonly IConfiguration _configuration;
    private readonly bool _azureEnabled;

    public AzureDocumentExtractionService(
        IConfiguration configuration,
        ILogger<AzureDocumentExtractionService> logger)
    {
        _configuration = configuration;
        _logger = logger;

        var endpoint = _configuration["Azure:DocumentIntelligence:Endpoint"];
        var apiKey = _configuration["Azure:DocumentIntelligence:ApiKey"];

        if (!string.IsNullOrEmpty(endpoint) && !string.IsNullOrEmpty(apiKey))
        {
            try
            {
                _documentAnalysisClient = new DocumentAnalysisClient(
                    new Uri(endpoint), 
                    new AzureKeyCredential(apiKey));
                _azureEnabled = true;
                _logger.LogInformation("Azure Document Intelligence client initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Azure Document Intelligence client, will use local extraction");
                _azureEnabled = false;
            }
        }
        else
        {
            _logger.LogInformation("Azure Document Intelligence not configured, using local extraction only");
            _azureEnabled = false;
        }
    }

    /// <summary>
    /// Extrait le texte d'un document avec fallback automatique
    /// </summary>
    public async Task<DocumentExtractionResult> ExtractTextAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        if (_azureEnabled)
        {
            try
            {
                return await ExtractTextWithAzureAsync(documentContent, documentType, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Azure extraction failed, falling back to local extraction");
                return await ExtractTextLocallyAsync(documentContent, documentType, cancellationToken);
            }
        }
        else
        {
            return await ExtractTextLocallyAsync(documentContent, documentType, cancellationToken);
        }
    }

    /// <summary>
    /// Extrait le texte avec Azure Document Intelligence
    /// </summary>
    public async Task<DocumentExtractionResult> ExtractTextWithAzureAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        if (!_azureEnabled || _documentAnalysisClient == null)
        {
            throw new InvalidOperationException("Azure Document Intelligence is not available");
        }

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting Azure Document Intelligence extraction for document type: {DocumentType}", documentType);

        try
        {
            using var stream = new MemoryStream(documentContent);
            
            // Utiliser le modèle de lecture général
            var operation = await _documentAnalysisClient.AnalyzeDocumentAsync(
                WaitUntil.Completed,
                "prebuilt-read",
                stream,
                cancellationToken: cancellationToken);

            var result = operation.Value;
            stopwatch.Stop();

            var extractionResult = new DocumentExtractionResult
            {
                ExtractedText = result.Content,
                ConfidenceScore = CalculateAverageConfidence(result),
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ExtractionMethod = "Azure Document Intelligence",
                Pages = ConvertPages(result.Pages),
                Tables = ConvertTables(result.Tables),
                Fields = new List<DocumentFields>() // KeyValuePairs ne sont pas directement convertibles
            };

            _logger.LogInformation("Azure extraction completed successfully. Processing time: {ProcessingTime}ms, Pages: {PageCount}", 
                stopwatch.ElapsedMilliseconds, result.Pages.Count);

            return extractionResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Azure Document Intelligence extraction");
            throw;
        }
    }

    /// <summary>
    /// Extrait les métadonnées du document
    /// </summary>
    public async Task<DocumentMetadata> ExtractMetadataAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        var metadata = new DocumentMetadata
        {
            FileSizeBytes = documentContent.Length,
            Language = "fr" // Détection automatique à implémenter
        };

        try
        {
            switch (documentType.ToLowerInvariant())
            {
                case "pdf":
                    metadata = ExtractPdfMetadata(documentContent, metadata);
                    break;
                case "docx":
                case "doc":
                    metadata = ExtractWordMetadata(documentContent, metadata);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract metadata for document type: {DocumentType}", documentType);
        }

        return metadata;
    }

    // Méthodes privées pour l'extraction locale

    private async Task<DocumentExtractionResult> ExtractTextLocallyAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting local text extraction for document type: {DocumentType}", documentType);

        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            string extractedText = documentType.ToLowerInvariant() switch
            {
                "pdf" => ExtractTextFromPdf(documentContent),
                "docx" => ExtractTextFromDocx(documentContent),
                "doc" => ExtractTextFromDoc(documentContent),
                "txt" => Encoding.UTF8.GetString(documentContent),
                _ => throw new NotSupportedException($"Document type {documentType} is not supported for local extraction")
            };

            stopwatch.Stop();

            var result = new DocumentExtractionResult
            {
                ExtractedText = extractedText,
                ConfidenceScore = 0.8, // Score fixe pour l'extraction locale
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                ExtractionMethod = "Local Extraction",
                Pages = new List<DocumentPages>
                {
                    new DocumentPages
                    {
                        PageNumber = 1,
                        Text = extractedText,
                        ConfidenceScore = 0.8,
                        Lines = new List<Application.Interfaces.DocumentLine>()
                    }
                }
            };

            _logger.LogInformation("Local extraction completed successfully. Processing time: {ProcessingTime}ms", 
                stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during local text extraction");
            throw;
        }
    }

    private string ExtractTextFromPdf(byte[] pdfContent)
    {
        var text = new StringBuilder();

        using var memoryStream = new MemoryStream(pdfContent);
        using var pdfReader = new PdfReader(memoryStream);
        using var pdfDocument = new PdfDocument(pdfReader);

        for (int pageNum = 1; pageNum <= pdfDocument.GetNumberOfPages(); pageNum++)
        {
            var page = pdfDocument.GetPage(pageNum);
            var strategy = new SimpleTextExtractionStrategy();
            var pageText = PdfTextExtractor.GetTextFromPage(page, strategy);
            text.AppendLine(pageText);
        }

        return text.ToString();
    }

    private string ExtractTextFromDocx(byte[] docxContent)
    {
        using var stream = new MemoryStream(docxContent);
        using var document = WordprocessingDocument.Open(stream, false);
        
        var body = document.MainDocumentPart?.Document?.Body;
        if (body == null) return string.Empty;

        var text = new StringBuilder();
        foreach (var paragraph in body.Elements<Paragraph>())
        {
            text.AppendLine(paragraph.InnerText);
        }
        
        return text.ToString();
    }

    private string ExtractTextFromDoc(byte[] docContent)
    {
        // Pour les fichiers .doc, une conversion plus complexe serait nécessaire
        // Pour l'instant, retourner un message d'erreur informatif
        throw new NotSupportedException("Legacy .doc format requires additional libraries. Please convert to .docx format.");
    }

    private DocumentMetadata ExtractPdfMetadata(byte[] pdfContent, DocumentMetadata metadata)
    {
        try
        {
            using var memoryStream = new MemoryStream(pdfContent);
            using var pdfReader = new PdfReader(memoryStream);
            using var pdfDocument = new PdfDocument(pdfReader);

            var docInfo = pdfDocument.GetDocumentInfo();

            metadata.Title = docInfo.GetTitle() ?? string.Empty;
            metadata.Author = docInfo.GetAuthor() ?? string.Empty;
            metadata.Subject = docInfo.GetSubject() ?? string.Empty;
            metadata.PageCount = pdfDocument.GetNumberOfPages();

            // Extraction des dates de création (iText7 utilise des propriétés différentes)
            try
            {
                var creationDateStr = docInfo.GetMoreInfo("CreationDate");
                if (!string.IsNullOrEmpty(creationDateStr) && DateTime.TryParse(creationDateStr, out var creationDate))
                {
                    metadata.CreationDate = creationDate;
                }
            }
            catch
            {
                // Ignore si la conversion échoue
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract PDF metadata");
        }

        return metadata;
    }

    private DocumentMetadata ExtractWordMetadata(byte[] docxContent, DocumentMetadata metadata)
    {
        try
        {
            using var stream = new MemoryStream(docxContent);
            using var document = WordprocessingDocument.Open(stream, false);
            
            var coreProps = document.PackageProperties;
            metadata.Title = coreProps.Title ?? string.Empty;
            metadata.Author = coreProps.Creator ?? string.Empty;
            metadata.Subject = coreProps.Subject ?? string.Empty;
            metadata.CreationDate = coreProps.Created;
            metadata.ModificationDate = coreProps.Modified;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract Word metadata");
        }
        
        return metadata;
    }

    // Méthodes utilitaires pour Azure

    private static double CalculateAverageConfidence(AnalyzeResult result)
    {
        var confidences = new List<double>();
        
        foreach (var page in result.Pages)
        {
            foreach (var line in page.Lines)
            {
                confidences.Add((double)line.Confidence);
            }
        }
        
        return confidences.Any() ? confidences.Average() : 0.0;
    }

    private static List<DocumentPages> ConvertPages(IReadOnlyList<Azure.AI.FormRecognizer.DocumentAnalysis.DocumentPage> azurePages)
    {
        return azurePages.Select((page, index) => new DocumentPages
        {
            PageNumber = index + 1,
            Text = string.Join("\n", page.Lines.Select(l => l.Content)),
            ConfidenceScore = page.Lines.Any() ? page.Lines.Average(l => (double)l.Confidence) : 0.0,
            Lines = page.Lines.Select(l => new Application.Interfaces.DocumentLine
            {
                Text = l.Content,
                ConfidenceScore = (double)l.Confidence,
                BoundingBox = new Application.Interfaces.BoundingBox
                {
                    X = l.BoundingPolygon.FirstOrDefault().X,
                    Y = l.BoundingPolygon.FirstOrDefault().Y,
                    Width = 0, // Calcul approximatif nécessaire
                    Height = 0
                }
            }).ToList()
        }).ToList();
    }

    private static List<DocumentTables> ConvertTables(IReadOnlyList<Azure.AI.FormRecognizer.DocumentAnalysis.DocumentTable> azureTables)
    {
        return azureTables.Select(table => new DocumentTables
        {
            RowCount = table.RowCount,
            ColumnCount = table.ColumnCount,
            Cells = table.Cells.Select(cell => new Application.Interfaces.DocumentTableCell
            {
                RowIndex = cell.RowIndex,
                ColumnIndex = cell.ColumnIndex,
                Text = cell.Content,
                ConfidenceScore = (double)cell.Confidence
            }).ToList()
        }).ToList();
    }

    private static List<DocumentFields> ConvertKeyValuePairs(IReadOnlyDictionary<string, Azure.AI.FormRecognizer.DocumentAnalysis.DocumentField> azureFields)
    {
        return azureFields.Select(kvp => new DocumentFields
        {
            Name = kvp.Key,
            Value = kvp.Value.Content,
            ConfidenceScore = (double)kvp.Value.Confidence,
            Type = kvp.Value.FieldType.ToString()
        }).ToList();
    }
}
