# 🔄 LexAI Data Preprocessing Service - Architecture Agentic

Service de préprocessing de données avec architecture agentic pour enrichir les bases vectorielles du système RAG juridique LexAI. Utilise une équipe d'agents spécialisés pour traiter les documents de manière modulaire et scalable.

## 🧠 Architecture Agentic - Équipe d'Agents Spécialisés

### 🤖 **Agents Métier**

| Agent | Rôle | Responsabilités |
|-------|------|----------------|
| **ExtractionAgent** | Extraction de texte | Extraction de contenu depuis PDF, DOCX, TXT |
| **ClassificationAgent** | Classification juridique | Détection du domaine juridique, extraction d'entités |
| **ChunkingAgent** | Découpage intelligent | Chunking sémantique et stratégique |
| **VectorizationAgent** | Vectorisation | Génération d'embeddings avec OpenAI |
| **RoutingAgent** | Routage intelligent | Direction vers les bonnes bases vectorielles |
| **QualityAssuranceAgent** | Assurance qualité | Validation et contrôle qualité |
| **OrchestrationAgent** | Orchestration | Coordination de tous les agents |

### 🏗️ **Pipeline de Traitement**

```mermaid
graph TD
    A[Document Upload] --> B[ExtractionAgent]
    B --> C[ClassificationAgent]
    C --> D[ChunkingAgent]
    D --> E[VectorizationAgent]
    E --> F[RoutingAgent]
    F --> G[QualityAssuranceAgent]
    G --> H[Vector Storage]
    
    O[OrchestrationAgent] --> B
    O --> C
    O --> D
    O --> E
    O --> F
    O --> G
```

## 📋 Fonctionnalités Complètes

### ✅ **Pipeline de Traitement Complet**
- **Upload de documents** - PDF, DOCX, TXT avec validation
- **Extraction de texte** - Multi-format avec métadonnées
- **Classification juridique** - 20+ domaines juridiques supportés
- **Chunking intelligent** - 7 stratégies de découpage
- **Vectorisation** - OpenAI embeddings (3 modèles)
- **Routage adaptatif** - Vers MongoDB, Qdrant, Weaviate, Pinecone
- **Assurance qualité** - Validation automatique et suggestions

### 🔧 **Fonctionnalités Avancées**
- **Retry automatique** - Reprise depuis n'importe quelle étape
- **Processing en arrière-plan** - Hangfire pour jobs asynchrones
- **Rate limiting** - Protection contre l'abus
- **Monitoring complet** - Métriques et observabilité
- **Cache intelligent** - Optimisation des performances
- **Déduplication** - Détection de documents dupliqués

## 🚀 Démarrage Rapide

### Prérequis
- .NET 9 SDK
- PostgreSQL 16+ (stockage documents et jobs)
- MongoDB 7+ (cache et métadonnées)
- Clé API OpenAI
- Docker (optionnel)

### Installation

1. **Configurer les variables d'environnement**
```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_EMBEDDING_MODEL="text-embedding-3-small"

# Bases de données
export ConnectionStrings__PostgreSql="Host=localhost;Database=data_preprocessing_db;Username=lexai_user;Password=lexai_password_2024!"
export ConnectionStrings__MongoDB="mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_preprocessing?authSource=admin"

# Vector Databases
export VectorDatabases__MongoDB__ConnectionString="mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_vectors?authSource=admin"
export VectorDatabases__Qdrant__Url="http://localhost:6333"
```

2. **Démarrer l'infrastructure**
```bash
# Avec Docker Compose : naviguer dans le dossier du projet ouvrir 
docker-compose up -d 

# Qdrant (optionnel)
docker run -d --name qdrant \
  -p 6333:6333 \
  qdrant/qdrant

# Ou passer par le powershell script : se placer a la racine du projet

.\src\Services\DataPreprocessing\scripts\start-dev.ps1

```

3. **Appliquer les migrations**
```bash
# Générer la migration initiale : Se placer a la racine du projet
.\src\Services\DataPreprocessing\scripts\create-migration.ps1 "InitialMigration"
# Appliquer la migration
.\src\Services\DataPreprocessing\scripts\update-database.ps1
```

4. **Lancer le service**
```bash
dotnet run --project src/Services/DataPreprocessing/LexAI.DataPreprocessing.API
```

5. **Accéder aux interfaces**
- API: http://localhost:8084
- Swagger: http://localhost:8084/swagger
- Hangfire Dashboard: http://localhost:8084/hangfire

## 📚 API Documentation

### Endpoints principaux

#### Upload et Traitement
- `POST /api/documents/upload` - Upload et traitement automatique
- `POST /api/documents/{id}/process` - Traitement manuel avec configuration
- `POST /api/documents/{id}/retry` - Retry depuis une étape spécifique
- `POST /api/documents/{id}/cancel` - Annulation du traitement

#### Monitoring et Status
- `GET /api/documents/{id}/status` - Status de traitement en temps réel
- `GET /api/documents` - Liste des documents utilisateur
- `GET /health` - Health checks du service
- `GET /hangfire` - Dashboard des jobs en arrière-plan

### Modèles de données

#### DocumentUploadRequestDto
```json
{
  "fileName": "contrat-commercial.pdf",
  "fileContent": "base64-encoded-content",
  "mimeType": "application/pdf",
  "userId": "guid",
  "configuration": {
    "chunking": {
      "strategy": "Semantic",
      "maxChunkSize": 1000,
      "overlapSize": 200,
      "preserveSentences": true
    },
    "embeddingModel": "OpenAISmall",
    "targetDatabases": ["MongoDB", "Qdrant"],
    "performQualityAssurance": true,
    "extractNamedEntities": true,
    "extractKeywords": true,
    "priority": "Normal"
  },
  "metadata": {}
}
```

#### ProcessingResultDto
```json
{
  "documentId": "guid",
  "success": true,
  "finalStatus": "Completed",
  "totalProcessingTime": "00:00:45.123",
  "completedSteps": [
    {
      "stepName": "Extraction",
      "success": true,
      "duration": "00:00:02.456",
      "agentName": "ExtractionAgent",
      "metadata": {
        "textLength": 15420,
        "confidence": 0.95
      }
    },
    {
      "stepName": "Classification",
      "success": true,
      "duration": "00:00:03.789",
      "agentName": "ClassificationAgent",
      "metadata": {
        "detectedDomain": "Commercial",
        "confidence": 0.87,
        "keywordCount": 25,
        "entityCount": 12
      }
    }
  ],
  "totalChunks": 18,
  "totalTokens": 3850,
  "estimatedCost": 0.0077,
  "vectorDatabasesUsed": ["MongoDB/commercial", "Qdrant/legal-docs"],
  "qualityAssessment": {
    "overallScore": 0.92,
    "qualityPassed": true,
    "issues": [],
    "recommendations": []
  }
}
```

## 🔧 Configuration Avancée

### Stratégies de Chunking

| Stratégie | Description | Cas d'usage |
|-----------|-------------|-------------|
| `FixedSize` | Taille fixe avec overlap | Documents uniformes |
| `Semantic` | Découpage sémantique | Documents complexes |
| `Sentence` | Par phrases | Textes courts |
| `Paragraph` | Par paragraphes | Documents structurés |
| `Section` | Par sections | Documents longs |
| `SlidingWindow` | Fenêtre glissante | Analyse continue |
| `Recursive` | Récursif adaptatif | Documents mixtes |

### Modèles d'Embedding

| Modèle | Dimension | Coût | Performance |
|--------|-----------|------|-------------|
| `OpenAISmall` | 1536 | Bas | Rapide |
| `OpenAILarge` | 3072 | Élevé | Précis |
| `OpenAIAda002` | 1536 | Moyen | Équilibré |

### Bases Vectorielles Supportées

| Base | Type | Avantages | Cas d'usage |
|------|------|-----------|-------------|
| **MongoDB** | Document + Vector | Intégration native | Développement |
| **Qdrant** | Vector natif | Performance | Production |
| **Weaviate** | Graph + Vector | Sémantique | Recherche complexe |
| **Pinecone** | Cloud | Scalabilité | Enterprise |

## 🧪 Tests

### Exécuter tous les tests
```bash
.\scripts\run-tests.ps1 -All -Project "DataPreprocessing"
```

### Tests par catégorie
```bash
# Tests unitaires
.\scripts\run-tests.ps1 -Unit -Project "DataPreprocessing"

# Tests d'intégration
.\scripts\run-tests.ps1 -Integration -Project "DataPreprocessing"

# Tests d'agents
.\scripts\run-tests.ps1 -Category "Agents" -Project "DataPreprocessing"
```

### Structure des tests
```
tests/
├── LexAI.DataPreprocessing.UnitTests/
│   ├── Domain/                    # Tests des entités et value objects
│   ├── Application/               # Tests des handlers et agents
│   └── Infrastructure/            # Tests des services d'infrastructure
└── LexAI.DataPreprocessing.IntegrationTests/
    ├── Agents/                    # Tests d'intégration des agents
    ├── Pipeline/                  # Tests du pipeline complet
    └── VectorStorage/             # Tests avec bases vectorielles
```

## 📊 Monitoring et Observabilité

### Métriques clés
- **Throughput de traitement** - Documents/heure par agent
- **Latence par étape** - Temps de traitement par agent
- **Taux de succès** - Pourcentage de documents traités avec succès
- **Coût par document** - Coût OpenAI par traitement
- **Qualité moyenne** - Score qualité des documents traités
- **Utilisation des agents** - Charge de travail par agent

### Health Checks
- `GET /health` - Santé globale du service
- Vérification PostgreSQL et MongoDB
- Test de connectivité OpenAI
- Validation des bases vectorielles
- Status des agents et de l'orchestrateur

### Logs structurés
- **Corrélation des traitements** avec ID unique
- **Métriques de performance** par agent
- **Audit trail** complet du pipeline
- **Erreurs détaillées** avec contexte d'agent

## 🔐 Sécurité

### Authentification et Autorisation
- **JWT Bearer tokens** requis pour tous les endpoints
- **Validation des rôles** (Lawyer, SeniorLawyer, Administrator)
- **Rate limiting** : 10 uploads/minute, 5 processings/minute

### Protection des données
- **Validation de contenu** avant traitement
- **Chiffrement** des fichiers stockés
- **Anonymisation** dans les logs
- **Audit trail** complet des opérations

### Sécurité des agents
- **Isolation des agents** - Chaque agent a son scope
- **Validation des inputs** - Tous les agents valident leurs entrées
- **Error handling** - Gestion sécurisée des erreurs
- **Resource limiting** - Limitation des ressources par agent

## 🚀 Déploiement

### Docker
```bash
# Build de l'image
docker build -t lexai-data-preprocessing:latest -f src/Services/DataPreprocessing/LexAI.DataPreprocessing.API/Dockerfile .

# Lancement du conteneur
docker run -p 8084:8084 \
  -e OPENAI_API_KEY=your-key \
  -e ConnectionStrings__PostgreSql=your-connection \
  lexai-data-preprocessing:latest
```

### Docker Compose
```bash
# Démarrage complet avec infrastructure
docker-compose up -d data-preprocessing-service
```

## 📈 Performance

### Optimisations implémentées
- **Processing asynchrone** avec Hangfire
- **Cache intelligent** pour éviter les retraitements
- **Parallel processing** des chunks
- **Connection pooling** optimisé
- **Batch operations** pour les bases vectorielles

### Benchmarks
- **Upload simple** : ~200ms
- **Extraction PDF** : ~2-5s selon la taille
- **Classification** : ~1-3s avec OpenAI
- **Chunking sémantique** : ~500ms-2s
- **Vectorisation** : ~100ms par chunk
- **Pipeline complet** : ~10-30s selon la complexité

### Scalabilité
- **Horizontal scaling** - Plusieurs instances avec load balancer
- **Agent scaling** - Chaque agent peut être scalé indépendamment
- **Queue management** - Hangfire gère la distribution des jobs
- **Database sharding** - Support du sharding pour les gros volumes

## 🤝 Contribution

### Standards de code
- **Clean Architecture** avec agents spécialisés
- **CQRS** pour séparation des responsabilités
- **Tests obligatoires** (>90% couverture)
- **Documentation XML** complète
- **Logging structuré** avec Serilog

### Workflow de développement
1. Créer une branche feature
2. Implémenter l'agent ou la fonctionnalité
3. Tester l'intégration avec le pipeline
4. Vérifier les coûts OpenAI
5. Créer une Pull Request
6. Review et merge

## 📝 Changelog

### v1.0.0 (En cours)
- ✅ Architecture agentic complète avec 7 agents spécialisés
- ✅ Pipeline de traitement complet (Upload → Vector Storage)
- ✅ Support multi-format (PDF, DOCX, TXT)
- ✅ Classification juridique avec 20+ domaines
- ✅ 7 stratégies de chunking intelligent
- ✅ Intégration OpenAI (3 modèles d'embedding)
- ✅ Support 4 bases vectorielles (MongoDB, Qdrant, Weaviate, Pinecone)
- ✅ Assurance qualité automatique
- ✅ Processing en arrière-plan avec Hangfire
- ✅ Rate limiting et sécurité
- ✅ Tests complets et documentation

### Prochaines versions
- 🔄 Agent de résumé automatique
- 🔄 Agent de traduction multilingue
- 🔄 Agent d'extraction de métadonnées avancées
- 🔄 Support OCR pour images et documents scannés
- 🔄 Agent de validation juridique spécialisé
