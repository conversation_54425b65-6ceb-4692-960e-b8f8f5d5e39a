2025-06-12 19:49:14.302 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-12 19:49:14.432 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-12 19:49:14.872 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-12 19:49:14.875 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-12 19:49:14.973 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-12 19:49:14.977 +04:00 [INF] Hosting environment: Development
2025-06-12 19:49:14.980 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-12 19:49:16.520 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-12 19:49:16.745 +04:00 [INF] Request GET / started with correlation ID 1f4551bb-1ba7-4b0b-b9a8-d1cf69747323
2025-06-12 19:49:18.623 +04:00 [INF] Request GET / completed in 1871ms with status 404 (Correlation ID: 1f4551bb-1ba7-4b0b-b9a8-d1cf69747323)
2025-06-12 19:49:18.648 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2136.51ms
2025-06-12 19:49:18.693 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
