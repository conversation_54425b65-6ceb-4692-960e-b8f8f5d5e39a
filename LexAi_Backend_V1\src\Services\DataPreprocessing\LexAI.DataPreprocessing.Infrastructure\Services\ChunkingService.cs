using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Chunking service implementation
/// </summary>
public class ChunkingService : IChunkingService
{
    private readonly ILogger<ChunkingService> _logger;

    /// <summary>
    /// Supported chunking strategies
    /// </summary>
    public IEnumerable<ChunkingStrategy> SupportedStrategies => Enum.GetValues<ChunkingStrategy>();

    /// <summary>
    /// Initializes a new instance of the ChunkingService
    /// </summary>
    /// <param name="logger">Logger</param>
    public ChunkingService(ILogger<ChunkingService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Chunks text into smaller pieces
    /// </summary>
    /// <param name="text">Text to chunk</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Text chunks</returns>
    public async Task<IEnumerable<TextChunk>> ChunkTextAsync(
        string text, 
        ChunkingConfiguration configuration, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Chunking text with strategy {Strategy}. Text length: {Length}", 
            configuration.Strategy, text.Length);

        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return Enumerable.Empty<TextChunk>();

            var chunks = configuration.Strategy switch
            {
                ChunkingStrategy.FixedSize => ChunkByFixedSize(text, configuration),
                ChunkingStrategy.Semantic => await ChunkBySemanticAsync(text, configuration, cancellationToken),
                ChunkingStrategy.Sentence => ChunkBySentence(text, configuration),
                ChunkingStrategy.Paragraph => ChunkByParagraph(text, configuration),
                ChunkingStrategy.Section => ChunkBySection(text, configuration),
                ChunkingStrategy.SlidingWindow => ChunkBySlidingWindow(text, configuration),
                ChunkingStrategy.Recursive => ChunkByRecursive(text, configuration),
                _ => ChunkByFixedSize(text, configuration)
            };

            var chunkList = chunks.ToList();
            _logger.LogInformation("Created {ChunkCount} chunks using {Strategy} strategy", 
                chunkList.Count, configuration.Strategy);

            return chunkList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error chunking text with strategy {Strategy}", configuration.Strategy);
            throw;
        }
    }

    /// <summary>
    /// Estimates the number of chunks
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <returns>Estimated chunk count</returns>
    public int EstimateChunkCount(string text, ChunkingConfiguration configuration)
    {
        if (string.IsNullOrWhiteSpace(text))
            return 0;

        return configuration.Strategy switch
        {
            ChunkingStrategy.FixedSize => EstimateFixedSizeChunks(text, configuration),
            ChunkingStrategy.Semantic => EstimateSemanticChunks(text, configuration),
            ChunkingStrategy.Sentence => EstimateSentenceChunks(text, configuration),
            ChunkingStrategy.Paragraph => EstimateParagraphChunks(text, configuration),
            ChunkingStrategy.Section => EstimateSectionChunks(text, configuration),
            ChunkingStrategy.SlidingWindow => EstimateSlidingWindowChunks(text, configuration),
            ChunkingStrategy.Recursive => EstimateRecursiveChunks(text, configuration),
            _ => EstimateFixedSizeChunks(text, configuration)
        };
    }

    private static IEnumerable<TextChunk> ChunkByFixedSize(string text, ChunkingConfiguration configuration)
    {
        var chunks = new List<TextChunk>();
        var position = 0;
        var chunkIndex = 0;

        while (position < text.Length)
        {
            var chunkSize = Math.Min(configuration.MaxChunkSize, text.Length - position);
            var chunkText = text.Substring(position, chunkSize);

            // Adjust chunk boundaries if preserving sentences
            if (configuration.PreserveSentences && chunkSize == configuration.MaxChunkSize)
            {
                var lastSentenceEnd = FindLastSentenceEnd(chunkText);
                if (lastSentenceEnd > configuration.MinChunkSize)
                {
                    chunkText = chunkText.Substring(0, lastSentenceEnd);
                    chunkSize = lastSentenceEnd;
                }
            }

            chunks.Add(new TextChunk
            {
                Text = chunkText.Trim(),
                StartPosition = position,
                EndPosition = position + chunkSize,
                Type = DetermineChunkType(chunkText),
                Metadata = new Dictionary<string, object>
                {
                    ["chunkIndex"] = chunkIndex,
                    ["strategy"] = ChunkingStrategy.FixedSize.ToString()
                }
            });

            position += chunkSize - configuration.OverlapSize;
            chunkIndex++;
        }

        return chunks;
    }

    private static async Task<IEnumerable<TextChunk>> ChunkBySemanticAsync(
        string text, 
        ChunkingConfiguration configuration, 
        CancellationToken cancellationToken)
    {
        // For now, use sentence-based chunking as a semantic approximation
        // In a real implementation, you would use NLP libraries or AI models
        var sentences = SplitIntoSentences(text);
        var chunks = new List<TextChunk>();
        var currentChunk = new List<string>();
        var currentLength = 0;
        var position = 0;
        var chunkIndex = 0;

        foreach (var sentence in sentences)
        {
            if (currentLength + sentence.Length > configuration.MaxChunkSize && currentChunk.Any())
            {
                // Create chunk from current sentences
                var chunkText = string.Join(" ", currentChunk);
                chunks.Add(new TextChunk
                {
                    Text = chunkText.Trim(),
                    StartPosition = position - currentLength,
                    EndPosition = position,
                    Type = DetermineChunkType(chunkText),
                    Metadata = new Dictionary<string, object>
                    {
                        ["chunkIndex"] = chunkIndex,
                        ["strategy"] = ChunkingStrategy.Semantic.ToString(),
                        ["sentenceCount"] = currentChunk.Count
                    }
                });

                // Start new chunk with overlap
                if (configuration.OverlapSize > 0 && currentChunk.Count > 1)
                {
                    var overlapSentences = currentChunk.TakeLast(1).ToList();
                    currentChunk = overlapSentences;
                    currentLength = overlapSentences.Sum(s => s.Length);
                }
                else
                {
                    currentChunk.Clear();
                    currentLength = 0;
                }

                chunkIndex++;
            }

            currentChunk.Add(sentence);
            currentLength += sentence.Length;
            position += sentence.Length;
        }

        // Add final chunk
        if (currentChunk.Any())
        {
            var chunkText = string.Join(" ", currentChunk);
            chunks.Add(new TextChunk
            {
                Text = chunkText.Trim(),
                StartPosition = position - currentLength,
                EndPosition = position,
                Type = DetermineChunkType(chunkText),
                Metadata = new Dictionary<string, object>
                {
                    ["chunkIndex"] = chunkIndex,
                    ["strategy"] = ChunkingStrategy.Semantic.ToString(),
                    ["sentenceCount"] = currentChunk.Count
                }
            });
        }

        return chunks;
    }

    private static IEnumerable<TextChunk> ChunkBySentence(string text, ChunkingConfiguration configuration)
    {
        var sentences = SplitIntoSentences(text);
        var chunks = new List<TextChunk>();
        var position = 0;

        for (int i = 0; i < sentences.Count; i++)
        {
            var sentence = sentences[i];
            chunks.Add(new TextChunk
            {
                Text = sentence.Trim(),
                StartPosition = position,
                EndPosition = position + sentence.Length,
                Type = ChunkType.Paragraph, // Sentences are typically paragraph content
                Metadata = new Dictionary<string, object>
                {
                    ["chunkIndex"] = i,
                    ["strategy"] = ChunkingStrategy.Sentence.ToString(),
                    ["sentenceIndex"] = i
                }
            });

            position += sentence.Length;
        }

        return chunks;
    }

    private static IEnumerable<TextChunk> ChunkByParagraph(string text, ChunkingConfiguration configuration)
    {
        var paragraphs = text.Split(new[] { "\n\n", "\r\n\r\n" }, StringSplitOptions.RemoveEmptyEntries);
        var chunks = new List<TextChunk>();
        var position = 0;

        for (int i = 0; i < paragraphs.Length; i++)
        {
            var paragraph = paragraphs[i].Trim();
            if (string.IsNullOrWhiteSpace(paragraph))
                continue;

            chunks.Add(new TextChunk
            {
                Text = paragraph,
                StartPosition = position,
                EndPosition = position + paragraph.Length,
                Type = ChunkType.Paragraph,
                Metadata = new Dictionary<string, object>
                {
                    ["chunkIndex"] = i,
                    ["strategy"] = ChunkingStrategy.Paragraph.ToString(),
                    ["paragraphIndex"] = i
                }
            });

            position += paragraph.Length + 2; // Account for paragraph separator
        }

        return chunks;
    }

    private static IEnumerable<TextChunk> ChunkBySection(string text, ChunkingConfiguration configuration)
    {
        // Look for section headers (lines that start with numbers, letters, or common section indicators)
        var lines = text.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
        var chunks = new List<TextChunk>();
        var currentSection = new List<string>();
        var position = 0;
        var chunkIndex = 0;

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            
            // Check if this line looks like a section header
            if (IsSectionHeader(trimmedLine) && currentSection.Any())
            {
                // Create chunk from current section
                var sectionText = string.Join("\n", currentSection);
                chunks.Add(new TextChunk
                {
                    Text = sectionText.Trim(),
                    StartPosition = position - sectionText.Length,
                    EndPosition = position,
                    Type = DetermineChunkType(sectionText),
                    Metadata = new Dictionary<string, object>
                    {
                        ["chunkIndex"] = chunkIndex,
                        ["strategy"] = ChunkingStrategy.Section.ToString(),
                        ["lineCount"] = currentSection.Count
                    }
                });

                currentSection.Clear();
                chunkIndex++;
            }

            currentSection.Add(line);
            position += line.Length + 1; // +1 for newline
        }

        // Add final section
        if (currentSection.Any())
        {
            var sectionText = string.Join("\n", currentSection);
            chunks.Add(new TextChunk
            {
                Text = sectionText.Trim(),
                StartPosition = position - sectionText.Length,
                EndPosition = position,
                Type = DetermineChunkType(sectionText),
                Metadata = new Dictionary<string, object>
                {
                    ["chunkIndex"] = chunkIndex,
                    ["strategy"] = ChunkingStrategy.Section.ToString(),
                    ["lineCount"] = currentSection.Count
                }
            });
        }

        return chunks;
    }

    private static IEnumerable<TextChunk> ChunkBySlidingWindow(string text, ChunkingConfiguration configuration)
    {
        var chunks = new List<TextChunk>();
        var windowSize = configuration.MaxChunkSize;
        var stepSize = windowSize - configuration.OverlapSize;
        var position = 0;
        var chunkIndex = 0;

        while (position < text.Length)
        {
            var chunkSize = Math.Min(windowSize, text.Length - position);
            var chunkText = text.Substring(position, chunkSize);

            chunks.Add(new TextChunk
            {
                Text = chunkText.Trim(),
                StartPosition = position,
                EndPosition = position + chunkSize,
                Type = DetermineChunkType(chunkText),
                Metadata = new Dictionary<string, object>
                {
                    ["chunkIndex"] = chunkIndex,
                    ["strategy"] = ChunkingStrategy.SlidingWindow.ToString(),
                    ["windowSize"] = windowSize,
                    ["stepSize"] = stepSize
                }
            });

            position += stepSize;
            chunkIndex++;

            if (position >= text.Length)
                break;
        }

        return chunks;
    }

    private static IEnumerable<TextChunk> ChunkByRecursive(string text, ChunkingConfiguration configuration)
    {
        // Recursive chunking: try paragraph first, then sentence, then fixed size
        var chunks = new List<TextChunk>();
        
        // First, try to chunk by paragraphs
        var paragraphs = text.Split(new[] { "\n\n", "\r\n\r\n" }, StringSplitOptions.RemoveEmptyEntries);
        var position = 0;
        var chunkIndex = 0;

        foreach (var paragraph in paragraphs)
        {
            if (paragraph.Length <= configuration.MaxChunkSize)
            {
                // Paragraph fits in one chunk
                chunks.Add(new TextChunk
                {
                    Text = paragraph.Trim(),
                    StartPosition = position,
                    EndPosition = position + paragraph.Length,
                    Type = ChunkType.Paragraph,
                    Metadata = new Dictionary<string, object>
                    {
                        ["chunkIndex"] = chunkIndex,
                        ["strategy"] = ChunkingStrategy.Recursive.ToString(),
                        ["level"] = "paragraph"
                    }
                });
            }
            else
            {
                // Paragraph too large, chunk by sentences
                var sentences = SplitIntoSentences(paragraph);
                var currentChunk = new List<string>();
                var currentLength = 0;

                foreach (var sentence in sentences)
                {
                    if (currentLength + sentence.Length > configuration.MaxChunkSize && currentChunk.Any())
                    {
                        var chunkText = string.Join(" ", currentChunk);
                        chunks.Add(new TextChunk
                        {
                            Text = chunkText.Trim(),
                            StartPosition = position,
                            EndPosition = position + chunkText.Length,
                            Type = ChunkType.Paragraph,
                            Metadata = new Dictionary<string, object>
                            {
                                ["chunkIndex"] = chunkIndex,
                                ["strategy"] = ChunkingStrategy.Recursive.ToString(),
                                ["level"] = "sentence"
                            }
                        });

                        currentChunk.Clear();
                        currentLength = 0;
                        chunkIndex++;
                    }

                    currentChunk.Add(sentence);
                    currentLength += sentence.Length;
                }

                if (currentChunk.Any())
                {
                    var chunkText = string.Join(" ", currentChunk);
                    chunks.Add(new TextChunk
                    {
                        Text = chunkText.Trim(),
                        StartPosition = position,
                        EndPosition = position + chunkText.Length,
                        Type = ChunkType.Paragraph,
                        Metadata = new Dictionary<string, object>
                        {
                            ["chunkIndex"] = chunkIndex,
                            ["strategy"] = ChunkingStrategy.Recursive.ToString(),
                            ["level"] = "sentence"
                        }
                    });
                }
            }

            position += paragraph.Length + 2; // Account for paragraph separator
            chunkIndex++;
        }

        return chunks;
    }

    private static List<string> SplitIntoSentences(string text)
    {
        // Simple sentence splitting (in a real implementation, use NLP libraries)
        var sentences = Regex.Split(text, @"(?<=[.!?])\s+")
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .ToList();

        return sentences;
    }

    private static bool IsSectionHeader(string line)
    {
        if (string.IsNullOrWhiteSpace(line))
            return false;

        // Check for common section header patterns
        var patterns = new[]
        {
            @"^\d+\.", // "1.", "2.", etc.
            @"^[A-Z]\.", // "A.", "B.", etc.
            @"^(Article|Section|Chapter|Part)\s+\d+", // "Article 1", "Section 2", etc.
            @"^[A-Z][A-Z\s]+$", // ALL CAPS headers
            @"^\d+\.\d+", // "1.1", "2.3", etc.
        };

        return patterns.Any(pattern => Regex.IsMatch(line, pattern));
    }

    private static ChunkType DetermineChunkType(string text)
    {
        var trimmedText = text.Trim();
        
        if (string.IsNullOrWhiteSpace(trimmedText))
            return ChunkType.Other;

        // Check for title patterns
        if (trimmedText.Length < 100 && trimmedText.All(c => char.IsUpper(c) || char.IsWhiteSpace(c) || char.IsPunctuation(c)))
            return ChunkType.Title;

        // Check for header patterns
        if (IsSectionHeader(trimmedText))
            return ChunkType.Header;

        // Check for list patterns
        if (Regex.IsMatch(trimmedText, @"^\s*[-*•]\s") || Regex.IsMatch(trimmedText, @"^\s*\d+\.\s"))
            return ChunkType.List;

        // Check for table patterns (simple heuristic)
        if (trimmedText.Contains("|") && trimmedText.Split('|').Length > 3)
            return ChunkType.Table;

        // Default to paragraph
        return ChunkType.Paragraph;
    }

    private static int FindLastSentenceEnd(string text)
    {
        var sentenceEnders = new[] { '.', '!', '?' };
        
        for (int i = text.Length - 1; i >= 0; i--)
        {
            if (sentenceEnders.Contains(text[i]))
            {
                return i + 1;
            }
        }

        return text.Length;
    }

    private static int EstimateFixedSizeChunks(string text, ChunkingConfiguration configuration)
    {
        var effectiveChunkSize = configuration.MaxChunkSize - configuration.OverlapSize;
        return (int)Math.Ceiling((double)text.Length / effectiveChunkSize);
    }

    private static int EstimateSemanticChunks(string text, ChunkingConfiguration configuration)
    {
        var sentences = SplitIntoSentences(text);
        var avgSentenceLength = sentences.Any() ? sentences.Average(s => s.Length) : 100;
        var sentencesPerChunk = configuration.MaxChunkSize / avgSentenceLength;
        return (int)Math.Ceiling(sentences.Count / sentencesPerChunk);
    }

    private static int EstimateSentenceChunks(string text, ChunkingConfiguration configuration)
    {
        return SplitIntoSentences(text).Count;
    }

    private static int EstimateParagraphChunks(string text, ChunkingConfiguration configuration)
    {
        return text.Split(new[] { "\n\n", "\r\n\r\n" }, StringSplitOptions.RemoveEmptyEntries).Length;
    }

    private static int EstimateSectionChunks(string text, ChunkingConfiguration configuration)
    {
        var lines = text.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
        return lines.Count(line => IsSectionHeader(line.Trim())) + 1;
    }

    private static int EstimateSlidingWindowChunks(string text, ChunkingConfiguration configuration)
    {
        var stepSize = configuration.MaxChunkSize - configuration.OverlapSize;
        return (int)Math.Ceiling((double)text.Length / stepSize);
    }

    private static int EstimateRecursiveChunks(string text, ChunkingConfiguration configuration)
    {
        // Estimate based on paragraph chunking as baseline
        return EstimateParagraphChunks(text, configuration);
    }
}
