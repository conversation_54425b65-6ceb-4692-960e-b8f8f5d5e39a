[{"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchAnalytics", "RelativePath": "api/Search/analytics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Search/analyze", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "ProvideFeedback", "RelativePath": "api/Search/feedback/{queryId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "queryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "feedback", "Type": "LexAI.LegalResearch.Application.DTOs.UserFeedbackDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchHistory", "RelativePath": "api/Search/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "offset", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "HybridSearch", "RelativePath": "api/Search/hybrid-search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "Search", "RelativePath": "api/Search/perform", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "Search", "RelativePath": "api/Search/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "FindSimilarDocuments", "RelativePath": "api/Search/similar/{documentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[LexAI.LegalResearch.Application.DTOs.SearchResultDto, LexAI.LegalResearch.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchSuggestions", "RelativePath": "api/Search/suggestions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "TestSearch", "RelativePath": "api/Search/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchAnalytics", "RelativePath": "api/v1/search/analytics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/v1/search/analyze", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "ProvideFeedback", "RelativePath": "api/v1/search/feedback/{queryId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "queryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "feedback", "Type": "LexAI.LegalResearch.Application.DTOs.UserFeedbackDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchHistory", "RelativePath": "api/v1/search/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "offset", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "HybridSearch", "RelativePath": "api/v1/search/hybrid-search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "Search", "RelativePath": "api/v1/search/perform", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "Search", "RelativePath": "api/v1/search/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "FindSimilarDocuments", "RelativePath": "api/v1/search/similar/{documentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[LexAI.LegalResearch.Application.DTOs.SearchResultDto, LexAI.LegalResearch.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchSuggestions", "RelativePath": "api/v1/search/suggestions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "TestSearch", "RelativePath": "api/v1/search/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}]