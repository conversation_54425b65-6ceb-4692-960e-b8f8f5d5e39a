[{"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchAnalytics", "RelativePath": "api/Search/analytics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sessionId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchAnalyticsDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Search/analyze", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.QueryAnalysisDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "ProvideFeedback", "RelativePath": "api/Search/feedback/{queryId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "queryId", "Type": "System.Guid", "IsRequired": true}, {"Name": "feedback", "Type": "LexAI.LegalResearch.Application.DTOs.UserFeedbackDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "HybridSearch", "RelativePath": "api/Search/hybrid-search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "Search", "RelativePath": "api/Search/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 429}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "FindSimilarDocuments", "RelativePath": "api/Search/similar/{documentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[LexAI.LegalResearch.Application.DTOs.SearchResultDto, LexAI.LegalResearch.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "GetSearchSuggestions", "RelativePath": "api/Search/suggestions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "LexAI.LegalResearch.API.Controllers.SearchController", "Method": "TestSearch", "RelativePath": "api/Search/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LexAI.LegalResearch.Application.DTOs.SearchRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "LexAI.LegalResearch.Application.DTOs.SearchResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}]