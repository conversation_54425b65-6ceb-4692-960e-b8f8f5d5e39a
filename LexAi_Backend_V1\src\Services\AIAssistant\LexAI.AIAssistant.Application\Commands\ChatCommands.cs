using LexAI.AIAssistant.Application.DTOs;
using LexAI.AIAssistant.Application.Interfaces;
using LexAI.AIAssistant.Domain.Entities;
using LexAI.AIAssistant.Domain.ValueObjects;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;
using FluentValidation;
using LexAI.Shared.Application.DTOs;

namespace LexAI.AIAssistant.Application.Commands;

/// <summary>
/// Command to send a message to the AI assistant
/// </summary>
public class SendMessageCommand : IRequest<ChatResponseDto>
{
    /// <summary>
    /// Chat request
    /// </summary>
    public ChatRequestDto Request { get; set; } = null!;
}

/// <summary>
/// Handler for SendMessageCommand
/// </summary>
public class SendMessageCommandHandler : IRequestHandler<SendMessageCommand, ChatResponseDto>
{
    private readonly IAIAssistantService _aiAssistantService;
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<SendMessageCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the SendMessageCommandHandler
    /// </summary>
    /// <param name="aiAssistantService">AI assistant service</param>
    /// <param name="conversationRepository">Conversation repository</param>
    /// <param name="logger">Logger</param>
    public SendMessageCommandHandler(
        IAIAssistantService aiAssistantService,
        IConversationRepository conversationRepository,
        ILogger<SendMessageCommandHandler> logger)
    {
        _aiAssistantService = aiAssistantService;
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the SendMessageCommand
    /// </summary>
    /// <param name="request">Send message command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chat response</returns>
    public async Task<ChatResponseDto> Handle(SendMessageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing message from user {UserId}: {Message}",
            request.Request.UserId, request.Request.Message);

        try
        {
            // Basic content validation
            if (string.IsNullOrWhiteSpace(request.Request.Message))
            {
                throw new ValidationException("Message cannot be empty.");
            }

            if (request.Request.Message.Length > 10000)
            {
                throw new ValidationException("Message is too long. Maximum length is 10,000 characters.");
            }

            // Get or create conversation
            Conversation conversation;
            if (request.Request.ConversationId.HasValue)
            {
                conversation = await _conversationRepository.GetByIdAsync(
                    request.Request.ConversationId.Value, cancellationToken);

                if (conversation == null)
                {
                    throw new EntityNotFoundException("Conversation", request.Request.ConversationId.Value);
                }

                if (conversation.UserId != request.Request.UserId)
                {
                    throw new UnauthorizedAccessException("User does not have access to this conversation");
                }
            }
            else
            {
                // Create new conversation
                var context = request.Request.Context != null
                    ? MapToConversationContext(request.Request.Context)
                    : ConversationContext.CreateDefault();

                conversation = Conversation.Create(
                    "New Conversation",
                    request.Request.UserId,
                    request.Request.SessionId,
                    context);

                await _conversationRepository.AddAsync(conversation, cancellationToken);
            }

            // Create user message
            var userMessage = Message.CreateUserMessage(
                conversation.Id,
                request.Request.Message,
                request.Request.UserId,
                MessageType.Question);

            // Add attachments if any
            foreach (var attachment in request.Request.Attachments)
            {
                var messageAttachment = MapToMessageAttachment(attachment);
                userMessage.AddAttachment(messageAttachment);
            }

            conversation.AddMessage(userMessage);

            // Process message and generate AI response
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            var aiResponse = await _aiAssistantService.SendMessageAsync(request.Request, cancellationToken);

            stopwatch.Stop();

            // Create AI message
            var aiMessage = Message.CreateAssistantMessage(
                conversation.Id,
                aiResponse.Response,
                aiResponse.ResponseType,
                stopwatch.Elapsed,
                aiResponse.TokensUsed,
                aiResponse.EstimatedCost);

            // Set AI message properties
            aiMessage.SetDetectedClassification(
                aiResponse.DetectedDomain,
                aiResponse.DetectedIntent,
                aiResponse.ConfidenceScore);

            // Add citations
            if (aiResponse.Citations.Any())
            {
                var citations = aiResponse.Citations.Select(MapToCitation);
                aiMessage.AddCitations(citations);
            }

            conversation.AddMessage(aiMessage);

            // Save conversation using the robust SaveAsync method
            await _conversationRepository.SaveAsync(conversation, cancellationToken);

            _logger.LogInformation("Message processed successfully for user {UserId}. " +
                "Conversation: {ConversationId}, Tokens: {TokensUsed}, Cost: {Cost}",
                request.Request.UserId, conversation.Id, aiResponse.TokensUsed, aiResponse.EstimatedCost);

            // Set conversation ID in response
            aiResponse.ConversationId = conversation.Id;
            aiResponse.MessageId = aiMessage.Id;
            aiResponse.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return aiResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message for user {UserId}", request.Request.UserId);
            throw;
        }
    }

    private static ConversationContext MapToConversationContext(ConversationContextDto dto)
    {
        return ConversationContext.Create(
            dto.ModelType,
            dto.Mode,
            dto.SystemPrompt,
            dto.MaxTokens,
            dto.Temperature,
            dto.IncludeLegalResearch,
            dto.IncludeCitations,
            dto.Language,
            dto.Jurisdiction,
            dto.UserRole);
    }

    private static MessageAttachment MapToMessageAttachment(MessageAttachmentDto dto)
    {
        if (!string.IsNullOrWhiteSpace(dto.Content))
        {
            return MessageAttachment.CreateWithContent(
                dto.FileName,
                dto.Content,
                dto.MimeType,
                "user");
        }

        return MessageAttachment.Create(
            dto.FileName,
            dto.FileSize,
            dto.MimeType,
            dto.Url,
            "user");
    }

    private static Citation MapToCitation(CitationDto dto)
    {
        var citation = Citation.Create(
            dto.Type,
            dto.Title,
            dto.Url,
            dto.Source,
            dto.RelevanceScore);

        if (dto.PublicationDate.HasValue)
        {
            citation.SetPublicationInfo(dto.PublicationDate);
        }

        if (!string.IsNullOrWhiteSpace(dto.Excerpt))
        {
            citation.SetExcerpt(dto.Excerpt);
        }

        return citation;
    }
}

/// <summary>
/// Command to continue an existing conversation
/// </summary>
public class ContinueConversationCommand : IRequest<ChatResponseDto>
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid ConversationId { get; set; }

    /// <summary>
    /// User message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
/// Handler for ContinueConversationCommand
/// </summary>
public class ContinueConversationCommandHandler : IRequestHandler<ContinueConversationCommand, ChatResponseDto>
{
    private readonly IMediator _mediator;

    /// <summary>
    /// Initializes a new instance of the ContinueConversationCommandHandler
    /// </summary>
    /// <param name="mediator">MediatR mediator</param>
    public ContinueConversationCommandHandler(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Handles the ContinueConversationCommand
    /// </summary>
    /// <param name="request">Continue conversation command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chat response</returns>
    public async Task<ChatResponseDto> Handle(ContinueConversationCommand request, CancellationToken cancellationToken)
    {
        var chatRequest = new ChatRequestDto
        {
            Message = request.Message,
            UserId = request.UserId,
            ConversationId = request.ConversationId,
            SessionId = request.ConversationId.ToString() // Use conversation ID as session ID
        };

        var sendMessageCommand = new SendMessageCommand { Request = chatRequest };
        return await _mediator.Send(sendMessageCommand, cancellationToken);
    }
}

/// <summary>
/// Command to rate a message
/// </summary>
public class RateMessageCommand : IRequest<bool>
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid MessageId { get; set; }

    /// <summary>
    /// User rating (1-5)
    /// </summary>
    public int Rating { get; set; }

    /// <summary>
    /// Optional feedback
    /// </summary>
    public string? Feedback { get; set; }

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }
}

/// <summary>
/// Handler for RateMessageCommand
/// </summary>
public class RateMessageCommandHandler : IRequestHandler<RateMessageCommand, bool>
{
    private readonly IConversationRepository _conversationRepository;
    private readonly ILogger<RateMessageCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the RateMessageCommandHandler
    /// </summary>
    /// <param name="conversationRepository">Conversation repository</param>
    /// <param name="logger">Logger</param>
    public RateMessageCommandHandler(
        IConversationRepository conversationRepository,
        ILogger<RateMessageCommandHandler> logger)
    {
        _conversationRepository = conversationRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the RateMessageCommand
    /// </summary>
    /// <param name="request">Rate message command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if rating was successful</returns>
    public async Task<bool> Handle(RateMessageCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Rating message {MessageId} by user {UserId} with rating {Rating}",
            request.MessageId, request.UserId, request.Rating);

        try
        {
            var conversation = await _conversationRepository.GetByMessageIdAsync(request.MessageId, cancellationToken);
            if (conversation == null)
            {
                _logger.LogWarning("Conversation not found for message {MessageId}", request.MessageId);
                return false;
            }

            if (conversation.UserId != request.UserId)
            {
                _logger.LogWarning("User {UserId} does not have access to message {MessageId}",
                    request.UserId, request.MessageId);
                return false;
            }

            var message = conversation.Messages.FirstOrDefault(m => m.Id == request.MessageId);
            if (message == null)
            {
                _logger.LogWarning("Message {MessageId} not found in conversation {ConversationId}",
                    request.MessageId, conversation.Id);
                return false;
            }

            if (!message.IsAssistantMessage())
            {
                _logger.LogWarning("Cannot rate non-assistant message {MessageId}", request.MessageId);
                return false;
            }

            message.SetUserRating(request.Rating, request.Feedback, request.UserId.ToString());
            await _conversationRepository.UpdateAsync(conversation, cancellationToken);

            _logger.LogInformation("Message {MessageId} rated successfully with rating {Rating}",
                request.MessageId, request.Rating);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rating message {MessageId}", request.MessageId);
            throw;
        }
    }
}

/// <summary>
/// Repository interface for conversations
/// </summary>
public interface IConversationRepository
{
    /// <summary>
    /// Gets a conversation by ID
    /// </summary>
    /// <param name="id">Conversation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation</returns>
    Task<Conversation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a conversation by message ID
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation</returns>
    Task<Conversation?> GetByMessageIdAsync(Guid messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a new conversation
    /// </summary>
    /// <param name="conversation">Conversation to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task AddAsync(Conversation conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates a conversation
    /// </summary>
    /// <param name="conversation">Conversation to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task UpdateAsync(Conversation conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves a conversation (add or update)
    /// </summary>
    /// <param name="conversation">Conversation to save</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Saved conversation</returns>
    Task<Conversation> SaveAsync(Conversation conversation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves changes to the database context
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets conversations for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="limit">Maximum number of conversations</param>
    /// <param name="offset">Offset for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User conversations</returns>
    Task<IEnumerable<Conversation>> GetByUserIdAsync(
        Guid userId,
        int limit = 20,
        int offset = 0,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a conversation
    /// </summary>
    /// <param name="id">Conversation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}
