using MediatR;

namespace LexAI.Identity.Application.Commands;

/// <summary>
/// Command for changing a user's password
/// </summary>
public class ChangePasswordCommand : IRequest<bool>
{
    /// <summary>
    /// The unique identifier of the user whose password is being changed
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// The current password of the user
    /// </summary>
    public string CurrentPassword { get; set; }

    /// <summary>
    /// The new password to set for the user
    /// </summary>
    public string NewPassword { get; set; }

    /// <summary>
    /// The identifier of the entity or person who initiated the password change
    /// </summary>
    public string ChangedBy { get; set; }
}
