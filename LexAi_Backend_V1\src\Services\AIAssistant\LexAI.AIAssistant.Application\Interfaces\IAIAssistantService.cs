using LexAI.AIAssistant.Application.DTOs;
using LexAI.AIAssistant.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;

namespace LexAI.AIAssistant.Application.Interfaces;

/// <summary>
/// Interface for AI assistant service
/// </summary>
public interface IAIAssistantService
{
    /// <summary>
    /// Sends a message to the AI assistant and gets a response
    /// </summary>
    /// <param name="request">Chat request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AI response</returns>
    Task<ChatResponseDto> SendMessageAsync(ChatRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Continues an existing conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="message">User message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AI response</returns>
    Task<ChatResponseDto> ContinueConversationAsync(Guid conversationId, string message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes a legal document
    /// </summary>
    /// <param name="request">Document analysis request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document analysis response</returns>
    Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(DocumentAnalysisRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs legal research and provides insights
    /// </summary>
    /// <param name="request">Research request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Research response</returns>
    Task<LegalResearchResponseDto> PerformLegalResearchAsync(LegalResearchRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a legal document based on user requirements
    /// </summary>
    /// <param name="request">Document generation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated document</returns>
    Task<DocumentGenerationResponseDto> GenerateDocumentAsync(DocumentGenerationRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Summarizes a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation summary</returns>
    Task<ConversationSummaryDto> SummarizeConversationAsync(Guid conversationId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for AI model service
/// </summary>
public interface IAIModelService
{
    /// <summary>
    /// Generates a response using the specified AI model
    /// </summary>
    /// <param name="request">Model request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Model response</returns>
    Task<AIModelResponseDto> GenerateResponseAsync(AIModelRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates embeddings for text
    /// </summary>
    /// <param name="text">Text to embed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vector</returns>
    Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Estimates the cost of a request
    /// </summary>
    /// <param name="modelType">AI model type</param>
    /// <param name="inputTokens">Input tokens</param>
    /// <param name="outputTokens">Output tokens</param>
    /// <returns>Estimated cost</returns>
    decimal EstimateCost(AIModelType modelType, int inputTokens, int outputTokens);

    /// <summary>
    /// Gets the maximum context length for a model
    /// </summary>
    /// <param name="modelType">AI model type</param>
    /// <returns>Maximum context length</returns>
    int GetMaxContextLength(AIModelType modelType);
}

/// <summary>
/// Interface pour l'int�gration avec le service de recherche juridique
/// </summary>
public interface ILegalResearchIntegrationService
{
    /// <summary>
    /// Searches for relevant legal documents
    /// </summary>
    /// <param name="query">Search query</param>
    /// <param name="domain">Legal domain filter</param>
    /// <param name="limit">Maximum number of results</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search results</returns>
    Task<IEnumerable<LegalDocumentSummaryDto>> SearchLegalDocumentsAsync(
        string query, 
        LegalDomain? domain = null, 
        int limit = 10, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets detailed information about a legal document
    /// </summary>
    /// <param name="documentId">Document ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Document details</returns>
    Task<LegalDocumentDetailsDto?> GetLegalDocumentAsync(Guid documentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finds similar legal documents
    /// </summary>
    /// <param name="documentId">Reference document ID</param>
    /// <param name="limit">Maximum number of results</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar documents</returns>
    Task<IEnumerable<LegalDocumentSummaryDto>> FindSimilarDocumentsAsync(
        Guid documentId, 
        int limit = 5, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Recherche par citation
    /// </summary>
    Task<IEnumerable<LegalDocumentSummaryDto>> SearchByCitationAsync(
        string citation,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for conversation management service
/// </summary>
public interface IConversationService
{
    /// <summary>
    /// Creates a new conversation
    /// </summary>
    /// <param name="request">Conversation creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created conversation</returns>
    Task<ConversationDto> CreateConversationAsync(CreateConversationRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a conversation by ID
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation details</returns>
    Task<ConversationDto?> GetConversationAsync(Guid conversationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets conversations for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="limit">Maximum number of conversations</param>
    /// <param name="offset">Offset for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User conversations</returns>
    Task<IEnumerable<ConversationSummaryDto>> GetUserConversationsAsync(
        Guid userId, 
        int limit = 20, 
        int offset = 0, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="request">Update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated conversation</returns>
    Task<ConversationDto> UpdateConversationAsync(
        Guid conversationId, 
        UpdateConversationRequestDto request, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task DeleteConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Archives a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task ArchiveConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the total count of conversations for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total conversation count</returns>
    Task<int> GetUserConversationCountAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a conversation by ID with user verification
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Conversation details</returns>
    Task<ConversationDto?> GetConversationAsync(Guid conversationId, Guid userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for message processing service
/// </summary>
public interface IMessageProcessingService
{
    /// <summary>
    /// Processes a user message and generates AI response
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="userMessage">User message</param>
    /// <param name="context">Conversation context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AI response message</returns>
    Task<MessageDto> ProcessMessageAsync(
        Guid conversationId, 
        string userMessage, 
        ConversationContextDto context, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes message intent and extracts entities
    /// </summary>
    /// <param name="message">Message to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message analysis</returns>
    Task<MessageAnalysisDto> AnalyzeMessageAsync(string message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates follow-up questions based on conversation context
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Follow-up questions</returns>
    Task<IEnumerable<string>> GenerateFollowUpQuestionsAsync(Guid conversationId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for content moderation service
/// </summary>
public interface IContentModerationService
{
    /// <summary>
    /// Checks if content is appropriate and safe
    /// </summary>
    /// <param name="content">Content to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Moderation result</returns>
    Task<ContentModerationResultDto> ModerateContentAsync(string content, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a message contains sensitive legal information
    /// </summary>
    /// <param name="message">Message to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if message contains sensitive information</returns>
    Task<bool> ContainsSensitiveInformationAsync(string message, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sanitizes content by removing or masking sensitive information
    /// </summary>
    /// <param name="content">Content to sanitize</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Sanitized content</returns>
    Task<string> SanitizeContentAsync(string content, CancellationToken cancellationToken = default);
}
