// Types pour le service Identity

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  user: User
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  profile?: string
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  profile: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  newPassword: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface UpdateUserRequest {
  firstName?: string
  lastName?: string
  email?: string
  profile?: string
}
