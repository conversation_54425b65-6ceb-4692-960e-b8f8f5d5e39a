2025-06-12 19:49:17.150 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-12 19:49:17.210 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-12 19:49:17.220 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-12 19:49:18.308 +04:00 [INF] Executed DbCommand (119ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-12 19:49:18.350 +04:00 [INF] LexAI Identity Service started successfully
2025-06-12 19:49:18.452 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-12 19:49:19.018 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-12 19:49:19.020 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-12 19:49:19.404 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-12 19:49:19.407 +04:00 [INF] Hosting environment: Development
2025-06-12 19:49:19.409 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-12 19:49:19.841 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-12 19:49:20.135 +04:00 [INF] Request GET / started with correlation ID 574daa1c-a25c-49b9-a836-43f3bfdf4067
2025-06-12 19:49:21.760 +04:00 [INF] Request GET / completed in 1620ms with status 404 (Correlation ID: 574daa1c-a25c-49b9-a836-43f3bfdf4067)
2025-06-12 19:49:21.777 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 1944.9817ms
2025-06-12 19:49:21.786 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-12 19:54:44.753 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-12 19:54:44.775 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID bb45166c-375a-4755-9dd8-66fffc2a7bf2
2025-06-12 19:54:44.782 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:44.787 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 7ms with status 204 (Correlation ID: bb45166c-375a-4755-9dd8-66fffc2a7bf2)
2025-06-12 19:54:44.793 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 40.8397ms
2025-06-12 19:54:44.797 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-12 19:54:44.809 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 043d8b38-6998-4a6f-96fd-a98b5d50b561
2025-06-12 19:54:44.814 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:44.820 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:54:44.874 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-12 19:54:44.988 +04:00 [INF] Token refresh attempt
2025-06-12 19:54:45.076 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-12 19:54:46.127 +04:00 [INF] Executed DbCommand (53ms) [Parameters=[@__token_0='VuDO7YKOn0nws9afSHkGXPbpe6JSfmb8/kwsJr5h2fdbDeHihepqZucu0MEuTeNII3FAvC1aBhacoPJOe+2nYw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-12 19:54:46.428 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-12 19:54:46.935 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-12 19:54:47.048 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-12 19:54:47.100 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 2212.8408ms
2025-06-12 19:54:47.108 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:54:47.112 +04:00 [INF] Request POST /api/auth/refresh completed in 2299ms with status 401 (Correlation ID: 043d8b38-6998-4a6f-96fd-a98b5d50b561)
2025-06-12 19:54:47.127 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 401 null application/json; charset=utf-8 2330.6835ms
2025-06-12 19:54:47.128 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-12 19:54:47.151 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-12 19:54:47.154 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID adadb659-dbeb-4b53-b914-9fde14356695
2025-06-12 19:54:47.167 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 5588f2a0-5072-4f9b-885d-fc6ac7f405ae
2025-06-12 19:54:47.173 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:47.176 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:47.179 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 6ms with status 204 (Correlation ID: adadb659-dbeb-4b53-b914-9fde14356695)
2025-06-12 19:54:47.182 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 6ms with status 204 (Correlation ID: 5588f2a0-5072-4f9b-885d-fc6ac7f405ae)
2025-06-12 19:54:47.191 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 63.1216ms
2025-06-12 19:54:47.199 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-12 19:54:47.208 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 56.1709ms
2025-06-12 19:54:47.214 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-12 19:54:47.239 +04:00 [INF] Request POST /api/auth/logout started with correlation ID f5e08292-2816-4109-8e92-47ef15284b00
2025-06-12 19:54:47.253 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 1560e9c7-3aae-4873-aaa0-a0b7a30defcd
2025-06-12 19:54:47.257 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:47.260 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:54:47.519 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:54:47 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:54:47.519 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:54:47 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:54:47.529 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:54:47 PM'.
2025-06-12 19:54:47.531 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:54:47 PM'.
2025-06-12 19:54:47.533 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:54:47 PM'.
2025-06-12 19:54:47.535 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:54:47 PM'.
2025-06-12 19:54:47.558 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:54:47.558 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:54:47.580 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:54:47.580 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:54:47.586 +04:00 [INF] Request POST /api/auth/logout completed in 326ms with status 401 (Correlation ID: 1560e9c7-3aae-4873-aaa0-a0b7a30defcd)
2025-06-12 19:54:47.590 +04:00 [INF] Request POST /api/auth/logout completed in 333ms with status 401 (Correlation ID: f5e08292-2816-4109-8e92-47ef15284b00)
2025-06-12 19:54:47.598 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 499 null null 384.3193ms
2025-06-12 19:54:47.604 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 499 null null 404.7604ms
2025-06-12 19:55:17.021 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-12 19:55:17.039 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 4a322365-83ad-4f2b-8057-ef26ca97dfb4
2025-06-12 19:55:17.045 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.047 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 2ms with status 204 (Correlation ID: 4a322365-83ad-4f2b-8057-ef26ca97dfb4)
2025-06-12 19:55:17.052 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 31.5261ms
2025-06-12 19:55:17.054 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-12 19:55:17.066 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID ef55cead-a292-4524-b944-8cad8a1fb2b0
2025-06-12 19:55:17.069 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.071 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:55:17.074 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-12 19:55:17.081 +04:00 [INF] Token refresh attempt
2025-06-12 19:55:17.191 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-12 19:55:17.205 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='VuDO7YKOn0nws9afSHkGXPbpe6JSfmb8/kwsJr5h2fdbDeHihepqZucu0MEuTeNII3FAvC1aBhacoPJOe+2nYw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-12 19:55:17.213 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-12 19:55:17.374 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-12 19:55:17.379 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-12 19:55:17.383 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 304.9997ms
2025-06-12 19:55:17.388 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:55:17.392 +04:00 [INF] Request POST /api/auth/refresh completed in 322ms with status 401 (Correlation ID: ef55cead-a292-4524-b944-8cad8a1fb2b0)
2025-06-12 19:55:17.396 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 401 null application/json; charset=utf-8 342.0991ms
2025-06-12 19:55:17.401 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-12 19:55:17.423 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID 07d5ec32-b14c-4e3d-a45e-ed5ba3d4017c
2025-06-12 19:55:17.430 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-12 19:55:17.430 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.444 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID fba7f8db-306b-4868-aeb5-d5eda4caf000
2025-06-12 19:55:17.447 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 16ms with status 204 (Correlation ID: 07d5ec32-b14c-4e3d-a45e-ed5ba3d4017c)
2025-06-12 19:55:17.452 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.459 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 57.5835ms
2025-06-12 19:55:17.513 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 61ms with status 204 (Correlation ID: fba7f8db-306b-4868-aeb5-d5eda4caf000)
2025-06-12 19:55:17.513 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-12 19:55:17.528 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-12 19:55:17.525 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 95.0757ms
2025-06-12 19:55:17.548 +04:00 [INF] Request POST /api/auth/logout started with correlation ID a15416a6-359a-481b-8f12-fc4e79e45409
2025-06-12 19:55:17.556 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 815f9c1f-6520-471d-8d21-b50d4b7bdd70
2025-06-12 19:55:17.570 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.575 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:17.578 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:17 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:55:17.580 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:17 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:55:17.584 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:17 PM'.
2025-06-12 19:55:17.588 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:17 PM'.
2025-06-12 19:55:17.589 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:17 PM'.
2025-06-12 19:55:17.590 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:17 PM'.
2025-06-12 19:55:17.595 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:55:17.595 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:55:17.599 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:55:17.601 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:55:17.604 +04:00 [INF] Request POST /api/auth/logout completed in 29ms with status 401 (Correlation ID: 815f9c1f-6520-471d-8d21-b50d4b7bdd70)
2025-06-12 19:55:17.606 +04:00 [INF] Request POST /api/auth/logout completed in 35ms with status 401 (Correlation ID: a15416a6-359a-481b-8f12-fc4e79e45409)
2025-06-12 19:55:17.610 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 499 null null 82.4638ms
2025-06-12 19:55:17.615 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 499 null null 102.304ms
2025-06-12 19:55:41.685 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - null null
2025-06-12 19:55:41.732 +04:00 [INF] Request OPTIONS /api/auth/logout started with correlation ID d96b9481-0632-4835-86de-9f8f591567ab
2025-06-12 19:55:41.734 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:41.736 +04:00 [INF] Request OPTIONS /api/auth/logout completed in 2ms with status 204 (Correlation ID: d96b9481-0632-4835-86de-9f8f591567ab)
2025-06-12 19:55:41.740 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/logout - 204 null null 54.7174ms
2025-06-12 19:55:41.741 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-12 19:55:41.748 +04:00 [INF] Request POST /api/auth/logout started with correlation ID e61123b6-af82-4966-9eef-f674937275ab
2025-06-12 19:55:41.751 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:41.753 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:41 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:55:41.757 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:41 PM'.
2025-06-12 19:55:41.759 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:41 PM'.
2025-06-12 19:55:41.761 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:55:41.762 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:55:41.764 +04:00 [INF] Request POST /api/auth/logout completed in 12ms with status 401 (Correlation ID: e61123b6-af82-4966-9eef-f674937275ab)
2025-06-12 19:55:41.768 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 26.9409ms
2025-06-12 19:55:41.771 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-12 19:55:41.777 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 2e3eb2ec-0f11-4044-9618-6a6e0646cc64
2025-06-12 19:55:41.779 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:41.781 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 1ms with status 204 (Correlation ID: 2e3eb2ec-0f11-4044-9618-6a6e0646cc64)
2025-06-12 19:55:41.785 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 13.4076ms
2025-06-12 19:55:41.786 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-12 19:55:41.795 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID a9699d69-aecb-4b1d-a6d2-0ffcf41e4536
2025-06-12 19:55:41.798 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:41.800 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:55:41.801 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-12 19:55:41.806 +04:00 [INF] Token refresh attempt
2025-06-12 19:55:41.814 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-12 19:55:41.821 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__token_0='VuDO7YKOn0nws9afSHkGXPbpe6JSfmb8/kwsJr5h2fdbDeHihepqZucu0MEuTeNII3FAvC1aBhacoPJOe+2nYw=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-12 19:55:41.825 +04:00 [WRN] Invalid or expired refresh token used from IP ::1
2025-06-12 19:55:41.970 +04:00 [WRN] Token refresh failed
LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException: Invalid or expired refresh token
   at LexAI.Identity.Application.Commands.RefreshTokenCommandHandler.Handle(RefreshTokenCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.Application\Commands\AuthenticationCommands.cs:line 226
   at LexAI.Identity.API.Controllers.AuthController.RefreshToken(RefreshTokenDto refreshTokenDto) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API\Controllers\AuthController.cs:line 103
2025-06-12 19:55:41.976 +04:00 [INF] Executing UnauthorizedObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-12 19:55:41.978 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 173.4224ms
2025-06-12 19:55:41.981 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:55:41.990 +04:00 [INF] Request POST /api/auth/refresh completed in 192ms with status 401 (Correlation ID: a9699d69-aecb-4b1d-a6d2-0ffcf41e4536)
2025-06-12 19:55:41.993 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 401 null application/json; charset=utf-8 207.4696ms
2025-06-12 19:55:41.996 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/logout - application/json 0
2025-06-12 19:55:42.011 +04:00 [INF] Request POST /api/auth/logout started with correlation ID 768f84be-4867-478d-bfc8-7280337d2f4c
2025-06-12 19:55:42.017 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:55:42.022 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:42 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-12 19:55:42.031 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:42 PM'.
2025-06-12 19:55:42.033 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/4/2025 5:36:35 PM', Current time (UTC): '6/12/2025 3:55:42 PM'.
2025-06-12 19:55:42.036 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-12 19:55:42.038 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-12 19:55:42.041 +04:00 [INF] Request POST /api/auth/logout completed in 24ms with status 401 (Correlation ID: 768f84be-4867-478d-bfc8-7280337d2f4c)
2025-06-12 19:55:42.048 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/logout - 401 0 null 51.6279ms
2025-06-12 19:56:16.317 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/login - null null
2025-06-12 19:56:16.328 +04:00 [INF] Request OPTIONS /api/auth/login started with correlation ID dbf5e4f5-02b6-46c0-9005-bdbd35f42935
2025-06-12 19:56:16.330 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:16.331 +04:00 [INF] Request OPTIONS /api/auth/login completed in 1ms with status 204 (Correlation ID: dbf5e4f5-02b6-46c0-9005-bdbd35f42935)
2025-06-12 19:56:16.334 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/login - 204 null null 17.1303ms
2025-06-12 19:56:16.337 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/login - application/json 52
2025-06-12 19:56:16.348 +04:00 [INF] Request POST /api/auth/login started with correlation ID a9997de9-25b8-455b-9c7f-3a2341d8f843
2025-06-12 19:56:16.352 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:16.354 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-12 19:56:16.363 +04:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] Login(LexAI.Identity.Application.DTOs.LoginDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-12 19:56:16.375 +04:00 [INF] Login attempt <NAME_EMAIL>
2025-06-12 19:56:16.385 +04:00 [INF] Login attempt <NAME_EMAIL> from IP ::1
2025-06-12 19:56:16.457 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-12 19:56:16.516 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__ToLowerInvariant_0='<EMAIL>'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Email" = @__ToLowerInvariant_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-12 19:56:17.564 +04:00 [INF] Executed DbCommand (25ms) [Parameters=[@p0='b112aa81-c891-47b2-bc64-7207f46afe0a', @p1='Login' (Nullable = false), @p2='"Successful login from ::1"' (DbType = Object), @p3='2025-06-12T15:56:17.2052294Z' (DbType = DateTime), @p4=NULL, @p5=NULL (DbType = DateTime), @p6=NULL, @p7='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p8='User' (Nullable = false), @p9=NULL, @p10='False', @p11='2025-06-12T15:56:17.2060006Z' (DbType = DateTime), @p12='2025-06-12T15:56:17.3385109Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14=NULL, @p15='44e9958d-1537-4614-bdbb-f4fe57509fd9' (Nullable = false), @p37='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p16='2025-06-04T16:36:29.4616840Z' (DbType = DateTime), @p17='system', @p18=NULL (DbType = DateTime), @p19=NULL, @p20='0', @p21='Senior' (Nullable = false), @p22='True', @p23='False', @p24='False', @p25='False', @p26='2025-06-12T15:56:17.2030533Z' (Nullable = true) (DbType = DateTime), @p27='::1', @p28='Lawyer' (Nullable = false), @p29=NULL (DbType = DateTime), @p30='$2a$11$IwkLcL8eN5wkkUnfgHtMZeW8Amjcv2Ot9F4VxIZ3WFp9cAD.cqVjy' (Nullable = false), @p31='fr-FR' (Nullable = false), @p32=NULL, @p33='Lawyer' (Nullable = false), @p34='Europe/Paris' (Nullable = false), @p35='2025-06-12T15:56:17.3383727Z' (Nullable = true) (DbType = DateTime), @p36='system', @p38='829' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditEntries" ("Id", "Action", "Changes", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EntityId", "EntityType", "IpAddress", "IsDeleted", "Timestamp", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15)
RETURNING xmin;
UPDATE "Users" SET "CreatedAt" = @p16, "CreatedBy" = @p17, "DeletedAt" = @p18, "DeletedBy" = @p19, "FailedLoginAttempts" = @p20, "FirstName" = @p21, "IsActive" = @p22, "IsDeleted" = @p23, "IsEmailVerified" = @p24, "IsLocked" = @p25, "LastLoginAt" = @p26, "LastLoginIpAddress" = @p27, "LastName" = @p28, "LockedAt" = @p29, "PasswordHash" = @p30, "PreferredLanguage" = @p31, "ProfilePictureUrl" = @p32, "Role" = @p33, "TimeZone" = @p34, "UpdatedAt" = @p35, "UpdatedBy" = @p36
WHERE "Id" = @p37 AND xmin = @p38
RETURNING xmin;
2025-06-12 19:56:17.638 +04:00 [INF] User updated successfully: "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-12 19:56:17.683 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='b08cf10b-2b00-4373-9fed-84caa06fad80', @p1='2025-06-12T15:56:17.6741820Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-19T15:56:17.6626819Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='fzRz3QSm743jKW8zxwroZRo5cHyxPKZDfhpPvISX+ZEH/vGP3CsXyvKdsj9sxqXhgsqupzWf2EwA12rmvoCqMQ==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='44e9958d-1537-4614-bdbb-f4fe57509fd9'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-12 19:56:17.691 +04:00 [INF] Refresh token added successfully: "b08cf10b-2b00-4373-9fed-84caa06fad80"
2025-06-12 19:56:17.693 +04:00 [INF] User "44e9958d-1537-4614-bdbb-f4fe57509fd9" logged in successfully
2025-06-12 19:56:17.698 +04:00 [INF] Login successful <NAME_EMAIL>
2025-06-12 19:56:17.702 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-12 19:56:17.741 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API) in 1373.4086ms
2025-06-12 19:56:17.745 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.Login (LexAI.Identity.API)'
2025-06-12 19:56:17.749 +04:00 [INF] Request POST /api/auth/login completed in 1397ms with status 200 (Correlation ID: a9997de9-25b8-455b-9c7f-3a2341d8f843)
2025-06-12 19:56:17.755 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/login - 200 null application/json; charset=utf-8 1418.1261ms
2025-06-12 19:56:21.718 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-12 19:56:21.728 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID c9826f97-4aed-4c47-8e51-01a23e05f2b0
2025-06-12 19:56:21.737 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:21.741 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 3ms with status 204 (Correlation ID: c9826f97-4aed-4c47-8e51-01a23e05f2b0)
2025-06-12 19:56:21.750 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 32.432ms
2025-06-12 19:56:21.752 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-12 19:56:21.767 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID cd974d6a-a87b-41fe-a8f0-57b6b2eb8939
2025-06-12 19:56:21.775 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:21.778 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:56:21.781 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-12 19:56:21.786 +04:00 [INF] Token refresh attempt
2025-06-12 19:56:21.790 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-12 19:56:21.798 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='fzRz3QSm743jKW8zxwroZRo5cHyxPKZDfhpPvISX+ZEH/vGP3CsXyvKdsj9sxqXhgsqupzWf2EwA12rmvoCqMQ=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-12 19:56:21.823 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-12 19:56:21.869 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='44e9958d-1537-4614-bdbb-f4fe57509fd9'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-12 19:56:21.921 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__token_0='fzRz3QSm743jKW8zxwroZRo5cHyxPKZDfhpPvISX+ZEH/vGP3CsXyvKdsj9sxqXhgsqupzWf2EwA12rmvoCqMQ=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-12 19:56:21.955 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p16='b08cf10b-2b00-4373-9fed-84caa06fad80', @p0='2025-06-12T15:56:17.6741820Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-19T15:56:17.6626810Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-12T15:56:21.9308799Z' (Nullable = true) (DbType = DateTime), @p10='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p11='fzRz3QSm743jKW8zxwroZRo5cHyxPKZDfhpPvISX+ZEH/vGP3CsXyvKdsj9sxqXhgsqupzWf2EwA12rmvoCqMQ==' (Nullable = false), @p12='2025-06-12T15:56:21.9332639Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='44e9958d-1537-4614-bdbb-f4fe57509fd9', @p17='832' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-12 19:56:21.966 +04:00 [INF] Refresh token updated successfully: "b08cf10b-2b00-4373-9fed-84caa06fad80"
2025-06-12 19:56:21.979 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='14aa35df-7a69-4650-af30-e0943aa71995', @p1='2025-06-12T15:56:21.9727949Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-19T15:56:21.9701891Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='64ylJxITCvvDbY3rokVez5PAq2Q9TrAfY+fOmY2f0dKB8H75ke6ZJkWdKxeg0/nU0Qi377TD1TzaHZFZ+klT9Q==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='44e9958d-1537-4614-bdbb-f4fe57509fd9'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-12 19:56:21.984 +04:00 [INF] Refresh token added successfully: "14aa35df-7a69-4650-af30-e0943aa71995"
2025-06-12 19:56:21.986 +04:00 [INF] Token refreshed successfully for user "44e9958d-1537-4614-bdbb-f4fe57509fd9"
2025-06-12 19:56:21.987 +04:00 [INF] Token refresh successful
2025-06-12 19:56:21.990 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-12 19:56:21.993 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 207.4949ms
2025-06-12 19:56:21.999 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-12 19:56:22.003 +04:00 [INF] Request POST /api/auth/refresh completed in 227ms with status 200 (Correlation ID: cd974d6a-a87b-41fe-a8f0-57b6b2eb8939)
2025-06-12 19:56:22.012 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 259.2026ms
2025-06-12 19:56:29.336 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/search/history - null null
2025-06-12 19:56:29.336 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/search/history - null null
2025-06-12 19:56:29.343 +04:00 [INF] Request OPTIONS /api/search/history started with correlation ID 2d2b1280-b0f2-49a0-8f26-373d0ef3396b
2025-06-12 19:56:29.349 +04:00 [INF] Request OPTIONS /api/search/history started with correlation ID 81b6029d-7fe1-4cf6-853a-4639e83f4171
2025-06-12 19:56:29.353 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:29.358 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:29.361 +04:00 [INF] Request OPTIONS /api/search/history completed in 7ms with status 204 (Correlation ID: 2d2b1280-b0f2-49a0-8f26-373d0ef3396b)
2025-06-12 19:56:29.363 +04:00 [INF] Request OPTIONS /api/search/history completed in 5ms with status 204 (Correlation ID: 81b6029d-7fe1-4cf6-853a-4639e83f4171)
2025-06-12 19:56:29.369 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/search/history - 204 null null 32.2989ms
2025-06-12 19:56:29.377 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/search/history - 204 null null 40.8484ms
2025-06-12 19:56:29.376 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/api/search/history - application/json null
2025-06-12 19:56:29.431 +04:00 [INF] Request GET /api/search/history started with correlation ID af12e85b-99eb-4f6f-ae9d-8f7a5ccf015a
2025-06-12 19:56:29.434 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:29.454 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-12 19:56:29.465 +04:00 [INF] Request GET /api/search/history completed in 30ms with status 404 (Correlation ID: af12e85b-99eb-4f6f-ae9d-8f7a5ccf015a)
2025-06-12 19:56:29.473 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/api/search/history - 404 0 null 97.9053ms
2025-06-12 19:56:29.477 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/api/search/history - application/json null
2025-06-12 19:56:29.481 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/api/search/history, Response status code: 404
2025-06-12 19:56:29.484 +04:00 [INF] Request GET /api/search/history started with correlation ID 39ddced2-325c-456e-9795-7ed556907cae
2025-06-12 19:56:29.490 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:29.492 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-12 19:56:29.493 +04:00 [INF] Request GET /api/search/history completed in 2ms with status 404 (Correlation ID: 39ddced2-325c-456e-9795-7ed556907cae)
2025-06-12 19:56:29.497 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/api/search/history - 404 0 null 20.2201ms
2025-06-12 19:56:29.503 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/api/search/history, Response status code: 404
2025-06-12 19:56:38.468 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/search/perform - null null
2025-06-12 19:56:38.474 +04:00 [INF] Request OPTIONS /api/search/perform started with correlation ID 0d3c3ab0-3fc6-40fa-b785-050df33bf657
2025-06-12 19:56:38.478 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:38.479 +04:00 [INF] Request OPTIONS /api/search/perform completed in 1ms with status 204 (Correlation ID: 0d3c3ab0-3fc6-40fa-b785-050df33bf657)
2025-06-12 19:56:38.484 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/search/perform - 204 null null 16.5767ms
2025-06-12 19:56:38.486 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/search/perform - application/json 58
2025-06-12 19:56:38.497 +04:00 [INF] Request POST /api/search/perform started with correlation ID e3a612c5-7ade-439c-bd7c-aac59abbdc91
2025-06-12 19:56:38.499 +04:00 [INF] CORS policy execution successful.
2025-06-12 19:56:38.501 +04:00 [INF] JWT Token validated for user: Senior Lawyer
2025-06-12 19:56:38.502 +04:00 [INF] Request POST /api/search/perform completed in 3ms with status 404 (Correlation ID: e3a612c5-7ade-439c-bd7c-aac59abbdc91)
2025-06-12 19:56:38.516 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/search/perform - 404 0 null 29.5696ms
2025-06-12 19:56:38.533 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5000/api/search/perform, Response status code: 404
