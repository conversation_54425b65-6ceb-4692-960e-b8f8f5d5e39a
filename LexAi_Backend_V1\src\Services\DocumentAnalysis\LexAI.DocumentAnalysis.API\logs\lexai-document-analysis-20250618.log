2025-06-18 00:09:30.881 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-18 00:09:30.909 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 29870a30-2f01-48c1-807f-0d3c80319e98
2025-06-18 00:09:30.915 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:09:30.917 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 2ms with status 204 (Correlation ID: 29870a30-2f01-48c1-807f-0d3c80319e98)
2025-06-18 00:09:30.926 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 44.7067ms
2025-06-18 00:09:30.930 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-18 00:09:30.942 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID a3226ecb-51f7-4f71-8af0-cd959fb497b1
2025-06-18 00:09:30.948 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:09:30.951 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:09:30.955 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:09:30.994 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:09:31.004 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:09:31.007 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:09:31.010 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:09:31.013 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:09:31.018 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:09:31.064 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-18 00:09:31.076 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-18 00:09:31.084 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:09:31.089 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-18 00:09:31.099 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 97.3076ms
2025-06-18 00:09:31.104 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:09:31.109 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 161ms with status 200 (Correlation ID: a3226ecb-51f7-4f71-8af0-cd959fb497b1)
2025-06-18 00:09:31.118 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 187.1065ms
2025-06-18 00:09:39.352 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - null null
2025-06-18 00:09:39.361 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID 10fe7b90-99e0-4423-b435-b66abc4fd9a3
2025-06-18 00:09:39.365 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:09:39.371 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 5ms with status 204 (Correlation ID: 10fe7b90-99e0-4423-b435-b66abc4fd9a3)
2025-06-18 00:09:39.383 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 204 null null 30.6439ms
2025-06-18 00:09:39.388 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - application/json null
2025-06-18 00:09:39.411 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID e3a7a70a-901a-4dd6-bcaa-1f0ac67f1a04
2025-06-18 00:09:39.415 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:09:39.419 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:09:39.423 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-18 00:09:39.435 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:09:39.442 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:09:39.444 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:09:39.448 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:09:39.451 +04:00 [INF] Retrieving analysis "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:09:39.458 +04:00 [INF] Retrieving analysis result: "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:09:39.506 +04:00 [INF] Executed DbCommand (43ms) [Parameters=[@__id_0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentPurpose", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExecutiveSummary", d2."ExtractedText", d2."FinancialTerms", d2."ImportantDates", d2."IsDeleted", d2."KeyPoints", d2."MainParties", d2."ModelUsed", d2."OverallRiskLevel", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-18 00:09:39.749 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-18 00:09:39.771 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 330.8722ms
2025-06-18 00:09:39.775 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-18 00:09:39.778 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 363ms with status 200 (Correlation ID: e3a7a70a-901a-4dd6-bcaa-1f0ac67f1a04)
2025-06-18 00:09:39.782 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 200 null application/json; charset=utf-8 394.0979ms
2025-06-18 00:11:12.782 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-18 00:11:12.795 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 13b6768d-7396-4c41-af71-f90f8440c46b
2025-06-18 00:11:12.802 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:11:12.809 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 6ms with status 204 (Correlation ID: 13b6768d-7396-4c41-af71-f90f8440c46b)
2025-06-18 00:11:12.823 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 40.4448ms
2025-06-18 00:11:12.833 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-18 00:11:12.875 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID d1962eef-2c71-40ad-8904-43305a534957
2025-06-18 00:11:12.881 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:11:12.884 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:11:12.892 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:11:12.912 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:11:12.926 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:11:12.929 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:11:12.936 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:11:12.943 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:11:12.946 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:11:12.957 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-18 00:11:12.994 +04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-18 00:11:13.002 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:11:13.004 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-18 00:11:13.012 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 88.0218ms
2025-06-18 00:11:13.015 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:11:13.016 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 135ms with status 200 (Correlation ID: d1962eef-2c71-40ad-8904-43305a534957)
2025-06-18 00:11:13.021 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 187.7391ms
2025-06-18 00:11:21.057 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - null null
2025-06-18 00:11:21.075 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID f3cb0be8-4c7d-492c-9224-a08a415e1788
2025-06-18 00:11:21.079 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:11:21.081 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 1ms with status 204 (Correlation ID: f3cb0be8-4c7d-492c-9224-a08a415e1788)
2025-06-18 00:11:21.091 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 204 null null 33.8148ms
2025-06-18 00:11:21.134 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - application/json null
2025-06-18 00:11:21.146 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID cb4136d2-d579-43a0-8f09-3359256b5891
2025-06-18 00:11:21.150 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:11:21.151 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:11:21.153 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-18 00:11:21.162 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:11:21.168 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:11:21.170 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:11:21.173 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:11:21.176 +04:00 [INF] Retrieving analysis "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:11:21.185 +04:00 [INF] Retrieving analysis result: "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:11:21.237 +04:00 [INF] Executed DbCommand (44ms) [Parameters=[@__id_0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentPurpose", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExecutiveSummary", d2."ExtractedText", d2."FinancialTerms", d2."ImportantDates", d2."IsDeleted", d2."KeyPoints", d2."MainParties", d2."ModelUsed", d2."OverallRiskLevel", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-18 00:11:21.585 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-18 00:11:21.605 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 437.776ms
2025-06-18 00:11:21.608 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-18 00:11:21.610 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 459ms with status 200 (Correlation ID: cb4136d2-d579-43a0-8f09-3359256b5891)
2025-06-18 00:11:21.614 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 200 null application/json; charset=utf-8 479.8745ms
2025-06-18 00:19:55.326 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-18 00:19:55.356 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID cf2a9440-4acb-4076-9490-5807d5817193
2025-06-18 00:19:55.362 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:19:55.365 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 2ms with status 204 (Correlation ID: cf2a9440-4acb-4076-9490-5807d5817193)
2025-06-18 00:19:55.372 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 45.8066ms
2025-06-18 00:19:55.382 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-18 00:19:55.411 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 0c2afb86-4e12-4baf-ae66-cfd4636475d2
2025-06-18 00:19:55.415 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:19:55.418 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:19:55.420 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:19:55.423 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:19:55.428 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:19:55.430 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:19:55.434 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:19:55.436 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:19:55.438 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:19:55.474 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-18 00:19:55.484 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-18 00:19:55.489 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:19:55.491 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-18 00:19:55.494 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 67.5003ms
2025-06-18 00:19:55.496 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:19:55.498 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 82ms with status 200 (Correlation ID: 0c2afb86-4e12-4baf-ae66-cfd4636475d2)
2025-06-18 00:19:55.501 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 119.3991ms
2025-06-18 00:22:56.048 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-18 00:22:56.174 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 2735d4a5-2e5a-4b1c-8c03-bf332542232c
2025-06-18 00:22:56.218 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:22:56.236 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 18ms with status 204 (Correlation ID: 2735d4a5-2e5a-4b1c-8c03-bf332542232c)
2025-06-18 00:22:56.288 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 239.7437ms
2025-06-18 00:22:56.309 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-18 00:22:56.489 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID b9e58b9b-ef3c-4dfe-97f3-fad588af3f9a
2025-06-18 00:22:56.494 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:22:56.496 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:22:56.504 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:22:56.518 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:22:56.523 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:22:56.526 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:22:56.529 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:22:56.534 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:22:56.540 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:22:56.551 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-18 00:22:56.623 +04:00 [INF] Executed DbCommand (64ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-18 00:22:56.632 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:22:56.634 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-18 00:22:56.647 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 125.5319ms
2025-06-18 00:22:56.649 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-18 00:22:56.658 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 164ms with status 200 (Correlation ID: b9e58b9b-ef3c-4dfe-97f3-fad588af3f9a)
2025-06-18 00:22:56.664 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 355.5809ms
2025-06-18 00:23:11.393 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - null null
2025-06-18 00:23:11.400 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID ee1a4faf-5923-4393-a6c0-f58a72971aa7
2025-06-18 00:23:11.405 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:23:11.407 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 1ms with status 204 (Correlation ID: ee1a4faf-5923-4393-a6c0-f58a72971aa7)
2025-06-18 00:23:11.411 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 204 null null 17.6605ms
2025-06-18 00:23:11.414 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - application/json null
2025-06-18 00:23:11.422 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID 446a1761-c837-4606-93cf-836ed0bcdcc1
2025-06-18 00:23:11.425 +04:00 [INF] CORS policy execution successful.
2025-06-18 00:23:11.427 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-18 00:23:11.429 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-18 00:23:11.435 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-18 00:23:11.442 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-18 00:23:11.444 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-18 00:23:11.449 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-18 00:23:11.451 +04:00 [INF] Retrieving analysis "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:23:11.454 +04:00 [INF] Retrieving analysis result: "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-18 00:23:11.492 +04:00 [INF] Executed DbCommand (34ms) [Parameters=[@__id_0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentPurpose", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExecutiveSummary", d2."ExtractedText", d2."FinancialTerms", d2."ImportantDates", d2."IsDeleted", d2."KeyPoints", d2."MainParties", d2."ModelUsed", d2."OverallRiskLevel", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-18 00:23:11.741 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-18 00:23:11.758 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 317.7179ms
2025-06-18 00:23:11.762 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-18 00:23:11.765 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 339ms with status 200 (Correlation ID: 446a1761-c837-4606-93cf-836ed0bcdcc1)
2025-06-18 00:23:11.772 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 200 null application/json; charset=utf-8 357.8975ms
2025-06-18 00:58:05.323 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-18 00:58:05.740 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-18 00:58:06.362 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-18 00:58:06.365 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-18 00:58:06.878 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-18 00:58:06.885 +04:00 [INF] Hosting environment: Development
2025-06-18 00:58:06.891 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-18 00:58:07.404 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-18 00:58:07.640 +04:00 [INF] Request GET / started with correlation ID 5c261d02-9efd-4653-8005-ed6bdc9ccf29
2025-06-18 00:58:07.786 +04:00 [INF] Request GET / completed in 140ms with status 404 (Correlation ID: 5c261d02-9efd-4653-8005-ed6bdc9ccf29)
2025-06-18 00:58:07.800 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 399.4345ms
2025-06-18 00:58:07.821 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
