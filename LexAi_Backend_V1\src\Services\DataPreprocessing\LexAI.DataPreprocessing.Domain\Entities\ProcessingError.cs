using LexAI.DataPreprocessing.Domain.ValueObjects;

namespace LexAI.DataPreprocessing.Domain.Entities;

/// <summary>
/// Processing error entity
/// </summary>
public class ProcessingError
{
    /// <summary>
    /// Error ID
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Document ID
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Error code
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Step name where error occurred
    /// </summary>
    public string StepName { get; set; } = string.Empty;

    /// <summary>
    /// Agent name that caused the error
    /// </summary>
    public string AgentName { get; set; } = string.Empty;

    /// <summary>
    /// Error severity
    /// </summary>
    public ErrorSeverity Severity { get; set; }

    /// <summary>
    /// When the error occurred
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Error metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Whether the error has been resolved
    /// </summary>
    public bool IsResolved { get; set; }

    /// <summary>
    /// Resolution notes
    /// </summary>
    public string? ResolutionNotes { get; set; }

    /// <summary>
    /// When the error was resolved
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// Creates a new processing error
    /// </summary>
    /// <param name="errorCode">Error code</param>
    /// <param name="errorMessage">Error message</param>
    /// <param name="severity">Error severity</param>
    /// <param name="stepName">Step name</param>
    /// <param name="agentName">Agent name</param>
    /// <returns>Processing error</returns>
    public static ProcessingError Create(string errorCode, string errorMessage, ErrorSeverity severity, string stepName, string agentName)
    {
        return new ProcessingError
        {
            ErrorCode = errorCode,
            ErrorMessage = errorMessage,
            Severity = severity,
            StepName = stepName,
            AgentName = agentName,
            OccurredAt = DateTime.UtcNow
        };
    }
}
