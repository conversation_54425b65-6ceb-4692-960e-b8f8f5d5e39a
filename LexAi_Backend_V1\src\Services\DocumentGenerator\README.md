# 📝 Service de Génération de Documents - LexAI

Service dédié à la génération automatique de documents juridiques personnalisés.

## 🎯 Fonctionnalités

### ✅ Implémentées
- **Génération de documents** - Création automatique de documents juridiques
- **Templates dynamiques** - Modèles personnalisables par type de document
- **Formulaires intelligents** - Collecte de données structurées
- **Export multi-format** - PDF, DOCX, TXT
- **Historique des générations** - Suivi et versioning
- **Validation juridique** - Vérification automatique des clauses
- **Personnalisation** - Adaptation selon la juridiction
- **Intégration IA** - Utilisation d'OpenAI ou modèles locaux

### 🚧 En cours de développement
- **Templates avancés** - Modèles complexes avec logique conditionnelle
- **Signature électronique** - Intégration DocuSign/Yousign
- **Collaboration** - Édition collaborative en temps réel
- **Workflow d'approbation** - Processus de validation multi-niveaux

## 🏗️ Architecture

Le service suit le pattern **Clean Architecture** :

```
LexAI.DocumentGenerator/
├── Domain/                    # Entités métier et logique de domaine
│   ├── Entities/             # Document, Template, Generation
│   ├── ValueObjects/         # DocumentContent, TemplateField
│   └── Enums/               # DocumentType, GenerationStatus
├── Application/              # Use cases et services applicatifs
│   ├── Commands/            # GenerateDocumentCommand, CreateTemplateCommand
│   ├── Queries/             # GetTemplateQuery, GetGenerationHistoryQuery
│   ├── DTOs/               # GenerationRequestDto, DocumentResponseDto
│   ├── Interfaces/         # IDocumentGeneratorService, ITemplateService
│   └── Validators/         # Validation FluentValidation
├── Infrastructure/           # Services d'infrastructure et IA
│   ├── Services/           # OpenAIGeneratorService, TemplateEngine
│   ├── Data/              # Entity Framework DbContext
│   ├── Repositories/      # Implémentation des repositories
│   └── Export/            # Services d'export PDF/DOCX
└── API/                     # Contrôleurs et configuration
    ├── Controllers/        # DocumentController, TemplateController
    └── Configuration/      # Configuration de l'API
```

## 🤖 Intelligence Artificielle

### Modèles utilisés
- **Génération** : OpenAI `gpt-4` pour la création de contenu
- **Validation** : OpenAI `gpt-3.5-turbo` pour la vérification
- **Templates** : Moteur de templates Liquid/Handlebars
- **Export** : iTextSharp pour PDF, OpenXML pour DOCX

### Pipeline de génération
1. **Template Selection** : Sélection du modèle approprié
2. **Data Collection** : Collecte des données via formulaires
3. **Content Generation** : Génération du contenu avec IA
4. **Validation** : Vérification juridique automatique
5. **Formatting** : Application du style et mise en forme
6. **Export** : Génération du fichier final
7. **Storage** : Sauvegarde et historique

## 📊 Types de documents supportés

### Contrats
- Contrat de travail (CDI, CDD, Stage)
- Contrat de prestation de services
- Contrat de bail (habitation, commercial)
- Accord de confidentialité (NDA)
- Contrat de partenariat
- Conditions générales de vente/utilisation

### Correspondance
- Lettres de mise en demeure
- Courriers administratifs
- Notifications juridiques
- Lettres de résiliation
- Demandes d'information

### Documents procéduraux
- Assignations
- Conclusions
- Requêtes
- Mémoires
- Actes de procédure

### Documents constitutifs
- Statuts de société
- Règlements intérieurs
- Procurations
- Mandats

## 🔧 Configuration

### appsettings.json
```json
{
  "ConnectionStrings": {
    "PostgreSql": "Host=localhost;Database=document_generator_db;Username=lexai_user;Password=lexai_password_2024!",
    "MongoDB": "mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_templates?authSource=admin"
  },
  "OpenAI": {
    "ApiKey": "your-openai-api-key",
    "GenerationModel": "gpt-4",
    "ValidationModel": "gpt-3.5-turbo"
  },
  "DocumentGeneration": {
    "DefaultLanguage": "fr",
    "DefaultJurisdiction": "France",
    "MaxDocumentLength": 50000,
    "EnableValidation": true,
    "ExportFormats": ["PDF", "DOCX", "TXT"]
  },
  "Templates": {
    "StoragePath": "./templates",
    "CacheExpirationMinutes": 60,
    "EnableVersioning": true
  }
}
```

## 🚀 API Endpoints

### Génération de documents
- `POST /api/documents/generate` - Générer un nouveau document
- `POST /api/documents/generate-from-template` - Générer depuis un template
- `GET /api/documents/{id}` - Récupérer un document généré
- `GET /api/documents/{id}/download` - Télécharger un document

### Gestion des templates
- `GET /api/templates` - Lister les templates disponibles
- `GET /api/templates/{id}` - Détails d'un template
- `POST /api/templates` - Créer un nouveau template
- `PUT /api/templates/{id}` - Modifier un template
- `DELETE /api/templates/{id}` - Supprimer un template

### Historique et suivi
- `GET /api/generations` - Historique des générations
- `GET /api/generations/{id}` - Détails d'une génération
- `POST /api/generations/{id}/regenerate` - Régénérer un document

## 📋 Modèles de données

### Document généré
```json
{
  "id": "uuid",
  "templateId": "uuid",
  "title": "string",
  "content": "string",
  "format": "PDF|DOCX|TXT",
  "status": "Generated|Validated|Signed|Archived",
  "metadata": {
    "generatedAt": "datetime",
    "generatedBy": "uuid",
    "tokensUsed": "number",
    "processingTime": "number"
  },
  "data": {
    "field1": "value1",
    "field2": "value2"
  }
}
```

### Template
```json
{
  "id": "uuid",
  "name": "string",
  "description": "string",
  "category": "Contract|Letter|Procedure|Corporate",
  "content": "string",
  "fields": [
    {
      "name": "string",
      "type": "text|number|date|select|boolean",
      "required": "boolean",
      "validation": "string"
    }
  ],
  "jurisdiction": "string",
  "language": "string",
  "version": "string"
}
```

## 🧪 Tests

### Tests unitaires
```bash
dotnet test LexAI.DocumentGenerator.Tests.Unit
```

### Tests d'intégration
```bash
dotnet test LexAI.DocumentGenerator.Tests.Integration
```

### Tests de performance
```bash
dotnet test LexAI.DocumentGenerator.Tests.Performance
```

## 📈 Métriques et monitoring

### Métriques clés
- Nombre de documents générés par jour/mois
- Temps moyen de génération
- Taux de succès des générations
- Utilisation des templates
- Coûts IA (tokens utilisés)

### Alertes
- Échec de génération > 5%
- Temps de génération > 30s
- Utilisation excessive de tokens
- Erreurs de validation

## 🔒 Sécurité

### Contrôles d'accès
- Authentification JWT obligatoire
- Autorisation basée sur les rôles (RBAC)
- Limitation par utilisateur/organisation
- Audit des générations

### Protection des données
- Chiffrement des documents sensibles
- Anonymisation des données de test
- Purge automatique des anciens documents
- Conformité RGPD

## 🚀 Déploiement

### Prérequis
- .NET 9 Runtime
- PostgreSQL 15+
- MongoDB 7+
- Redis (optionnel, pour cache)

### Variables d'environnement
```bash
ASPNETCORE_ENVIRONMENT=Production
ConnectionStrings__PostgreSql=...
ConnectionStrings__MongoDB=...
OpenAI__ApiKey=...
```

### Docker
```bash
docker build -t lexai-document-generator .
docker run -p 8085:8080 lexai-document-generator
```

## 📝 Changelog

### v1.0.0 (En cours)
- ✅ Génération de documents avec OpenAI
- ✅ Templates dynamiques
- ✅ Export PDF/DOCX
- ✅ Validation automatique
- ✅ Historique des générations
- ✅ API REST complète
- ✅ Tests complets et documentation

### Roadmap v1.1.0
- 🔄 Templates avancés avec logique conditionnelle
- 🔄 Signature électronique intégrée
- 🔄 Collaboration en temps réel
- 🔄 Workflow d'approbation
- 🔄 Intégration avec systèmes externes
