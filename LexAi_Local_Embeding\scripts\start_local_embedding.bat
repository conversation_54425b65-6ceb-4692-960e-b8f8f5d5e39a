﻿@echo off
echo ========================================
echo   Service d'Embedding Local - LexAI
echo ========================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Python n'est pas installé ou pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)
cd ..
echo ✅ Python détecté

REM Créer un environnement virtuel s'il n'existe pas
if not exist "venv" (
    echo 📦 Création de l'environnement virtuel...
    python -m venv venv
)

REM Activer l'environnement virtuel
echo 🔄 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Installer les dépendances
echo 📥 Installation des dépendances...
pip install -r requirements.txt

echo.
echo 🚀 Démarrage du service d'embedding local...
echo 📍 URL: http://localhost:8000
echo 📍 Health Check: http://localhost:8000/health
echo.
echo Appuyez sur Ctrl+C pour arrêter le service
echo.

REM Démarrer le service
python local_embedding_service.py
