2025-06-23 21:10:31.589 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-23 21:10:31.800 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 21:10:32.587 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-23 21:10:32.594 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-23 21:10:32.757 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 21:10:32.760 +04:00 [INF] Hosting environment: Development
2025-06-23 21:10:32.762 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-23 21:10:35.100 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-23 21:10:35.464 +04:00 [INF] Request GET / started with correlation ID a8c35730-acf6-4dd4-908d-a543581c7e3b
2025-06-23 21:10:37.650 +04:00 [INF] Request GET / completed in 2176ms with status 404 (Correlation ID: a8c35730-acf6-4dd4-908d-a543581c7e3b)
2025-06-23 21:10:37.683 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2583.8351ms
2025-06-23 21:10:37.735 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-23 21:26:42.182 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-23 21:26:42.182 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-23 21:26:42.207 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID a642a1d9-62f5-47be-8b24-1b785cd00da2
2025-06-23 21:26:42.207 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 60fb6644-7d24-4e19-9af1-bbf3699a4c24
2025-06-23 21:26:42.227 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:42.228 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:42.232 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 5ms with status 204 (Correlation ID: 60fb6644-7d24-4e19-9af1-bbf3699a4c24)
2025-06-23 21:26:42.232 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 11ms with status 204 (Correlation ID: a642a1d9-62f5-47be-8b24-1b785cd00da2)
2025-06-23 21:26:42.238 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 56.0673ms
2025-06-23 21:26:42.250 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-23 21:26:42.251 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 68.7651ms
2025-06-23 21:26:42.269 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 51e2dc49-746b-4652-a175-5c2aa9a02c47
2025-06-23 21:26:42.275 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:42.503 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:42.519 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-23 21:26:42.562 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-23 21:26:43.881 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:46.046 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:26:46.050 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:26:46.066 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 21:26:46.117 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-23 21:26:46.118 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 3544.0628ms
2025-06-23 21:26:46.123 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 528912c3-20aa-4d7e-9429-f6736684ecb0
2025-06-23 21:26:46.125 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-23 21:26:46.127 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:26:46.128 +04:00 [INF] Request GET /api/v1/search/history completed in 3853ms with status 200 (Correlation ID: 51e2dc49-746b-4652-a175-5c2aa9a02c47)
2025-06-23 21:26:46.133 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 3882.7831ms
2025-06-23 21:26:46.133 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:26:46.139 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-23 21:26:46.141 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-23 21:26:46.144 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:26:46.147 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:26:46.150 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:26:46.153 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 21:26:46.156 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 11.9942ms
2025-06-23 21:26:46.157 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-23 21:26:46.159 +04:00 [INF] Request GET /api/v1/search/history completed in 32ms with status 200 (Correlation ID: 528912c3-20aa-4d7e-9429-f6736684ecb0)
2025-06-23 21:26:46.162 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 45.0371ms
