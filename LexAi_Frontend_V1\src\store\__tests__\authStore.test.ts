import { renderHook, act } from '@testing-library/react'
import { useAuthStore } from '../authStore'
import { authApi, ApiException } from '../../services/api'
import { UserRole } from '../../types'

// Mock du service API
vi.mock('../../services/api')
const mockAuthApi = authApi as any

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('AuthStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      accessToken: null,
      refreshToken: null,
    })
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  it('should have initial state', () => {
    const { result } = renderHook(() => useAuthStore())

    expect(result.current.user).toBeNull()
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.accessToken).toBeNull()
    expect(result.current.refreshToken).toBeNull()
  })

  it('should login successfully', async () => {
    const authResponse = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'Client' as UserRole,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      tokenType: 'Bearer',
      expiresIn: 3600,
    }

    mockAuthApi.login.mockResolvedValue(authResponse)

    const { result } = renderHook(() => useAuthStore())

    await act(async () => {
      await result.current.login('<EMAIL>', 'password123')
    })

    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.user).toEqual(authResponse.user)
    expect(result.current.accessToken).toBe(authResponse.accessToken)
    expect(result.current.refreshToken).toBe(authResponse.refreshToken)
    expect(result.current.isLoading).toBe(false)
    expect(localStorageMock.setItem).toHaveBeenCalledWith('accessToken', authResponse.accessToken)
  })

  it('should logout successfully', () => {
    const { result } = renderHook(() => useAuthStore())

    // First set a user with tokens
    act(() => {
      useAuthStore.setState({
        user: {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: 'Lawyer' as UserRole,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        isAuthenticated: true,
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      })
    })

    expect(result.current.isAuthenticated).toBe(true)

    // Then logout
    act(() => {
      result.current.logout()
    })

    expect(result.current.user).toBeNull()
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.accessToken).toBeNull()
    expect(result.current.refreshToken).toBeNull()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('accessToken')
  })

  it('should register successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      password: 'Password123!',
      confirmPassword: 'Password123!',
      role: 'Client' as UserRole,
      preferredLanguage: 'fr-FR',
      timeZone: 'Europe/Paris',
      acceptTerms: true,
      acceptPrivacyPolicy: true,
    }

    const createdUser = {
      id: '1',
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    mockAuthApi.register.mockResolvedValue(createdUser)

    const { result } = renderHook(() => useAuthStore())

    await act(async () => {
      await result.current.register(userData)
    })

    expect(result.current.user).toEqual(createdUser)
    expect(result.current.isAuthenticated).toBe(false) // Registration doesn't auto-authenticate
    expect(result.current.isLoading).toBe(false)
  })

  it('should set loading state', () => {
    const { result } = renderHook(() => useAuthStore())

    act(() => {
      result.current.setLoading(true)
    })

    expect(result.current.isLoading).toBe(true)

    act(() => {
      result.current.setLoading(false)
    })

    expect(result.current.isLoading).toBe(false)
  })

  it('should set and clear tokens', () => {
    const { result } = renderHook(() => useAuthStore())

    act(() => {
      result.current.setTokens('access-token', 'refresh-token')
    })

    expect(result.current.accessToken).toBe('access-token')
    expect(result.current.refreshToken).toBe('refresh-token')
    expect(localStorageMock.setItem).toHaveBeenCalledWith('accessToken', 'access-token')

    act(() => {
      result.current.clearTokens()
    })

    expect(result.current.accessToken).toBeNull()
    expect(result.current.refreshToken).toBeNull()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('accessToken')
  })
})
