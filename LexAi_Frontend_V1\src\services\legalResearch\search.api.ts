import { BaseApiService } from '../api/base'
import { 
  SearchRequest,
  SearchResponse,
  DocumentSearchRequest,
  CaseSearchRequest,
  AdvancedSearchRequest,
  SearchSuggestion,
  SearchHistoryItem,
  SearchAnalytics,
  SearchFeedback,
  SimilarDocumentsRequest,
  SearchResult
} from './types'

const LEGAL_RESEARCH_API_BASE_URL = import.meta.env.VITE_LEGAL_RESEARCH_API_URL || 'http://localhost:51403'

class SearchApiService extends BaseApiService {
  constructor() {
    super(LEGAL_RESEARCH_API_BASE_URL)
  }

  /**
   * Effectue une recherche juridique principale
   */
  async performSearch(request: SearchRequest): Promise<SearchResponse> {
    return this.post('/api/v1/search/perform', request)
  }

  /**
   * Recherche de documents
   */
  async searchDocuments(request: DocumentSearchRequest): Promise<SearchResponse> {
    return this.post('/api/v1/search/documents', request)
  }

  /**
   * Recherche de jurisprudences
   */
  async searchCases(request: CaseSearchRequest): Promise<SearchResponse> {
    return this.post('/api/v1/search/cases', request)
  }

  /**
   * Recherche avancée
   */
  async advancedSearch(request: AdvancedSearchRequest): Promise<SearchResponse> {
    return this.post('/api/v1/search/advanced', request)
  }

  /**
   * Obtient des suggestions de recherche
   */
  async getSearchSuggestions(query: string): Promise<SearchSuggestion[]> {
    return this.get(`/api/v1/search/suggestions?q=${encodeURIComponent(query)}`)
  }

  /**
   * Obtient l'historique de recherche
   */
  async getSearchHistory(): Promise<SearchHistoryItem[]> {
    return this.get('/api/v1/search/history')
  }

  /**
   * Obtient les analytics de recherche
   */
  async getSearchAnalytics(sessionId?: string): Promise<SearchAnalytics> {
    const url = sessionId 
      ? `/api/v1/search/analytics?sessionId=${sessionId}`
      : '/api/v1/search/analytics'
    return this.get(url)
  }

  /**
   * Fournit un feedback sur les résultats de recherche
   */
  async provideFeedback(feedback: SearchFeedback): Promise<void> {
    return this.post(`/api/v1/search/feedback/${feedback.searchId}`, {
      relevantResults: feedback.relevantResults,
      irrelevantResults: feedback.irrelevantResults,
      rating: feedback.rating,
      comments: feedback.comments
    })
  }

  /**
   * Obtient des documents similaires
   */
  async getSimilarDocuments(request: SimilarDocumentsRequest): Promise<SearchResult[]> {
    const maxResults = request.maxResults || 5
    return this.get(`/api/v1/search/similar/${request.documentId}?maxResults=${maxResults}`)
  }

  /**
   * Sauvegarde une recherche dans l'historique
   */
  async saveSearchToHistory(query: string, resultCount: number, filters?: any): Promise<void> {
    return this.post('/api/v1/search/history', {
      query,
      resultCount,
      filters
    })
  }

  /**
   * Supprime un élément de l'historique
   */
  async deleteFromHistory(historyId: string): Promise<void> {
    return this.delete(`/api/v1/search/history/${historyId}`)
  }

  /**
   * Efface tout l'historique
   */
  async clearHistory(): Promise<void> {
    return this.delete('/api/v1/search/history')
  }
}

export const searchApi = new SearchApiService()
