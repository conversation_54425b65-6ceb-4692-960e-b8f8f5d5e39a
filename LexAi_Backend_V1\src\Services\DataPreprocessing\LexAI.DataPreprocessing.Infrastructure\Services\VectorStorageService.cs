using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using System.Diagnostics;

namespace LexAI.DataPreprocessing.Infrastructure.Services;

/// <summary>
/// Vector storage service implementation
/// </summary>
public class VectorStorageService : IVectorStorageService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<VectorStorageService> _logger;
    private readonly IMongoClient _mongoClient;
    private readonly QdrantClient _qdrantClient;

    /// <summary>
    /// Supported database types
    /// </summary>
    public IEnumerable<VectorDatabaseType> SupportedDatabases => new[]
    {
        VectorDatabaseType.MongoDB,
        VectorDatabaseType.Qdrant,
        VectorDatabaseType.Weaviate,
        VectorDatabaseType.Pinecone
    };

    /// <summary>
    /// Initializes a new instance of the VectorStorageService
    /// </summary>
    /// <param name="configuration">Configuration</param>
    /// <param name="logger">Logger</param>
    /// <param name="mongoClient">MongoDB client</param>
    public VectorStorageService(
        IConfiguration configuration,
        ILogger<VectorStorageService> logger,
        IMongoClient mongoClient)
    {
        _configuration = configuration;
        _logger = logger;
        _mongoClient = mongoClient;

        // Initialize Qdrant client
        var qdrantHost = configuration["Qdrant:Host"] ?? "localhost";
        var qdrantPort = int.Parse(configuration["Qdrant:Port"] ?? "6334");
        _qdrantClient = new QdrantClient(qdrantHost, qdrantPort, https: false);
    }

    /// <summary>
    /// Stores vectors in the database
    /// </summary>
    /// <param name="chunks">Chunks with vectors to store</param>
    /// <param name="databaseType">Target database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Storage result</returns>
    public async Task<VectorStorageResult> StoreVectorsAsync(
        IEnumerable<DocumentChunk> chunks,
        VectorDatabaseType databaseType,
        string collection,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Storing {ChunkCount} vectors in {DatabaseType}/{Collection}",
            chunks.Count(), databaseType, collection);

        var stopwatch = Stopwatch.StartNew();
        var result = new VectorStorageResult
        {
            Success = false
        };

        try
        {
            var chunkList = chunks.ToList();
            if (!chunkList.Any())
            {
                result.Errors.Add("No chunks provided for storage");
                return result;
            }

            // Filter only vectorized chunks
            var vectorizedChunks = chunkList.Where(c => c.IsVectorized && c.EmbeddingVector != null).ToList();
            if (!vectorizedChunks.Any())
            {
                result.Errors.Add("No vectorized chunks found");
                return result;
            }

            var vectorIds = new List<string>();

            switch (databaseType)
            {
                case VectorDatabaseType.MongoDB:
                    vectorIds = await StoreInMongoDBAsync(vectorizedChunks, collection, cancellationToken);
                    break;

                case VectorDatabaseType.Qdrant:
                    vectorIds = await StoreInQdrantAsync(vectorizedChunks, collection, cancellationToken);
                    break;

                case VectorDatabaseType.Weaviate:
                    vectorIds = await StoreInWeaviateAsync(vectorizedChunks, collection, cancellationToken);
                    break;

                case VectorDatabaseType.Pinecone:
                    vectorIds = await StoreInPineconeAsync(vectorizedChunks, collection, cancellationToken);
                    break;

                default:
                    result.Errors.Add($"Unsupported database type: {databaseType}");
                    return result;
            }

            stopwatch.Stop();

            result.Success = vectorIds.Any();
            result.VectorsStored = vectorIds.Count;
            result.StorageTime = stopwatch.Elapsed;
            result.VectorIds = vectorIds;

            if (!result.Success)
            {
                result.Errors.Add("No vectors were successfully stored");
            }

            _logger.LogInformation("Vector storage completed. Stored: {StoredCount}/{TotalCount}, Time: {Time}ms",
                vectorIds.Count, vectorizedChunks.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.StorageTime = stopwatch.Elapsed;
            result.Errors.Add($"Vector storage failed: {ex.Message}");

            _logger.LogError(ex, "Error storing vectors in {DatabaseType}/{Collection}", databaseType, collection);
            return result;
        }
    }

    /// <summary>
    /// Searches for similar vectors
    /// </summary>
    /// <param name="queryVector">Query vector</param>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="limit">Maximum results</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Similar vectors</returns>
    public async Task<IEnumerable<VectorSearchResult>> SearchSimilarAsync(
        float[] queryVector,
        VectorDatabaseType databaseType,
        string collection,
        int limit = 10,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (queryVector == null || queryVector.Length == 0)
                return Enumerable.Empty<VectorSearchResult>();

            return databaseType switch
            {
                VectorDatabaseType.MongoDB => await SearchInMongoDBAsync(queryVector, collection, limit, cancellationToken),
                VectorDatabaseType.Qdrant => await SearchInQdrantAsync(queryVector, collection, limit, cancellationToken),
                VectorDatabaseType.Weaviate => await SearchInWeaviateAsync(queryVector, collection, limit, cancellationToken),
                VectorDatabaseType.Pinecone => await SearchInPineconeAsync(queryVector, collection, limit, cancellationToken),
                _ => Enumerable.Empty<VectorSearchResult>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vectors in {DatabaseType}/{Collection}", databaseType, collection);
            return Enumerable.Empty<VectorSearchResult>();
        }
    }

    /// <summary>
    /// Deletes vectors from the database
    /// </summary>
    /// <param name="vectorIds">Vector IDs to delete</param>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task DeleteVectorsAsync(
        IEnumerable<string> vectorIds,
        VectorDatabaseType databaseType,
        string collection,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var idList = vectorIds.ToList();
            if (!idList.Any())
                return;

            switch (databaseType)
            {
                case VectorDatabaseType.MongoDB:
                    await DeleteFromMongoDBAsync(idList, collection, cancellationToken);
                    break;

                case VectorDatabaseType.Qdrant:
                    await DeleteFromQdrantAsync(idList, collection, cancellationToken);
                    break;

                case VectorDatabaseType.Weaviate:
                    await DeleteFromWeaviateAsync(idList, collection, cancellationToken);
                    break;

                case VectorDatabaseType.Pinecone:
                    await DeleteFromPineconeAsync(idList, collection, cancellationToken);
                    break;

                default:
                    _logger.LogWarning("Unsupported database type for deletion: {DatabaseType}", databaseType);
                    break;
            }

            _logger.LogInformation("Deleted {VectorCount} vectors from {DatabaseType}/{Collection}",
                idList.Count, databaseType, collection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vectors from {DatabaseType}/{Collection}", databaseType, collection);
            throw;
        }
    }

    /// <summary>
    /// Creates a collection if it doesn't exist
    /// </summary>
    /// <param name="databaseType">Database type</param>
    /// <param name="collection">Collection name</param>
    /// <param name="dimension">Vector dimension</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task EnsureCollectionExistsAsync(
        VectorDatabaseType databaseType,
        string collection,
        int dimension,
        CancellationToken cancellationToken = default)
    {
        try
        {
            switch (databaseType)
            {
                case VectorDatabaseType.MongoDB:
                    await EnsureMongoDBCollectionAsync(collection, dimension, cancellationToken);
                    break;

                case VectorDatabaseType.Qdrant:
                    await EnsureQdrantCollectionAsync(collection, dimension, cancellationToken);
                    break;

                case VectorDatabaseType.Weaviate:
                    await EnsureWeaviateCollectionAsync(collection, dimension, cancellationToken);
                    break;

                case VectorDatabaseType.Pinecone:
                    await EnsurePineconeCollectionAsync(collection, dimension, cancellationToken);
                    break;

                default:
                    _logger.LogWarning("Unsupported database type for collection creation: {DatabaseType}", databaseType);
                    break;
            }

            _logger.LogDebug("Ensured collection exists: {DatabaseType}/{Collection} (dimension: {Dimension})",
                databaseType, collection, dimension);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring collection exists: {DatabaseType}/{Collection}", databaseType, collection);
            throw;
        }
    }

    // MongoDB implementation
    private async Task<List<string>> StoreInMongoDBAsync(List<DocumentChunk> chunks, string collection, CancellationToken cancellationToken)
    {
        try
        {
            var database = _mongoClient.GetDatabase("lexai_preprocessing");
            var mongoCollection = database.GetCollection<VectorDocument>(collection);

            var documents = chunks.Select(chunk => new VectorDocument
            {
                Id = chunk.VectorId ?? Guid.NewGuid().ToString(),
                ChunkId = chunk.Id.ToString(),
                DocumentId = chunk.DocumentId.ToString(),
                Content = chunk.Content,
                Vector = chunk.EmbeddingVector!,
                Metadata = new Dictionary<string, object>
                {
                    ["sequenceNumber"] = chunk.SequenceNumber,
                    ["qualityScore"] = chunk.QualityScore,
                    ["importanceScore"] = chunk.ImportanceScore,
                    ["tokenCount"] = chunk.TokenCount,
                    ["characterCount"] = chunk.CharacterCount,
                    ["chunkType"] = chunk.Type.ToString(),
                    ["keywords"] = chunk.Keywords.ToArray(),
                    ["createdAt"] = DateTime.UtcNow
                }
            }).ToList();

            await mongoCollection.InsertManyAsync(documents, cancellationToken: cancellationToken);

            return documents.Select(d => d.Id).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing vectors in MongoDB collection {Collection}", collection);
            return new List<string>();
        }
    }

    private async Task<IEnumerable<VectorSearchResult>> SearchInMongoDBAsync(float[] queryVector, string collection, int limit, CancellationToken cancellationToken)
    {
        try
        {
            // For now, return empty results as MongoDB vector search requires specific setup
            // In a real implementation, you would use MongoDB Atlas Vector Search
            _logger.LogWarning("MongoDB vector search not implemented yet for collection {Collection}", collection);
            return Enumerable.Empty<VectorSearchResult>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vectors in MongoDB collection {Collection}", collection);
            return Enumerable.Empty<VectorSearchResult>();
        }
    }

    private async Task DeleteFromMongoDBAsync(List<string> vectorIds, string collection, CancellationToken cancellationToken)
    {
        try
        {
            var database = _mongoClient.GetDatabase("lexai_preprocessing");
            var mongoCollection = database.GetCollection<VectorDocument>(collection);

            var filter = Builders<VectorDocument>.Filter.In(d => d.Id, vectorIds);
            await mongoCollection.DeleteManyAsync(filter, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vectors from MongoDB collection {Collection}", collection);
            throw;
        }
    }

    private async Task EnsureMongoDBCollectionAsync(string collection, int dimension, CancellationToken cancellationToken)
    {
        try
        {
            var database = _mongoClient.GetDatabase("lexai_preprocessing");

            // Check if collection exists
            var collections = await database.ListCollectionNamesAsync(cancellationToken: cancellationToken);
            var collectionList = await collections.ToListAsync(cancellationToken);

            if (!collectionList.Contains(collection))
            {
                await database.CreateCollectionAsync(collection, cancellationToken: cancellationToken);
                _logger.LogInformation("Created MongoDB collection: {Collection}", collection);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring MongoDB collection {Collection}", collection);
            throw;
        }
    }

    // Qdrant implementation
    private async Task<List<string>> StoreInQdrantAsync(List<DocumentChunk> chunks, string collection, CancellationToken cancellationToken)
    {
        try
        {
            var vectorIds = new List<string>();
            var points = new List<PointStruct>();

            foreach (var chunk in chunks)
            {
                var vectorId = chunk.VectorId ?? Guid.NewGuid().ToString();
                vectorIds.Add(vectorId);

                var point = new PointStruct
                {
                    Id = new PointId { Uuid = vectorId },
                    Vectors = chunk.EmbeddingVector!
                };

                // Ajouter le payload
                point.Payload.Add("chunk_id", chunk.Id.ToString());
                point.Payload.Add("document_id", chunk.DocumentId.ToString());
                point.Payload.Add("content", chunk.Content ?? "");
                point.Payload.Add("sequence_number", chunk.SequenceNumber);
                point.Payload.Add("quality_score", chunk.QualityScore);
                point.Payload.Add("importance_score", chunk.ImportanceScore);
                point.Payload.Add("token_count", chunk.TokenCount);
                point.Payload.Add("character_count", chunk.CharacterCount);
                point.Payload.Add("chunk_type", chunk.Type.ToString());
                point.Payload.Add("keywords", string.Join(",", chunk.Keywords));
                point.Payload.Add("created_at", DateTime.UtcNow.ToString("O"));

                points.Add(point);
            }

            await _qdrantClient.UpsertAsync(collection, points, cancellationToken: cancellationToken);

            _logger.LogInformation("Successfully stored {Count} vectors in Qdrant collection {Collection}",
                vectorIds.Count, collection);

            return vectorIds;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing vectors in Qdrant collection {Collection}", collection);
            return new List<string>();
        }
    }

    private async Task<List<string>> StoreInWeaviateAsync(List<DocumentChunk> chunks, string collection, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        _logger.LogWarning("Weaviate storage not implemented yet for collection {Collection}", collection);
        return chunks.Select(c => c.VectorId ?? Guid.NewGuid().ToString()).ToList();
    }

    private async Task<List<string>> StoreInPineconeAsync(List<DocumentChunk> chunks, string collection, CancellationToken cancellationToken)
    {
        // Placeholder implementation
        _logger.LogWarning("Pinecone storage not implemented yet for collection {Collection}", collection);
        return chunks.Select(c => c.VectorId ?? Guid.NewGuid().ToString()).ToList();
    }

    private async Task<IEnumerable<VectorSearchResult>> SearchInQdrantAsync(float[] queryVector, string collection, int limit, CancellationToken cancellationToken)
    {
        try
        {
            var searchResult = await _qdrantClient.SearchAsync(
                collection,
                queryVector,
                limit: (ulong)limit,
                cancellationToken: cancellationToken);

            return searchResult.Select(point => new VectorSearchResult
            {
                VectorId = point.Id.Uuid,
                Score = point.Score,
                Metadata = point.Payload.ToDictionary(
                    kvp => kvp.Key,
                    kvp => (object)(kvp.Value.StringValue ?? kvp.Value.ToString()))
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching vectors in Qdrant collection {Collection}", collection);
            return Enumerable.Empty<VectorSearchResult>();
        }
    }

    private async Task<IEnumerable<VectorSearchResult>> SearchInWeaviateAsync(float[] queryVector, string collection, int limit, CancellationToken cancellationToken)
    {
        _logger.LogWarning("Weaviate search not implemented yet for collection {Collection}", collection);
        return Enumerable.Empty<VectorSearchResult>();
    }

    private async Task<IEnumerable<VectorSearchResult>> SearchInPineconeAsync(float[] queryVector, string collection, int limit, CancellationToken cancellationToken)
    {
        _logger.LogWarning("Pinecone search not implemented yet for collection {Collection}", collection);
        return Enumerable.Empty<VectorSearchResult>();
    }

    private async Task DeleteFromQdrantAsync(List<string> vectorIds, string collection, CancellationToken cancellationToken)
    {
        try
        {
            var pointIds = vectorIds.Select(id => new PointId { Uuid = id });

            // Utiliser une approche différente pour la suppression
            foreach (var pointId in pointIds)
            {
                // Simplification temporaire - utiliser l'ID comme ulong
                if (ulong.TryParse(pointId.Uuid, out var id))
                {
                    await _qdrantClient.DeleteAsync(collection, id, cancellationToken: cancellationToken);
                }
            }

            _logger.LogInformation("Successfully deleted {Count} vectors from Qdrant collection {Collection}",
                vectorIds.Count, collection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting vectors from Qdrant collection {Collection}", collection);
            throw;
        }
    }

    private async Task DeleteFromWeaviateAsync(List<string> vectorIds, string collection, CancellationToken cancellationToken)
    {
        _logger.LogWarning("Weaviate deletion not implemented yet for collection {Collection}", collection);
    }

    private async Task DeleteFromPineconeAsync(List<string> vectorIds, string collection, CancellationToken cancellationToken)
    {
        _logger.LogWarning("Pinecone deletion not implemented yet for collection {Collection}", collection);
    }

    private async Task EnsureQdrantCollectionAsync(string collection, int dimension, CancellationToken cancellationToken)
    {
        try
        {
            // Check if collection exists
            var collections = await _qdrantClient.ListCollectionsAsync(cancellationToken: cancellationToken);
            var collectionExists = collections.Any(c => c == collection);

            if (!collectionExists)
            {
                await _qdrantClient.CreateCollectionAsync(
                    collection,
                    new VectorParams { Size = (ulong)dimension, Distance = Distance.Cosine },
                    cancellationToken: cancellationToken);

                _logger.LogInformation("Created Qdrant collection: {Collection} with dimension {Dimension}",
                    collection, dimension);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring Qdrant collection {Collection}", collection);
            throw;
        }
    }

    private async Task EnsureWeaviateCollectionAsync(string collection, int dimension, CancellationToken cancellationToken)
    {
        _logger.LogWarning("Weaviate collection creation not implemented yet for collection {Collection}", collection);
    }

    private async Task EnsurePineconeCollectionAsync(string collection, int dimension, CancellationToken cancellationToken)
    {
        _logger.LogWarning("Pinecone collection creation not implemented yet for collection {Collection}", collection);
    }

    private class VectorDocument
    {
        public string Id { get; set; } = string.Empty;
        public string ChunkId { get; set; } = string.Empty;
        public string DocumentId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public float[] Vector { get; set; } = Array.Empty<float>();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
