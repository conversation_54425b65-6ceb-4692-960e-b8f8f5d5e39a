# RabbitMQ Configuration for LexAI

# Clustering
cluster_formation.peer_discovery_backend = rabbit_peer_discovery_classic_config

# Logging
log.console = true
log.console.level = info
log.file = true
log.file.level = info

# Memory and disk thresholds
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# Connection limits
num_acceptors.tcp = 10
handshake_timeout = 10000
heartbeat = 580

# Management plugin
management.tcp.port = 15672
management.tcp.ip = 0.0.0.0

# Load definitions on startup
management.load_definitions = /etc/rabbitmq/definitions.json

# Default user and vhost
default_vhost = lexai_vhost
default_user = lexai_user
default_pass = lexai_rabbitmq_password_2024!
default_user_tags.administrator = true

# Permissions
default_permissions.configure = .*
default_permissions.read = .*
default_permissions.write = .*

# Queue settings
queue_master_locator = min-masters

# Message store settings
msg_store_file_size_limit = 16777216

# Network settings
tcp_listen_options.backlog = 128
tcp_listen_options.nodelay = true
tcp_listen_options.linger.on = true
tcp_listen_options.linger.timeout = 0

# SSL/TLS (disabled for development)
listeners.ssl = none

# Plugins
plugins.directories = ["/usr/lib/rabbitmq/plugins"]
