2025-06-17 21:12:45.064 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'RefreshToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-17 21:12:45.151 +04:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserPermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-17 21:12:45.162 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-17 21:12:46.348 +04:00 [INF] Executed DbCommand (156ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-17 21:12:46.368 +04:00 [INF] LexAI Identity Service started successfully
2025-06-17 21:12:46.434 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 21:12:46.835 +04:00 [INF] Now listening on: https://localhost:5000
2025-06-17 21:12:46.836 +04:00 [INF] Now listening on: http://localhost:59999
2025-06-17 21:12:47.211 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 21:12:47.214 +04:00 [INF] Hosting environment: Development
2025-06-17 21:12:47.217 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\Identity\LexAI.Identity.API
2025-06-17 21:12:47.862 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5000/ - null null
2025-06-17 21:12:48.039 +04:00 [INF] Request GET / started with correlation ID a65e8b6b-2edd-49e3-ab07-db7ae043e685
2025-06-17 21:12:49.707 +04:00 [INF] Request GET / completed in 1658ms with status 404 (Correlation ID: a65e8b6b-2edd-49e3-ab07-db7ae043e685)
2025-06-17 21:12:49.719 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5000/ - 404 0 null 1870.2219ms
2025-06-17 21:12:49.732 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5000/, Response status code: 404
2025-06-17 21:13:54.107 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - null null
2025-06-17 21:13:54.159 +04:00 [INF] Request OPTIONS /api/auth/refresh started with correlation ID 9c999c62-088f-44df-bedf-97cf34a83751
2025-06-17 21:13:54.171 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:13:54.180 +04:00 [INF] Request OPTIONS /api/auth/refresh completed in 14ms with status 204 (Correlation ID: 9c999c62-088f-44df-bedf-97cf34a83751)
2025-06-17 21:13:54.189 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5000/api/auth/refresh - 204 null null 81.8783ms
2025-06-17 21:13:54.195 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5000/api/auth/refresh - application/json 107
2025-06-17 21:13:54.211 +04:00 [INF] Request POST /api/auth/refresh started with correlation ID 4c8b9aec-79bd-45af-adb1-84b5bfc8b33c
2025-06-17 21:13:54.218 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:13:54.223 +04:00 [INF] Executing endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-17 21:13:54.280 +04:00 [INF] Route matched with {action = "RefreshToken", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.Identity.Application.DTOs.AuthenticationResponseDto]] RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto) on controller LexAI.Identity.API.Controllers.AuthController (LexAI.Identity.API).
2025-06-17 21:13:54.411 +04:00 [INF] Token refresh attempt
2025-06-17 21:13:54.559 +04:00 [INF] Token refresh attempt from IP ::1
2025-06-17 21:13:56.164 +04:00 [INF] Executed DbCommand (101ms) [Parameters=[@__token_0='yKMyC5FIkGoKY1rRpMS5d4LuwOBzxOT0tCkQN6X43f4ePRcLoTmqdB/3NY/E5R/jibhApECFb/yHu0fsBU+gEQ=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-17 21:13:56.784 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-17 21:13:56.882 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT u1."Id", u1."CreatedAt", u1."CreatedBy", u1."DeletedAt", u1."DeletedBy", u1."FailedLoginAttempts", u1."FirstName", u1."IsActive", u1."IsDeleted", u1."IsEmailVerified", u1."IsLocked", u1."LastLoginAt", u1."LastLoginIpAddress", u1."LastName", u1."LockedAt", u1."PasswordHash", u1."PreferredLanguage", u1."ProfilePictureUrl", u1."Role", u1."TimeZone", u1."UpdatedAt", u1."UpdatedBy", u1.xmin, u1."Email", u1."PhoneCountryCode", u1."PhoneNumber", r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."Action", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."ExpiresAt", u0."GrantedAt", u0."GrantedBy", u0."IsDeleted", u0."Permission", u0."Resource", u0."UpdatedAt", u0."UpdatedBy", u0."UserId", u0.xmin
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted") AND u."Id" = @__id_0
    LIMIT 1
) AS u1
LEFT JOIN "RefreshTokens" AS r ON u1."Id" = r."UserId"
LEFT JOIN "UserPermissions" AS u0 ON u1."Id" = u0."UserId"
ORDER BY u1."Id", r."Id"
2025-06-17 21:13:56.958 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__token_0='yKMyC5FIkGoKY1rRpMS5d4LuwOBzxOT0tCkQN6X43f4ePRcLoTmqdB/3NY/E5R/jibhApECFb/yHu0fsBU+gEQ=='], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."ExpiresAt", r."IpAddress", r."IsDeleted", r."IsRevoked", r."RevocationReason", r."RevokedAt", r."RevokedBy", r."Token", r."UpdatedAt", r."UpdatedBy", r."UserAgent", r."UserId", r.xmin, u0."Id", u0."CreatedAt", u0."CreatedBy", u0."DeletedAt", u0."DeletedBy", u0."FailedLoginAttempts", u0."FirstName", u0."IsActive", u0."IsDeleted", u0."IsEmailVerified", u0."IsLocked", u0."LastLoginAt", u0."LastLoginIpAddress", u0."LastName", u0."LockedAt", u0."PasswordHash", u0."PreferredLanguage", u0."ProfilePictureUrl", u0."Role", u0."TimeZone", u0."UpdatedAt", u0."UpdatedBy", u0.xmin, u0."Email", u0."PhoneCountryCode", u0."PhoneNumber"
FROM "RefreshTokens" AS r
INNER JOIN (
    SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeletedAt", u."DeletedBy", u."FailedLoginAttempts", u."FirstName", u."IsActive", u."IsDeleted", u."IsEmailVerified", u."IsLocked", u."LastLoginAt", u."LastLoginIpAddress", u."LastName", u."LockedAt", u."PasswordHash", u."PreferredLanguage", u."ProfilePictureUrl", u."Role", u."TimeZone", u."UpdatedAt", u."UpdatedBy", u.xmin, u."Email", u."PhoneCountryCode", u."PhoneNumber"
    FROM "Users" AS u
    WHERE NOT (u."IsDeleted")
) AS u0 ON r."UserId" = u0."Id"
WHERE r."Token" = @__token_0
LIMIT 1
2025-06-17 21:13:57.309 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@p16='9c566aea-ef2d-4e4a-982a-feae84edca53', @p0='2025-06-14T21:47:06.9234190Z' (DbType = DateTime), @p1=NULL, @p2=NULL (DbType = DateTime), @p3=NULL, @p4='2025-06-21T21:47:06.8788060Z' (DbType = DateTime), @p5='::1', @p6='False', @p7='True', @p8='Token refreshed', @p9='2025-06-17T17:13:56.9672336Z' (Nullable = true) (DbType = DateTime), @p10='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p11='yKMyC5FIkGoKY1rRpMS5d4LuwOBzxOT0tCkQN6X43f4ePRcLoTmqdB/3NY/E5R/jibhApECFb/yHu0fsBU+gEQ==' (Nullable = false), @p12='2025-06-17T17:13:57.1263653Z' (Nullable = true) (DbType = DateTime), @p13=NULL, @p14='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p15='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @p17='809' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "RefreshTokens" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "ExpiresAt" = @p4, "IpAddress" = @p5, "IsDeleted" = @p6, "IsRevoked" = @p7, "RevocationReason" = @p8, "RevokedAt" = @p9, "RevokedBy" = @p10, "Token" = @p11, "UpdatedAt" = @p12, "UpdatedBy" = @p13, "UserAgent" = @p14, "UserId" = @p15
WHERE "Id" = @p16 AND xmin = @p17
RETURNING xmin;
2025-06-17 21:13:57.353 +04:00 [INF] Refresh token updated successfully: "9c566aea-ef2d-4e4a-982a-feae84edca53"
2025-06-17 21:13:57.618 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='ce935b10-268d-45c1-b717-4bc00225888b', @p1='2025-06-17T17:13:57.5987216Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='2025-06-24T17:13:57.5612144Z' (DbType = DateTime), @p6='::1', @p7='False', @p8='False', @p9=NULL, @p10=NULL (DbType = DateTime), @p11=NULL, @p12='rDN+bkB9BKTMNBZWjhbIipDvIYW1szf2XEaYnQN7bocLIHy2IeIXvlP2Z2LlfvdhFcj39vRpHPLkUt3vgUeI1w==' (Nullable = false), @p13=NULL (DbType = DateTime), @p14=NULL, @p15='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', @p16='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "RefreshTokens" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "ExpiresAt", "IpAddress", "IsDeleted", "IsRevoked", "RevocationReason", "RevokedAt", "RevokedBy", "Token", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING xmin;
2025-06-17 21:13:57.635 +04:00 [INF] Refresh token added successfully: "ce935b10-268d-45c1-b717-4bc00225888b"
2025-06-17 21:13:57.640 +04:00 [INF] Token refreshed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:13:57.654 +04:00 [INF] Token refresh successful
2025-06-17 21:13:57.683 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.Identity.Application.DTOs.AuthenticationResponseDto'.
2025-06-17 21:13:57.762 +04:00 [INF] Executed action LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API) in 3465.7703ms
2025-06-17 21:13:57.772 +04:00 [INF] Executed endpoint 'LexAI.Identity.API.Controllers.AuthController.RefreshToken (LexAI.Identity.API)'
2025-06-17 21:13:57.780 +04:00 [INF] Request POST /api/auth/refresh completed in 3562ms with status 200 (Correlation ID: 4c8b9aec-79bd-45af-adb1-84b5bfc8b33c)
2025-06-17 21:13:57.800 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5000/api/auth/refresh - 200 null application/json; charset=utf-8 3605.69ms
