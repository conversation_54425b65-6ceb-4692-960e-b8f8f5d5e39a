2025-06-15 02:25:36.581 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-15 02:25:36.739 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-15 02:25:37.050 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-15 02:25:37.053 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-15 02:25:37.667 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 02:25:37.686 +04:00 [INF] Hosting environment: Development
2025-06-15 02:25:37.688 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-15 02:25:38.357 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-15 02:25:38.685 +04:00 [INF] Request GET / started with correlation ID 25613d15-fb74-4642-8b0d-c55e2c60bc2c
2025-06-15 02:25:38.789 +04:00 [INF] Request GET / completed in 95ms with status 404 (Correlation ID: 25613d15-fb74-4642-8b0d-c55e2c60bc2c)
2025-06-15 02:25:38.797 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 445.8144ms
2025-06-15 02:25:38.819 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-15 02:26:23.524 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-15 02:26:23.628 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID dbc85efa-3319-46aa-9cae-d735573e7a65
2025-06-15 02:26:23.636 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:26:23.640 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 6ms with status 204 (Correlation ID: dbc85efa-3319-46aa-9cae-d735573e7a65)
2025-06-15 02:26:23.644 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 119.9676ms
2025-06-15 02:26:23.648 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundarymvTP2cK6JBQEgTVE 599138
2025-06-15 02:26:23.653 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 6dc0d5e9-8610-43af-9aef-5ea7ce24f3d8
2025-06-15 02:26:23.655 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:26:23.708 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 02:26:23.715 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-15 02:26:23.737 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 02:26:24.040 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 02:26:24.079 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 02:26:25.452 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 02:26:25.507 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_Paris2.pdf
2025-06-15 02:26:25.520 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_Paris2.pdf
2025-06-15 02:26:25.547 +04:00 [INF] Starting comprehensive document analysis for: Cdd_test_Paris2.pdf
2025-06-15 02:26:25.556 +04:00 [INF] Document stored successfully: b7448e11e5e6118546a1eb3fb4e3c137b05f22c05618fbbb27d2aa796b18bd32 -> ./storage/documents\b7448e11e5e6118546a1eb3fb4e3c137b05f22c05618fbbb27d2aa796b18bd32.pdf
2025-06-15 02:26:25.559 +04:00 [INF] Document stored with hash: b7448e11e5e6118546a1eb3fb4e3c137b05f22c05618fbbb27d2aa796b18bd32 at ./storage/documents\b7448e11e5e6118546a1eb3fb4e3c137b05f22c05618fbbb27d2aa796b18bd32.pdf
2025-06-15 02:26:25.579 +04:00 [INF] Starting Azure Document Intelligence extraction for document type: pdf
2025-06-15 02:26:30.959 +04:00 [INF] Azure extraction completed successfully. Processing time: 5373ms, Pages: 2
2025-06-15 02:26:30.963 +04:00 [INF] Starting clause analysis for document type: pdf
2025-06-15 02:26:36.570 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "clauses": [\n    {\n      "clauseText": "Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activi
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:38.797 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause indique que le contrat est un CDD conclu conformément à l'article L1242-2 du Code du travail, ce qui précise sa base légale pour un accroissement temporaire d'activité. Cep
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:40.465 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise la date de début du contrat au 1er juillet 2025 et sa durée de 6 mois, se terminant le 31 décembre 2025 inclus. Elle est claire et concise, permettant aux parties d
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:42.226 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause définit la fonction de M. Jean Luc en tant que Chargé de gestion des risques numériques, sous la supervision du Chef de département sécurité. Elle précise le poste occupé m
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:44.224 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise que le salarié doit exercer ses fonctions au siège ou dans un autre lieu désigné par l'employeur dans un rayon de 20 km, ce qui limite la mobilité géographique. Ell
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:46.103 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise que la rémunération brute mensuelle est de 1200 euros, versée à terme échu, conformément aux modalités du Code du travail. Elle est claire sur le montant, la périod
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:47.969 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause établit une durée de travail hebdomadaire de 48 heures réparties du lundi au samedi, avec des horaires déterminés par l'employeur en fonction de l'activité. Elle ne précise
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:49.942 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause informe le salarié des risques professionnels liés à son poste, notamment l'exposition aux écrans, la charge mentale, la manipulation de données confidentielles et les risq
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:51.879 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause impose au salarié une obligation de confidentialité couvrant à la fois la durée du contrat et une période post-contractuelle indéfinie, ce qui peut poser des problèmes de c
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:54.496 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "Cette clause limite la possibilité de rupture anticipée du contrat aux seuls cas prévus par la loi, ce qui offre une certaine sécurité juridique pour l'employeur et l'employé. Cepend
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:56.546 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause prévoit la fin automatique du contrat à une date fixe sans nécessiter de notification, ce qui simplifie la procédure mais limite la flexibilité pour les parties. L'absence 
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:26:56.677 +04:00 [INF] Clause analysis completed. Found 10 clauses
2025-06-15 02:26:56.681 +04:00 [INF] Starting risk assessment for document type: pdf
2025-06-15 02:26:59.191 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "risks": [\n    {\n      "riskType": "Risques structurels",\n      "description": "Organisation potentiellement inadéquate pour gérer les risques liés à la gestion des risques numériques, notamme
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:02.277 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "risques": {\n    "conformite_rgpd": {\n      "risques_identifies": [\n        "Manipulation et traitement de données confidentielles sans mention explicite de mesures de sécurité appropriées",\n
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:02.407 +04:00 [INF] Risk assessment completed. Identified 3 risks
2025-06-15 02:27:02.410 +04:00 [INF] Generating recommendations for document type: pdf
2025-06-15 02:27:04.497 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "type": "Risk Mitigation",\n      "title": "Renforcer la conformité réglementaire en cybersécurité et protection des données",\n      "description": "Mettre en 
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:08.499 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "clause": "nature du contrat",\n      "recommendation": "Préciser la durée exacte du contrat, les motifs précis justifiant l'utilisation d'un CDD selon l'articl
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:11.411 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "category": "Structure",\n      "description": "Organiser le contrat en sections clairement délimitées avec des titres en gras ou en majuscules pour faciliter l
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:16.388 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "type": "Compliance",\n      "description": "Vérifier que la mention de l'article L1242-2 du Code du travail est conforme à la législation en vigueur et qu'elle
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:16.586 +04:00 [INF] Prioritizing 30 recommendations
2025-06-15 02:27:16.599 +04:00 [INF] Generated 3 recommendations
2025-06-15 02:27:21.600 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "entities": [\n    {\n      "text": "LexBot S.A.R.L.",\n      "type": "ORGANIZATION",\n      "startPosition": 15,\n      "endPosition": 30,\n      "confidence": 0.95,\n      "normalizedValue": "L
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:22.714 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "citations": [\n    {\n      "title": "article L1242-2 du Code du travail",\n      "source": "Code du travail",\n      "url": "https://www.legifrance.gouv.fr/codes/id/LEGIARTI000006902747/",\n   
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:24.359 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "executiveSummary": "Ce contrat de travail à durée déterminée de 6 mois entre LexBot S.A.R.L. et M. Jean Luc définit les modalités d'emploi pour un poste de Chargé de gestion des risques numériqu
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 02:27:31.339 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-15 02:27:31.369 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 02:27:31.378 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 02:27:31.382 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 02:27:31.385 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 02:27:32.015 +04:00 [INF] Executed DbCommand (116ms) [Parameters=[@p0='51764e83-da50-4569-8184-891ce018b949', @p1='Analyse juridique complète du Contrat de Travail à Durée Déterminée (CDD)

---

**Synthèse des clauses importantes**

1. **Nature du contrat (Article 1)**  
- Contrat à durée déterminée (CDD) pour accroissement temporaire d'activité, conformément à l’article L1242-2 du Code du travail.  
- Important : la nature précise du motif (augmentation temporaire d’activité) est mentionnée, ce qui est essentiel pour la légalité du CDD.

2. **Durée et date de début/fin (Article 2)**  
- Du 1er juillet 2025 au 31 décembre 2025 (6 mois).  
- La date de fin est claire, ce qui facilite la gestion administrative et la conformité.

3. **Poste occupé (Article 3)**  
- Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.  
- La description du poste est précise, permettant d’évaluer la conformité avec la qualification du salarié.

4. **Lieu de travail (Article 4)**  
- Siège ou autre lieu dans un rayon de 20 km.  
- Flexibilité géographique, mais à préciser si nécessaire dans une annexe ou un avenant.

5. **Rémunération (Article 5)**  
- 1 200 euros brut mensuel, versé à terme échu.  
- Modalités conformes au Code du travail.

6. **Durée du travail (Article 6)**  
- 48 heures par semaine, du lundi au samedi, horaires définis par l’employeur.  
- La durée hebdomadaire dépasse la limite légale (35 heures), ce qui nécessite une attention particulière pour la conformité avec la réglementation sur les heures supplémentaires et la majoration.

7. **Risques liés au poste (Article 7)**  
- Exposition aux écrans, charge mentale, manipulation de données confidentielles, risques psychosociaux.  
- L’employeur propose un accompagnement médical, ce qui est conforme à ses obligations de sécurité.

8. **Clause de confidentialité (Article 8)**  
- Engagement de confidentialité pendant et après le contrat.  
- Clause essentielle pour la protection des informations sensibles.

9. **Rupture anticipée (Article 9)**  
- Possible en cas de faute grave, force majeure, accord commun ou embauche en CDI.  
- Conformité avec la législation, mais il serait prudent de préciser les modalités de rupture anticipée.

10. **Fin du contrat (Article 10)**  
- Fin automatique au 31 décembre 2025, sans renouvellement prévu.  
- Clarté sur la fin du contrat, évitant toute ambiguïté.

---

**Risques identifiés**

1. **Non-conformité potentielle à la durée hebdomadaire de travail**  
- La durée de 48 heures par semaine peut dépasser la limite légale (35 heures), sauf dérogation ou accord collectif spécifique.  
- Risque de contentieux si cette clause n’est pas justifiée ou si les heures supplémentaires ne sont pas correctement rémunérées.

2. **Absence de clauses précises sur la rupture anticipée**  
- La mention « sauf faute grave, force majeure, accord » est standard, mais il serait prudent d’ajouter des modalités précises pour éviter des litiges.

3. **Manque d’informations sur le renouvellement ou la possibilité de prolongation**  
- L’absence de clause de renouvellement ou de possibilité de prolongation pourrait poser problème si la relation doit durer au-delà de la date initiale.

---

**Recommandations**

1. **Vérifier la conformité des horaires de travail**  
- Assurez-vous que la durée hebdomadaire de 48 heures est justifiée par un accord collectif ou une dérogation.  
- Mettre en place une gestion rigoureuse des heures supplémentaires, avec majoration et déclaration.

2. **Préciser les modalités de rupture anticipée**  
- Ajouter une clause détaillée sur la procédure en cas de rupture anticipée (préavis, indemnités éventuelles).

3. **Clarifier la possibilité de renouvellement ou de prolongation**  
- Si une prolongation est envisagée, prévoir une clause spécifique pour éviter toute ambiguïté.

4. **Vérifier la conformité avec la réglementation sur les CDD**  
- S’assurer que le motif d’accroissement temporaire d’activité est bien justifié et documenté.

5. **Inclure une clause relative à la période d’essai** (si applicable)  
- La présence ou absence d’une période d’essai doit être précisée pour respecter la réglementation.

6. **Respecter les obligations en matière de santé et sécurité**  
- Continuer à assurer un suivi médical et mettre en place des mesures de prévention adaptées.

---

**Évaluation globale**

Ce contrat semble globalement conforme aux exigences légales pour un CDD, avec une description claire du poste, de la durée, et des obligations du salarié. Cependant, la durée hebdomadaire de 48 heures nécessite une vérification approfondie pour éviter toute infraction au Code du travail. La clause de confidentialité et la gestion des risques liés au poste sont bien intégrées.

---

**Conseils juridiques**

- **Vérifier la conformité des horaires** : Si la durée de 48 heures est exceptionnelle, obtenir une dérogation ou ajuster la clause pour respecter la législation en vigueur.
- **Documenter le motif du CDD** : Conserver toutes les preuves justifiant le motif d’accroissement temporaire d’activité.
- **Mettre à jour le contrat si nécessaire** : Ajouter des clauses sur la période d’essai, la possibilité de renouvellement, ou de prolongation.
- **Respecter les obligations en matière de santé et sécurité** : Continuer à suivre les recommandations du médecin du travail et assurer un environnement de travail sécurisé.

---

**Conclusion**

Ce contrat de CDD est bien structuré et couvre les aspects essentiels, mais il doit être vérifié notamment sur la durée du travail pour assurer sa conformité avec la législation. En suivant les recommandations, l’employeur pourra limiter les risques juridiques et assurer une relation de travail conforme aux exigences légales.

---

N'hésitez pas à consulter un avocat spécialisé en droit du travail pour une analyse approfondie et adaptée à votre situation spécifique.' (Nullable = false), @p2='2025-06-14T22:27:30.8625110Z' (DbType = DateTime), @p3='0.9499999999999998', @p4='2025-06-14T22:27:30.8625624Z' (DbType = DateTime), @p5=NULL, @p6=NULL (DbType = DateTime), @p7=NULL, @p8='b7448e11e5e6118546a1eb3fb4e3c137b05f22c05618fbbb27d2aa796b18bd32' (Nullable = false), @p9='Cdd_test_Paris2.pdf' (Nullable = false), @p10='./storage/documents\b7448e11e5e6118546a1eb3fb4e3c137b05f22c05618fbbb27d2aa796b18bd32.pdf' (Nullable = false), @p11='pdf' (Nullable = false), @p12='0', @p13='CONTRAT DE TRAVAIL À DURÉE DÉTERMINÉE (CDD)
Entre les soussignés :
L'employeur
LexBot S.A.R.L., au capital de 50 000 euros, dont le siège social est situé au 12, Rue de l'Intelligence Artificielle, 75001 Paris, immatriculée au RCS de Paris sous le numéro 882 000 123, Représentée par Mme Clara Dupont, en sa qualité de Directrice Générale, Ci-après dénommée « l'Employeur »
Et
M. Jean Luc, né le 12 mars 1989 à Lyon (France), demeurant au 8, rue des Lilas, 69001 Lyon, de nationalité française, Ci-après dénommé « le Salarié »
Article 1 - Nature du contrat
Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.
Article 2 - Date de début et de fin
Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.
Article 3 - Poste occupé
M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.
Article 4 - Lieu de travail
Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.
Article 5 - Rémunération
La rémunération brute mensuelle sera de 1 200 euros, versée à terme échu, selon les modalités prévues par le Code du travail.
1 | Page KEVIN WILFRIED
Article 6 - Durée du travail
La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.
Article 7 - Risques liés au poste
Le salarié est informé que le poste comporte les risques professionnels suivants :
· Exposition prolongée aux écrans (fatigue visuelle, TMS)
· Charge mentale importante liée à la gestion des risques informatiques
· Manipulation de données confidentielles (obligation de vigilance accrue)
· Risques psychosociaux en cas d'incident majeur de cybersécurité
L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.
Article 8 - Clause de confidentialité
Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.
Article 9 - Rupture anticipée
Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi : faute grave, force majeure, accord commun ou embauche en CDI.
Article 10 - Fin du contrat
Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.
Fait à Paris, le 14 juin 2025
En deux exemplaires originaux.
Signature de l'Employeur (LexBot)
Signature :
Signature du Salarié (Jean Luc)
Signature :
2 | Page KEVIN WILFRIED' (Nullable = false), @p14='False', @p15='lexai-gpt-4.1-nano' (Nullable = false), @p16='65307', @p17='Completed' (Nullable = false), @p18='1452', @p19='2025-06-14T22:27:30.8626260Z' (Nullable = true) (DbType = DateTime), @p20=NULL, @p21='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_analysis_results ("Id", "AnalysisContent", "AnalyzedAt", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentHash", "DocumentName", "DocumentStoragePath", "DocumentType", "EstimatedCost", "ExtractedText", "IsDeleted", "ModelUsed", "ProcessingTimeMs", "Status", "TokensUsed", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-15 02:27:32.047 +04:00 [INF] Analysis saved to database with ID: "51764e83-da50-4569-8184-891ce018b949"
2025-06-15 02:27:32.106 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='e14f6db6-ef67-4fd9-a04b-808e694e10ba', @p1='La clause indique que le contrat est un CDD conclu conformément à l'article L1242-2 du Code du travail, ce qui précise sa base légale pour un accroissement temporaire d'activité. Cependant, elle manque de détails sur la durée précise, les motifs spécifiques d'utilisation de ce type de contrat, ainsi que sur les modalités de renouvellement ou de fin. La formulation est claire quant à la nature du contrat, mais pourrait bénéficier de précisions supplémentaires pour éviter toute ambiguïté.' (Nullable = false), @p2='Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.' (Nullable = false), @p3='nature du contrat' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.0522889Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='232', @p11='False', @p12='Medium' (Nullable = false), @p13='132', @p14='Préciser la durée exacte du contrat, les motifs précis d'utilisation du CDD conformément à l'article L1242-2, et les modalités de renouvellement ou de fin.', @p15='["contrat \u00E0 dur\u00E9e d\u00E9termin\u00E9e","Code du travail","accroissement temporaire d\u0027activit\u00E9"]' (Nullable = false), @p16='2025-06-14T22:27:32.0522894Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.116 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='c8354a51-0ddb-4529-89cd-2bcc8f6d8598', @p1='La clause précise la date de début du contrat au 1er juillet 2025 et sa durée de 6 mois, se terminant le 31 décembre 2025 inclus. Elle est claire et concise, permettant aux parties de connaître précisément la période d'application du contrat. Cependant, elle ne mentionne pas les modalités de renouvellement ou de résiliation anticipée, ce qui pourrait poser problème en cas de changement de circonstances. La formulation est standard et généralement conforme aux pratiques contractuelles.' (Nullable = false), @p2='Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.' (Nullable = false), @p3='date de début et de fin' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1125780Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='290', @p11='False', @p12='Low' (Nullable = false), @p13='233', @p14='Ajouter une clause précisant les conditions de renouvellement ou de résiliation anticipée pour renforcer la clarté et la flexibilité du contrat.', @p15='["date","dur\u00E9e","clart\u00E9"]' (Nullable = false), @p16='2025-06-14T22:27:32.1125783Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.125 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='236ff6bf-f613-488a-bd3b-dbecd896bd28', @p1='La clause définit la fonction de M. Jean Luc en tant que Chargé de gestion des risques numériques, sous la supervision du Chef de département sécurité. Elle précise le poste occupé mais manque de détails sur la durée, les modalités d'exercice, la rémunération ou les conditions spécifiques. La formulation est claire mais sommaire, ce qui peut poser des problèmes en cas de litige ou d'interprétation ultérieure.' (Nullable = false), @p2='M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.' (Nullable = false), @p3='poste occupé' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1206380Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='370', @p11='False', @p12='Medium' (Nullable = false), @p13='291', @p14='Inclure des détails sur la durée du poste, les responsabilités précises, la rémunération, et les modalités de supervision pour renforcer la clarté et la sécurité juridique.', @p15='["emploi","clart\u00E9","responsabilit\u00E9s","conformit\u00E9"]' (Nullable = false), @p16='2025-06-14T22:27:32.1206382Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.132 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='10ef835b-909d-4b71-8432-61b74985d479', @p1='La clause précise que le salarié doit exercer ses fonctions au siège ou dans un autre lieu désigné par l'employeur dans un rayon de 20 km, ce qui limite la mobilité géographique. Elle est relativement claire sur le lieu de travail, mais pourrait manquer de détails concernant la procédure de désignation ou la flexibilité en cas de nécessité. La clause semble équilibrée en termes d'obligation pour le salarié, mais pourrait poser des problèmes si le lieu de travail change fréquemment ou de manière imprévisible.' (Nullable = false), @p2='Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.' (Nullable = false), @p3='lieu de travail' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1297064Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='448', @p11='False', @p12='Medium' (Nullable = false), @p13='371', @p14='Le salarié exercera ses fonctions principalement au siège de l'entreprise ou dans tout autre lieu désigné par l'employeur dans un rayon de 20 km, sous réserve de notification préalable. Toute modification du lieu de travail devra être justifiée et raisonnable.', @p15='["lieu de travail","mobilit\u00E9","droit du travail"]' (Nullable = false), @p16='2025-06-14T22:27:32.1297067Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.140 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='80ab5e5d-bed2-4b14-860b-becee9f5d20b', @p1='La clause précise que la rémunération brute mensuelle est de 1200 euros, versée à terme échu, conformément aux modalités du Code du travail. Elle est claire sur le montant, la périodicité, et la référence légale, ce qui facilite la compréhension et la conformité. Cependant, elle ne détaille pas les modalités spécifiques de paiement, telles que la date précise de versement ou les éventuelles déductions, ce qui pourrait entraîner des ambiguïtés ou des malentendus. La mention du Code du travail garantit une conformité légale générale, mais l'absence de précisions supplémentaires peut poser des risques en cas de litige.' (Nullable = false), @p2='La rémunération brute mensuelle sera de 1 200 euros, versée à terme échu, selon les modalités prévues par le Code du travail.' (Nullable = false), @p3='rémunération' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1367475Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='529', @p11='False', @p12='Low' (Nullable = false), @p13='449', @p14='La clause pourrait être complétée par des précisions sur la date exacte de versement, les modalités de paiement (virement, chèque, etc.), et les éventuelles retenues ou déductions applicables.', @p15='["r\u00E9mun\u00E9ration","paiement","conformit\u00E9","clart\u00E9"]' (Nullable = false), @p16='2025-06-14T22:27:32.1367478Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.148 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='55fdf7aa-08a0-4538-9b91-3685c5c6769a', @p1='La clause établit une durée de travail hebdomadaire de 48 heures réparties du lundi au samedi, avec des horaires déterminés par l'employeur en fonction de l'activité. Elle ne précise pas la répartition exacte des heures, ni si des heures supplémentaires sont possibles ou rémunérées, ni si la durée maximale respecte la législation en vigueur. La flexibilité laissée à l'employeur peut entraîner des risques pour la santé des salariés et des ambiguïtés sur leurs droits.' (Nullable = false), @p2='La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.' (Nullable = false), @p3='durée du travail' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1452658Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='629', @p11='False', @p12='Medium' (Nullable = false), @p13='530', @p14='Préciser la répartition quotidienne des heures, indiquer si des heures supplémentaires sont possibles, leur rémunération, et assurer la conformité avec la législation locale sur la durée maximale de travail.', @p15='["dur\u00E9e du travail","l\u00E9gislation","risque juridique"]' (Nullable = false), @p16='2025-06-14T22:27:32.1452660Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.157 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='79d15b70-f9db-4a2d-8b65-a85987428669', @p1='La clause informe le salarié des risques professionnels liés à son poste, notamment l'exposition aux écrans, la charge mentale, la manipulation de données confidentielles et les risques psychosociaux en cas d'incident de cybersécurité. Elle mentionne également les mesures d'accompagnement proposées par l'entreprise. La formulation est claire et couvre plusieurs aspects importants, mais pourrait bénéficier de précisions sur la nature des risques et les mesures concrètes d'atténuation.' (Nullable = false), @p2='Le salarié est informé que le poste comporte les risques professionnels suivants: Exposition prolongée aux écrans (fatigue visuelle, TMS), Charge mentale importante liée à la gestion des risques informatiques, Manipulation de données confidentielles (obligation de vigilance accrue), Risques psychosociaux en cas d'incident majeur de cybersécurité. L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.' (Nullable = false), @p3='risques liés au poste' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1533182Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='727', @p11='False', @p12='Medium' (Nullable = false), @p13='630', @p14='Préciser les mesures concrètes de prévention et d'accompagnement, ainsi que la nature exacte des risques pour renforcer la clarté et la transparence.', @p15='["risques professionnels","obligation d\u0027information","sant\u00E9 et s\u00E9curit\u00E9","cybers\u00E9curit\u00E9"]' (Nullable = false), @p16='2025-06-14T22:27:32.1533185Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.166 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='03c20fc5-fbc6-4664-85af-afb2e8bf93ec', @p1='La clause impose au salarié une obligation de confidentialité couvrant à la fois la durée du contrat et une période post-contractuelle indéfinie, ce qui peut poser des problèmes de clarté et d'équilibre. La formulation est concise mais manque de précisions sur la nature des informations protégées, les modalités d'application, et les éventuelles exceptions. Elle pourrait également limiter excessivement la liberté du salarié après la fin du contrat, notamment si aucune période déterminée n'est spécifiée.' (Nullable = false), @p2='Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.' (Nullable = false), @p3='clause de confidentialité' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1619965Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='798', @p11='False', @p12='Medium' (Nullable = false), @p13='728', @p14='Clarifier la durée de l'obligation après la fin du contrat (par exemple, 2 ou 5 ans), définir explicitement ce qui constitue une information confidentielle, et préciser les exceptions légales ou réglementaires à la confidentialité.', @p15='["confidentialit\u00E9","droit du travail","clart\u00E9","\u00E9quit\u00E9"]' (Nullable = false), @p16='2025-06-14T22:27:32.1619967Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.173 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='7508cfcd-1bb9-4796-8343-e5b3449abc9c', @p1='Cette clause limite la possibilité de rupture anticipée du contrat aux seuls cas prévus par la loi, ce qui offre une certaine sécurité juridique pour l'employeur et l'employé. Cependant, elle manque de précision concernant la définition de certains termes comme 'faute grave' ou 'accord commun', ce qui pourrait entraîner des ambiguïtés ou des litiges. La mention de 'embauche en CDI' comme motif de rupture anticipée est inhabituelle, car elle semble indiquer qu'une embauche en CDI pourrait justifier une rupture, ce qui pourrait être confus ou inapproprié selon le contexte. La clause est relativement claire dans sa formulation, mais pourrait bénéficier d'une clarification pour éviter toute interprétation divergente. Sur le plan de l'équité, elle semble équilibrée en limitant la rupture anticipée à des cas légaux, mais la mention de l'embauche en CDI pourrait poser problème si elle n'est pas contextualisée. En termes de conformité légale, la clause semble respecter le cadre général du droit du travail, mais la référence à 'embauche en CDI' pourrait nécessiter une vérification pour s'assurer qu'elle ne contrevient pas à la législation locale ou aux conventions collectives applicables.' (Nullable = false), @p2='Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi: faute grave, force majeure, accord commun ou embauche en CDI.' (Nullable = false), @p3='rupture anticipée' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1705184Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='887', @p11='False', @p12='Medium' (Nullable = false), @p13='799', @p14='Clarifier la notion d'embauche en CDI ou la supprimer si elle n'est pas pertinente, et préciser les définitions de 'faute grave' et 'accord commun' pour éviter toute ambiguïté.', @p15='["rupture anticip\u00E9e","clause contractuelle","droit du travail","risque juridique"]' (Nullable = false), @p16='2025-06-14T22:27:32.1705186Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.182 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='912d57d0-491c-4ddb-a873-524d05fcffeb', @p1='La clause prévoit la fin automatique du contrat à une date fixe sans nécessiter de notification, ce qui simplifie la procédure mais limite la flexibilité pour les parties. L'absence de renouvellement explicite clarifie l'intention de ne pas prolonger le contrat, mais pourrait poser problème si une partie souhaite prolonger ou négocier une nouvelle entente. La formulation est concise mais pourrait manquer de précisions sur les conditions en cas de modifications ou de situations exceptionnelles.' (Nullable = false), @p2='Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.' (Nullable = false), @p3='fin du contrat' (Nullable = false), @p4='0.95', @p5='2025-06-14T22:27:32.1793838Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='51764e83-da50-4569-8184-891ce018b949', @p10='958', @p11='False', @p12='Medium' (Nullable = false), @p13='888', @p14='Le contrat prendra automatiquement fin le 31 décembre 2025. Aucun renouvellement n'est prévu, sauf accord écrit préalable des parties. Toute partie souhaitant prolonger le contrat devra en faire la demande au moins 30 jours avant la date de fin.', @p15='["fin de contrat","clause automatique","non-renouvellement"]' (Nullable = false), @p16='2025-06-14T22:27:32.1793841Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-15 02:27:32.232 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='72d63232-78b1-40de-8ebd-154819e4cfe4', @p1='["Article 7 - Risques li\u00E9s au poste","Article 8 - Clause de confidentialit\u00E9"]' (Nullable = false), @p2='2025-06-14T22:27:32.1884092Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Non-respect potentiel des obligations légales et réglementaires relatives à la protection des données personnelles et à la cybersécurité, notamment en lien avec la manipulation de données confidentielles et la gestion des risques informatiques.' (Nullable = false), @p7='51764e83-da50-4569-8184-891ce018b949', @p8='Sanctions légales, amendes, ou poursuites pour non-conformité, ainsi qu'une atteinte à la réputation de l'entreprise.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.7', @p12='Global: Risques de conformité' (Nullable = false), @p13='Critical' (Nullable = false), @p14='2025-06-14T22:27:32.1884100Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-15 02:27:32.244 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='503e3e3a-c1b6-4e7e-bca5-8f69cfd9d6f0', @p1='["Article 7 - Risques li\u00E9s au poste"]' (Nullable = false), @p2='2025-06-14T22:27:32.2385017Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Organisation potentiellement inadéquate pour gérer les risques liés à la gestion des risques numériques, notamment en termes de ressources, de formation ou de processus internes, ce qui peut entraîner des défaillances dans la prévention ou la réponse aux incidents.' (Nullable = false), @p7='51764e83-da50-4569-8184-891ce018b949', @p8='Défaillance dans la gestion des risques informatiques pouvant entraîner des incidents majeurs, pertes financières ou atteinte à la réputation de l'entreprise.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.6', @p12='Global: Risques structurels' (Nullable = false), @p13='High' (Nullable = false), @p14='2025-06-14T22:27:32.2385020Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-15 02:27:32.251 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='1197ebf3-3304-48f4-b460-56c2c9731153', @p1='["Article 6 - Dur\u00E9e du travail","Article 7 - Risques li\u00E9s au poste"]' (Nullable = false), @p2='2025-06-14T22:27:32.2484699Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Risques liés à la surcharge de travail, notamment avec une durée hebdomadaire de 48 heures, pouvant conduire à l'épuisement professionnel, erreurs ou baisse de performance.' (Nullable = false), @p7='51764e83-da50-4569-8184-891ce018b949', @p8='Diminution de la productivité, erreurs opérationnelles, et risques psychosociaux pour le salarié.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.65', @p12='Global: Risques opérationnels' (Nullable = false), @p13='Medium' (Nullable = false), @p14='2025-06-14T22:27:32.2484702Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-15 02:27:32.287 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='fb21d09c-be0d-418a-9969-0241ef69224d', @p1='2025-06-14T22:27:32.2568890Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Mettre en place un cadre de conformité robuste en assurant la conformité aux lois telles que le RGPD, en réalisant des audits réguliers et en mettant à jour les politiques internes pour garantir la gestion adéquate des données personnelles et la sécurité informatique.' (Nullable = false), @p6='51764e83-da50-4569-8184-891ce018b949', @p7='False', @p8='Règlement Général sur la Protection des Données (RGPD), Code de la cybersécurité', @p9='Critical' (Nullable = false), @p10='["Article 5 RGPD","Article L.312-1 du Code de la cybers\u00E9curit\u00E9"]' (Nullable = false), @p11='Effectuer un audit de conformité, former le personnel aux obligations légales, et déployer des politiques de sécurité actualisées.', @p12='Renforcer la conformité réglementaire en cybersécurité et protection des données' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-14T22:27:32.2568896Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-15 02:27:32.297 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='0c2bc443-c8ee-4bb7-a469-2a8ded3b802a', @p1='2025-06-14T22:27:32.2942143Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Adapter la structure organisationnelle en créant une équipe dédiée à la gestion des risques numériques, en formant régulièrement le personnel et en définissant des processus internes clairs pour la prévention et la réponse aux incidents.' (Nullable = false), @p6='51764e83-da50-4569-8184-891ce018b949', @p7='False', @p8='Directive NIS2, recommandations de l'ANSSI', @p9='High' (Nullable = false), @p10='["Directive NIS2, Article 21","Recommandation de l\u0027ANSSI sur la gouvernance de la cybers\u00E9curit\u00E9"]' (Nullable = false), @p11='Désigner un responsable de la sécurité des systèmes d'information (RSSI), mettre en place un plan de formation continue, et élaborer des procédures d'intervention en cas d'incident.', @p12='Renforcer l'organisation interne pour la gestion des risques numériques' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-14T22:27:32.2942146Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-15 02:27:32.543 +04:00 [ERR] Error saving related entities for analysis: "51764e83-da50-4569-8184-891ce018b949"
System.ArgumentException: Must specify valid information for parsing in the string. (Parameter 'value')
   at System.Enum.ThrowInvalidEmptyParseArgument()
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.Parse[TEnum](String value, Boolean ignoreCase)
   at LexAI.DocumentAnalysis.Infrastructure.Services.DocumentAnalysisService.SaveRelatedEntitiesAsync(Guid analysisId, List`1 clauses, List`1 risks, List`1 recommendations, List`1 entities, List`1 citations, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Infrastructure\Services\DocumentAnalysisService.cs:line 729
2025-06-15 02:27:32.551 +04:00 [INF] Related entities saved for analysis: "51764e83-da50-4569-8184-891ce018b949"
2025-06-15 02:27:32.557 +04:00 [INF] Document analysis completed successfully. Processing time: 65307ms, Tokens: 1452
2025-06-15 02:27:32.613 +04:00 [INF] Document analysis completed successfully for document: Cdd_test_Paris2.pdf, Analysis ID: "51764e83-da50-4569-8184-891ce018b949"
2025-06-15 02:27:32.616 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "51764e83-da50-4569-8184-891ce018b949"
2025-06-15 02:27:32.625 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-15 02:27:32.639 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 68897.6161ms
2025-06-15 02:27:32.644 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-15 02:27:32.648 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 68993ms with status 200 (Correlation ID: 6dc0d5e9-8610-43af-9aef-5ea7ce24f3d8)
2025-06-15 02:27:32.666 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-15 02:27:32.681 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 69032.0825ms
2025-06-15 02:27:32.682 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 174e6d01-3108-4a43-a0aa-1fdbfb5e01f8
2025-06-15 02:27:32.688 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:27:32.689 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: 174e6d01-3108-4a43-a0aa-1fdbfb5e01f8)
2025-06-15 02:27:32.693 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 27.4382ms
2025-06-15 02:27:32.698 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-15 02:27:32.705 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 22d8287e-9597-4027-b750-1d24493fbfee
2025-06-15 02:27:32.708 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:27:32.711 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 02:27:32.712 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 02:27:32.716 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 02:27:32.718 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 02:27:32.720 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 02:27:32.722 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 02:27:32.729 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:27:32.734 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:27:33.177 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-15 02:27:33.286 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-15 02:27:33.332 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-15 02:27:33.360 +04:00 [INF] Retrieved 3 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:27:33.364 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-15 02:27:33.369 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 651.6577ms
2025-06-15 02:27:33.372 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 02:27:33.374 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 665ms with status 200 (Correlation ID: 22d8287e-9597-4027-b750-1d24493fbfee)
2025-06-15 02:27:33.378 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 679.2948ms
2025-06-15 02:29:31.898 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-15 02:29:31.907 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 14e2b679-fbeb-413c-9935-a35dcba8b8db
2025-06-15 02:29:31.912 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:29:31.916 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 3ms with status 204 (Correlation ID: 14e2b679-fbeb-413c-9935-a35dcba8b8db)
2025-06-15 02:29:31.921 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 23.5574ms
2025-06-15 02:29:31.925 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-15 02:29:31.932 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 386c0f18-55e5-4457-a678-0b496446c088
2025-06-15 02:29:31.934 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:29:31.936 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 02:29:31.940 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 02:29:31.941 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 02:29:31.945 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 02:29:31.947 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 02:29:31.952 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 02:29:31.955 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:29:31.957 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:29:31.979 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-15 02:29:31.990 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-15 02:29:31.994 +04:00 [INF] Retrieved 3 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:29:31.995 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-15 02:29:31.996 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 53.4124ms
2025-06-15 02:29:31.999 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 02:29:32.001 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 66ms with status 200 (Correlation ID: 386c0f18-55e5-4457-a678-0b496446c088)
2025-06-15 02:29:32.009 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?status=completed&page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 84.2313ms
2025-06-15 02:30:11.594 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - null null
2025-06-15 02:30:11.600 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 started with correlation ID 599cd1bc-5940-4406-87c6-20116fb6efed
2025-06-15 02:30:11.604 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:30:11.605 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 completed in 1ms with status 204 (Correlation ID: 599cd1bc-5940-4406-87c6-20116fb6efed)
2025-06-15 02:30:11.607 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - 204 null null 13.834ms
2025-06-15 02:30:11.609 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - application/json null
2025-06-15 02:30:11.613 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 started with correlation ID 73d3e1cd-08f2-472a-9369-4c45b17cfaee
2025-06-15 02:30:11.615 +04:00 [INF] CORS policy execution successful.
2025-06-15 02:30:11.616 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 02:30:11.617 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-15 02:30:11.622 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 02:30:11.625 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 02:30:11.626 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 02:30:11.628 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 02:30:11.630 +04:00 [INF] Retrieving analysis "51764e83-da50-4569-8184-891ce018b949" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:30:11.633 +04:00 [INF] Retrieving analysis result: "51764e83-da50-4569-8184-891ce018b949" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 02:30:11.659 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-15 02:30:11.717 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='51764e83-da50-4569-8184-891ce018b949', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExtractedText", d2."IsDeleted", d2."ModelUsed", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-15 02:30:11.743 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-15 02:30:11.755 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 130.6541ms
2025-06-15 02:30:11.758 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-15 02:30:11.759 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 completed in 143ms with status 200 (Correlation ID: 73d3e1cd-08f2-472a-9369-4c45b17cfaee)
2025-06-15 02:30:11.762 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - 200 null application/json; charset=utf-8 153.093ms
