2025-06-15 00:23:02.709 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-15 00:23:02.731 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID fec2ed31-1e55-4f38-9c2d-d659554123a2
2025-06-15 00:23:02.739 +04:00 [INF] CORS policy execution successful.
2025-06-15 00:23:02.742 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 3ms with status 204 (Correlation ID: fec2ed31-1e55-4f38-9c2d-d659554123a2)
2025-06-15 00:23:02.748 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 39.5815ms
2025-06-15 00:23:02.788 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-15 00:23:02.807 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 6b597634-0f3b-4fcf-bc17-4581283efe9d
2025-06-15 00:23:02.811 +04:00 [INF] CORS policy execution successful.
2025-06-15 00:23:02.818 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 7:23:15 PM', Current time (UTC): '6/14/2025 8:23:02 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-15 00:23:02.826 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 7:23:15 PM', Current time (UTC): '6/14/2025 8:23:02 PM'.
2025-06-15 00:23:02.829 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 7:23:15 PM', Current time (UTC): '6/14/2025 8:23:02 PM'.
2025-06-15 00:23:02.838 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-15 00:23:02.844 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-15 00:23:02.847 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 36ms with status 401 (Correlation ID: 6b597634-0f3b-4fcf-bc17-4581283efe9d)
2025-06-15 00:23:02.854 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 401 0 null 65.5607ms
2025-06-15 00:23:24.009 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-15 00:23:24.016 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 9eaa351c-6cc2-421b-a345-f62e5f60b4ab
2025-06-15 00:23:24.018 +04:00 [INF] CORS policy execution successful.
2025-06-15 00:23:24.019 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: 9eaa351c-6cc2-421b-a345-f62e5f60b4ab)
2025-06-15 00:23:24.024 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 14.6583ms
2025-06-15 00:23:24.025 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-15 00:23:24.031 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 740d2164-c103-48be-b639-2c2ed5355afe
2025-06-15 00:23:24.033 +04:00 [INF] CORS policy execution successful.
2025-06-15 00:23:24.034 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 00:23:24.036 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 00:23:24.041 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 00:23:24.045 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 00:23:29.984 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 00:23:29.996 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 00:23:32.043 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 00:23:52.135 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-15 00:23:52.227 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-15 00:23:52.260 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentName", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."OriginalContent", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", d1.xmin, r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", r.xmin, d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0.xmin
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentName", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."OriginalContent", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId", d.xmin
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-15 00:23:59.129 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 00:23:59.134 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-15 00:23:59.139 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 35095.828ms
2025-06-15 00:23:59.141 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 00:23:59.143 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 35110ms with status 200 (Correlation ID: 740d2164-c103-48be-b639-2c2ed5355afe)
2025-06-15 00:23:59.147 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 35121.4821ms
2025-06-15 01:18:46.434 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 247
2025-06-15 01:21:02.812 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 247
2025-06-15 01:21:37.564 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-15 01:21:37.717 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-15 01:21:38.252 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-15 01:21:38.256 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-15 01:21:38.365 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-15 01:21:38.367 +04:00 [INF] Hosting environment: Development
2025-06-15 01:21:38.376 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-15 01:21:39.587 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-15 01:21:39.896 +04:00 [INF] Request GET / started with correlation ID aa82aa4c-dbd5-4108-9d79-89c1404c3ba9
2025-06-15 01:21:40.082 +04:00 [INF] Request GET / completed in 179ms with status 404 (Correlation ID: aa82aa4c-dbd5-4108-9d79-89c1404c3ba9)
2025-06-15 01:21:40.093 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 508.654ms
2025-06-15 01:21:40.122 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-15 01:22:11.843 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-15 01:22:11.919 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 1eeeaefa-2ee0-437c-a277-c066566b0bc6
2025-06-15 01:22:11.949 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:22:11.958 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 16ms with status 204 (Correlation ID: 1eeeaefa-2ee0-437c-a277-c066566b0bc6)
2025-06-15 01:22:11.963 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 120.5223ms
2025-06-15 01:22:11.968 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-15 01:22:11.979 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 4ec16ae5-5d03-4cde-ba40-d50dd967b0f7
2025-06-15 01:22:11.983 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:22:12.139 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 01:22:12.158 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 01:22:12.225 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 01:22:12.362 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 01:22:18.631 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 01:22:19.125 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 01:22:19.161 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:22:20.939 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:22:22.402 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-15 01:22:22.418 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 01:22:22.422 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 01:22:22.424 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 01:22:22.426 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-15 01:22:22.931 +04:00 [INF] Executed DbCommand (49ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-15 01:22:23.100 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-15 01:22:23.195 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-15 01:22:28.277 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:22:28.289 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-15 01:22:28.308 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 16074.3643ms
2025-06-15 01:22:28.312 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 01:22:28.314 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 16332ms with status 200 (Correlation ID: 4ec16ae5-5d03-4cde-ba40-d50dd967b0f7)
2025-06-15 01:22:28.326 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 16358.2046ms
2025-06-15 01:23:24.568 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-15 01:23:24.612 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 3d19215d-83ef-4507-858f-fe33e9e15724
2025-06-15 01:23:24.616 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:23:24.618 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 2ms with status 204 (Correlation ID: 3d19215d-83ef-4507-858f-fe33e9e15724)
2025-06-15 01:23:24.631 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 62.3847ms
2025-06-15 01:23:24.644 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundaryD0Q3By83pW1ZtSME 599663
2025-06-15 01:23:24.683 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 9e74794c-61a9-4039-a80b-95e35c2b0706
2025-06-15 01:23:24.687 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:23:24.696 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 01:23:24.702 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-15 01:23:24.743 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 01:23:24.752 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 01:23:24.754 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 01:23:24.759 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 01:23:24.842 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_cmr1.pdf
2025-06-15 01:23:24.865 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_cmr1.pdf
2025-06-15 01:23:24.920 +04:00 [INF] Starting comprehensive document analysis for: Cdd_test_cmr1.pdf
2025-06-15 01:23:24.964 +04:00 [INF] Document stored successfully: 49b85c173e9587859d2880aed60c1428f88dffeb5369aee082fae995ede2aa8f -> ./storage/documents\49b85c173e9587859d2880aed60c1428f88dffeb5369aee082fae995ede2aa8f.pdf
2025-06-15 01:23:24.974 +04:00 [INF] Document stored with hash: 49b85c173e9587859d2880aed60c1428f88dffeb5369aee082fae995ede2aa8f at ./storage/documents\49b85c173e9587859d2880aed60c1428f88dffeb5369aee082fae995ede2aa8f.pdf
2025-06-15 01:23:24.997 +04:00 [INF] Starting Azure Document Intelligence extraction for document type: pdf
2025-06-15 01:23:30.160 +04:00 [INF] Azure extraction completed successfully. Processing time: 5140ms, Pages: 2
2025-06-15 01:23:30.168 +04:00 [INF] Starting clause analysis for document type: pdf
2025-06-15 01:23:36.641 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "clauses": [\n    {\n      "clauseText": "Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activi
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:36.854 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "clauses": [\n    {\n      "clauseText": "Le présent contrat est un contrat à durée déterminée , Cleaned: {\n  \"clauses": [\n    {\n      \"clauseText": \"Le présent contrat est un contrat à durée détermin
2025-06-15 01:23:36.859 +04:00 [INF] Clause analysis completed. Found 0 clauses
2025-06-15 01:23:36.863 +04:00 [INF] Starting risk assessment for document type: pdf
2025-06-15 01:23:39.464 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "risks": [\n    {\n      "riskType": "Risques structurels",\n      "description": "Le contrat ne prévoit pas de mécanismes pour gérer une éventuelle prolongation ou renouvellement, ce qui pourrai
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:39.602 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "risks": [\n    {\n      "riskType": "Risques structurels",\n      "description": "Le contrat n, Cleaned: {\n  \"risks": [\n    {\n      \"riskType": \"Risques structurels",\n      \"description": \"Le cont
2025-06-15 01:23:42.967 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "risques": {\n    "conformite_RGPD": {\n      "risques_identifies": [\n        "Manipulation et traitement de données confidentielles sans mention explicite des mesures de protection spécifiques"
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:43.107 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "risques": {\n    "conformite_RGPD": {\n      "risques_identifies": [\n        "Manipulation et, Cleaned: {\n  \"risques": {\n    \"conformite_RGPD": {\n      \"risques_identifies": [\n        \"Manipulatio
2025-06-15 01:23:43.119 +04:00 [INF] Risk assessment completed. Identified 0 risks
2025-06-15 01:23:43.127 +04:00 [INF] Generating recommendations for document type: pdf
2025-06-15 01:23:46.438 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "structure": {\n    "points_forts": [\n      "Présence d'une structure claire avec introduction, articles numérotés, et signatures.",\n      "Identification précise des parties (employeur et sala
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:46.589 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "structure": {\n    "points_forts": [\n      "Présence d'une structure claire avec introduction, Cleaned: {\n  \"structure": {\n    \"points_forts": [\n      \"Présence d'une structure claire avec introduct
2025-06-15 01:23:50.185 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "type": "Compliance",\n      "description": "Vérifier que la durée maximale du contrat à durée déterminée (CDD) est respectée conformément à la législation loca
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:50.318 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "recommendations": [\n    {\n      "type": "Compliance",\n      "description": "Vérifier que la, Cleaned: {\n  \"recommendations": [\n    {\n      \"type": \"Compliance",\n      \"description": \"Vérifier q
2025-06-15 01:23:50.324 +04:00 [INF] Generated 0 recommendations
2025-06-15 01:23:55.363 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "entities": [\n    {\n      "text": "LexBot S.A.R.L.",\n      "type": "ORGANIZATION",\n      "startPosition": 56,\n      "endPosition": 70,\n      "confidence": 0.95,\n      "normalizedValue": "L
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:55.503 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "entities": [\n    {\n      "text": "LexBot S.A.R.L.",\n      "type": "ORGANIZATION",\n      "s, Cleaned: {\n  \"entities": [\n    {\n      \"text": \"LexBot S.A.R.L.",\n      \"type": \"ORGANIZATION",\n   
2025-06-15 01:23:56.812 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "citations": [\n    {\n      "title": "article L1242-2 du Code du travail",\n      "source": "Code du travail (Législation française)",\n      "url": "https://www.legifrance.gouv.fr/codes/id/LEGI
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-15 01:23:57.001 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "citations": [\n    {\n      "title": "article L1242-2 du Code du travail",\n      "source": "C, Cleaned: {\n  \"citations": [\n    {\n      \"title": \"article L1242-2 du Code du travail",\n      \"source"
2025-06-15 01:24:07.259 +04:00 [INF] Executed DbCommand (27ms) [Parameters=[@p0='da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2', @p1='Analyse juridique complète du contrat de travail à durée déterminée (CDD)

---

**1. Synthèse des clauses importantes**

- **Parties au contrat :**  
  - Employeur : LexBot S.A.R.L., société immatriculée au RCS de Douala, représentée par Mme Kevin Dupont.  
  - Salarié : M. Jean Luc, de nationalité camerounaise.

- **Nature du contrat (Article 1) :**  
  - CDD conclu en application de l’article L1242-2 du Code du travail, pour accroissement temporaire d’activité.

- **Durée (Article 2) :**  
  - Du 1er juillet 2025 au 31 décembre 2025 (6 mois).

- **Poste (Article 3) :**  
  - Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.

- **Lieu de travail (Article 4) :**  
  - Siège de l'entreprise ou tout autre lieu dans un rayon de 20 km.

- **Rémunération (Article 5) :**  
  - 120 000 FCFA brut mensuel, versé à terme échu.

- **Durée du travail (Article 6) :**  
  - 48 heures par semaine, du lundi au samedi, horaires à définir par l’employeur.

- **Risques liés au poste (Article 7) :**  
  - Exposition aux écrans, charge mentale, manipulation de données confidentielles, risques psychosociaux.  
  - L’employeur propose un accompagnement médical et en santé mentale.

- **Clause de confidentialité (Article 8) :**  
  - Engagement à ne pas divulguer d’informations pendant et après le contrat.

- **Rupture anticipée (Article 9) :**  
  - Possible uniquement en cas de faute grave, force majeure, accord mutuel ou embauche en CDI.

- **Fin du contrat (Article 10) :**  
  - Fin automatique au 31 décembre 2025, sans renouvellement prévu.

---

**2. Risques identifiés**

- **Conformité légale :**  
  - La référence à l’article L1242-2 du Code du travail est correcte pour un CDD pour accroissement temporaire d’activité.

- **Formalisme :**  
  - Absence de mention explicite de la période d’essai, ce qui pourrait limiter la possibilité de rupture anticipée durant cette période.

- **Durée et renouvellement :**  
  - La durée est claire, mais il est conseillé de préciser si le contrat peut être renouvelé ou non, même si le document indique qu’aucun renouvellement n’est prévu.

- **Lieu de travail :**  
  - La clause prévoit une flexibilité géographique limitée à 20 km, ce qui est cohérent.

- **Rémunération :**  
  - La rémunération est conforme, mais il serait prudent de préciser si des primes ou avantages sont prévus.

- **Temps de travail :**  
  - 48 heures par semaine est supérieur à la durée légale (40h), ce qui nécessite une justification et une rémunération majorée ou des contreparties.

- **Risques professionnels :**  
  - La liste est pertinente, mais il serait utile d’ajouter une mention de l’obligation de l’employeur de respecter la réglementation en matière de santé et sécurité.

- **Confidentialité :**  
  - Clause standard, mais il est conseillé de préciser la durée de l’obligation après la fin du contrat.

- **Rupture anticipée :**  
  - La clause est conforme, mais il serait utile d’ajouter une procédure précise en cas de rupture anticipée.

- **Fin du contrat :**  
  - La fin automatique est conforme, mais il est conseillé d’ajouter une mention sur la possibilité de renouvellement ou de reconduction.

---

**3. Recommandations**

- **Clarifier la période d’essai :**  
  - Ajouter une clause précisant la durée de la période d’essai, si applicable, et ses modalités.

- **Préciser les modalités de paiement :**  
  - Indiquer si la rémunération inclut des primes ou autres avantages.

- **Respect du temps de travail :**  
  - Vérifier que la durée hebdomadaire de 48h respecte la législation locale ou prévoir une majoration.

- **Ajouter une clause de renouvellement ou de reconduction :**  
  - Même si aucune reconduction n’est prévue, il est utile de le mentionner explicitement.

- **Renforcer la clause de confidentialité :**  
  - Préciser la durée de l’obligation après la fin du contrat.

- **Inclure une clause de non-concurrence ou de non-sollicitation (si pertinent) :**  
  - En fonction de la sensibilité des données traitées.

- **Vérifier la conformité avec la législation locale :**  
  - Notamment en ce qui concerne la durée du travail, la rémunération, et la santé et sécurité.

---

**4. Évaluation globale**

Ce contrat semble globalement conforme aux principes fondamentaux du droit du travail pour un CDD. Cependant, quelques précisions et ajustements sont recommandés pour renforcer la sécurité juridique et la conformité légale. La mention claire de la période d’essai, des modalités de rupture, et des obligations en matière de santé et sécurité est essentielle.

---

**5. Conseils juridiques**

- **Vérifier la législation locale :**  
  - Assurez-vous que la durée hebdomadaire et la rémunération respectent la législation camerounaise en vigueur.

- **Respecter les formalités :**  
  - Le contrat doit être signé en deux exemplaires, ce qui est fait, mais il est conseillé de remettre une copie au salarié.

- **Anticiper les risques liés à la santé mentale :**  
  - Mettre en place des mesures concrètes pour accompagner le salarié face aux risques psychosociaux.

- **Suivi et documentation :**  
  - Documenter toute modification ou avenant éventuel au contrat.

- **Consultation juridique spécialisée :**  
  - En cas de doute, faire relire le contrat par un avocat spécialisé en droit du travail local.

---

**Conclusion**

Ce contrat de CDD est bien structuré, mais nécessite quelques précisions pour assurer une conformité optimale et limiter les risques juridiques. La vigilance doit porter sur la législation locale, notamment en matière de durée du travail, de rémunération, et de santé et sécurité.

---

**N.B. :**  
Ce résumé ne remplace pas un avis juridique personnalisé. Pour toute situation spécifique ou complexe, consultez un avocat spécialisé en droit du travail dans votre juridiction.' (Nullable = false), @p2='2025-06-14T21:24:06.7900110Z' (DbType = DateTime), @p3='0.8', @p4='2025-06-14T21:24:06.7901273Z' (DbType = DateTime), @p5=NULL, @p6=NULL (DbType = DateTime), @p7=NULL, @p8='49b85c173e9587859d2880aed60c1428f88dffeb5369aee082fae995ede2aa8f' (Nullable = false), @p9='Cdd_test_cmr1.pdf' (Nullable = false), @p10='./storage/documents\49b85c173e9587859d2880aed60c1428f88dffeb5369aee082fae995ede2aa8f.pdf' (Nullable = false), @p11='pdf' (Nullable = false), @p12='0', @p13='CONTRAT DE TRAVAIL À DURÉE DÉTERMINÉE (CDD)
Entre les soussignés :
L'employeur
LexBot S.A.R.L., au capital de 5 000 000 FCFA, dont le siège social est situé au 12, Rue de l'Intelligence Artificielle, 75001 Douala, immatriculée au RCS de Douala sous le numéro 882 000 123, Représentée par Mme Kevin Dupont, en sa qualité de Directrice Générale, Ci-après dénommée « l'Employeur »
Et
M. Jean Luc, né le 12 mars 1989 à Baham (Cameroun), demeurant au 8, rue des Lilas, 69001 Ouest, de nationalité camerounaise, Ci-après dénommé « le Salarié »
Article 1 - Nature du contrat
Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.
Article 2 - Date de début et de fin
Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.
Article 3 - Poste occupé
M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.
Article 4 - Lieu de travail
Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.
Article 5 - Rémunération
La rémunération brute mensuelle sera de 120 000 FCFA, versée à terme échu, selon les modalités prévues par le Code du travail.
1 | Page KEVIN WILFRIED
Article 6 - Durée du travail
La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.
Article 7 - Risques liés au poste
Le salarié est informé que le poste comporte les risques professionnels suivants :
· Exposition prolongée aux écrans (fatigue visuelle, TMS)
· Charge mentale importante liée à la gestion des risques informatiques
· Manipulation de données confidentielles (obligation de vigilance accrue)
· Risques psychosociaux en cas d'incident majeur de cybersécurité
L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.
Article 8 - Clause de confidentialité
Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.
Article 9 - Rupture anticipée
Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi : faute grave, force majeure, accord commun ou embauche en CDI.
Article 10 - Fin du contrat
Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.
Fait à Paris, le 14 juin 2025
En deux exemplaires originaux.
Signature de l'Employeur (LexBot)
Signature :
Signature du Salarié (Jean Luc)
Signature :
2 | Page KEVIN WILFRIED' (Nullable = false), @p14='False', @p15='lexai-gpt-4.1-nano' (Nullable = false), @p16='41867', @p17='Completed' (Nullable = false), @p18='1462', @p19='2025-06-14T21:24:06.7901921Z' (Nullable = true) (DbType = DateTime), @p20=NULL, @p21='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_analysis_results ("Id", "AnalysisContent", "AnalyzedAt", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentHash", "DocumentName", "DocumentStoragePath", "DocumentType", "EstimatedCost", "ExtractedText", "IsDeleted", "ModelUsed", "ProcessingTimeMs", "Status", "TokensUsed", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-15 01:24:07.291 +04:00 [INF] Analysis saved to database with ID: "da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2"
2025-06-15 01:24:07.296 +04:00 [INF] Document analysis completed successfully. Processing time: 41867ms, Tokens: 1462
2025-06-15 01:24:07.352 +04:00 [INF] Document analysis completed successfully for document: Cdd_test_cmr1.pdf, Analysis ID: "da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2"
2025-06-15 01:24:07.356 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2"
2025-06-15 01:24:07.360 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-15 01:24:07.383 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 42634.7725ms
2025-06-15 01:24:07.390 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-15 01:24:07.393 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 42705ms with status 200 (Correlation ID: 9e74794c-61a9-4039-a80b-95e35c2b0706)
2025-06-15 01:24:07.401 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 42757.4386ms
2025-06-15 01:24:59.335 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-15 01:24:59.360 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 248372ed-64ee-4243-a6e5-b7140ff97a5b
2025-06-15 01:24:59.369 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:24:59.372 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 2ms with status 204 (Correlation ID: 248372ed-64ee-4243-a6e5-b7140ff97a5b)
2025-06-15 01:24:59.377 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 42.0871ms
2025-06-15 01:24:59.387 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-15 01:24:59.396 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 5ec5ef13-fbb6-4da7-a799-8325be59faf2
2025-06-15 01:24:59.400 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:24:59.402 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 01:24:59.409 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 01:24:59.413 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 01:24:59.431 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 01:24:59.436 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 01:24:59.450 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 01:24:59.457 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:24:59.460 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:24:59.632 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-15 01:24:59.646 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-15 01:24:59.692 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:24:59.698 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-15 01:24:59.703 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 282.5557ms
2025-06-15 01:24:59.708 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-15 01:24:59.709 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 309ms with status 200 (Correlation ID: 5ec5ef13-fbb6-4da7-a799-8325be59faf2)
2025-06-15 01:24:59.715 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 328.0582ms
2025-06-15 01:25:13.868 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 - null null
2025-06-15 01:25:13.882 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 started with correlation ID c6634f83-ebbe-48c7-89d9-29d75a1eaea1
2025-06-15 01:25:13.895 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:25:13.901 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 completed in 5ms with status 204 (Correlation ID: c6634f83-ebbe-48c7-89d9-29d75a1eaea1)
2025-06-15 01:25:13.916 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 - 204 null null 47.7363ms
2025-06-15 01:25:13.935 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 - application/json null
2025-06-15 01:25:14.019 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 started with correlation ID 79d0aac3-90ce-449d-909f-36c05700d2f2
2025-06-15 01:25:14.022 +04:00 [INF] CORS policy execution successful.
2025-06-15 01:25:14.027 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-15 01:25:14.034 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-15 01:25:14.046 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-15 01:25:14.051 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-15 01:25:14.052 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-15 01:25:14.056 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-15 01:25:14.059 +04:00 [INF] Retrieving analysis "da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:25:14.066 +04:00 [INF] Retrieving analysis result: "da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-15 01:25:14.153 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-15 01:25:14.303 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@__id_0='da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExtractedText", d2."IsDeleted", d2."ModelUsed", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-15 01:25:14.344 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-15 01:25:14.349 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 298.4397ms
2025-06-15 01:25:14.352 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-15 01:25:14.353 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 completed in 330ms with status 200 (Correlation ID: 79d0aac3-90ce-449d-909f-36c05700d2f2)
2025-06-15 01:25:14.355 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/da83fddb-ffb5-4cf8-bfa5-6f5edc02d7d2 - 200 null application/json; charset=utf-8 420.144ms
