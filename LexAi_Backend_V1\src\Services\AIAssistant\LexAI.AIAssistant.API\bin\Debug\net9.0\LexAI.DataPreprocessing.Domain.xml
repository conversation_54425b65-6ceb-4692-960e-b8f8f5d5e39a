<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.DataPreprocessing.Domain</name>
    </assembly>
    <members>
        <member name="T:LexAI.DataPreprocessing.Domain.Entities.Document">
            <summary>
            Represents a document in the preprocessing pipeline
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.FileName">
            <summary>
            Original file name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.FileSize">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.MimeType">
            <summary>
            MIME type of the file
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.FileHash">
            <summary>
            File hash for deduplication
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.StoragePath">
            <summary>
            Storage path or URL
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.Status">
            <summary>
            Document processing status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.DetectedDomain">
            <summary>
            Detected legal domain
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.ClassificationConfidence">
            <summary>
            Classification confidence score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.ExtractedText">
            <summary>
            Extracted text content
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.Metadata">
            <summary>
            Document metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.ProcessingSteps">
            <summary>
            Processing pipeline steps
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.Chunks">
            <summary>
            Document chunks created during processing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.Errors">
            <summary>
            Processing errors if any
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.ProcessingTime">
            <summary>
            Total processing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.ChunkCount">
            <summary>
            Number of chunks created
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.TotalTokens">
            <summary>
            Total tokens in the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.EstimatedCost">
            <summary>
            Estimated processing cost
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.IsVectorized">
            <summary>
            Whether the document is vectorized
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.VectorDatabase">
            <summary>
            Vector database where chunks are stored
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.Document.VectorCollection">
            <summary>
            Collection/index name in vector database
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.Create(System.String,System.Int64,System.String,System.String,System.String,System.String)">
            <summary>
            Creates a new document for processing
            </summary>
            <param name="fileName">Original file name</param>
            <param name="fileSize">File size in bytes</param>
            <param name="mimeType">MIME type</param>
            <param name="fileHash">File hash</param>
            <param name="storagePath">Storage path</param>
            <param name="uploadedBy">User who uploaded the document</param>
            <returns>New document instance</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.StartExtraction(System.String)">
            <summary>
            Starts the extraction process
            </summary>
            <param name="agentName">Name of the extraction agent</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.CompleteExtraction(System.String,System.String,System.TimeSpan)">
            <summary>
            Completes the extraction process
            </summary>
            <param name="extractedText">Extracted text content</param>
            <param name="agentName">Name of the extraction agent</param>
            <param name="extractionTime">Time taken for extraction</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.StartClassification(System.String)">
            <summary>
            Starts the classification process
            </summary>
            <param name="agentName">Name of the classification agent</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.CompleteClassification(LexAI.Shared.Application.DTOs.LegalDomain,System.Double,System.String,System.TimeSpan)">
            <summary>
            Completes the classification process
            </summary>
            <param name="domain">Detected legal domain</param>
            <param name="confidence">Classification confidence</param>
            <param name="agentName">Name of the classification agent</param>
            <param name="classificationTime">Time taken for classification</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.StartChunking(System.String)">
            <summary>
            Starts the chunking process
            </summary>
            <param name="agentName">Name of the chunking agent</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.AddChunk(LexAI.DataPreprocessing.Domain.Entities.DocumentChunk)">
            <summary>
            Adds a chunk to the document
            </summary>
            <param name="chunk">Document chunk to add</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.CompleteChunking(System.String,System.TimeSpan)">
            <summary>
            Completes the chunking process
            </summary>
            <param name="agentName">Name of the chunking agent</param>
            <param name="chunkingTime">Time taken for chunking</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.StartVectorization(System.String)">
            <summary>
            Starts the vectorization process
            </summary>
            <param name="agentName">Name of the vectorization agent</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.CompleteVectorization(System.String,System.String,System.String,System.TimeSpan,System.Decimal)">
            <summary>
            Completes the vectorization process
            </summary>
            <param name="vectorDatabase">Vector database name</param>
            <param name="vectorCollection">Vector collection name</param>
            <param name="agentName">Name of the vectorization agent</param>
            <param name="vectorizationTime">Time taken for vectorization</param>
            <param name="estimatedCost">Estimated cost for vectorization</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.MarkAsFailed(LexAI.DataPreprocessing.Domain.Entities.ProcessingError,System.String)">
            <summary>
            Marks the document as failed
            </summary>
            <param name="error">Processing error</param>
            <param name="agentName">Name of the agent that failed</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.RetryProcessing(System.String,System.String)">
            <summary>
            Retries processing from a specific step
            </summary>
            <param name="fromStep">Step to retry from</param>
            <param name="retriedBy">User who initiated the retry</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.GetCurrentStep">
            <summary>
            Gets the current processing step
            </summary>
            <returns>Current processing step</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.IsReadyForStep(System.String)">
            <summary>
            Checks if the document is ready for a specific step
            </summary>
            <param name="step">Step to check</param>
            <returns>True if ready for the step</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.Document.GetStatistics">
            <summary>
            Gets processing statistics
            </summary>
            <returns>Processing statistics</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk">
            <summary>
            Represents a chunk of a document created during processing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.DocumentId">
            <summary>
            Document ID this chunk belongs to
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.SequenceNumber">
            <summary>
            Chunk sequence number within the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.Content">
            <summary>
            Chunk content text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.StartPosition">
            <summary>
            Start position in the original document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.EndPosition">
            <summary>
            End position in the original document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.TokenCount">
            <summary>
            Number of tokens in this chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.CharacterCount">
            <summary>
            Character count in this chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.Metadata">
            <summary>
            Chunk metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.EmbeddingVector">
            <summary>
            Vector embedding for this chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.EmbeddingModel">
            <summary>
            Embedding model used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.VectorDimension">
            <summary>
            Vector dimension
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.IsVectorized">
            <summary>
            Whether this chunk is vectorized
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.VectorId">
            <summary>
            Vector ID in the vector database
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.Keywords">
            <summary>
            Keywords extracted from this chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.NamedEntities">
            <summary>
            Named entities found in this chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.QualityScore">
            <summary>
            Chunk quality score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.ImportanceScore">
            <summary>
            Chunk importance score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.DomainRelevance">
            <summary>
            Legal domain relevance scores
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.Create(System.Guid,System.Int32,System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new document chunk
            </summary>
            <param name="documentId">Document ID</param>
            <param name="sequenceNumber">Sequence number</param>
            <param name="content">Chunk content</param>
            <param name="type">Chunk type</param>
            <param name="startPosition">Start position</param>
            <param name="endPosition">End position</param>
            <param name="createdBy">Agent that created the chunk</param>
            <returns>New document chunk</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.SetEmbedding(System.Single[],System.String,System.String)">
            <summary>
            Sets the embedding vector for this chunk
            </summary>
            <param name="vector">Embedding vector</param>
            <param name="model">Embedding model used</param>
            <param name="vectorId">Vector ID in the database</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.AddKeywords(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Adds keywords to this chunk
            </summary>
            <param name="keywords">Keywords to add</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.AddNamedEntities(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.NamedEntity})">
            <summary>
            Adds named entities to this chunk
            </summary>
            <param name="entities">Named entities to add</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.SetDomainRelevance(System.Collections.Generic.Dictionary{LexAI.Shared.Application.DTOs.LegalDomain,System.Double})">
            <summary>
            Sets domain relevance scores
            </summary>
            <param name="relevanceScores">Domain relevance scores</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.GetMostRelevantDomain">
            <summary>
            Gets the most relevant legal domain
            </summary>
            <returns>Most relevant domain and its score</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.UpdateScores(System.Double,System.Double)">
            <summary>
            Updates quality and importance scores
            </summary>
            <param name="qualityScore">Quality score (0-1)</param>
            <param name="importanceScore">Importance score (0-1)</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.OverlapsWith(LexAI.DataPreprocessing.Domain.Entities.DocumentChunk)">
            <summary>
            Checks if this chunk overlaps with another chunk
            </summary>
            <param name="other">Other chunk to check</param>
            <returns>True if chunks overlap</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.GetOverlapPercentage(LexAI.DataPreprocessing.Domain.Entities.DocumentChunk)">
            <summary>
            Gets the overlap percentage with another chunk
            </summary>
            <param name="other">Other chunk</param>
            <returns>Overlap percentage (0-1)</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.GetStatistics">
            <summary>
            Gets chunk statistics
            </summary>
            <returns>Chunk statistics</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.DocumentChunk.GetPreview(System.Int32)">
            <summary>
            Creates a preview of the chunk content
            </summary>
            <param name="maxLength">Maximum length of preview</param>
            <returns>Content preview</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.Entities.NamedEntity">
            <summary>
            Named entity found in document chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.Id">
            <summary>
            Entity ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.ChunkId">
            <summary>
            Chunk ID this entity belongs to
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.Text">
            <summary>
            Entity text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.Type">
            <summary>
            Entity type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.StartPosition">
            <summary>
            Start position in chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.EndPosition">
            <summary>
            End position in chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.Confidence">
            <summary>
            Confidence score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.Metadata">
            <summary>
            Entity metadata
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.NamedEntity.Create(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.EntityType,System.Int32,System.Int32,System.Double)">
            <summary>
            Creates a new named entity
            </summary>
            <param name="text">Entity text</param>
            <param name="type">Entity type</param>
            <param name="startPosition">Start position</param>
            <param name="endPosition">End position</param>
            <param name="confidence">Confidence score</param>
            <returns>Named entity</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.Entities.ProcessingError">
            <summary>
            Processing error entity
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.Id">
            <summary>
            Error ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.ErrorCode">
            <summary>
            Error code
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.ErrorMessage">
            <summary>
            Error message
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.StepName">
            <summary>
            Step name where error occurred
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.AgentName">
            <summary>
            Agent name that caused the error
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.Severity">
            <summary>
            Error severity
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.OccurredAt">
            <summary>
            When the error occurred
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.Metadata">
            <summary>
            Error metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.IsResolved">
            <summary>
            Whether the error has been resolved
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.ResolutionNotes">
            <summary>
            Resolution notes
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.ResolvedAt">
            <summary>
            When the error was resolved
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.Entities.ProcessingError.Create(System.String,System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ErrorSeverity,System.String,System.String)">
            <summary>
            Creates a new processing error
            </summary>
            <param name="errorCode">Error code</param>
            <param name="errorMessage">Error message</param>
            <param name="severity">Error severity</param>
            <param name="stepName">Step name</param>
            <param name="agentName">Agent name</param>
            <returns>Processing error</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep">
            <summary>
            Processing step entity
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.Id">
            <summary>
            Step ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.StepName">
            <summary>
            Step name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.AgentName">
            <summary>
            Agent name that executed the step
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.StartedAt">
            <summary>
            When the step started
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.CompletedAt">
            <summary>
            When the step completed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.IsSuccessful">
            <summary>
            Whether the step was successful
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.ErrorMessage">
            <summary>
            Error message if step failed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.Metadata">
            <summary>
            Step metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.Entities.ProcessingStep.Duration">
            <summary>
            Duration of the step
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity">
            <summary>
            Named entity value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.Text">
            <summary>
            Entity text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.Type">
            <summary>
            Entity type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.StartPosition">
            <summary>
            Start position in the text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.EndPosition">
            <summary>
            End position in the text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.Confidence">
            <summary>
            Confidence score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.Create(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.EntityType,System.Int32,System.Int32,System.Double)">
            <summary>
            Creates a named entity
            </summary>
            <param name="text">Entity text</param>
            <param name="type">Entity type</param>
            <param name="startPosition">Start position</param>
            <param name="endPosition">End position</param>
            <param name="confidence">Confidence score</param>
            <returns>Named entity</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.NamedEntity.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata">
            <summary>
            Vector metadata value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.VectorId">
            <summary>
            Vector ID in the database
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.DatabaseType">
            <summary>
            Vector database type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.Collection">
            <summary>
            Collection or index name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.Dimension">
            <summary>
            Vector dimension
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.EmbeddingModel">
            <summary>
            Embedding model used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.Domain">
            <summary>
            Legal domain for routing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.CreatedAt">
            <summary>
            When the vector was created
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.AdditionalMetadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.Create(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Int32,System.String,LexAI.Shared.Application.DTOs.LegalDomain)">
            <summary>
            Creates vector metadata
            </summary>
            <param name="vectorId">Vector ID</param>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="dimension">Vector dimension</param>
            <param name="embeddingModel">Embedding model</param>
            <param name="domain">Legal domain</param>
            <returns>Vector metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.VectorMetadata.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics">
            <summary>
            Processing statistics value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.FileSize">
            <summary>
            File size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.Status">
            <summary>
            Processing status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.ChunkCount">
            <summary>
            Number of chunks created
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.TotalTokens">
            <summary>
            Total tokens
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.ProcessingTime">
            <summary>
            Processing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.StepCount">
            <summary>
            Number of processing steps
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.ErrorCount">
            <summary>
            Number of errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.IsVectorized">
            <summary>
            Whether vectorized
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ProcessingStatistics.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics">
            <summary>
            Chunk statistics value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.ChunkId">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.SequenceNumber">
            <summary>
            Sequence number
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.TokenCount">
            <summary>
            Token count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.CharacterCount">
            <summary>
            Character count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.QualityScore">
            <summary>
            Quality score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.ImportanceScore">
            <summary>
            Importance score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.KeywordCount">
            <summary>
            Keyword count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.EntityCount">
            <summary>
            Entity count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.IsVectorized">
            <summary>
            Whether vectorized
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.VectorDimension">
            <summary>
            Vector dimension
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.DomainRelevanceCount">
            <summary>
            Domain relevance count
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkStatistics.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration">
            <summary>
            Chunking configuration value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.Strategy">
            <summary>
            Chunking strategy
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.MaxChunkSize">
            <summary>
            Maximum chunk size in characters
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.OverlapSize">
            <summary>
            Overlap size between chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.MinChunkSize">
            <summary>
            Minimum chunk size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.PreserveSentences">
            <summary>
            Whether to preserve sentence boundaries
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.PreserveParagraphs">
            <summary>
            Whether to preserve paragraph boundaries
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.CustomSeparators">
            <summary>
            Custom separators for chunking
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.Create(LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Creates chunking configuration
            </summary>
            <param name="strategy">Chunking strategy</param>
            <param name="maxChunkSize">Maximum chunk size</param>
            <param name="overlapSize">Overlap size</param>
            <param name="minChunkSize">Minimum chunk size</param>
            <param name="preserveSentences">Preserve sentences</param>
            <param name="preserveParagraphs">Preserve paragraphs</param>
            <returns>Chunking configuration</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata">
            <summary>
            Document metadata value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.FileName">
            <summary>
            Original file name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.FileSize">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.MimeType">
            <summary>
            MIME type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.Language">
            <summary>
            Document language (ISO 639-1)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.Title">
            <summary>
            Document title extracted from content
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.Author">
            <summary>
            Document author if available
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.DocumentCreatedAt">
            <summary>
            Document creation date if available
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.DocumentModifiedAt">
            <summary>
            Document modification date if available
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.PageCount">
            <summary>
            Number of pages in the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.WordCount">
            <summary>
            Number of words in the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.CharacterCount">
            <summary>
            Number of characters in the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.ParagraphCount">
            <summary>
            Number of paragraphs in the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.Tags">
            <summary>
            Document tags
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.Keywords">
            <summary>
            Document keywords
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.CustomMetadata">
            <summary>
            Additional custom metadata
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.#ctor(System.Guid,System.String,System.Int64,System.String,System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Constructor for JSON deserialization
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.Create(System.Guid,System.String,System.Int64,System.String)">
            <summary>
            Creates document metadata
            </summary>
            <param name="documentId">Document ID</param>
            <param name="fileName">File name</param>
            <param name="fileSize">File size</param>
            <param name="mimeType">MIME type</param>
            <returns>Document metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.WithTextStatistics(System.String)">
            <summary>
            Updates metadata with text statistics
            </summary>
            <param name="text">Extracted text</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.WithDocumentProperties(System.String,System.String,System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32})">
            <summary>
            Updates metadata with document properties
            </summary>
            <param name="title">Document title</param>
            <param name="author">Document author</param>
            <param name="language">Document language</param>
            <param name="createdAt">Document creation date</param>
            <param name="modifiedAt">Document modification date</param>
            <param name="pageCount">Page count</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.WithTags(System.String[])">
            <summary>
            Adds tags to the metadata
            </summary>
            <param name="tags">Tags to add</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.WithKeywords(System.String[])">
            <summary>
            Adds keywords to the metadata
            </summary>
            <param name="keywords">Keywords to add</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.WithCustomMetadata(System.String,System.Object)">
            <summary>
            Adds custom metadata
            </summary>
            <param name="key">Metadata key</param>
            <param name="value">Metadata value</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.GetCustomMetadata``1(System.String,``0)">
            <summary>
            Gets a custom metadata value
            </summary>
            <typeparam name="T">Metadata type</typeparam>
            <param name="key">Metadata key</param>
            <param name="defaultValue">Default value if not found</param>
            <returns>Metadata value</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentMetadata.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata">
            <summary>
            Chunk metadata value object
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.ChunkId">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.TokenCount">
            <summary>
            Token count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.CharacterCount">
            <summary>
            Character count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.EmbeddingModel">
            <summary>
            Embedding model used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.VectorDimension">
            <summary>
            Vector dimension
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.Language">
            <summary>
            Chunk language
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.AdditionalMetadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.#ctor">
            <summary>
            Private constructor for Entity Framework
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.#ctor(System.Guid,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType,System.Int32,System.Int32,System.String,System.Nullable{System.Int32},System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Constructor for JSON deserialization
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.Create(System.Guid,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType,System.Int32,System.Int32)">
            <summary>
            Creates chunk metadata
            </summary>
            <param name="chunkId">Chunk ID</param>
            <param name="type">Chunk type</param>
            <param name="tokenCount">Token count</param>
            <param name="characterCount">Character count</param>
            <returns>Chunk metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.WithEmbedding(System.String,System.Int32)">
            <summary>
            Updates metadata with embedding information
            </summary>
            <param name="model">Embedding model</param>
            <param name="dimension">Vector dimension</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.WithCustomMetadata(System.String,System.Object)">
            <summary>
            Adds custom metadata
            </summary>
            <param name="key">Metadata key</param>
            <param name="value">Metadata value</param>
            <returns>Updated metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkMetadata.GetAtomicValues">
            <summary>
            Gets the components for value object equality
            </summary>
            <returns>Equality components</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus">
            <summary>
            Document processing status enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Uploaded">
            <summary>
            Document has been uploaded
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Extracting">
            <summary>
            Text extraction in progress
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Extracted">
            <summary>
            Text extraction completed
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Classifying">
            <summary>
            Domain classification in progress
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Classified">
            <summary>
            Domain classification completed
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Chunking">
            <summary>
            Document chunking in progress
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Chunked">
            <summary>
            Document chunking completed
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Vectorizing">
            <summary>
            Vectorization in progress
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Completed">
            <summary>
            Processing completed successfully
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Failed">
            <summary>
            Processing failed
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus.Reprocessing">
            <summary>
            Document is being reprocessed
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType">
            <summary>
            Document chunk type enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Header">
            <summary>
            Section header
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Paragraph">
            <summary>
            Regular paragraph
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.List">
            <summary>
            List item
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Table">
            <summary>
            Table content
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Summary">
            <summary>
            Summary section
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Conclusion">
            <summary>
            Conclusion section
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Footer">
            <summary>
            Footer content
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Article">
            <summary>
            Legal article or section
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Definition">
            <summary>
            Legal definition
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.CaseLaw">
            <summary>
            Case law reference
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Citation">
            <summary>
            Citation or reference
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkType.Other">
            <summary>
            Other content type
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ErrorSeverity">
            <summary>
            Processing error severity enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ErrorSeverity.Low">
            <summary>
            Low severity - warning
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ErrorSeverity.Medium">
            <summary>
            Medium severity - error but processing can continue
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ErrorSeverity.High">
            <summary>
            High severity - critical error, processing must stop
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ErrorSeverity.Critical">
            <summary>
            Critical severity - system error
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType">
            <summary>
            Agent type enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.Extraction">
            <summary>
            Text extraction agent
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.Classification">
            <summary>
            Document classification agent
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.Chunking">
            <summary>
            Document chunking agent
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.Vectorization">
            <summary>
            Vectorization agent
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.Routing">
            <summary>
            Routing agent
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.QualityAssurance">
            <summary>
            Quality assurance agent
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.AgentType.Orchestration">
            <summary>
            Orchestration agent
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType">
            <summary>
            Vector database type enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType.MongoDB">
            <summary>
            MongoDB with vector search
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType.Qdrant">
            <summary>
            Qdrant vector database
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType.Weaviate">
            <summary>
            Weaviate vector database
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType.Pinecone">
            <summary>
            Pinecone vector database
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType.Chroma">
            <summary>
            Chroma vector database
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType.FAISS">
            <summary>
            FAISS vector index
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy">
            <summary>
            Chunking strategy enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.FixedSize">
            <summary>
            Fixed size chunks with overlap
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.Semantic">
            <summary>
            Semantic chunking based on meaning
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.Sentence">
            <summary>
            Sentence-based chunking
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.Paragraph">
            <summary>
            Paragraph-based chunking
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.Section">
            <summary>
            Section-based chunking
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.SlidingWindow">
            <summary>
            Sliding window chunking
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingStrategy.Recursive">
            <summary>
            Recursive chunking
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType">
            <summary>
            Embedding model type enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType.OpenAISmall">
            <summary>
            OpenAI text-embedding-3-small
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType.OpenAILarge">
            <summary>
            OpenAI text-embedding-3-large
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType.OpenAIAda002">
            <summary>
            OpenAI text-embedding-ada-002
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType.Cohere">
            <summary>
            Cohere embed model
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType.HuggingFace">
            <summary>
            HuggingFace sentence transformers
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType.CustomLegal">
            <summary>
            Custom legal domain model
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType">
            <summary>
            Named entity type enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Person">
            <summary>
            Person name
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Organization">
            <summary>
            Organization name
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Location">
            <summary>
            Location or place
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Date">
            <summary>
            Date or time
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Money">
            <summary>
            Monetary amount
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.LegalReference">
            <summary>
            Legal reference
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.CaseNumber">
            <summary>
            Case number
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Law">
            <summary>
            Law or regulation
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Court">
            <summary>
            Court or tribunal
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Contract">
            <summary>
            Contract type
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Domain.ValueObjects.EntityType.Other">
            <summary>
            Other entity type
            </summary>
        </member>
    </members>
</doc>
