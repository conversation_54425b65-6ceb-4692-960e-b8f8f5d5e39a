# Script PowerShell pour démarrer les services LLM/NLP de LexAI
# Auteur: LexAI Team
# Version: 1.0

Write-Host "🤖 Démarrage des services LLM/NLP LexAI..." -ForegroundColor Green

# Configuration
$projectRoot = Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)
$embeddingServicePath = Join-Path (Split-Path -Parent $projectRoot) "LexAi_Local_Embeding"

# Fonction pour vérifier si un service est en cours d'exécution
function Test-ServiceRunning {
    param(
        [string]$Url,
        [string]$ServiceName
    )
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method GET -TimeoutSec 5
        Write-Host "✅ $ServiceName est en cours d'exécution" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ $ServiceName n'est pas accessible" -ForegroundColor Red
        return $false
    }
}

# Fonction pour démarrer Ollama
function Start-Ollama {
    Write-Host "🦙 Vérification d'Ollama..." -ForegroundColor Cyan
    
    # Vérifier si Ollama est installé
    try {
        $ollamaVersion = ollama --version 2>$null
        if ($ollamaVersion) {
            Write-Host "✅ Ollama détecté: $ollamaVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Ollama n'est pas installé" -ForegroundColor Red
        Write-Host "💡 Installez Ollama depuis: https://ollama.ai" -ForegroundColor Cyan
        return $false
    }
    
    # Vérifier si Ollama est en cours d'exécution
    if (Test-ServiceRunning -Url "http://localhost:11434/api/tags" -ServiceName "Ollama") {
        return $true
    }
    
    # Démarrer Ollama
    Write-Host "🚀 Démarrage d'Ollama..." -ForegroundColor Yellow
    try {
        Start-Process "ollama" -ArgumentList "serve" -WindowStyle Hidden
        Start-Sleep -Seconds 5
        
        if (Test-ServiceRunning -Url "http://localhost:11434/api/tags" -ServiceName "Ollama") {
            return $true
        }
    } catch {
        Write-Host "❌ Erreur lors du démarrage d'Ollama" -ForegroundColor Red
        return $false
    }
    
    return $false
}

# Fonction pour télécharger le modèle llama3.2:3b
function Install-LlamaModel {
    Write-Host "📥 Vérification du modèle llama3.2:3b..." -ForegroundColor Cyan
    
    try {
        $models = ollama list 2>$null | Out-String
        if ($models -match "llama3.2:3b") {
            Write-Host "✅ Modèle llama3.2:3b déjà installé" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "⚠️  Impossible de vérifier les modèles installés" -ForegroundColor Yellow
    }
    
    Write-Host "📥 Téléchargement du modèle llama3.2:3b (cela peut prendre du temps)..." -ForegroundColor Yellow
    try {
        $process = Start-Process "ollama" -ArgumentList "pull", "llama3.2:3b" -Wait -PassThru -NoNewWindow
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ Modèle llama3.2:3b installé avec succès" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Erreur lors de l'installation du modèle" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Erreur lors du téléchargement du modèle" -ForegroundColor Red
        return $false
    }
}

# Fonction pour démarrer le service d'embedding local
function Start-LocalEmbeddingService {
    Write-Host "🧠 Démarrage du service d'embedding local..." -ForegroundColor Cyan
    
    # Vérifier si le service est déjà en cours d'exécution
    if (Test-ServiceRunning -Url "http://localhost:8000/health" -ServiceName "Service d'embedding local") {
        return $true
    }
    
    # Vérifier si le répertoire existe
    if (-not (Test-Path $embeddingServicePath)) {
        Write-Host "❌ Répertoire du service d'embedding non trouvé: $embeddingServicePath" -ForegroundColor Red
        return $false
    }
    
    # Vérifier si Python est installé
    try {
        $pythonVersion = python --version 2>$null
        if ($pythonVersion) {
            Write-Host "✅ Python détecté: $pythonVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Python n'est pas installé" -ForegroundColor Red
        return $false
    }
    
    # Vérifier si les dépendances sont installées
    $requirementsFile = Join-Path $embeddingServicePath "requirements.txt"
    if (Test-Path $requirementsFile) {
        Write-Host "📦 Installation des dépendances Python..." -ForegroundColor Yellow
        try {
            Set-Location $embeddingServicePath
            $installProcess = Start-Process "pip" -ArgumentList "install", "-r", "requirements.txt" -Wait -PassThru -NoNewWindow
            if ($installProcess.ExitCode -ne 0) {
                Write-Host "⚠️  Erreur lors de l'installation des dépendances" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "⚠️  Erreur lors de l'installation des dépendances" -ForegroundColor Yellow
        }
    }
    
    # Démarrer le service
    try {
        Set-Location $embeddingServicePath
        $appFile = Join-Path $embeddingServicePath "app.py"
        if (Test-Path $appFile) {
            Write-Host "🚀 Démarrage du service d'embedding..." -ForegroundColor Yellow
            Start-Process "python" -ArgumentList "app.py" -WindowStyle Hidden
            Start-Sleep -Seconds 10
            
            if (Test-ServiceRunning -Url "http://localhost:8000/health" -ServiceName "Service d'embedding local") {
                return $true
            }
        } else {
            Write-Host "❌ Fichier app.py non trouvé" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Erreur lors du démarrage du service d'embedding" -ForegroundColor Red
        return $false
    } finally {
        Set-Location $projectRoot
    }
    
    return $false
}

# Fonction pour tester les services LLM/NLP
function Test-LLMServices {
    Write-Host "🧪 Test des services LLM/NLP..." -ForegroundColor Cyan
    
    $allServicesOk = $true
    
    # Test Ollama
    Write-Host "Testing Ollama..." -ForegroundColor Yellow
    try {
        $ollamaTest = Invoke-RestMethod -Uri "http://localhost:11434/api/generate" -Method POST -Body (@{
            model = "llama3.2:3b"
            prompt = "Test"
            stream = $false
        } | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 30
        
        if ($ollamaTest) {
            Write-Host "✅ Ollama fonctionne correctement" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur lors du test d'Ollama: $($_.Exception.Message)" -ForegroundColor Red
        $allServicesOk = $false
    }
    
    # Test service d'embedding local
    Write-Host "Testing Local Embedding Service..." -ForegroundColor Yellow
    try {
        $embeddingTest = Invoke-RestMethod -Uri "http://localhost:8000/embed" -Method POST -Body (@{
            text = "Test embedding"
            model = "sentence-transformers/all-MiniLM-L6-v2"
        } | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 30
        
        if ($embeddingTest.embedding -and $embeddingTest.embedding.Count -gt 0) {
            Write-Host "✅ Service d'embedding fonctionne correctement (dimensions: $($embeddingTest.embedding.Count))" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Erreur lors du test du service d'embedding: $($_.Exception.Message)" -ForegroundColor Red
        $allServicesOk = $false
    }
    
    return $allServicesOk
}

# Fonction principale
function Main {
    Write-Host "🎯 Initialisation des services LLM/NLP pour LexAI" -ForegroundColor Cyan
    Write-Host "📁 Répertoire de travail: $projectRoot" -ForegroundColor Gray
    
    $services = @()
    
    # 1. Démarrer Ollama
    if (Start-Ollama) {
        $services += "Ollama"
        
        # Installer le modèle llama3.2:3b
        if (Install-LlamaModel) {
            Write-Host "✅ Modèle llama3.2:3b prêt" -ForegroundColor Green
        }
    }
    
    # 2. Démarrer le service d'embedding local
    if (Start-LocalEmbeddingService) {
        $services += "Service d'embedding local"
    }
    
    # 3. Tester tous les services
    Write-Host "`n🧪 Test des services..." -ForegroundColor Cyan
    $allOk = Test-LLMServices
    
    # 4. Résumé
    Write-Host "`n📊 Résumé des services:" -ForegroundColor Cyan
    if ($services.Count -gt 0) {
        foreach ($service in $services) {
            Write-Host "   ✅ $service" -ForegroundColor Green
        }
    } else {
        Write-Host "   ❌ Aucun service démarré" -ForegroundColor Red
    }
    
    # 5. URLs d'accès
    Write-Host "`n🌐 URLs des services:" -ForegroundColor Cyan
    Write-Host "   🦙 Ollama: http://localhost:11434" -ForegroundColor White
    Write-Host "   🧠 Embedding Local: http://localhost:8000" -ForegroundColor White
    Write-Host "   📊 Health Check Embedding: http://localhost:8000/health" -ForegroundColor White
    Write-Host "   📚 Documentation Embedding: http://localhost:8000/docs" -ForegroundColor White
    
    # 6. Commandes utiles
    Write-Host "`n💡 Commandes utiles:" -ForegroundColor Cyan
    Write-Host "   Test Ollama: ollama run llama3.2:3b" -ForegroundColor White
    Write-Host "   Liste modèles: ollama list" -ForegroundColor White
    Write-Host "   Arrêter Ollama: taskkill /f /im ollama.exe" -ForegroundColor White
    
    if ($allOk) {
        Write-Host "`n🎉 Tous les services LLM/NLP sont opérationnels !" -ForegroundColor Green
        Write-Host "💡 Vous pouvez maintenant démarrer les services backend LexAI" -ForegroundColor Cyan
    } else {
        Write-Host "`n⚠️  Certains services ont des problèmes" -ForegroundColor Yellow
        Write-Host "💡 Vérifiez les logs ci-dessus et corrigez les erreurs" -ForegroundColor Cyan
    }
}

# Exécuter le script principal
try {
    Main
} catch {
    Write-Host "❌ Erreur critique: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Vérifiez votre configuration et réessayez" -ForegroundColor Cyan
}

Write-Host "`n✅ Script terminé !" -ForegroundColor Green
