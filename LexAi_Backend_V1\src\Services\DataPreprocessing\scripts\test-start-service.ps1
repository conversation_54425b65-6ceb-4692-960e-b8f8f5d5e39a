﻿# Script pour tester le service Data Preprocessing de bout en bout
Write-Host "🧪 Test complet du service Data Preprocessing" -ForegroundColor Green

# Naviguer vers le répertoire du service
$serviceDir = "src/Services/DataPreprocessing"
if (!(Test-Path $serviceDir)) {
    Write-Error "Répertoire du service non trouvé : $serviceDir"
    exit 1
}

Set-Location $serviceDir

try {
    Write-Host ""
    Write-Host "📋 Étape 1: Vérification des prérequis" -ForegroundColor Cyan
    
    # Vérifier Docker
    try {
        docker version | Out-Null
        Write-Host "✅ Docker disponible" -ForegroundColor Green
    } catch {
        Write-Error "❌ Docker non disponible"
        exit 1
    }
    
    # Vérifier .NET
    try {
        dotnet --version | Out-Null
        Write-Host "✅ .NET disponible" -ForegroundColor Green
    } catch {
        Write-Error "❌ .NET non disponible"
        exit 1
    }

    Write-Host ""
    Write-Host "📋 Étape 2: Démarrage des conteneurs" -ForegroundColor Cyan
    docker-compose up -d postgres mongodb pgadmin mongo-express
    
    # Attendre que les services soient prêts
    Write-Host "⏳ Attente des services..." -ForegroundColor Yellow
    Start-Sleep -Seconds 15

    Write-Host ""
    Write-Host "📋 Étape 3: Configuration Hangfire" -ForegroundColor Cyan
    .\setup-hangfire.ps1

    Write-Host ""
    Write-Host "📋 Étape 4: Application des migrations" -ForegroundColor Cyan
    
    # Appliquer les migrations
    Write-Host "🔄 Application des migrations..." -ForegroundColor Yellow
    $migrationResult = dotnet ef database update --project LexAI.DataPreprocessing.Infrastructure --startup-project LexAI.DataPreprocessing.API --context DataPreprocessingDbContext 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migrations appliquées avec succès" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Problème avec les migrations : $migrationResult"
    }

    Write-Host ""
    Write-Host "📋 Étape 5: Test de démarrage du service" -ForegroundColor Cyan
    
    # Démarrer le service en arrière-plan
    Write-Host "🚀 Démarrage du service..." -ForegroundColor Yellow
    
    $serviceProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project LexAI.DataPreprocessing.API --urls http://localhost:5001" -PassThru -WindowStyle Hidden
    
    # Attendre que le service démarre
    $maxWait = 30
    $waited = 0
    $serviceReady = $false
    
    while ($waited -lt $maxWait -and -not $serviceReady) {
        Start-Sleep -Seconds 3
        $waited += 3
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5001/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $serviceReady = $true
                Write-Host "✅ Service démarré avec succès !" -ForegroundColor Green
            }
        } catch {
            Write-Host "⏳ Attente du service... ($waited/$maxWait secondes)" -ForegroundColor Yellow
        }
    }
    
    if (-not $serviceReady) {
        Write-Error "❌ Le service n'a pas démarré dans les temps"
        if ($serviceProcess -and -not $serviceProcess.HasExited) {
            $serviceProcess.Kill()
        }
        exit 1
    }

    Write-Host ""
    Write-Host "📋 Étape 6: Tests des endpoints" -ForegroundColor Cyan
    
    # Test Health Check
    try {
        $healthResponse = Invoke-WebRequest -Uri "http://localhost:5001/health" -UseBasicParsing
        Write-Host "✅ Health Check: $($healthResponse.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Warning "⚠️ Health Check échoué: $_"
    }
    
    # Test Swagger
    try {
        $swaggerResponse = Invoke-WebRequest -Uri "http://localhost:5001/swagger" -UseBasicParsing
        Write-Host "✅ Swagger UI: $($swaggerResponse.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Warning "⚠️ Swagger UI échoué: $_"
    }
    
    # Test Hangfire Dashboard
    try {
        $hangfireResponse = Invoke-WebRequest -Uri "http://localhost:5001/hangfire" -UseBasicParsing
        Write-Host "✅ Hangfire Dashboard: $($hangfireResponse.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Warning "⚠️ Hangfire Dashboard échoué: $_"
    }

    Write-Host ""
    Write-Host "📋 Étape 7: Vérification des bases de données" -ForegroundColor Cyan
    
    # Vérifier les tables dans data_preprocessing_db
    $tablesMain = docker-compose exec -T postgres psql -U lexai_user -d data_preprocessing_db -c "\dt" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Tables dans data_preprocessing_db créées" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Problème avec les tables principales"
    }
    
    # Vérifier les tables Hangfire
    $tablesHangfire = docker-compose exec -T postgres psql -U lexai_user -d data_preprocessing_hangfire -c "\dt" 2>&1
    if ($LASTEXITCODE -eq 0 -and $tablesHangfire -match "hangfire") {
        Write-Host "✅ Tables Hangfire créées" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Tables Hangfire non trouvées (normal au premier démarrage)"
    }

    Write-Host ""
    Write-Host "🎉 Tests terminés avec succès !" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Services disponibles :" -ForegroundColor Cyan
    Write-Host "  • API Data Preprocessing : http://localhost:5001" -ForegroundColor White
    Write-Host "  • Swagger UI            : http://localhost:5001/swagger" -ForegroundColor White
    Write-Host "  • Health Checks         : http://localhost:5001/health" -ForegroundColor White
    Write-Host "  • Hangfire Dashboard    : http://localhost:5001/hangfire (admin/admin123)" -ForegroundColor White
    Write-Host "  • pgAdmin               : http://localhost:5050 (<EMAIL>/pgadmin_2024!)" -ForegroundColor White
    Write-Host "  • Mongo Express         : http://localhost:8081 (admin/mongoexpress_2024!)" -ForegroundColor White
    
    Write-Host ""
    Write-Host "💡 Le service est maintenant prêt à être utilisé !" -ForegroundColor Green
    Write-Host "💡 Appuyez sur Ctrl+C pour arrêter le service" -ForegroundColor Yellow
    
    # Garder le service en vie
    try {
        $serviceProcess.WaitForExit()
    } catch {
        Write-Host "Service arrêté" -ForegroundColor Yellow
    }

}
catch {
    Write-Error "❌ Erreur lors du test : $_"
    exit 1
}
finally {
    # Nettoyer le processus du service
    if ($serviceProcess -and -not $serviceProcess.HasExited) {
        Write-Host "🛑 Arrêt du service..." -ForegroundColor Yellow
        $serviceProcess.Kill()
    }
    
    # Retourner au répertoire racine
    Set-Location ../../..
}
