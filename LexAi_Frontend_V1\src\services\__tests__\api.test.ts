import { ApiService, ApiException, authApi } from '../api'

// Mock fetch
global.fetch = jest.fn()
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('ApiService', () => {
  let apiService: ApiService
  
  beforeEach(() => {
    jest.clearAllMocks()
    apiService = new ApiService('http://localhost:5000')
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('GET requests', () => {
    it('should make successful GET request', async () => {
      const mockData = { id: 1, name: 'Test' }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockData,
      } as Response)

      const result = await apiService.get('/test')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      })
      expect(result).toEqual(mockData)
    })

    it('should include authorization header when token is available', async () => {
      const token = 'test-token'
      localStorageMock.getItem.mockReturnValue(token)
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({}),
      } as Response)

      await apiService.get('/test')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      })
    })
  })

  describe('POST requests', () => {
    it('should make successful POST request with data', async () => {
      const requestData = { name: 'Test' }
      const responseData = { id: 1, ...requestData }
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => responseData,
      } as Response)

      const result = await apiService.post('/test', requestData)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestData),
      })
      expect(result).toEqual(responseData)
    })

    it('should make POST request without body when no data provided', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({}),
      } as Response)

      await apiService.post('/test')

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: undefined,
      })
    })
  })

  describe('Error handling', () => {
    it('should throw ApiException for HTTP errors', async () => {
      const errorResponse = {
        detail: 'Validation failed',
        message: 'Invalid data',
      }
      
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => errorResponse,
      } as Response)

      await expect(apiService.get('/test')).rejects.toThrow(ApiException)
      
      try {
        await apiService.get('/test')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiException)
        expect((error as ApiException).status).toBe(400)
        expect((error as ApiException).message).toBe('Validation failed')
      }
    })

    it('should handle non-JSON error responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: async () => { throw new Error('Not JSON') },
      } as Response)

      await expect(apiService.get('/test')).rejects.toThrow(ApiException)
      
      try {
        await apiService.get('/test')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiException)
        expect((error as ApiException).status).toBe(500)
        expect((error as ApiException).message).toBe('HTTP 500: Internal Server Error')
      }
    })

    it('should handle 204 No Content responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 204,
      } as Response)

      const result = await apiService.delete('/test')
      expect(result).toEqual({})
    })
  })

  describe('File upload', () => {
    it('should upload file with additional data', async () => {
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' })
      const additionalData = { description: 'Test file' }
      const responseData = { id: 1, filename: 'test.txt' }
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => responseData,
      } as Response)

      const result = await apiService.upload('/upload', file, additionalData)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/upload', {
        method: 'POST',
        headers: {},
        body: expect.any(FormData),
      })
      expect(result).toEqual(responseData)
    })

    it('should include authorization header in file upload', async () => {
      const token = 'test-token'
      localStorageMock.getItem.mockReturnValue(token)
      
      const file = new File(['test'], 'test.txt')
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({}),
      } as Response)

      await apiService.upload('/upload', file)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: expect.any(FormData),
      })
    })
  })
})

describe('authApi', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  it('should call login endpoint with credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'password' }
    const authResponse = {
      accessToken: 'access-token',
      refreshToken: 'refresh-token',
      user: { id: 1, email: '<EMAIL>' },
    }
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: async () => authResponse,
    } as Response)

    const result = await authApi.login(credentials.email, credentials.password)

    expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(credentials),
    })
    expect(result).toEqual(authResponse)
  })

  it('should call register endpoint with user data', async () => {
    const userData = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      password: 'Password123!',
      role: 'Client',
    }
    const userResponse = {
      id: 1,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    }
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 201,
      json: async () => userResponse,
    } as Response)

    const result = await authApi.register(userData)

    expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(userData),
    })
    expect(result).toEqual(userResponse)
  })

  it('should call refresh token endpoint', async () => {
    const refreshToken = 'refresh-token'
    const tokenResponse = {
      accessToken: 'new-access-token',
      refreshToken: 'new-refresh-token',
    }
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: async () => tokenResponse,
    } as Response)

    const result = await authApi.refreshToken(refreshToken)

    expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    })
    expect(result).toEqual(tokenResponse)
  })
})

describe('ApiException', () => {
  it('should create exception with message and status', () => {
    const exception = new ApiException('Test error', 400)
    
    expect(exception.message).toBe('Test error')
    expect(exception.status).toBe(400)
    expect(exception.name).toBe('ApiException')
  })

  it('should create exception with details', () => {
    const details = { field: 'email', error: 'Invalid format' }
    const exception = new ApiException('Validation error', 400, details)
    
    expect(exception.details).toEqual(details)
  })
})
