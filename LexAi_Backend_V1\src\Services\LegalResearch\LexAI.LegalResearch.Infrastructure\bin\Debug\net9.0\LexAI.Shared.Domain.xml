<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.Shared.Domain</name>
    </assembly>
    <members>
        <member name="T:LexAI.Shared.Domain.Common.AuditableEntity">
            <summary>
            Base auditable entity with audit trail functionality
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditableEntity.AuditEntries">
            <summary>
            Collection of audit entries for this entity
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.AuditableEntity.AddAuditEntry(System.String,System.String,System.String)">
            <summary>
            Adds an audit entry to track changes
            </summary>
            <param name="action">The action performed (Create, Update, Delete)</param>
            <param name="userId">The user who performed the action</param>
            <param name="changes">Description of changes made</param>
        </member>
        <member name="T:LexAI.Shared.Domain.Common.AuditEntry">
            <summary>
            Represents an audit entry for tracking entity changes
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.EntityId">
            <summary>
            The ID of the entity being audited
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.EntityType">
            <summary>
            The type of entity being audited
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.Action">
            <summary>
            The action performed (Create, Update, Delete, etc.)
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.UserId">
            <summary>
            The user who performed the action
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.Timestamp">
            <summary>
            When the action was performed
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.Changes">
            <summary>
            Description of changes made (JSON format for complex changes)
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.IpAddress">
            <summary>
            IP address of the user who performed the action
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.AuditEntry.UserAgent">
            <summary>
            User agent of the client used to perform the action
            </summary>
        </member>
        <member name="T:LexAI.Shared.Domain.Common.BaseEntity">
            <summary>
            Base entity class providing common properties for all domain entities
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.Id">
            <summary>
            Unique identifier for the entity
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.CreatedAt">
            <summary>
            Date and time when the entity was created
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.UpdatedAt">
            <summary>
            Date and time when the entity was last updated
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.CreatedBy">
            <summary>
            Identifier of the user who created the entity
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.UpdatedBy">
            <summary>
            Identifier of the user who last updated the entity
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.IsDeleted">
            <summary>
            Indicates if the entity is soft deleted
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.DeletedAt">
            <summary>
            Date and time when the entity was soft deleted
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.BaseEntity.DeletedBy">
            <summary>
            Identifier of the user who deleted the entity
            </summary>
        </member>
        <member name="T:LexAI.Shared.Domain.Common.PagedResult`1">
            <summary>
            Represents a paginated result set.
            </summary>
            <typeparam name="T">The type of items in the result set.</typeparam>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.Items">
            <summary>
            Gets or sets the items in the result set.
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.TotalCount">
            <summary>
            Gets or sets the total count of items.
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.PageNumber">
            <summary>
            Gets or sets the current page number.
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.PageSize">
            <summary>
            Gets or sets the size of the page.
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.HasPreviousPage">
            <summary>
            Gets or sets a value indicating whether there is a previous page.
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.HasNextPage">
            <summary>
            Gets or sets a value indicating whether there is a next page.
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Common.PagedResult`1.TotalPages">
            <summary>
            Gets or sets the total number of pages.
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.PagedResult`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:LexAI.Shared.Domain.Common.PagedResult`1"/> class.
            </summary>
            <param name="items">The items in the result set.</param>
            <param name="totalCount">The total count of items.</param>
            <param name="pageNumber">The current page number.</param>
            <param name="pageSize">The size of the page.</param>
            <param name="totalPage"></param>
            <param name="hasNextPage"></param>
            <param name="hasPreviousPage"></param>
        </member>
        <member name="T:LexAI.Shared.Domain.Common.ValueObject">
            <summary>
            Base class for value objects in the domain
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.ValueObject.GetAtomicValues">
            <summary>
            Gets the atomic values that make up this value object
            </summary>
            <returns>Collection of atomic values</returns>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.ValueObject.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object
            </summary>
            <param name="obj">The object to compare with the current object</param>
            <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.ValueObject.GetHashCode">
            <summary>
            Serves as the default hash function
            </summary>
            <returns>A hash code for the current object</returns>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.ValueObject.GetCopy">
            <summary>
            Creates a copy of this value object
            </summary>
            <returns>A copy of this value object</returns>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.ValueObject.op_Equality(LexAI.Shared.Domain.Common.ValueObject,LexAI.Shared.Domain.Common.ValueObject)">
            <summary>
            Equality operator
            </summary>
            <param name="left">Left operand</param>
            <param name="right">Right operand</param>
            <returns>true if operands are equal; otherwise, false</returns>
        </member>
        <member name="M:LexAI.Shared.Domain.Common.ValueObject.op_Inequality(LexAI.Shared.Domain.Common.ValueObject,LexAI.Shared.Domain.Common.ValueObject)">
            <summary>
            Inequality operator
            </summary>
            <param name="left">Left operand</param>
            <param name="right">Right operand</param>
            <returns>true if operands are not equal; otherwise, false</returns>
        </member>
        <member name="T:LexAI.Shared.Domain.Enums.UserRole">
            <summary>
            Enumeration of user roles in the LexAI system
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.UserRole.Administrator">
            <summary>
            System administrator with full access
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.UserRole.SeniorLawyer">
            <summary>
            Senior lawyer with management capabilities
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.UserRole.Lawyer">
            <summary>
            Regular lawyer with standard access
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.UserRole.LegalAssistant">
            <summary>
            Legal assistant with limited access
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.UserRole.Client">
            <summary>
            Client with restricted access to their own data
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.UserRole.Guest">
            <summary>
            Guest user with minimal access
            </summary>
        </member>
        <member name="T:LexAI.Shared.Domain.Enums.DocumentType">
            <summary>
            Enumeration of document types in the legal system
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Contract">
            <summary>
            Legal contract document
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Letter">
            <summary>
            Formal legal letter
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Notice">
            <summary>
            Legal notice or demand letter
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Statutes">
            <summary>
            Company statutes or bylaws
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Brief">
            <summary>
            Legal brief or memorandum
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Pleading">
            <summary>
            Court pleading document
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Opinion">
            <summary>
            Legal opinion or advice
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.PowerOfAttorney">
            <summary>
            Power of attorney document
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Will">
            <summary>
            Will or testament
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.DocumentType.Other">
            <summary>
            Other legal document type
            </summary>
        </member>
        <member name="T:LexAI.Shared.Domain.Enums.CaseStatus">
            <summary>
            Enumeration of case or matter status
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.New">
            <summary>
            New case, not yet started
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.InProgress">
            <summary>
            Case is currently being worked on
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.OnHold">
            <summary>
            Case is on hold or paused
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.WaitingForClient">
            <summary>
            Waiting for client response or action
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.WaitingForCourt">
            <summary>
            Waiting for court or third party
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.Completed">
            <summary>
            Case has been completed successfully
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.Closed">
            <summary>
            Case has been closed without completion
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.CaseStatus.Cancelled">
            <summary>
            Case has been cancelled
            </summary>
        </member>
        <member name="T:LexAI.Shared.Domain.Enums.PracticeArea">
            <summary>
            Enumeration of legal practice areas
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Corporate">
            <summary>
            Corporate and business law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Employment">
            <summary>
            Employment and labor law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.RealEstate">
            <summary>
            Real estate law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Family">
            <summary>
            Family law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Criminal">
            <summary>
            Criminal law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.CivilLitigation">
            <summary>
            Civil litigation
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.IntellectualProperty">
            <summary>
            Intellectual property law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Tax">
            <summary>
            Tax law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Immigration">
            <summary>
            Immigration law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.PersonalInjury">
            <summary>
            Personal injury law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Contract">
            <summary>
            Contract law
            </summary>
        </member>
        <member name="F:LexAI.Shared.Domain.Enums.PracticeArea.Other">
            <summary>
            Other practice area
            </summary>
        </member>
        <member name="T:LexAI.Shared.Domain.Exceptions.DomainException">
            <summary>
            Base exception class for domain-specific exceptions
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Exceptions.DomainException.ErrorCode">
            <summary>
            Error code for the exception
            </summary>
        </member>
        <member name="P:LexAI.Shared.Domain.Exceptions.DomainException.Details">
            <summary>
            Additional details about the exception
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.DomainException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the DomainException class
            </summary>
            <param name="message">The error message</param>
            <param name="errorCode">The error code</param>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.DomainException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the DomainException class
            </summary>
            <param name="message">The error message</param>
            <param name="errorCode">The error code</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.DomainException.WithDetail(System.String,System.Object)">
            <summary>
            Adds additional details to the exception
            </summary>
            <param name="key">The detail key</param>
            <param name="value">The detail value</param>
            <returns>The current exception instance for method chaining</returns>
        </member>
        <member name="T:LexAI.Shared.Domain.Exceptions.EntityNotFoundException">
            <summary>
            Exception thrown when a requested entity is not found
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.EntityNotFoundException.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the EntityNotFoundException class
            </summary>
            <param name="entityType">The type of entity that was not found</param>
            <param name="entityId">The ID of the entity that was not found</param>
        </member>
        <member name="T:LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException">
            <summary>
            Exception thrown when a business rule is violated
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.BusinessRuleViolationException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the BusinessRuleViolationException class
            </summary>
            <param name="message">The error message describing the rule violation</param>
            <param name="ruleName">The name of the violated business rule</param>
        </member>
        <member name="T:LexAI.Shared.Domain.Exceptions.EntityAlreadyExistsException">
            <summary>
            Exception thrown when an entity already exists
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.EntityAlreadyExistsException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the EntityAlreadyExistsException class
            </summary>
            <param name="entityType">The type of entity that already exists</param>
            <param name="identifier">The identifier of the existing entity</param>
        </member>
        <member name="T:LexAI.Shared.Domain.Exceptions.OperationNotPermittedException">
            <summary>
            Exception thrown when an operation is not permitted
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.OperationNotPermittedException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the OperationNotPermittedException class
            </summary>
            <param name="operation">The operation that is not permitted</param>
            <param name="reason">The reason why the operation is not permitted</param>
        </member>
        <member name="T:LexAI.Shared.Domain.Exceptions.InvalidDataException">
            <summary>
            Exception thrown when invalid data is provided
            </summary>
        </member>
        <member name="M:LexAI.Shared.Domain.Exceptions.InvalidDataException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the InvalidDataException class
            </summary>
            <param name="message">The error message</param>
            <param name="fieldName">The name of the invalid field</param>
        </member>
    </members>
</doc>
