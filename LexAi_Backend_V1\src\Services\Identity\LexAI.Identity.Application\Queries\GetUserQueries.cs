using LexAI.Identity.Application.DTOs;
using LexAI.Identity.Application.Interfaces;
using LexAI.Shared.Domain.Common;
using LexAI.Shared.Domain.Enums;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LexAI.Identity.Application.Queries;

/// <summary>
/// Query to get a user by ID
/// </summary>
public class GetUserByIdQuery : IRequest<UserDto>
{
    /// <summary>
    /// User ID to retrieve
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Initializes a new instance of GetUserByIdQuery
    /// </summary>
    /// <param name="userId">User ID</param>
    public GetUserByIdQuery(Guid userId)
    {
        UserId = userId;
    }
}

/// <summary>
/// Handler for GetUserByIdQuery
/// </summary>
public class GetUserByIdQueryHandler : IRequestHandler<GetUserByIdQuery, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<GetUserByIdQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the GetUserByIdQueryHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="logger">Logger</param>
    public GetUserByIdQueryHandler(IUserRepository userRepository, ILogger<GetUserByIdQueryHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the GetUserByIdQuery
    /// </summary>
    /// <param name="request">Get user by ID query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User DTO</returns>
    public async Task<UserDto> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving user {UserId}", request.UserId);

        var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning("User {UserId} not found", request.UserId);
            throw new EntityNotFoundException("User", request.UserId);
        }

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            IsEmailVerified = user.IsEmailVerified,
            IsActive = user.IsActive,
            IsLocked = user.IsLocked,
            LastLoginAt = user.LastLoginAt,
            PreferredLanguage = user.PreferredLanguage,
            TimeZone = user.TimeZone,
            ProfilePictureUrl = user.ProfilePictureUrl,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };
    }
}

/// <summary>
/// Query to get a user by email
/// </summary>
public class GetUserByEmailQuery : IRequest<UserDto?>
{
    /// <summary>
    /// Email address to search for
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// Initializes a new instance of GetUserByEmailQuery
    /// </summary>
    /// <param name="email">Email address</param>
    public GetUserByEmailQuery(string email)
    {
        Email = email;
    }
}

/// <summary>
/// Handler for GetUserByEmailQuery
/// </summary>
public class GetUserByEmailQueryHandler : IRequestHandler<GetUserByEmailQuery, UserDto?>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<GetUserByEmailQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the GetUserByEmailQueryHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="logger">Logger</param>
    public GetUserByEmailQueryHandler(IUserRepository userRepository, ILogger<GetUserByEmailQueryHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the GetUserByEmailQuery
    /// </summary>
    /// <param name="request">Get user by email query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User DTO or null if not found</returns>
    public async Task<UserDto?> Handle(GetUserByEmailQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving user by email {Email}", request.Email);

        var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (user == null)
        {
            _logger.LogInformation("User with email {Email} not found", request.Email);
            return null;
        }

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            IsEmailVerified = user.IsEmailVerified,
            IsActive = user.IsActive,
            IsLocked = user.IsLocked,
            LastLoginAt = user.LastLoginAt,
            PreferredLanguage = user.PreferredLanguage,
            TimeZone = user.TimeZone,
            ProfilePictureUrl = user.ProfilePictureUrl,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };
    }
}

/// <summary>
/// Query to get all users with pagination
/// </summary>
public class GetUsersQuery : IRequest<PagedResult<UserDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term for name or email
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Optional role filter
    /// </summary>
    public UserRole? Role { get; set; }

    /// <summary>
    /// Optional active status filter
    /// </summary>
    public bool? IsActive { get; set; }
}

/// <summary>
/// Handler for GetUsersQuery
/// </summary>
public class GetUsersQueryHandler : IRequestHandler<GetUsersQuery, PagedResult<UserDto>>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<GetUsersQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the GetUsersQueryHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="logger">Logger</param>
    public GetUsersQueryHandler(IUserRepository userRepository, ILogger<GetUsersQueryHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the GetUsersQuery
    /// </summary>
    /// <param name="request">Get users query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of user DTOs</returns>
    public async Task<PagedResult<UserDto>> Handle(GetUsersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving users - Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}, Role: {Role}, Active: {IsActive}",
            request.PageNumber, request.PageSize, request.SearchTerm, request.Role, request.IsActive);

        PagedResult<Domain.Entities.User> users;

        if (!string.IsNullOrWhiteSpace(request.SearchTerm) || request.Role.HasValue || request.IsActive.HasValue)
        {
            users = await _userRepository.SearchAsync(
                request.SearchTerm,
                request.Role,
                request.IsActive,
                request.PageNumber,
                request.PageSize,
                cancellationToken);
        }
        else
        {
            users = await _userRepository.GetAllAsync(request.PageNumber, request.PageSize, cancellationToken);
        }

        var userDtos = users.Items.Select(user => new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            IsEmailVerified = user.IsEmailVerified,
            IsActive = user.IsActive,
            IsLocked = user.IsLocked,
            LastLoginAt = user.LastLoginAt,
            PreferredLanguage = user.PreferredLanguage,
            TimeZone = user.TimeZone,
            ProfilePictureUrl = user.ProfilePictureUrl,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        }).ToList();

        return new PagedResult<UserDto>(
                userDtos,
                users.TotalCount,
                users.PageNumber,
                users.PageSize,
                users.TotalPages,
                users.HasNextPage,
                users.HasPreviousPage
            );
    }
}

/// <summary>
/// Query to get users by role
/// </summary>
public class GetUsersByRoleQuery : IRequest<IEnumerable<UserDto>>
{
    /// <summary>
    /// User role to filter by
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// Initializes a new instance of GetUsersByRoleQuery
    /// </summary>
    /// <param name="role">User role</param>
    public GetUsersByRoleQuery(UserRole role)
    {
        Role = role;
    }
}

/// <summary>
/// Handler for GetUsersByRoleQuery
/// </summary>
public class GetUsersByRoleQueryHandler : IRequestHandler<GetUsersByRoleQuery, IEnumerable<UserDto>>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<GetUsersByRoleQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the GetUsersByRoleQueryHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="logger">Logger</param>
    public GetUsersByRoleQueryHandler(IUserRepository userRepository, ILogger<GetUsersByRoleQueryHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the GetUsersByRoleQuery
    /// </summary>
    /// <param name="request">Get users by role query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of user DTOs</returns>
    public async Task<IEnumerable<UserDto>> Handle(GetUsersByRoleQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving users by role {Role}", request.Role);

        var users = await _userRepository.GetByRoleAsync(request.Role, cancellationToken);

        return users.Select(user => new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber?.Value,
            Role = user.Role,
            IsEmailVerified = user.IsEmailVerified,
            IsActive = user.IsActive,
            IsLocked = user.IsLocked,
            LastLoginAt = user.LastLoginAt,
            PreferredLanguage = user.PreferredLanguage,
            TimeZone = user.TimeZone,
            ProfilePictureUrl = user.ProfilePictureUrl,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        });
    }
}
