using LexAI.DocumentAnalysis.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service de validation des documents avant analyse
/// </summary>
public interface IDocumentValidationService
{
    /// <summary>
    /// Valide un document avant analyse
    /// </summary>
    Task<DocumentValidationResult> ValidateDocumentAsync(
        byte[] documentContent, 
        string contentType, 
        string fileName,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Résultat de validation d'un document
/// </summary>
public class DocumentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public DocumentValidationMetadata Metadata { get; set; } = new();
}

/// <summary>
/// Métadonnées de validation
/// </summary>
public class DocumentValidationMetadata
{
    public long FileSizeBytes { get; set; }
    public string DetectedContentType { get; set; } = string.Empty;
    public string DetectedEncoding { get; set; } = string.Empty;
    public int EstimatedPageCount { get; set; }
    public bool IsEncrypted { get; set; }
    public bool HasImages { get; set; }
    public bool HasTables { get; set; }
    public string Language { get; set; } = string.Empty;
    public double TextDensity { get; set; }
}

/// <summary>
/// Implémentation du service de validation
/// </summary>
public class DocumentValidationService : IDocumentValidationService
{
    private readonly AzureDocumentIntelligenceSettings _settings;
    private readonly ILogger<DocumentValidationService> _logger;

    public DocumentValidationService(
        IOptions<AzureDocumentIntelligenceSettings> settings,
        ILogger<DocumentValidationService> logger)
    {
        _settings = settings.Value;
        _logger = logger;
    }

    /// <summary>
    /// Valide un document avant analyse
    /// </summary>
    public async Task<DocumentValidationResult> ValidateDocumentAsync(
        byte[] documentContent, 
        string contentType, 
        string fileName,
        CancellationToken cancellationToken = default)
    {
        var result = new DocumentValidationResult();
        
        try
        {
            _logger.LogInformation("Starting document validation for file: {FileName}", fileName);

            // 1. Validation de base
            ValidateBasicRequirements(documentContent, contentType, fileName, result);

            // 2. Validation de la taille
            ValidateFileSize(documentContent, result);

            // 3. Validation du type de contenu
            ValidateContentType(contentType, result);

            // 4. Validation du contenu du fichier
            await ValidateFileContentAsync(documentContent, contentType, result, cancellationToken);

            // 5. Extraction des métadonnées
            await ExtractMetadataAsync(documentContent, contentType, result, cancellationToken);

            // 6. Validation de la qualité du document
            ValidateDocumentQuality(result);

            result.IsValid = result.Errors.Count == 0;

            _logger.LogInformation("Document validation completed. Valid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}",
                result.IsValid, result.Errors.Count, result.Warnings.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during document validation for file: {FileName}", fileName);
            result.IsValid = false;
            result.Errors.Add($"Erreur lors de la validation: {ex.Message}");
            return result;
        }
    }

    private void ValidateBasicRequirements(byte[] documentContent, string contentType, string fileName, DocumentValidationResult result)
    {
        if (documentContent == null || documentContent.Length == 0)
        {
            result.Errors.Add("Le document est vide");
            return;
        }

        if (string.IsNullOrEmpty(contentType))
        {
            result.Errors.Add("Le type de contenu n'est pas spécifié");
        }

        if (string.IsNullOrEmpty(fileName))
        {
            result.Warnings.Add("Le nom de fichier n'est pas spécifié");
        }

        // Vérifier l'extension du fichier
        if (!string.IsNullOrEmpty(fileName))
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            var allowedExtensions = new[] { ".pdf", ".docx", ".doc", ".txt", ".jpg", ".jpeg", ".png", ".tiff" };
            
            if (!allowedExtensions.Contains(extension))
            {
                result.Warnings.Add($"Extension de fichier non recommandée: {extension}");
            }
        }
    }

    private void ValidateFileSize(byte[] documentContent, DocumentValidationResult result)
    {
        var fileSizeBytes = documentContent.Length;
        var maxSizeBytes = _settings.GetMaxFileSizeBytes();

        result.Metadata.FileSizeBytes = fileSizeBytes;

        if (fileSizeBytes > maxSizeBytes)
        {
            result.Errors.Add($"Le fichier est trop volumineux ({fileSizeBytes / 1024 / 1024:F1} MB). Taille maximale autorisée: {_settings.MaxFileSizeMB} MB");
        }
        else if (fileSizeBytes > maxSizeBytes * 0.8) // Avertissement à 80% de la limite
        {
            result.Warnings.Add($"Le fichier est volumineux ({fileSizeBytes / 1024 / 1024:F1} MB). Cela peut affecter les performances");
        }

        if (fileSizeBytes < 1024) // Moins de 1KB
        {
            result.Warnings.Add("Le fichier semble très petit. Vérifiez qu'il contient du contenu analysable");
        }
    }

    private void ValidateContentType(string contentType, DocumentValidationResult result)
    {
        if (!_settings.IsFileTypeSupported(contentType))
        {
            result.Errors.Add($"Type de fichier non supporté: {contentType}. Types supportés: {string.Join(", ", _settings.SupportedFileTypes)}");
        }

        result.Metadata.DetectedContentType = contentType;
    }

    private async Task ValidateFileContentAsync(byte[] documentContent, string contentType, DocumentValidationResult result, CancellationToken cancellationToken)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        try
        {
            // Validation spécifique selon le type de fichier
            switch (contentType.ToLowerInvariant())
            {
                case "application/pdf":
                    ValidatePdfContent(documentContent, result);
                    break;
                case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                    ValidateDocxContent(documentContent, result);
                    break;
                case "text/plain":
                    ValidateTextContent(documentContent, result);
                    break;
                default:
                    // Validation générique pour les images
                    if (contentType.StartsWith("image/"))
                    {
                        ValidateImageContent(documentContent, result);
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            result.Warnings.Add($"Impossible de valider le contenu du fichier: {ex.Message}");
        }
    }

    private void ValidatePdfContent(byte[] documentContent, DocumentValidationResult result)
    {
        // Vérifier la signature PDF
        if (documentContent.Length < 4 || 
            documentContent[0] != 0x25 || documentContent[1] != 0x50 || 
            documentContent[2] != 0x44 || documentContent[3] != 0x46) // %PDF
        {
            result.Errors.Add("Le fichier ne semble pas être un PDF valide");
            return;
        }

        // Vérifier si le PDF est chiffré (signature basique)
        var content = System.Text.Encoding.ASCII.GetString(documentContent.Take(1024).ToArray());
        if (content.Contains("/Encrypt"))
        {
            result.Metadata.IsEncrypted = true;
            result.Warnings.Add("Le PDF semble être chiffré. Cela peut affecter l'extraction de texte");
        }

        // Estimer le nombre de pages (approximatif)
        var pageMatches = System.Text.RegularExpressions.Regex.Matches(content, @"/Type\s*/Page\b");
        result.Metadata.EstimatedPageCount = Math.Max(1, pageMatches.Count);
    }

    private void ValidateDocxContent(byte[] documentContent, DocumentValidationResult result)
    {
        // Vérifier la signature ZIP (DOCX est un fichier ZIP)
        if (documentContent.Length < 4 || 
            documentContent[0] != 0x50 || documentContent[1] != 0x4B)
        {
            result.Errors.Add("Le fichier ne semble pas être un DOCX valide");
            return;
        }

        result.Metadata.EstimatedPageCount = 1; // Estimation basique pour DOCX
    }

    private void ValidateTextContent(byte[] documentContent, DocumentValidationResult result)
    {
        try
        {
            var text = System.Text.Encoding.UTF8.GetString(documentContent);
            
            if (string.IsNullOrWhiteSpace(text))
            {
                result.Errors.Add("Le fichier texte est vide ou ne contient que des espaces");
                return;
            }

            // Détecter l'encodage
            result.Metadata.DetectedEncoding = "UTF-8";
            
            // Calculer la densité de texte
            var wordCount = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            result.Metadata.TextDensity = (double)wordCount / documentContent.Length * 1000; // mots pour 1000 caractères

            if (wordCount < 10)
            {
                result.Warnings.Add("Le document contient très peu de texte analysable");
            }
        }
        catch (Exception ex)
        {
            result.Warnings.Add($"Impossible de décoder le fichier texte: {ex.Message}");
        }
    }

    private void ValidateImageContent(byte[] documentContent, DocumentValidationResult result)
    {
        result.Metadata.HasImages = true;
        
        // Vérifications basiques pour les images
        if (documentContent.Length < 100)
        {
            result.Warnings.Add("L'image semble très petite. La qualité d'extraction de texte peut être affectée");
        }

        // Pour les images, recommander l'utilisation d'Azure Document Intelligence
        result.Warnings.Add("Pour les images, assurez-vous que le texte est lisible et de bonne qualité");
    }

    private async Task ExtractMetadataAsync(byte[] documentContent, string contentType, DocumentValidationResult result, CancellationToken cancellationToken)
    {
        await Task.Delay(1, cancellationToken); // Pour éviter l'avertissement async

        // Détection de langue basique (à améliorer)
        if (contentType == "text/plain")
        {
            try
            {
                var text = System.Text.Encoding.UTF8.GetString(documentContent);
                result.Metadata.Language = DetectLanguage(text);
            }
            catch
            {
                result.Metadata.Language = "unknown";
            }
        }
    }

    private void ValidateDocumentQuality(DocumentValidationResult result)
    {
        // Validation de la qualité basée sur les métadonnées
        if (result.Metadata.IsEncrypted)
        {
            result.Warnings.Add("Document chiffré détecté. L'extraction de texte peut être limitée");
        }

        if (result.Metadata.TextDensity < 0.1 && result.Metadata.DetectedContentType == "text/plain")
        {
            result.Warnings.Add("Faible densité de texte détectée. Le document peut contenir principalement des espaces ou caractères non-textuels");
        }

        if (result.Metadata.EstimatedPageCount > 100)
        {
            result.Warnings.Add("Document très long détecté. Le traitement peut prendre plus de temps");
        }
    }

    private string DetectLanguage(string text)
    {
        // Détection de langue très basique (à améliorer avec une vraie bibliothèque)
        var frenchWords = new[] { "le", "la", "les", "de", "du", "des", "et", "ou", "que", "qui", "dans", "pour", "avec", "sur" };
        var englishWords = new[] { "the", "and", "or", "that", "which", "in", "for", "with", "on", "at", "by", "from" };

        var words = text.ToLowerInvariant().Split(new[] { ' ', '\t', '\n', '\r', '.', ',', ';', ':', '!', '?' }, StringSplitOptions.RemoveEmptyEntries);
        
        var frenchCount = words.Count(w => frenchWords.Contains(w));
        var englishCount = words.Count(w => englishWords.Contains(w));

        if (frenchCount > englishCount)
            return "fr";
        else if (englishCount > frenchCount)
            return "en";
        else
            return "unknown";
    }
}
