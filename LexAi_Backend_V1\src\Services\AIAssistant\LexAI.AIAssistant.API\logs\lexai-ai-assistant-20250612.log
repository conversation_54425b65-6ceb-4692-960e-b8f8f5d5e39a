2025-06-12 19:49:14.630 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-12 19:49:14.748 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-12 19:49:15.120 +04:00 [INF] Now listening on: https://localhost:58323
2025-06-12 19:49:15.124 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-12 19:49:15.211 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-12 19:49:15.213 +04:00 [INF] Hosting environment: Development
2025-06-12 19:49:15.215 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-12 19:49:16.553 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/ - null null
2025-06-12 19:49:16.745 +04:00 [INF] Request GET / started with correlation ID d9b92666-2f8b-46d3-9b00-f4e8130563e1
2025-06-12 19:49:18.578 +04:00 [INF] Request GET / completed in 1826ms with status 404 (Correlation ID: d9b92666-2f8b-46d3-9b00-f4e8130563e1)
2025-06-12 19:49:18.595 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/ - 404 0 null 2043.58ms
2025-06-12 19:49:18.630 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:58323/, Response status code: 404
