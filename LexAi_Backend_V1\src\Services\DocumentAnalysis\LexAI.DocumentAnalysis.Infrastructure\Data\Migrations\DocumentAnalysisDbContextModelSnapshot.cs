﻿// <auto-generated />
using System;
using LexAI.DocumentAnalysis.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LexAI.DocumentAnalysis.Infrastructure.Data.Migrations
{
    [DbContext(typeof(DocumentAnalysisDbContext))]
    partial class DocumentAnalysisDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.ClauseAnalysis", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Analysis")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClauseText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClauseType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<double>("ConfidenceScore")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("DocumentAnalysisResultId")
                        .HasColumnType("uuid");

                    b.Property<int>("EndPosition")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("StartPosition")
                        .HasColumnType("integer");

                    b.Property<string>("SuggestedRevision")
                        .HasColumnType("text");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentAnalysisResultId");

                    b.HasIndex("RiskLevel");

                    b.ToTable("clause_analyses", (string)null);
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AnalysisContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("AnalyzedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("ConfidenceScore")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<string>("DocumentHash")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("DocumentName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentPurpose")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DocumentStoragePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("EstimatedCost")
                        .HasPrecision(10, 4)
                        .HasColumnType("numeric(10,4)");

                    b.Property<string>("ExecutiveSummary")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ExtractedText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FinancialTerms")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ImportantDates")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("KeyPoints")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MainParties")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ModelUsed")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OverallRiskLevel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProcessingTimeMs")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TokensUsed")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AnalyzedAt");

                    b.HasIndex("DocumentHash")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "Status");

                    b.ToTable("document_analysis_results", (string)null);
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.DocumentCitation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Context")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("DocumentAnalysisResultId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Reference")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<double>("RelevanceScore")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentAnalysisResultId");

                    b.HasIndex("Type");

                    b.ToTable("document_citations", (string)null);
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.DocumentRecommendation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("DocumentAnalysisResultId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LegalBasis")
                        .HasColumnType("text");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RelatedClauses")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SuggestedAction")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentAnalysisResultId");

                    b.HasIndex("Priority");

                    b.ToTable("document_recommendations", (string)null);
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.ExtractedEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("ConfidenceScore")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("DocumentAnalysisResultId")
                        .HasColumnType("uuid");

                    b.Property<int>("EndPosition")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("NormalizedValue")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("StartPosition")
                        .HasColumnType("integer");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentAnalysisResultId");

                    b.HasIndex("Type");

                    b.ToTable("extracted_entities", (string)null);
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.RiskAssessment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AffectedClauses")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("DocumentAnalysisResultId")
                        .HasColumnType("uuid");

                    b.Property<string>("Impact")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Mitigation")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("Probability")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.Property<string>("RiskType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentAnalysisResultId");

                    b.HasIndex("Severity");

                    b.ToTable("risk_assessments", (string)null);
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.ClauseAnalysis", b =>
                {
                    b.HasOne("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", "DocumentAnalysisResult")
                        .WithMany("Clauses")
                        .HasForeignKey("DocumentAnalysisResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentAnalysisResult");
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.DocumentCitation", b =>
                {
                    b.HasOne("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", "DocumentAnalysisResult")
                        .WithMany("Citations")
                        .HasForeignKey("DocumentAnalysisResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentAnalysisResult");
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.DocumentRecommendation", b =>
                {
                    b.HasOne("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", "DocumentAnalysisResult")
                        .WithMany("Recommendations")
                        .HasForeignKey("DocumentAnalysisResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentAnalysisResult");
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.ExtractedEntity", b =>
                {
                    b.HasOne("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", "DocumentAnalysisResult")
                        .WithMany("Entities")
                        .HasForeignKey("DocumentAnalysisResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentAnalysisResult");
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.RiskAssessment", b =>
                {
                    b.HasOne("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", "DocumentAnalysisResult")
                        .WithMany("Risks")
                        .HasForeignKey("DocumentAnalysisResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DocumentAnalysisResult");
                });

            modelBuilder.Entity("LexAI.DocumentAnalysis.Domain.Entities.DocumentAnalysisResult", b =>
                {
                    b.Navigation("Citations");

                    b.Navigation("Clauses");

                    b.Navigation("Entities");

                    b.Navigation("Recommendations");

                    b.Navigation("Risks");
                });
#pragma warning restore 612, 618
        }
    }
}
