D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\obj\Debug\net9.0\LexAI.DataPreprocessing.Infrastructure.csproj.AssemblyReference.cache
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\obj\Debug\net9.0\LexAI.DataPreprocessing.Infrastructure.GeneratedMSBuildEditorConfig.editorconfig
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\obj\Debug\net9.0\LexAI.DataPreprocessing.Infrastructure.AssemblyInfoInputs.cache
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\obj\Debug\net9.0\LexAI.DataPreprocessing.Infrastructure.AssemblyInfo.cs
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\obj\Debug\net9.0\LexAI.DataPreprocessing.Infrastructure.csproj.CoreCompileInputs.cache
D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.Infrastructure\obj\Debug\net9.0\LexAI.DataPreprocessing.Infrastructure.sourcelink.json
