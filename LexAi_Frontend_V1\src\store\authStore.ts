import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, AuthState, RegisterData, LoginData, AuthResponse, ChangePasswordData, ForgotPasswordData, ResetPasswordData } from '../types/index'
import { authApi, usersApi, ApiException } from '../services/api'

interface AuthStore extends AuthState {
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  updateProfile: (userData: Partial<User>) => Promise<void>
  changePassword: (passwordData: ChangePasswordData) => Promise<void>
  forgotPassword: (email: string) => Promise<void>
  resetPassword: (resetData: ResetPasswordData) => Promise<void>
  getCurrentUser: () => Promise<void>
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  accessToken: string | null
  refreshToken: string | null
  setTokens: (accessToken: string, refreshToken: string) => void
  clearTokens: () => void
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      accessToken: null,
      refreshToken: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        try {
          const authResponse: AuthResponse = await authApi.login({ email, password })

          // Store tokens and user data
          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
            accessToken: authResponse.accessToken,
            refreshToken: authResponse.refreshToken
          })

          // Store access token in localStorage for API calls
          localStorage.setItem('accessToken', authResponse.accessToken)
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })

        try {
          // Appeler l'endpoint de déconnexion
          await authApi.logout()
        } catch (error) {
          // Continuer même si l'appel API échoue
          console.warn('Logout API call failed:', error)
        } finally {
          // Clear tokens from localStorage
          localStorage.removeItem('accessToken')

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            accessToken: null,
            refreshToken: null
          })
        }
      },

      register: async (userData: RegisterData) => {
        set({ isLoading: true })
        try {
          const authResponse: AuthResponse = await authApi.register(userData)
          const newUser: User = authResponse.user

          // Store the user data (registration doesn't return tokens, user needs to login)
          set({
            user: newUser,
            isAuthenticated: false, // User needs to verify email or login
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true })
        try {
          const { user } = get()
          if (!user) {
            throw new Error('Utilisateur non connecté')
          }

          const updatedUser: User = await usersApi.updateUser(user.id, userData)

          set({
            user: updatedUser,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      changePassword: async (passwordData: ChangePasswordData) => {
        set({ isLoading: true })
        try {
          await authApi.changePassword({
            currentPassword: passwordData.currentPassword,
            newPassword: passwordData.newPassword
          })
          set({ isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true })
        try {
          await authApi.forgotPassword({ email })
          set({ isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      resetPassword: async (resetData: ResetPasswordData) => {
        set({ isLoading: true })
        try {
          await authApi.resetPassword({
            token: resetData.token,
            newPassword: resetData.newPassword
          })
          set({ isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      getCurrentUser: async () => {
        set({ isLoading: true })
        try {
          const user: User = await authApi.getCurrentUser()
          set({
            user,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          if (error instanceof ApiException) {
            throw new Error(error.message)
          }
          throw error
        }
      },

      setUser: (user: User | null) => {
        set({
          user,
          isAuthenticated: !!user
        })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setTokens: (accessToken: string, refreshToken: string) => {
        localStorage.setItem('accessToken', accessToken)
        set({ accessToken, refreshToken })
      },

      clearTokens: () => {
        localStorage.removeItem('accessToken')
        set({ accessToken: null, refreshToken: null })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken
      })
    }
  )
)
