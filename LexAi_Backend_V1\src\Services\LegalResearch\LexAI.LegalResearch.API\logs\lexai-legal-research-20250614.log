2025-06-14 15:53:31.605 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 15:53:31.691 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:53:32.155 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 15:53:32.157 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 15:53:32.229 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:53:32.231 +04:00 [INF] Hosting environment: Development
2025-06-14 15:53:32.233 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 15:53:32.760 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 15:53:32.973 +04:00 [INF] Request GET / started with correlation ID 53be605c-46ed-46fa-855f-d818ece31abc
2025-06-14 15:53:33.045 +04:00 [INF] Request GET / completed in 66ms with status 404 (Correlation ID: 53be605c-46ed-46fa-855f-d818ece31abc)
2025-06-14 15:53:33.054 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 302.9896ms
2025-06-14 15:53:33.065 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-14 15:53:53.195 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 15:53:53.195 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 15:53:53.257 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 5654856e-0295-4402-8ddc-7bff839a20b9
2025-06-14 15:53:53.261 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID c428c608-00b4-4f91-9682-acff0ec9182b
2025-06-14 15:53:53.268 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:53:53.269 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:53:53.271 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 7ms with status 204 (Correlation ID: 5654856e-0295-4402-8ddc-7bff839a20b9)
2025-06-14 15:53:53.271 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 3ms with status 204 (Correlation ID: c428c608-00b4-4f91-9682-acff0ec9182b)
2025-06-14 15:53:53.275 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 113.4454ms
2025-06-14 15:53:53.277 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-14 15:53:53.278 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 121.4764ms
2025-06-14 15:53:53.287 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 1a6a655f-d713-4240-920f-d3cbce68a5eb
2025-06-14 15:53:53.291 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:53:53.340 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:53:53.352 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-14 15:53:53.375 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-14 15:54:22.084 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-14 15:54:22.089 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 28704.5554ms
2025-06-14 15:54:22.091 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 152e8da5-a3f7-43bf-b3c0-146d8e4de739
2025-06-14 15:54:22.096 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-14 15:54:22.096 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:54:30.451 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: OpenAI API key not configured
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService..ctor(HttpClient httpClient, IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 34
   at InvokeStub_OpenAIEmbeddingService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method10(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Program.cs:line 261
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__11>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Program.cs:line 246
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-14 15:54:30.453 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:54:30.498 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-14 15:54:30.499 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-14 15:54:31.871 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 1369.9477ms
2025-06-14 15:54:31.876 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-14 15:54:33.484 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: OpenAI API key not configured
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService..ctor(HttpClient httpClient, IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 34
   at InvokeStub_OpenAIEmbeddingService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method10(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Program.cs:line 261
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__11>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Program.cs:line 246
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-14 15:54:33.493 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 500 null text/plain; charset=utf-8 40215.5005ms
2025-06-14 15:54:33.510 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 500 null text/plain; charset=utf-8 11426.1419ms
