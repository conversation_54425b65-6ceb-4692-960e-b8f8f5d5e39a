2025-06-14 19:30:47.253 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 19:30:47.343 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 19:30:47.639 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 19:30:47.641 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 19:30:47.708 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 19:30:47.709 +04:00 [INF] Hosting environment: Development
2025-06-14 19:30:47.711 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 19:30:49.538 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 19:30:49.958 +04:00 [INF] Request GET / started with correlation ID 370b9545-9b6a-4002-a33b-de42ba9eaade
2025-06-14 19:30:50.187 +04:00 [INF] Request GET / completed in 217ms with status 404 (Correlation ID: 370b9545-9b6a-4002-a33b-de42ba9eaade)
2025-06-14 19:30:50.207 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 675.5233ms
2025-06-14 19:30:50.234 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
