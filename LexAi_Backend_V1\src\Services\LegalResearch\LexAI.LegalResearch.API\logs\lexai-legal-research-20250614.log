2025-06-14 19:30:47.253 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 19:30:47.343 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 19:30:47.639 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 19:30:47.641 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 19:30:47.708 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 19:30:47.709 +04:00 [INF] Hosting environment: Development
2025-06-14 19:30:47.711 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 19:30:49.538 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 19:30:49.958 +04:00 [INF] Request GET / started with correlation ID 370b9545-9b6a-4002-a33b-de42ba9eaade
2025-06-14 19:30:50.187 +04:00 [INF] Request GET / completed in 217ms with status 404 (Correlation ID: 370b9545-9b6a-4002-a33b-de42ba9eaade)
2025-06-14 19:30:50.207 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 675.5233ms
2025-06-14 19:30:50.234 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-14 21:21:28.110 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 21:21:28.225 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 21:21:28.693 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 21:21:28.757 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 21:21:28.934 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 21:21:28.935 +04:00 [INF] Hosting environment: Development
2025-06-14 21:21:28.937 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 21:21:30.236 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 21:21:30.729 +04:00 [INF] Request GET / started with correlation ID f629a173-be41-46aa-8f53-fe5bb886388d
2025-06-14 21:21:30.951 +04:00 [INF] Request GET / completed in 209ms with status 404 (Correlation ID: f629a173-be41-46aa-8f53-fe5bb886388d)
2025-06-14 21:21:30.965 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 730.4629ms
2025-06-14 21:21:30.987 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-14 22:22:42.545 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 22:22:42.669 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 22:22:43.186 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 22:22:43.189 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 22:22:43.247 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 22:22:43.250 +04:00 [INF] Hosting environment: Development
2025-06-14 22:22:43.251 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 22:22:44.601 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 22:22:45.101 +04:00 [INF] Request GET / started with correlation ID 417670e9-6875-41bf-bc8c-79076294beb8
2025-06-14 22:22:45.249 +04:00 [INF] Request GET / completed in 139ms with status 404 (Correlation ID: 417670e9-6875-41bf-bc8c-79076294beb8)
2025-06-14 22:22:45.261 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 663.1856ms
2025-06-14 22:22:45.284 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
