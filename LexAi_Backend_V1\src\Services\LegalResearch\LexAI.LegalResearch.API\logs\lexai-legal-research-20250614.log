2025-06-14 13:28:29.639 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 13:28:29.751 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:28:29.970 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 13:28:29.973 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 13:28:30.059 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:28:30.062 +04:00 [INF] Hosting environment: Development
2025-06-14 13:28:30.066 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 13:28:30.837 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 13:28:31.074 +04:00 [INF] Request GET / started with correlation ID 014a199b-a624-41f7-ac29-7622126928ff
2025-06-14 13:28:32.962 +04:00 [INF] Request GET / completed in 1882ms with status 404 (Correlation ID: 014a199b-a624-41f7-ac29-7622126928ff)
2025-06-14 13:28:32.972 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2152.1886ms
2025-06-14 13:28:32.981 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-14 13:29:27.378 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 13:29:27.378 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 13:29:27.421 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 3019fafd-e017-42a5-ae8a-4699ab5fd717
2025-06-14 13:29:27.423 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 1e90420b-f243-4486-921c-83d03794480b
2025-06-14 13:29:27.427 +04:00 [INF] CORS policy execution failed.
2025-06-14 13:29:27.428 +04:00 [INF] CORS policy execution failed.
2025-06-14 13:29:27.429 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-06-14 13:29:27.432 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-06-14 13:29:27.435 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 9ms with status 204 (Correlation ID: 3019fafd-e017-42a5-ae8a-4699ab5fd717)
2025-06-14 13:29:27.435 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 6ms with status 204 (Correlation ID: 1e90420b-f243-4486-921c-83d03794480b)
2025-06-14 13:29:27.438 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 60.2519ms
2025-06-14 13:29:27.440 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 62.5926ms
2025-06-14 13:55:58.683 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 13:55:58.683 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 13:55:58.702 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 19039166-46d2-4375-a6a5-ca873225e048
2025-06-14 13:55:58.708 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 735b71d0-fa9f-45e9-840c-6260be427f55
2025-06-14 13:55:58.713 +04:00 [INF] CORS policy execution failed.
2025-06-14 13:55:58.715 +04:00 [INF] CORS policy execution failed.
2025-06-14 13:55:58.716 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-06-14 13:55:58.718 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-06-14 13:55:58.719 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 6ms with status 204 (Correlation ID: 19039166-46d2-4375-a6a5-ca873225e048)
2025-06-14 13:55:58.721 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 5ms with status 204 (Correlation ID: 735b71d0-fa9f-45e9-840c-6260be427f55)
2025-06-14 13:55:58.723 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 40.5469ms
2025-06-14 13:55:58.737 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 54.1982ms
2025-06-14 13:56:33.411 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-14 13:56:33.431 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 4b2619c6-0a99-4aaf-961f-6dafba72d482
2025-06-14 13:56:33.438 +04:00 [INF] CORS policy execution failed.
2025-06-14 13:56:33.441 +04:00 [INF] Request origin http://localhost:5173 does not have permission to access the resource.
2025-06-14 13:56:33.444 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 6ms with status 204 (Correlation ID: 4b2619c6-0a99-4aaf-961f-6dafba72d482)
2025-06-14 13:56:33.454 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 43.6489ms
2025-06-14 14:00:55.649 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 14:00:55.712 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 14:00:55.974 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 14:00:55.978 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 14:00:56.014 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 14:00:56.016 +04:00 [INF] Hosting environment: Development
2025-06-14 14:00:56.022 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 14:00:56.711 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 14:00:56.877 +04:00 [INF] Request GET / started with correlation ID 807b42bd-5801-442c-95e1-52707ef71404
2025-06-14 14:00:58.839 +04:00 [INF] Request GET / completed in 1956ms with status 404 (Correlation ID: 807b42bd-5801-442c-95e1-52707ef71404)
2025-06-14 14:00:58.848 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 2154.2431ms
2025-06-14 14:00:58.858 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-14 14:01:56.909 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-14 14:01:56.930 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 8bc3c527-b814-481e-b62d-d740bfce7b76
2025-06-14 14:01:56.948 +04:00 [INF] CORS policy execution successful.
2025-06-14 14:01:56.967 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 29ms with status 204 (Correlation ID: 8bc3c527-b814-481e-b62d-d740bfce7b76)
2025-06-14 14:01:56.979 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 70.4991ms
2025-06-14 14:01:57.035 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/v1/search/perform - application/json 60
2025-06-14 14:01:57.040 +04:00 [INF] Request POST /api/v1/search/perform started with correlation ID d0651a1b-c1ad-44bf-948b-2d831f1da6bd
2025-06-14 14:01:57.044 +04:00 [INF] CORS policy execution successful.
2025-06-14 14:01:57.184 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 14:01:57.187 +04:00 [INF] Request POST /api/v1/search/perform completed in 143ms with status 404 (Correlation ID: d0651a1b-c1ad-44bf-948b-2d831f1da6bd)
2025-06-14 14:01:57.190 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/v1/search/perform - 404 0 null 155.8278ms
2025-06-14 14:01:57.197 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5002/api/v1/search/perform, Response status code: 404
2025-06-14 15:28:14.391 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-14 15:28:14.519 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:28:14.812 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-14 15:28:14.814 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-14 15:28:14.866 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:28:14.869 +04:00 [INF] Hosting environment: Development
2025-06-14 15:28:14.871 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-14 15:28:15.663 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-14 15:28:16.016 +04:00 [INF] Request GET / started with correlation ID 67900379-d435-4e1d-81ab-fb86e922cefd
2025-06-14 15:28:16.128 +04:00 [INF] Request GET / completed in 106ms with status 404 (Correlation ID: 67900379-d435-4e1d-81ab-fb86e922cefd)
2025-06-14 15:28:16.139 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 496.3152ms
2025-06-14 15:28:16.147 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-14 15:28:28.011 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 15:28:28.011 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 15:28:28.025 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 61a6d598-bc74-4919-9399-3a6abe2dd389
2025-06-14 15:28:28.025 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID cd754498-1c50-4800-b898-6865e6ea0bf3
2025-06-14 15:28:28.032 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:28.032 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:28.037 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 9ms with status 204 (Correlation ID: 61a6d598-bc74-4919-9399-3a6abe2dd389)
2025-06-14 15:28:28.037 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 6ms with status 204 (Correlation ID: cd754498-1c50-4800-b898-6865e6ea0bf3)
2025-06-14 15:28:28.043 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 32.1306ms
2025-06-14 15:28:28.045 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 34.8407ms
2025-06-14 15:28:28.047 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-14 15:28:28.062 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 1ac18122-4dc1-4465-8ae1-a6201ce1f4f8
2025-06-14 15:28:28.065 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:28.145 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:28 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 15:28:28.194 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:28 AM'.
2025-06-14 15:28:28.198 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:28 AM'.
2025-06-14 15:28:28.201 +04:00 [INF] Request GET /api/v1/search/history completed in 136ms with status 404 (Correlation ID: 1ac18122-4dc1-4465-8ae1-a6201ce1f4f8)
2025-06-14 15:28:28.206 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 404 0 null 159.5915ms
2025-06-14 15:28:28.210 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-14 15:28:28.213 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/api/v1/search/history, Response status code: 404
2025-06-14 15:28:28.221 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 4b1357fa-749a-48ef-bde7-df3a3ac1c1ea
2025-06-14 15:28:28.226 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:28:28.230 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:28 AM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 15:28:28.234 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:28 AM'.
2025-06-14 15:28:28.237 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:34:53 AM', Current time (UTC): '6/14/2025 11:28:28 AM'.
2025-06-14 15:28:28.239 +04:00 [INF] Request GET /api/v1/search/history completed in 13ms with status 404 (Correlation ID: 4b1357fa-749a-48ef-bde7-df3a3ac1c1ea)
2025-06-14 15:28:28.242 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 404 0 null 32.3226ms
2025-06-14 15:28:28.247 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/api/v1/search/history, Response status code: 404
2025-06-14 15:33:39.917 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 15:33:39.917 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-14 15:33:39.975 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 14412459-65f9-4220-8bd3-4cda151713ce
2025-06-14 15:33:39.984 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 6d734249-ffa7-4e5d-b0a6-b21cd198fcf8
2025-06-14 15:33:39.986 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:33:39.989 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:33:39.993 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 7ms with status 204 (Correlation ID: 14412459-65f9-4220-8bd3-4cda151713ce)
2025-06-14 15:33:39.995 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 6ms with status 204 (Correlation ID: 6d734249-ffa7-4e5d-b0a6-b21cd198fcf8)
2025-06-14 15:33:39.998 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 81.4406ms
2025-06-14 15:33:40.002 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 85.7165ms
2025-06-14 15:33:40.000 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-14 15:33:40.030 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 9c5874b3-0cb4-4deb-bf2d-bbd04acda171
2025-06-14 15:33:40.032 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:33:40.044 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:33:40.046 +04:00 [INF] Request GET /api/v1/search/history completed in 13ms with status 404 (Correlation ID: 9c5874b3-0cb4-4deb-bf2d-bbd04acda171)
2025-06-14 15:33:40.049 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 404 0 null 49.3308ms
2025-06-14 15:33:40.051 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-14 15:33:40.060 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/api/v1/search/history, Response status code: 404
2025-06-14 15:33:40.063 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 255ac663-7c62-4fb7-9daa-2847085a9ae8
2025-06-14 15:33:40.068 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:33:40.069 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:33:40.072 +04:00 [INF] Request GET /api/v1/search/history completed in 4ms with status 404 (Correlation ID: 255ac663-7c62-4fb7-9daa-2847085a9ae8)
2025-06-14 15:33:40.077 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 404 0 null 26.4232ms
2025-06-14 15:33:40.083 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/api/v1/search/history, Response status code: 404
2025-06-14 15:33:51.001 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-14 15:33:51.014 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 23a640e0-96dd-4d1e-8aea-96fc42ec2c8c
2025-06-14 15:33:51.017 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:33:51.018 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 1ms with status 204 (Correlation ID: 23a640e0-96dd-4d1e-8aea-96fc42ec2c8c)
2025-06-14 15:33:51.028 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 27.516ms
2025-06-14 15:33:51.033 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/v1/search/perform - application/json 58
2025-06-14 15:33:51.062 +04:00 [INF] Request POST /api/v1/search/perform started with correlation ID da72787e-f6fe-4736-983f-8f89baed45a3
2025-06-14 15:33:51.064 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:33:51.066 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:33:51.067 +04:00 [INF] Request POST /api/v1/search/perform completed in 2ms with status 404 (Correlation ID: da72787e-f6fe-4736-983f-8f89baed45a3)
2025-06-14 15:33:51.071 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/v1/search/perform - 404 0 null 37.6402ms
2025-06-14 15:33:51.077 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5002/api/v1/search/perform, Response status code: 404
