{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "LexAI.DocumentAnalysis": "Information"}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "https://localhost:3000", "https://localhost:3001"]}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production", "Issuer": "LexAI.DocumentAnalysis.API", "Audience": "LexAI.Frontend", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "Azure": {"DocumentIntelligence": {"Endpoint": "https://your-document-intelligence-resource.cognitiveservices.azure.com/", "ApiKey": "your-azure-document-intelligence-api-key", "ApiVersion": "2023-07-31", "DefaultModel": "prebuilt-read", "DocumentAnalysisModel": "prebuilt-document", "ContractAnalysisModel": "prebuilt-contract", "TimeoutSeconds": 120, "MaxFileSizeMB": 50, "SupportedFileTypes": ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword", "text/plain", "image/jpeg", "image/png", "image/tiff"], "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelayMs": 1000, "EnableCaching": true, "CacheDurationMinutes": 60, "EnableDetailedLogging": false}, "OpenAI": {"Endpoint": "https://your-openai-resource.openai.azure.com/", "ApiKey": "your-azure-openai-api-key", "ApiVersion": "2024-02-15-preview", "DeploymentName": "gpt-4", "MaxTokens": 8000, "Temperature": 0.1, "TopP": 0.95, "FrequencyPenalty": 0, "PresencePenalty": 0, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelayMs": 1000}}, "LLM": {"DefaultProvider": "Local", "Providers": {"AzureOpenAI": {"Endpoint": "https://your-openai-resource.openai.azure.com/", "ApiKey": "your-azure-openai-api-key", "ApiVersion": "2024-02-15-preview", "Models": {"Chat": "gpt-4", "Embedding": "text-embedding-ada-002"}}, "Local": {"Endpoint": "http://localhost:11434", "Models": {"Chat": "llama3.2:3b", "Embedding": "nomic-embed-text"}}}, "DefaultSettings": {"MaxTokens": 8000, "Temperature": 0.1, "TopP": 0.95, "FrequencyPenalty": 0, "PresencePenalty": 0}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=lexai_document_analysis;Username=postgres;Password=your-password", "Redis": "localhost:6379"}, "HealthChecks": {"UI": {"Enabled": false, "Path": "/health-ui"}}}