using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace LexAI.DataPreprocessing.Infrastructure.Agents;

/// <summary>
/// Vectorization agent implementation
/// </summary>
public class VectorizationAgent : IVectorizationAgent
{
    private readonly IEmbeddingService _embeddingService;
    private readonly ILogger<VectorizationAgent> _logger;

    /// <summary>
    /// Agent name
    /// </summary>
    public string AgentName => "VectorizationAgent";

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType AgentType => AgentType.Vectorization;

    /// <summary>
    /// Supported embedding models
    /// </summary>
    public IEnumerable<EmbeddingModelType> SupportedModels => _embeddingService.SupportedModels;

    /// <summary>
    /// Initializes a new instance of the VectorizationAgent
    /// </summary>
    /// <param name="embeddingService">Embedding service</param>
    /// <param name="logger">Logger</param>
    public VectorizationAgent(
        IEmbeddingService embeddingService,
        ILogger<VectorizationAgent> logger)
    {
        _embeddingService = embeddingService;
        _logger = logger;
    }

    /// <summary>
    /// Vectorizes document chunks
    /// </summary>
    /// <param name="chunks">Chunks to vectorize</param>
    /// <param name="modelType">Embedding model to use</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Vectorization result</returns>
    public async Task<VectorizationResultDto> VectorizeChunksAsync(
        IEnumerable<DocumentChunk> chunks, 
        EmbeddingModelType modelType, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting vectorization with model {ModelType} for {ChunkCount} chunks", 
            modelType, chunks.Count());

        var stopwatch = Stopwatch.StartNew();
        var result = new VectorizationResultDto
        {
            AgentName = AgentName,
            EmbeddingModel = modelType,
            VectorDimension = GetEmbeddingDimension(modelType),
            Success = false
        };

        try
        {
            var chunkList = chunks.ToList();
            if (!chunkList.Any())
            {
                result.Errors.Add("No chunks provided for vectorization");
                return result;
            }

            // Validate model is supported
            if (!SupportedModels.Contains(modelType))
            {
                result.Errors.Add($"Embedding model {modelType} is not supported");
                return result;
            }

            // Extract texts from chunks
            var texts = chunkList.Select(c => c.Content).ToList();
            var totalTokens = chunkList.Sum(c => c.TokenCount);

            // Generate embeddings
            var embeddingResults = await _embeddingService.GenerateEmbeddingsAsync(texts, modelType, cancellationToken);
            var embeddingList = embeddingResults.ToList();

            // Check for embedding failures
            var failedEmbeddings = embeddingList.Where(e => !e.Success).ToList();
            if (failedEmbeddings.Any())
            {
                foreach (var failed in failedEmbeddings)
                {
                    result.Errors.Add($"Embedding failed: {failed.ErrorMessage}");
                }
                
                if (failedEmbeddings.Count == embeddingList.Count)
                {
                    _logger.LogError("All embeddings failed for vectorization");
                    return result;
                }
            }

            // Create vectorized chunks
            var vectorizedChunks = new List<VectorizedChunkDto>();
            for (int i = 0; i < chunkList.Count; i++)
            {
                var chunk = chunkList[i];
                var embedding = embeddingList[i];

                if (embedding.Success)
                {
                    var vectorId = Guid.NewGuid().ToString();
                    
                    var vectorizedChunk = new VectorizedChunkDto
                    {
                        ChunkId = chunk.Id,
                        VectorId = vectorId,
                        EmbeddingVector = embedding.Vector,
                        Metadata = new VectorMetadataDto
                        {
                            DatabaseType = VectorDatabaseType.MongoDB, // Default, will be updated by routing
                            Collection = "default",
                            Dimension = embedding.Vector.Length,
                            EmbeddingModel = modelType.ToString(),
                            Domain = chunk.DomainRelevance.OrderByDescending(kvp => kvp.Value).FirstOrDefault().Key,
                            AdditionalMetadata = new Dictionary<string, object>
                            {
                                ["chunkSequence"] = chunk.SequenceNumber,
                                ["qualityScore"] = chunk.QualityScore,
                                ["importanceScore"] = chunk.ImportanceScore,
                                ["tokenCount"] = chunk.TokenCount
                            }
                        }
                    };

                    vectorizedChunks.Add(vectorizedChunk);
                }
            }

            // Calculate cost
            var estimatedCost = EstimateVectorizationCost(totalTokens, modelType);

            // Set result properties
            result.VectorizedChunks = vectorizedChunks;
            result.TotalTokens = totalTokens;
            result.EstimatedCost = estimatedCost;
            result.Success = vectorizedChunks.Any();

            stopwatch.Stop();
            result.VectorizationTime = stopwatch.Elapsed;

            _logger.LogInformation("Vectorization completed. " +
                "Successful: {SuccessCount}/{TotalCount}, Tokens: {TotalTokens}, Cost: ${Cost:F4}, Time: {Time}ms", 
                vectorizedChunks.Count, chunkList.Count, totalTokens, estimatedCost, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.VectorizationTime = stopwatch.Elapsed;
            result.Errors.Add($"Vectorization failed: {ex.Message}");
            
            _logger.LogError(ex, "Error during vectorization");
            return result;
        }
    }

    /// <summary>
    /// Generates embedding for a single text
    /// </summary>
    /// <param name="text">Text to embed</param>
    /// <param name="modelType">Embedding model</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Embedding vector</returns>
    public async Task<float[]> GenerateEmbeddingAsync(
        string text, 
        EmbeddingModelType modelType, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return Array.Empty<float>();

            var embeddings = await _embeddingService.GenerateEmbeddingsAsync(new[] { text }, modelType, cancellationToken);
            var embedding = embeddings.FirstOrDefault();

            return embedding?.Success == true ? embedding.Vector : Array.Empty<float>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating single embedding");
            return Array.Empty<float>();
        }
    }

    /// <summary>
    /// Gets the dimension of the embedding model
    /// </summary>
    /// <param name="modelType">Embedding model</param>
    /// <returns>Vector dimension</returns>
    public int GetEmbeddingDimension(EmbeddingModelType modelType)
    {
        return _embeddingService.GetEmbeddingDimension(modelType);
    }

    /// <summary>
    /// Estimates the cost of vectorization
    /// </summary>
    /// <param name="tokenCount">Number of tokens</param>
    /// <param name="modelType">Embedding model</param>
    /// <returns>Estimated cost</returns>
    public decimal EstimateVectorizationCost(int tokenCount, EmbeddingModelType modelType)
    {
        return _embeddingService.EstimateCost(tokenCount, modelType);
    }
}
