using LexAI.DocumentAnalysis.Application.DTOs;

namespace LexAI.DocumentAnalysis.Application.Interfaces;

/// <summary>
/// Service principal d'analyse de documents juridiques
/// </summary>
public interface IDocumentAnalysisService
{
    /// <summary>
    /// Analyse un document juridique complet
    /// </summary>
    Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient le résultat d'une analyse par ID
    /// </summary>
    Task<DocumentAnalysisResponseDto?> GetAnalysisResultAsync(
        Guid analysisId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient la liste des analyses pour un utilisateur
    /// </summary>
    Task<DocumentAnalysisListResponseDto> GetUserAnalysesAsync(
        DocumentAnalysisListRequestDto request, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime une analyse
    /// </summary>
    Task<bool> DeleteAnalysisAsync(
        Guid analysisId, 
        Guid userId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Régénère l'analyse d'un document existant
    /// </summary>
    Task<DocumentAnalysisResponseDto> RegenerateAnalysisAsync(
        Guid analysisId, 
        AnalysisOptions? newOptions = null, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service d'extraction de texte à partir de documents
/// </summary>
public interface IDocumentExtractionService
{
    /// <summary>
    /// Extrait le texte d'un document PDF/Word
    /// </summary>
    Task<DocumentExtractionResult> ExtractTextAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Extrait le texte avec Azure Document Intelligence
    /// </summary>
    Task<DocumentExtractionResult> ExtractTextWithAzureAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Extrait les métadonnées du document
    /// </summary>
    Task<DocumentMetadata> ExtractMetadataAsync(
        byte[] documentContent, 
        string documentType, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service d'analyse de clauses juridiques
/// </summary>
public interface IClauseAnalysisService
{
    /// <summary>
    /// Analyse les clauses d'un document
    /// </summary>
    Task<List<ClauseAnalysisDto>> AnalyzeClausesAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Identifie les clauses critiques
    /// </summary>
    Task<List<ClauseAnalysisDto>> IdentifyCriticalClausesAsync(
        string documentText, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Suggère des révisions pour une clause
    /// </summary>
    Task<string> SuggestClauseRevisionAsync(
        string clauseText, 
        string riskLevel, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service d'évaluation des risques
/// </summary>
public interface IRiskAssessmentService
{
    /// <summary>
    /// Évalue les risques d'un document
    /// </summary>
    Task<List<RiskAssessmentDto>> AssessDocumentRisksAsync(
        string documentText, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calcule le niveau de risque global
    /// </summary>
    Task<string> CalculateOverallRiskLevelAsync(
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Génère des stratégies de mitigation
    /// </summary>
    Task<List<string>> GenerateMitigationStrategiesAsync(
        RiskAssessmentDto risk, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Service de génération de recommandations
/// </summary>
public interface IRecommendationService
{
    /// <summary>
    /// Génère des recommandations pour un document
    /// </summary>
    Task<List<DocumentRecommendationDto>> GenerateRecommendationsAsync(
        string documentText, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Priorise les recommandations
    /// </summary>
    Task<List<DocumentRecommendationDto>> PrioritizeRecommendationsAsync(
        List<DocumentRecommendationDto> recommendations, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Résultat d'extraction de texte
/// </summary>
public class DocumentExtractionResult
{
    public string ExtractedText { get; set; } = string.Empty;
    public List<DocumentPages> Pages { get; set; } = new();
    public List<DocumentTables> Tables { get; set; } = new();
    public List<DocumentFields> Fields { get; set; } = new();
    public double ConfidenceScore { get; set; }
    public int ProcessingTimeMs { get; set; }
    public string ExtractionMethod { get; set; } = string.Empty;
}

/// <summary>
/// Page de document
/// </summary>
public class DocumentPages
{
    public int PageNumber { get; set; }
    public string Text { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public List<DocumentLine> Lines { get; set; } = new();
}

/// <summary>
/// Ligne de document
/// </summary>
public class DocumentLine
{
    public string Text { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public BoundingBox BoundingBox { get; set; } = new();
}

/// <summary>
/// Tableau dans le document
/// </summary>
public class DocumentTables
{
    public int RowCount { get; set; }
    public int ColumnCount { get; set; }
    public List<DocumentTableCells> Cells { get; set; } = new();
}

/// <summary>
/// Cellule de tableau
/// </summary>
public class DocumentTableCells
{
    public int RowIndex { get; set; }
    public int ColumnIndex { get; set; }
    public string Text { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
}

/// <summary>
/// Champ de document structuré
/// </summary>
public class DocumentFields
{
    public string Name { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public string Type { get; set; } = string.Empty;
}

/// <summary>
/// Boîte englobante
/// </summary>
public class BoundingBox
{
    public double X { get; set; }
    public double Y { get; set; }
    public double Width { get; set; }
    public double Height { get; set; }
}

/// <summary>
/// Métadonnées du document
/// </summary>
public class DocumentMetadata
{
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public DateTime? CreationDate { get; set; }
    public DateTime? ModificationDate { get; set; }
    public string Language { get; set; } = string.Empty;
    public int PageCount { get; set; }
    public long FileSizeBytes { get; set; }
    public Dictionary<string, object> CustomProperties { get; set; } = new();
}
