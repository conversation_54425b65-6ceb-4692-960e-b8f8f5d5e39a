using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.DocumentAnalysis.Domain.Entities;
using LexAI.Shared.Application.Interfaces;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.DTOs.LLM;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service principal d'analyse de documents juridiques avec IA
/// </summary>
public class DocumentAnalysisService : IDocumentAnalysisService
{
    private readonly IDocumentExtractionService _extractionService;
    private readonly IClauseAnalysisService _clauseAnalysisService;
    private readonly IRiskAssessmentService _riskAssessmentService;
    private readonly IRecommendationService _recommendationService;
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<DocumentAnalysisService> _logger;
    private readonly IConfiguration _configuration;

    public DocumentAnalysisService(
        IDocumentExtractionService extractionService,
        IClauseAnalysisService clauseAnalysisService,
        IRiskAssessmentService riskAssessmentService,
        IRecommendationService recommendationService,
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<DocumentAnalysisService> logger)
    {
        _extractionService = extractionService;
        _clauseAnalysisService = clauseAnalysisService;
        _riskAssessmentService = riskAssessmentService;
        _recommendationService = recommendationService;
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Analyse complète d'un document juridique
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting comprehensive document analysis for: {DocumentName}", request.DocumentName);

        try
        {
            // 1. Extraction du texte
            var extractionResult = await ExtractDocumentTextAsync(request, cancellationToken);
            
            // 2. Analyse des clauses (si demandée)
            var clauses = new List<ClauseAnalysisDto>();
            if (request.Options.ExtractClauses)
            {
                clauses = await _clauseAnalysisService.AnalyzeClausesAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    cancellationToken);
            }

            // 3. Évaluation des risques (si demandée)
            var risks = new List<RiskAssessmentDto>();
            if (request.Options.PerformRiskAssessment)
            {
                risks = await _riskAssessmentService.AssessDocumentRisksAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    clauses, 
                    cancellationToken);
            }

            // 4. Génération de recommandations (si demandée)
            var recommendations = new List<DocumentRecommendationDto>();
            if (request.Options.GenerateRecommendations)
            {
                recommendations = await _recommendationService.GenerateRecommendationsAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    clauses, 
                    risks, 
                    cancellationToken);
            }

            // 5. Extraction d'entités (si demandée)
            var entities = new List<ExtractedEntityDto>();
            if (request.Options.ExtractEntities)
            {
                entities = await ExtractEntitiesAsync(extractionResult.ExtractedText, cancellationToken);
            }

            // 6. Recherche de citations (si demandée)
            var citations = new List<DocumentCitationDto>();
            if (request.Options.FindCitations)
            {
                citations = await FindCitationsAsync(extractionResult.ExtractedText, cancellationToken);
            }

            // 7. Génération du résumé exécutif
            var summary = await GenerateDocumentSummaryAsync(
                extractionResult.ExtractedText, 
                request.DocumentType, 
                clauses, 
                risks, 
                cancellationToken);

            // 8. Analyse globale avec LLM
            var overallAnalysis = await GenerateOverallAnalysisAsync(
                extractionResult.ExtractedText, 
                request.DocumentType, 
                clauses, 
                risks, 
                recommendations, 
                cancellationToken);

            stopwatch.Stop();

            // Construction de la réponse
            var response = new DocumentAnalysisResponseDto
            {
                Id = Guid.NewGuid(),
                DocumentName = request.DocumentName,
                DocumentType = request.DocumentType,
                Status = DocumentAnalysisStatus.Completed.ToString(),
                AnalysisContent = overallAnalysis.Content,
                ConfidenceScore = CalculateOverallConfidence(clauses, risks, entities),
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                TokensUsed = overallAnalysis.TokensUsed,
                EstimatedCost = overallAnalysis.EstimatedCost,
                ModelUsed = overallAnalysis.ModelUsed,
                AnalyzedAt = DateTime.UtcNow,
                Clauses = clauses,
                Risks = risks,
                Recommendations = recommendations,
                Entities = entities,
                Citations = citations,
                Summary = summary
            };

            _logger.LogInformation("Document analysis completed successfully. Processing time: {ProcessingTime}ms, Tokens: {Tokens}", 
                stopwatch.ElapsedMilliseconds, overallAnalysis.TokensUsed);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during document analysis for: {DocumentName}", request.DocumentName);
            throw;
        }
    }

    /// <summary>
    /// Obtient le résultat d'une analyse par ID
    /// </summary>
    public async Task<DocumentAnalysisResponseDto?> GetAnalysisResultAsync(
        Guid analysisId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrieving analysis result: {AnalysisId}", analysisId);

        try
        {
            var analysis = await _context.DocumentAnalyses
                .Include(a => a.Risks)
                .Include(a => a.Recommendations)
                .Include(a => a.Citations)
                .Include(a => a.Entities)
                .FirstOrDefaultAsync(a => a.Id == analysisId, cancellationToken);

            if (analysis == null)
            {
                _logger.LogWarning("Analysis not found: {AnalysisId}", analysisId);
                return null;
            }

            return new DocumentAnalysisResponseDto
            {
                Id = analysis.Id,
                DocumentName = analysis.DocumentName,
                Status = analysis.Status.ToString(),
                AnalysisContent = analysis.AnalysisContent,
                ModelUsed = analysis.ModelUsed,
                ConfidenceScore = analysis.ConfidenceScore,
                ProcessingTimeMs = analysis.ProcessingTimeMs,
                CreatedAt = analysis.CreatedAt,
                CompletedAt = analysis.CompletedAt,
                Summary = !string.IsNullOrEmpty(analysis.ExecutiveSummary) ? new DocumentSummaryDto
                {
                    ExecutiveSummary = analysis.ExecutiveSummary,
                    KeyPoints = analysis.KeyPoints?.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>(),
                    OverallRiskLevel = analysis.OverallRiskLevel ?? "Unknown"
                } : null,
                Risks = analysis.Risks.Select(r => new DocumentRiskDto
                {
                    RiskType = r.RiskType,
                    Severity = r.Severity,
                    Description = r.Description,
                    Mitigation = r.Mitigation,
                    Confidence = r.Confidence
                }).ToList(),
                Recommendations = analysis.Recommendations.Select(r => new DocumentRecommendationDto
                {
                    Title = r.Title,
                    Description = r.Description,
                    Priority = r.Priority,
                    SuggestedAction = r.SuggestedAction,
                    Impact = r.Impact
                }).ToList(),
                Citations = analysis.Citations.Select(c => new DocumentCitationDto
                {
                    Title = c.Title,
                    Source = c.Source,
                    Url = c.Url,
                    Context = c.Context,
                    RelevanceScore = c.RelevanceScore
                }).ToList(),
                Entities = analysis.Entities.Select(e => new DocumentEntityDto
                {
                    Text = e.Text,
                    Type = e.Type,
                    Confidence = e.Confidence,
                    StartPosition = e.StartPosition,
                    EndPosition = e.EndPosition
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analysis result: {AnalysisId}", analysisId);
            throw;
        }
    }

    /// <summary>
    /// Obtient la liste des analyses pour un utilisateur
    /// </summary>
    public async Task<DocumentAnalysisListResponseDto> GetUserAnalysesAsync(
        DocumentAnalysisListRequestDto request, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrieving analyses for user: {UserId}", request.UserId);

        try
        {
            var query = _context.DocumentAnalyses
                .Where(a => a.UserId == request.UserId);

            // Filtres
            if (!string.IsNullOrEmpty(request.DocumentType))
            {
                query = query.Where(a => a.DocumentType == request.DocumentType);
            }

            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<AnalysisStatus>(request.Status, out var status))
                {
                    query = query.Where(a => a.Status == status);
                }
            }

            if (!string.IsNullOrEmpty(request.FromDate) && DateTime.TryParse(request.FromDate, out var fromDate))
            {
                query = query.Where(a => a.CreatedAt >= fromDate);
            }

            if (!string.IsNullOrEmpty(request.ToDate) && DateTime.TryParse(request.ToDate, out var toDate))
            {
                query = query.Where(a => a.CreatedAt <= toDate.AddDays(1));
            }

            // Tri
            query = !string.IsNullOrEmpty(request.SortBy) && request.SortBy.ToLower() == "createdat" && request.SortDescending == true
                ? query.OrderByDescending(a => a.CreatedAt)
                : query.OrderByDescending(a => a.CreatedAt);

            var totalCount = await query.CountAsync(cancellationToken);

            var page = request.Page ?? 1;
            var pageSize = request.PageSize ?? 10;
            var skip = (page - 1) * pageSize;

            var analyses = await query
                .Skip(skip)
                .Take(pageSize)
                .Select(a => new DocumentAnalysisSummaryDto
                {
                    Id = a.Id,
                    DocumentName = a.DocumentName,
                    DocumentType = a.DocumentType,
                    Status = a.Status.ToString(),
                    OverallRiskLevel = a.OverallRiskLevel ?? "Unknown",
                    CreatedAt = a.CreatedAt,
                    CompletedAt = a.CompletedAt,
                    ProcessingTimeMs = a.ProcessingTimeMs
                })
                .ToListAsync(cancellationToken);

            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            return new DocumentAnalysisListResponseDto
            {
                Items = analyses,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user analyses: {UserId}", request.UserId);
            throw;
        }
    }

    /// <summary>
    /// Supprime une analyse
    /// </summary>
    public async Task<bool> DeleteAnalysisAsync(
        Guid analysisId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Deleting analysis {AnalysisId} for user {UserId}", analysisId, userId);

        try
        {
            var analysis = await _context.DocumentAnalyses
                .FirstOrDefaultAsync(a => a.Id == analysisId && a.UserId == userId, cancellationToken);

            if (analysis == null)
            {
                _logger.LogWarning("Analysis not found or user not authorized: {AnalysisId}, {UserId}", analysisId, userId);
                return false;
            }

            _context.DocumentAnalyses.Remove(analysis);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Analysis deleted successfully: {AnalysisId}", analysisId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting analysis: {AnalysisId}, {UserId}", analysisId, userId);
            throw;
        }
    }

    /// <summary>
    /// Régénère l'analyse d'un document existant
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> RegenerateAnalysisAsync(
        Guid analysisId, 
        AnalysisOptions? newOptions = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Regenerating analysis: {AnalysisId}", analysisId);

        // TODO: Récupérer le document original et relancer l'analyse
        throw new NotImplementedException("Regeneration will be implemented with database integration");
    }

    // Méthodes privées utilitaires

    private async Task<DocumentExtractionResult> ExtractDocumentTextAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken)
    {
        if (request.Options.UseAzureDocumentIntelligence)
        {
            return await _extractionService.ExtractTextWithAzureAsync(
                request.DocumentContent, 
                request.DocumentType, 
                cancellationToken);
        }
        else
        {
            return await _extractionService.ExtractTextAsync(
                request.DocumentContent, 
                request.DocumentType, 
                cancellationToken);
        }
    }

    private async Task<List<ExtractedEntityDto>> ExtractEntitiesAsync(
        string text, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Extrayez les entités juridiques importantes de ce document.
Identifiez les personnes, organisations, dates, montants, références légales, etc.
Répondez en JSON avec le format:
{{
  ""entities"": [
    {{
      ""text"": ""texte de l'entité"",
      ""type"": ""PERSON|ORGANIZATION|DATE|AMOUNT|LEGAL_REFERENCE|LOCATION|OTHER"",
      ""startPosition"": 0,
      ""endPosition"": 10,
      ""confidence"": 0.95,
      ""normalizedValue"": ""valeur normalisée""
    }}
  ]
}}

Document: {text.Substring(0, Math.Min(text.Length, 4000))}";

        var options = LLMServiceExtensions.ForEntityExtraction(2000);
        var result = await _llmService.AnalyzeStructuredAsync<EntityExtractionResponse>(
            text, prompt, options, cancellationToken);

        return result?.Entities?.Select(e => new ExtractedEntityDto
        {
            Id = Guid.NewGuid(),
            Text = e.Text,
            Type = e.Type,
            ConfidenceScore = e.Confidence,
            StartPosition = e.StartPosition,
            EndPosition = e.EndPosition,
            NormalizedValue = e.NormalizedValue
        }).ToList() ?? new List<ExtractedEntityDto>();
    }

    private async Task<List<DocumentCitationDto>> FindCitationsAsync(
        string text,
        CancellationToken cancellationToken)
    {
        try
        {
            var prompt = $@"Identifiez toutes les références juridiques et citations dans ce document.
Recherchez les références à des lois, codes, jurisprudences, articles, décrets, etc.
Répondez en JSON avec le format:
{{
  ""citations"": [
    {{
      ""title"": ""Titre de la référence"",
      ""source"": ""Source (Code civil, Loi, Jurisprudence, etc.)"",
      ""url"": ""URL si disponible"",
      ""context"": ""Contexte d'utilisation dans le document"",
      ""relevanceScore"": 0.95
    }}
  ]
}}

Document: {text.Substring(0, Math.Min(text.Length, 4000))}";

            var options = LLMServiceExtensions.ForEntityExtraction(2000);
            var result = await _llmService.AnalyzeStructuredAsync<CitationExtractionResponse>(
                text, prompt, options, cancellationToken);

            return result?.Citations?.Select(c => new DocumentCitationDto
            {
                Title = c.Title,
                Source = c.Source,
                Url = c.Url,
                Context = c.Context,
                RelevanceScore = c.RelevanceScore
            }).ToList() ?? new List<DocumentCitationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error finding citations in document");
            return new List<DocumentCitationDto>();
        }
    }

    private async Task<DocumentSummaryDto> GenerateDocumentSummaryAsync(
        string text, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Générez un résumé exécutif de ce document juridique de type {documentType}.
Incluez les points clés, parties principales, dates importantes, termes financiers, et objectif du document.

Document: {text.Substring(0, Math.Min(text.Length, 6000))}";

        var options = LLMServiceExtensions.ForSummarization(3000);
        var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

        // TODO: Parser la réponse pour extraire les éléments structurés
        return new DocumentSummaryDto
        {
            ExecutiveSummary = result.Content,
            OverallRiskLevel = risks.Any() ? risks.Max(r => r.Severity) : "Low"
        };
    }

    private async Task<LLMResponse> GenerateOverallAnalysisAsync(
        string text, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        List<DocumentRecommendationDto> recommendations, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Effectuez une analyse juridique complète de ce document de type {documentType}.
Synthétisez les clauses importantes, les risques identifiés, et les recommandations.
Fournissez une évaluation globale et des conseils juridiques.

Document: {text.Substring(0, Math.Min(text.Length, 8000))}

Clauses analysées: {clauses.Count}
Risques identifiés: {risks.Count}
Recommandations: {recommendations.Count}";

        var options = LLMServiceExtensions.ForLegalAnalysis(8000);
        return await _llmService.SendPromptAsync(prompt, options, cancellationToken);
    }

    private static double CalculateOverallConfidence(
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        List<ExtractedEntityDto> entities)
    {
        var scores = new List<double>();
        
        if (clauses.Any())
            scores.Add(clauses.Average(c => c.ConfidenceScore));
        
        if (entities.Any())
            scores.Add(entities.Average(e => e.ConfidenceScore));

        return scores.Any() ? scores.Average() : 0.8;
    }

    // Classes pour la désérialisation JSON
    private class EntityExtractionResponse
    {
        public List<ExtractedEntityResponse> Entities { get; set; } = new();
    }

    private class ExtractedEntityResponse
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public double Confidence { get; set; }
        public string? NormalizedValue { get; set; }
    }

    private class CitationExtractionResponse
    {
        public List<CitationResponse> Citations { get; set; } = new();
    }

    private class CitationResponse
    {
        public string Title { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string? Url { get; set; }
        public string Context { get; set; } = string.Empty;
        public double RelevanceScore { get; set; }
    }
}
