using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.DocumentAnalysis.Domain.Entities;
using LexAI.DocumentAnalysis.Domain.Repositories;
using LexAI.Shared.Application.Interfaces;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.DTOs.LLM;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service principal d'analyse de documents juridiques avec IA
/// </summary>
public class DocumentAnalysisService : IDocumentAnalysisService
{
    private readonly IDocumentExtractionService _extractionService;
    private readonly IClauseAnalysisService _clauseAnalysisService;
    private readonly IRiskAssessmentService _riskAssessmentService;
    private readonly IRecommendationService _recommendationService;
    private readonly IUnifiedLLMService _llmService;
    private readonly IDocumentAnalysisRepository _repository;
    private readonly IDocumentStorageService _storageService;
    private readonly ILogger<DocumentAnalysisService> _logger;
    private readonly IConfiguration _configuration;

    public DocumentAnalysisService(
        IDocumentExtractionService extractionService,
        IClauseAnalysisService clauseAnalysisService,
        IRiskAssessmentService riskAssessmentService,
        IRecommendationService recommendationService,
        IUnifiedLLMService llmService,
        IDocumentAnalysisRepository repository,
        IDocumentStorageService storageService,
        IConfiguration configuration,
        ILogger<DocumentAnalysisService> logger)
    {
        _extractionService = extractionService;
        _clauseAnalysisService = clauseAnalysisService;
        _riskAssessmentService = riskAssessmentService;
        _recommendationService = recommendationService;
        _llmService = llmService;
        _repository = repository;
        _storageService = storageService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Analyse complète d'un document juridique
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting comprehensive document analysis for: {DocumentName}", request.DocumentName);

        try
        {
            // 0. Stocker le document localement
            var (documentHash, storagePath) = await _storageService.StoreDocumentAsync(
                request.DocumentContent,
                request.DocumentName,
                cancellationToken);

            _logger.LogInformation("Document stored with hash: {Hash} at {Path}", documentHash, storagePath);

            // 1. Extraction du texte
            var extractionResult = await ExtractDocumentTextAsync(request, cancellationToken);
            
            // 2. Analyse des clauses (si demandée)
            var clauses = new List<ClauseAnalysisDto>();
            if (request.Options.ExtractClauses)
            {
                clauses = await _clauseAnalysisService.AnalyzeClausesAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    cancellationToken);
            }

            // 3. Évaluation des risques (si demandée)
            var risks = new List<RiskAssessmentDto>();
            if (request.Options.PerformRiskAssessment)
            {
                risks = await _riskAssessmentService.AssessDocumentRisksAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    clauses, 
                    cancellationToken);
            }

            // 4. Génération de recommandations (si demandée)
            var recommendations = new List<DocumentRecommendationDto>();
            if (request.Options.GenerateRecommendations)
            {
                recommendations = await _recommendationService.GenerateRecommendationsAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    clauses, 
                    risks, 
                    cancellationToken);
            }

            // 5. Extraction d'entités (si demandée)
            var entities = new List<ExtractedEntityDto>();
            if (request.Options.ExtractEntities)
            {
                entities = await ExtractEntitiesAsync(extractionResult.ExtractedText, cancellationToken);
            }

            // 6. Recherche de citations (si demandée)
            var citations = new List<DocumentCitationDto>();
            if (request.Options.FindCitations)
            {
                citations = await FindCitationsAsync(extractionResult.ExtractedText, cancellationToken);
            }

            // 7. Génération du résumé exécutif
            var summary = await GenerateDocumentSummaryAsync(
                extractionResult.ExtractedText, 
                request.DocumentType, 
                clauses, 
                risks, 
                cancellationToken);

            // 8. Analyse globale avec LLM
            var overallAnalysis = await GenerateOverallAnalysisAsync(
                extractionResult.ExtractedText, 
                request.DocumentType, 
                clauses, 
                risks, 
                recommendations, 
                cancellationToken);

            stopwatch.Stop();

            // Création de l'entité pour la base de données
            var analysisEntity = new DocumentAnalysisResult
            {
                Id = Guid.NewGuid(),
                DocumentName = request.DocumentName,
                DocumentType = request.DocumentType,
                DocumentHash = documentHash,
                DocumentStoragePath = storagePath,
                ExtractedText = extractionResult.ExtractedText,
                AnalysisContent = overallAnalysis.Content,
                Status = DocumentAnalysisStatus.Completed,
                ConfidenceScore = CalculateOverallConfidence(clauses, risks, entities),
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                TokensUsed = overallAnalysis.TokensUsed,
                EstimatedCost = overallAnalysis.EstimatedCost,
                ModelUsed = overallAnalysis.ModelUsed,
                UserId = request.UserId,
                AnalyzedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                // Summary fields
                ExecutiveSummary = summary.ExecutiveSummary,
                KeyPoints = summary.KeyPoints ?? new List<string>(),
                MainParties = summary.MainParties ?? new List<string>(),
                ImportantDates = summary.ImportantDates ?? new List<string>(),
                FinancialTerms = summary.FinancialTerms ?? new List<string>(),
                DocumentPurpose = summary.DocumentPurpose ?? "Non déterminé",
                OverallRiskLevel = summary.OverallRiskLevel ?? "Unknown"
            };

            // Sauvegarde dans la base de données avec toutes les entités liées
            try
            {
                await _repository.AddAsync(analysisEntity, cancellationToken);
                _logger.LogInformation("Analysis saved to database with ID: {AnalysisId}", analysisEntity.Id);

                // Sauvegarder les entités liées
                await SaveRelatedEntitiesAsync(analysisEntity.Id, clauses, risks, recommendations, entities, citations, cancellationToken);
                _logger.LogInformation("Related entities saved for analysis: {AnalysisId}", analysisEntity.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save analysis to database, but analysis completed successfully");
                // Continue même si la sauvegarde échoue
            }

            // Construction de la réponse
            var response = new DocumentAnalysisResponseDto
            {
                Id = analysisEntity.Id,
                DocumentName = request.DocumentName,
                DocumentType = request.DocumentType,
                Status = DocumentAnalysisStatus.Completed.ToString(),
                AnalysisContent = overallAnalysis.Content,
                ConfidenceScore = CalculateOverallConfidence(clauses, risks, entities),
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                TokensUsed = overallAnalysis.TokensUsed,
                EstimatedCost = overallAnalysis.EstimatedCost,
                ModelUsed = overallAnalysis.ModelUsed,
                UserId = request.UserId,
                AnalyzedAt = DateTime.UtcNow,
                Clauses = clauses,
                Risks = risks,
                Recommendations = recommendations,
                Entities = entities,
                Citations = citations,
                Summary = summary
            };

            _logger.LogInformation("Document analysis completed successfully. Processing time: {ProcessingTime}ms, Tokens: {Tokens}",
                stopwatch.ElapsedMilliseconds, overallAnalysis.TokensUsed);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during document analysis for: {DocumentName}", request.DocumentName);
            throw;
        }
    }

    /// <summary>
    /// Obtient le résultat d'une analyse par ID
    /// </summary>
    public async Task<DocumentAnalysisResponseDto?> GetAnalysisResultAsync(
        Guid analysisId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrieving analysis result: {AnalysisId}", analysisId);

        try
        {
            // Pour l'instant, récupérer sans vérification userId (à améliorer pour la sécurité)
            var analysis = await _repository.GetByIdWithoutUserCheckAsync(analysisId, cancellationToken);

            if (analysis == null)
            {
                _logger.LogWarning("Analysis not found: {AnalysisId}", analysisId);
                return null;
            }

            // Convertir l'entité en DTO
            var response = new DocumentAnalysisResponseDto
            {
                Id = analysis.Id,
                DocumentName = analysis.DocumentName,
                DocumentType = analysis.DocumentType,
                Status = analysis.Status.ToString(),
                AnalysisContent = analysis.AnalysisContent,
                ConfidenceScore = analysis.ConfidenceScore,
                ProcessingTimeMs = analysis.ProcessingTimeMs,
                TokensUsed = analysis.TokensUsed,
                EstimatedCost = analysis.EstimatedCost,
                ModelUsed = analysis.ModelUsed,
                UserId = analysis.UserId,
                AnalyzedAt = analysis.AnalyzedAt,
                Clauses = analysis.Clauses?.Select(c => new ClauseAnalysisDto
                {
                    Id = c.Id,
                    ClauseText = c.ClauseText,
                    ClauseType = c.ClauseType,
                    Analysis = c.Analysis,
                    RiskLevel = c.RiskLevel.ToString(),
                    ConfidenceScore = c.ConfidenceScore,
                    StartPosition = c.StartPosition,
                    EndPosition = c.EndPosition,
                    SuggestedRevision = c.SuggestedRevision,
                    Tags = c.Tags
                }).ToList() ?? new List<ClauseAnalysisDto>(),
                Risks = analysis.Risks?.Select(r => new RiskAssessmentDto
                {
                    Id = r.Id,
                    RiskType = r.RiskType,
                    Description = r.Description,
                    Severity = r.Severity.ToString(),
                    Probability = r.Probability,
                    Impact = r.Impact,
                    Mitigation = r.Mitigation,
                    AffectedClauses = r.AffectedClauses
                }).ToList() ?? new List<RiskAssessmentDto>(),
                Recommendations = analysis.Recommendations?.Select(rec => new DocumentRecommendationDto
                {
                    Id = rec.Id,
                    Type = rec.Type,
                    Title = rec.Title,
                    Description = rec.Description,
                    Priority = rec.Priority.ToString(),
                    SuggestedAction = rec.SuggestedAction,
                    LegalBasis = rec.LegalBasis,
                    RelatedClauses = rec.RelatedClauses
                }).ToList() ?? new List<DocumentRecommendationDto>(),
                Entities = analysis.Entities?.Select(e => new ExtractedEntityDto
                {
                    Id = e.Id,
                    Text = e.Text,
                    Type = e.Type,
                    ConfidenceScore = e.ConfidenceScore,
                    StartPosition = e.StartPosition,
                    EndPosition = e.EndPosition,
                    NormalizedValue = e.NormalizedValue
                }).ToList() ?? new List<ExtractedEntityDto>(),
                Citations = analysis.Citations?.Select(c => new DocumentCitationDto
                {
                    Title = c.Title,
                    Source = c.Source,
                    Url = c.Url,
                    Context = c.Context,
                    RelevanceScore = c.RelevanceScore
                }).ToList() ?? new List<DocumentCitationDto>(),
                Summary = new DocumentSummaryDto
                {
                    ExecutiveSummary = analysis.ExecutiveSummary,
                    KeyPoints = analysis.KeyPoints,
                    MainParties = analysis.MainParties,
                    ImportantDates = analysis.ImportantDates,
                    FinancialTerms = analysis.FinancialTerms,
                    DocumentPurpose = analysis.DocumentPurpose,
                    OverallRiskLevel = analysis.OverallRiskLevel
                }
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analysis: {AnalysisId}", analysisId);
            return null;
        }
    }

    /// <summary>
    /// Obtient la liste des analyses pour un utilisateur
    /// </summary>
    public async Task<DocumentAnalysisListResponseDto> GetUserAnalysesAsync(
        DocumentAnalysisListRequestDto request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrieving analyses for user: {UserId}", request.UserId);

        try
        {
            if (!request.UserId.HasValue)
            {
                _logger.LogWarning("UserId is required for GetUserAnalysesAsync");
                return new DocumentAnalysisListResponseDto
                {
                    Items = new List<DocumentAnalysisSummaryDto>(),
                    TotalCount = 0,
                    Page = 1,
                    PageSize = 10,
                    TotalPages = 0
                };
            }

            var (analyses, totalCount) = await _repository.GetUserAnalysesAsync(
                request.UserId.Value,
                request.DocumentType,
                null, // status
                request.FromDate,
                request.ToDate,
                request.Page,
                request.PageSize,
                request.SortBy,
                request.SortDescending,
                cancellationToken);

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            var items = analyses.Select(a => new DocumentAnalysisSummaryDto
            {
                Id = a.Id,
                DocumentName = a.DocumentName,
                DocumentType = a.DocumentType,
                Status = a.Status.ToString(),
                ConfidenceScore = a.ConfidenceScore,
                AnalyzedAt = a.AnalyzedAt
            }).ToList();

            return new DocumentAnalysisListResponseDto
            {
                Items = items,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analyses for user: {UserId}", request.UserId);
            return new DocumentAnalysisListResponseDto
            {
                Items = new List<DocumentAnalysisSummaryDto>(),
                TotalCount = 0,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = 0
            };
        }
    }

    /// <summary>
    /// Supprime une analyse
    /// </summary>
    public async Task<bool> DeleteAnalysisAsync(
        Guid analysisId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Deleting analysis {AnalysisId} for user {UserId}", analysisId, userId);

        try
        {
            var result = await _repository.DeleteAsync(analysisId, userId, cancellationToken);

            if (result)
            {
                _logger.LogInformation("Analysis deleted successfully: {AnalysisId}", analysisId);
            }
            else
            {
                _logger.LogWarning("Analysis not found or not authorized: {AnalysisId}", analysisId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting analysis: {AnalysisId}", analysisId);
            return false;
        }
    }

    /// <summary>
    /// Régénère l'analyse d'un document existant
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> RegenerateAnalysisAsync(
        Guid analysisId,
        AnalysisOptions? newOptions = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Regenerating analysis: {AnalysisId}", analysisId);

        try
        {
            // Récupérer l'analyse existante depuis la base de données
            // Note: Nous avons besoin de l'userId pour la sécurité, mais cette méthode ne l'a pas
            // Pour l'instant, on va simuler une régénération

            // TODO: Modifier l'interface pour inclure userId ou récupérer depuis le contexte
            _logger.LogWarning("RegenerateAnalysisAsync called without userId - cannot retrieve original document safely");

            // Créer une nouvelle analyse simulée
            var response = new DocumentAnalysisResponseDto
            {
                Id = Guid.NewGuid(),
                DocumentName = "Document régénéré",
                DocumentType = "pdf",
                Status = DocumentAnalysisStatus.Completed.ToString(),
                AnalysisContent = "Analyse régénérée avec nouvelles options",
                ConfidenceScore = 0.85,
                ProcessingTimeMs = 1000,
                TokensUsed = 500,
                EstimatedCost = 0.01m,
                ModelUsed = "Local LLM",
                UserId = Guid.Empty, // Sera remplacé par l'userId réel
                AnalyzedAt = DateTime.UtcNow,
                Clauses = new List<ClauseAnalysisDto>(),
                Risks = new List<RiskAssessmentDto>(),
                Recommendations = new List<DocumentRecommendationDto>(),
                Entities = new List<ExtractedEntityDto>(),
                Citations = new List<DocumentCitationDto>(),
                Summary = new DocumentSummaryDto
                {
                    ExecutiveSummary = "Résumé de l'analyse régénérée",
                    OverallRiskLevel = "Medium"
                }
            };

            _logger.LogInformation("Analysis regeneration simulated successfully: {AnalysisId}", analysisId);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating analysis: {AnalysisId}", analysisId);
            throw;
        }
    }

    // Méthodes privées utilitaires

    private async Task<DocumentExtractionResult> ExtractDocumentTextAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken)
    {
        if (request.Options.UseAzureDocumentIntelligence)
        {
            return await _extractionService.ExtractTextWithAzureAsync(
                request.DocumentContent, 
                request.DocumentType, 
                cancellationToken);
        }
        else
        {
            return await _extractionService.ExtractTextAsync(
                request.DocumentContent, 
                request.DocumentType, 
                cancellationToken);
        }
    }

    private async Task<List<ExtractedEntityDto>> ExtractEntitiesAsync(
        string text, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Extrayez les entités juridiques importantes de ce document.
Identifiez les personnes, organisations, dates, montants, références légales, etc.
Répondez en JSON avec le format:
{{
  ""entities"": [
    {{
      ""text"": ""texte de l'entité"",
      ""type"": ""PERSON|ORGANIZATION|DATE|AMOUNT|LEGAL_REFERENCE|LOCATION|OTHER"",
      ""startPosition"": 0,
      ""endPosition"": 10,
      ""confidence"": 0.95,
      ""normalizedValue"": ""valeur normalisée""
    }}
  ]
}}

Document: {text.Substring(0, Math.Min(text.Length, 4000))}";

        var options = LLMServiceExtensions.ForEntityExtraction(2000);
        var result = await _llmService.AnalyzeStructuredAsync<EntityExtractionResponse>(
            text, prompt, options, cancellationToken);

        return result?.Entities?.Select(e => new ExtractedEntityDto
        {
            Id = Guid.NewGuid(),
            Text = e.Text,
            Type = e.Type,
            ConfidenceScore = e.Confidence,
            StartPosition = e.StartPosition,
            EndPosition = e.EndPosition,
            NormalizedValue = e.NormalizedValue
        }).ToList() ?? new List<ExtractedEntityDto>();
    }

    private async Task<List<DocumentCitationDto>> FindCitationsAsync(
        string text,
        CancellationToken cancellationToken)
    {
        try
        {
            var prompt = $@"Identifiez toutes les références juridiques et citations dans ce document.
Recherchez les références à des lois, codes, jurisprudences, articles, décrets, etc.
Répondez en JSON avec le format:
{{
  ""citations"": [
    {{
      ""title"": ""Titre de la référence"",
      ""source"": ""Source (Code civil, Loi, Jurisprudence, etc.)"",
      ""url"": ""URL si disponible"",
      ""context"": ""Contexte d'utilisation dans le document"",
      ""relevanceScore"": 0.95
    }}
  ]
}}

Document: {text.Substring(0, Math.Min(text.Length, 4000))}";

            var options = LLMServiceExtensions.ForEntityExtraction(2000);
            var result = await _llmService.AnalyzeStructuredAsync<CitationExtractionResponse>(
                text, prompt, options, cancellationToken);

            return result?.Citations?.Select(c => new DocumentCitationDto
            {
                Title = c.Title,
                Source = c.Source,
                Url = c.Url,
                Context = c.Context,
                RelevanceScore = c.RelevanceScore
            }).ToList() ?? new List<DocumentCitationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error finding citations in document");
            return new List<DocumentCitationDto>();
        }
    }

    private async Task<DocumentSummaryDto> GenerateDocumentSummaryAsync(
        string text,
        string documentType,
        List<ClauseAnalysisDto> clauses,
        List<RiskAssessmentDto> risks,
        CancellationToken cancellationToken)
    {
        var prompt = $@"Générez un résumé exécutif structuré de ce document juridique de type {documentType}.

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans retours à la ligne dans les valeurs.

Répondez en JSON avec ce format:
{{
  ""executiveSummary"": ""résumé exécutif du document"",
  ""keyPoints"": [""point1"", ""point2"", ""point3""],
  ""mainParties"": [""partie1"", ""partie2""],
  ""importantDates"": [""date1"", ""date2""],
  ""financialTerms"": [""terme1"", ""terme2""],
  ""documentPurpose"": ""objectif principal du document"",
  ""overallRiskLevel"": ""Low|Medium|High|Critical""
}}

Document: {text.Substring(0, Math.Min(text.Length, 6000))}";

        var options = LLMServiceExtensions.ForSummarization(3000);
        var result = await _llmService.AnalyzeStructuredAsync<DocumentSummaryResponse>(
            text, prompt, options, cancellationToken);

        if (result != null)
        {
            return new DocumentSummaryDto
            {
                ExecutiveSummary = result.ExecutiveSummary,
                KeyPoints = result.KeyPoints,
                MainParties = result.MainParties,
                ImportantDates = result.ImportantDates,
                FinancialTerms = result.FinancialTerms,
                DocumentPurpose = result.DocumentPurpose,
                OverallRiskLevel = result.OverallRiskLevel
            };
        }

        // Fallback si le parsing JSON échoue
        var fallbackResult = await _llmService.SendPromptAsync(prompt, options, cancellationToken);
        return new DocumentSummaryDto
        {
            ExecutiveSummary = fallbackResult.Content,
            OverallRiskLevel = risks.Any() ? risks.Max(r => r.Severity) : "Unknown",
            KeyPoints = new List<string>(),
            MainParties = new List<string>(),
            ImportantDates = new List<string>(),
            FinancialTerms = new List<string>(),
            DocumentPurpose = "Non déterminé"
        };
    }

    private async Task<LLMResponse> GenerateOverallAnalysisAsync(
        string text, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        List<DocumentRecommendationDto> recommendations, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Effectuez une analyse juridique complète de ce document de type {documentType}.
Synthétisez les clauses importantes, les risques identifiés, et les recommandations.
Fournissez une évaluation globale et des conseils juridiques.

Document: {text.Substring(0, Math.Min(text.Length, 8000))}

Clauses analysées: {clauses.Count}
Risques identifiés: {risks.Count}
Recommandations: {recommendations.Count}";

        var options = LLMServiceExtensions.ForLegalAnalysis(8000);
        return await _llmService.SendPromptAsync(prompt, options, cancellationToken);
    }

    private static double CalculateOverallConfidence(
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        List<ExtractedEntityDto> entities)
    {
        var scores = new List<double>();
        
        if (clauses.Any())
            scores.Add(clauses.Average(c => c.ConfidenceScore));
        
        if (entities.Any())
            scores.Add(entities.Average(e => e.ConfidenceScore));

        return scores.Any() ? scores.Average() : 0.8;
    }

    private async Task SaveRelatedEntitiesAsync(
        Guid analysisId,
        List<ClauseAnalysisDto> clauses,
        List<RiskAssessmentDto> risks,
        List<DocumentRecommendationDto> recommendations,
        List<ExtractedEntityDto> entities,
        List<DocumentCitationDto> citations,
        CancellationToken cancellationToken)
    {
        try
        {
            // Sauvegarder les clauses
            foreach (var clause in clauses)
            {
                var clauseEntity = new ClauseAnalysis
                {
                    Id = clause.Id,
                    DocumentAnalysisResultId = analysisId,
                    ClauseText = clause.ClauseText,
                    ClauseType = clause.ClauseType,
                    Analysis = clause.Analysis,
                    RiskLevel = Enum.Parse<ClauseRiskLevel>(clause.RiskLevel, true),
                    ConfidenceScore = clause.ConfidenceScore,
                    StartPosition = clause.StartPosition,
                    EndPosition = clause.EndPosition,
                    SuggestedRevision = clause.SuggestedRevision,
                    Tags = clause.Tags ?? new List<string>(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                await _repository.AddClauseAsync(clauseEntity, cancellationToken);
            }

            // Sauvegarder les risques
            foreach (var risk in risks)
            {
                var riskEntity = new RiskAssessment
                {
                    Id = risk.Id,
                    DocumentAnalysisResultId = analysisId,
                    RiskType = risk.RiskType,
                    Description = risk.Description,
                    Severity = Enum.Parse<RiskSeverity>(risk.Severity, true),
                    Probability = risk.Probability,
                    Impact = risk.Impact,
                    Mitigation = risk.Mitigation,
                    AffectedClauses = risk.AffectedClauses ?? new List<string>(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                await _repository.AddRiskAsync(riskEntity, cancellationToken);
            }

            // Sauvegarder les recommandations
            foreach (var recommendation in recommendations)
            {
                var recommendationEntity = new DocumentRecommendation
                {
                    Id = recommendation.Id,
                    DocumentAnalysisResultId = analysisId,
                    Type = recommendation.Type,
                    Title = recommendation.Title,
                    Description = recommendation.Description,
                    Priority = Enum.Parse<RecommendationPriority>(recommendation.Priority, true),
                    SuggestedAction = recommendation.SuggestedAction,
                    LegalBasis = recommendation.LegalBasis,
                    RelatedClauses = recommendation.RelatedClauses ?? new List<string>(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                await _repository.AddRecommendationAsync(recommendationEntity, cancellationToken);
            }

            // Sauvegarder les entités
            foreach (var entity in entities)
            {
                var entityEntity = new ExtractedEntity
                {
                    Id = entity.Id,
                    DocumentAnalysisResultId = analysisId,
                    Text = entity.Text,
                    Type = entity.Type,
                    ConfidenceScore = entity.ConfidenceScore,
                    StartPosition = entity.StartPosition,
                    EndPosition = entity.EndPosition,
                    NormalizedValue = entity.NormalizedValue,
                    Metadata = new Dictionary<string, object>(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                await _repository.AddEntityAsync(entityEntity, cancellationToken);
            }

            // Sauvegarder les citations
            foreach (var citation in citations)
            {
                var citationEntity = new DocumentCitation
                {
                    Id = Guid.NewGuid(),
                    DocumentAnalysisResultId = analysisId,
                    Type = "Legal Reference",
                    Title = citation.Title,
                    Source = citation.Source,
                    Url = citation.Url,
                    Reference = citation.Title, // Utiliser le titre comme référence
                    RelevanceScore = citation.RelevanceScore,
                    Context = citation.Context,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                await _repository.AddCitationAsync(citationEntity, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving related entities for analysis: {AnalysisId}", analysisId);
            // Ne pas faire échouer l'analyse complète si la sauvegarde des entités liées échoue
        }
    }

    // Classes pour la désérialisation JSON
    private class EntityExtractionResponse
    {
        public List<ExtractedEntityResponse> Entities { get; set; } = new();
    }

    private class ExtractedEntityResponse
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public double Confidence { get; set; }
        public string? NormalizedValue { get; set; }
    }

    private class CitationExtractionResponse
    {
        public List<CitationResponse> Citations { get; set; } = new();
    }

    private class CitationResponse
    {
        public string Title { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string? Url { get; set; }
        public string Context { get; set; } = string.Empty;
        public double RelevanceScore { get; set; }
    }

    private class DocumentSummaryResponse
    {
        public string ExecutiveSummary { get; set; } = string.Empty;
        public List<string> KeyPoints { get; set; } = new();
        public List<string> MainParties { get; set; } = new();
        public List<string> ImportantDates { get; set; } = new();
        public List<string> FinancialTerms { get; set; } = new();
        public string DocumentPurpose { get; set; } = string.Empty;
        public string OverallRiskLevel { get; set; } = "Unknown";
    }
}
