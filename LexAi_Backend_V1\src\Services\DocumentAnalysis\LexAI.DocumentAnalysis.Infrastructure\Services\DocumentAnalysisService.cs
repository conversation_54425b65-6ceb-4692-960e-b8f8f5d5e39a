using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.DocumentAnalysis.Domain.Entities;
using LexAI.Shared.Application.Interfaces;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.DTOs.LLM;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service principal d'analyse de documents juridiques avec IA
/// </summary>
public class DocumentAnalysisService : IDocumentAnalysisService
{
    private readonly IDocumentExtractionService _extractionService;
    private readonly IClauseAnalysisService _clauseAnalysisService;
    private readonly IRiskAssessmentService _riskAssessmentService;
    private readonly IRecommendationService _recommendationService;
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<DocumentAnalysisService> _logger;
    private readonly IConfiguration _configuration;

    public DocumentAnalysisService(
        IDocumentExtractionService extractionService,
        IClauseAnalysisService clauseAnalysisService,
        IRiskAssessmentService riskAssessmentService,
        IRecommendationService recommendationService,
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<DocumentAnalysisService> logger)
    {
        _extractionService = extractionService;
        _clauseAnalysisService = clauseAnalysisService;
        _riskAssessmentService = riskAssessmentService;
        _recommendationService = recommendationService;
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Analyse complète d'un document juridique
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogInformation("Starting comprehensive document analysis for: {DocumentName}", request.DocumentName);

        try
        {
            // 1. Extraction du texte
            var extractionResult = await ExtractDocumentTextAsync(request, cancellationToken);
            
            // 2. Analyse des clauses (si demandée)
            var clauses = new List<ClauseAnalysisDto>();
            if (request.Options.ExtractClauses)
            {
                clauses = await _clauseAnalysisService.AnalyzeClausesAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    cancellationToken);
            }

            // 3. Évaluation des risques (si demandée)
            var risks = new List<RiskAssessmentDto>();
            if (request.Options.PerformRiskAssessment)
            {
                risks = await _riskAssessmentService.AssessDocumentRisksAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    clauses, 
                    cancellationToken);
            }

            // 4. Génération de recommandations (si demandée)
            var recommendations = new List<DocumentRecommendationDto>();
            if (request.Options.GenerateRecommendations)
            {
                recommendations = await _recommendationService.GenerateRecommendationsAsync(
                    extractionResult.ExtractedText, 
                    request.DocumentType, 
                    clauses, 
                    risks, 
                    cancellationToken);
            }

            // 5. Extraction d'entités (si demandée)
            var entities = new List<ExtractedEntityDto>();
            if (request.Options.ExtractEntities)
            {
                entities = await ExtractEntitiesAsync(extractionResult.ExtractedText, cancellationToken);
            }

            // 6. Recherche de citations (si demandée)
            var citations = new List<DocumentCitationDto>();
            if (request.Options.FindCitations)
            {
                citations = await FindCitationsAsync(extractionResult.ExtractedText, cancellationToken);
            }

            // 7. Génération du résumé exécutif
            var summary = await GenerateDocumentSummaryAsync(
                extractionResult.ExtractedText, 
                request.DocumentType, 
                clauses, 
                risks, 
                cancellationToken);

            // 8. Analyse globale avec LLM
            var overallAnalysis = await GenerateOverallAnalysisAsync(
                extractionResult.ExtractedText, 
                request.DocumentType, 
                clauses, 
                risks, 
                recommendations, 
                cancellationToken);

            stopwatch.Stop();

            // Construction de la réponse
            var response = new DocumentAnalysisResponseDto
            {
                Id = Guid.NewGuid(),
                DocumentName = request.DocumentName,
                DocumentType = request.DocumentType,
                Status = DocumentAnalysisStatus.Completed.ToString(),
                AnalysisContent = overallAnalysis.Content,
                ConfidenceScore = CalculateOverallConfidence(clauses, risks, entities),
                ProcessingTimeMs = (int)stopwatch.ElapsedMilliseconds,
                TokensUsed = overallAnalysis.TokensUsed,
                EstimatedCost = overallAnalysis.EstimatedCost,
                ModelUsed = overallAnalysis.ModelUsed,
                AnalyzedAt = DateTime.UtcNow,
                Clauses = clauses,
                Risks = risks,
                Recommendations = recommendations,
                Entities = entities,
                Citations = citations,
                Summary = summary
            };

            _logger.LogInformation("Document analysis completed successfully. Processing time: {ProcessingTime}ms, Tokens: {Tokens}", 
                stopwatch.ElapsedMilliseconds, overallAnalysis.TokensUsed);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during document analysis for: {DocumentName}", request.DocumentName);
            throw;
        }
    }

    /// <summary>
    /// Obtient le résultat d'une analyse par ID
    /// </summary>
    public async Task<DocumentAnalysisResponseDto?> GetAnalysisResultAsync(
        Guid analysisId, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrieving analysis result: {AnalysisId}", analysisId);

        // TODO: Implémenter la récupération depuis la base de données
        // Pour l'instant, retourner null
        await Task.Delay(1, cancellationToken);
        return null;
    }

    /// <summary>
    /// Obtient la liste des analyses pour un utilisateur
    /// </summary>
    public async Task<DocumentAnalysisListResponseDto> GetUserAnalysesAsync(
        DocumentAnalysisListRequestDto request, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Retrieving analyses for user: {UserId}", request.UserId);

        // TODO: Implémenter la récupération depuis la base de données
        await Task.Delay(1, cancellationToken);
        
        return new DocumentAnalysisListResponseDto
        {
            Items = new List<DocumentAnalysisSummaryDto>(),
            TotalCount = 0,
            Page = request.Page,
            PageSize = request.PageSize,
            TotalPages = 0
        };
    }

    /// <summary>
    /// Supprime une analyse
    /// </summary>
    public async Task<bool> DeleteAnalysisAsync(
        Guid analysisId, 
        Guid userId, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Deleting analysis {AnalysisId} for user {UserId}", analysisId, userId);

        // TODO: Implémenter la suppression depuis la base de données
        await Task.Delay(1, cancellationToken);
        return true;
    }

    /// <summary>
    /// Régénère l'analyse d'un document existant
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> RegenerateAnalysisAsync(
        Guid analysisId, 
        AnalysisOptions? newOptions = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Regenerating analysis: {AnalysisId}", analysisId);

        // TODO: Récupérer le document original et relancer l'analyse
        throw new NotImplementedException("Regeneration will be implemented with database integration");
    }

    // Méthodes privées utilitaires

    private async Task<DocumentExtractionResult> ExtractDocumentTextAsync(
        DocumentAnalysisRequestDto request, 
        CancellationToken cancellationToken)
    {
        if (request.Options.UseAzureDocumentIntelligence)
        {
            return await _extractionService.ExtractTextWithAzureAsync(
                request.DocumentContent, 
                request.DocumentType, 
                cancellationToken);
        }
        else
        {
            return await _extractionService.ExtractTextAsync(
                request.DocumentContent, 
                request.DocumentType, 
                cancellationToken);
        }
    }

    private async Task<List<ExtractedEntityDto>> ExtractEntitiesAsync(
        string text, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Extrayez les entités juridiques importantes de ce document.
Identifiez les personnes, organisations, dates, montants, références légales, etc.
Répondez en JSON avec le format:
{{
  ""entities"": [
    {{
      ""text"": ""texte de l'entité"",
      ""type"": ""PERSON|ORGANIZATION|DATE|AMOUNT|LEGAL_REFERENCE|LOCATION|OTHER"",
      ""startPosition"": 0,
      ""endPosition"": 10,
      ""confidence"": 0.95,
      ""normalizedValue"": ""valeur normalisée""
    }}
  ]
}}

Document: {text.Substring(0, Math.Min(text.Length, 4000))}";

        var options = LLMServiceExtensions.ForEntityExtraction(2000);
        var result = await _llmService.AnalyzeStructuredAsync<EntityExtractionResponse>(
            text, prompt, options, cancellationToken);

        return result?.Entities?.Select(e => new ExtractedEntityDto
        {
            Id = Guid.NewGuid(),
            Text = e.Text,
            Type = e.Type,
            ConfidenceScore = e.Confidence,
            StartPosition = e.StartPosition,
            EndPosition = e.EndPosition,
            NormalizedValue = e.NormalizedValue
        }).ToList() ?? new List<ExtractedEntityDto>();
    }

    private async Task<List<DocumentCitationDto>> FindCitationsAsync(
        string text, 
        CancellationToken cancellationToken)
    {
        // TODO: Implémenter la recherche de citations juridiques
        await Task.Delay(1, cancellationToken);
        return new List<DocumentCitationDto>();
    }

    private async Task<DocumentSummaryDto> GenerateDocumentSummaryAsync(
        string text, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Générez un résumé exécutif de ce document juridique de type {documentType}.
Incluez les points clés, parties principales, dates importantes, termes financiers, et objectif du document.

Document: {text.Substring(0, Math.Min(text.Length, 6000))}";

        var options = LLMServiceExtensions.ForSummarization(3000);
        var result = await _llmService.SendPromptAsync(prompt, options, cancellationToken);

        // TODO: Parser la réponse pour extraire les éléments structurés
        return new DocumentSummaryDto
        {
            ExecutiveSummary = result.Content,
            OverallRiskLevel = risks.Any() ? risks.Max(r => r.Severity) : "Low"
        };
    }

    private async Task<LLMResponse> GenerateOverallAnalysisAsync(
        string text, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        List<DocumentRecommendationDto> recommendations, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Effectuez une analyse juridique complète de ce document de type {documentType}.
Synthétisez les clauses importantes, les risques identifiés, et les recommandations.
Fournissez une évaluation globale et des conseils juridiques.

Document: {text.Substring(0, Math.Min(text.Length, 8000))}

Clauses analysées: {clauses.Count}
Risques identifiés: {risks.Count}
Recommandations: {recommendations.Count}";

        var options = LLMServiceExtensions.ForLegalAnalysis(8000);
        return await _llmService.SendPromptAsync(prompt, options, cancellationToken);
    }

    private static double CalculateOverallConfidence(
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        List<ExtractedEntityDto> entities)
    {
        var scores = new List<double>();
        
        if (clauses.Any())
            scores.Add(clauses.Average(c => c.ConfidenceScore));
        
        if (entities.Any())
            scores.Add(entities.Average(e => e.ConfidenceScore));

        return scores.Any() ? scores.Average() : 0.8;
    }

    // Classes pour la désérialisation JSON
    private class EntityExtractionResponse
    {
        public List<ExtractedEntityResponse> Entities { get; set; } = new();
    }

    private class ExtractedEntityResponse
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public double Confidence { get; set; }
        public string? NormalizedValue { get; set; }
    }
}
