using FluentValidation;
using LexAI.AIAssistant.Application.Commands;
using LexAI.AIAssistant.Application.Interfaces;
using LexAI.AIAssistant.Infrastructure.Data;
using LexAI.AIAssistant.Infrastructure.Services;
using LexAI.AIAssistant.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using LexAI.Shared.Application.Interfaces;
using LexAI.Shared.Infrastructure.Configuration;
using LexAI.Shared.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Reflection;
using System.Text;
using System.Threading.RateLimiting;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/lexai-ai-assistant-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Add Serilog
    builder.Host.UseSerilog();

    // Add configuration
    builder.Configuration
        .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
        .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: false, reloadOnChange: true)
        .AddEnvironmentVariables();

    // Configure JWT settings
    var jwtSettings = new JwtSettings();
    builder.Configuration.GetSection(JwtSettings.SectionName).Bind(jwtSettings);
    builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection(JwtSettings.SectionName));

    // Add services
    builder.Services.AddControllers();
    builder.Services.AddEndpointsApiExplorer();

    // Configure Swagger
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "LexAI AI Assistant Service API",
            Version = "v1",
            Description = "Service d'assistant IA juridique pour LexAI",
            Contact = new OpenApiContact
            {
                Name = "LexAI Support",
                Email = "<EMAIL>"
            }
        });

        // Add JWT authentication to Swagger
        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            }
        });

        // Include XML comments
        var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
        var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
        if (File.Exists(xmlPath))
        {
            c.IncludeXmlComments(xmlPath);
        }
    });

    // Configure JWT Authentication
    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = jwtSettings.ValidateIssuer,
                ValidateAudience = jwtSettings.ValidateAudience,
                ValidateLifetime = jwtSettings.ValidateLifetime,
                ValidateIssuerSigningKey = jwtSettings.ValidateIssuerSigningKey,
                ValidIssuer = jwtSettings.Issuer,
                ValidAudience = jwtSettings.Audience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.SecretKey)),
                ClockSkew = TimeSpan.FromMinutes(jwtSettings.ClockSkewMinutes)
            };

            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    Log.Warning("JWT Authentication failed: {Error}", context.Exception.Message);
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    Log.Information("JWT Token validated for user: {User}", context.Principal?.Identity?.Name);
                    return Task.CompletedTask;
                }
            };
        });

    // Configure Authorization
    builder.Services.AddAuthorization(options =>
    {
        options.AddPolicy("RequireAuthentication", policy =>
            policy.RequireAuthenticatedUser());

        options.AddPolicy("RequireLawyerRole", policy =>
            policy.RequireRole("Administrator", "SeniorLawyer", "Lawyer"));
    });

    // Configure Rate Limiting
    builder.Services.AddRateLimiter(options =>
    {
        options.AddFixedWindowLimiter("ChatPolicy", configure =>
        {
            configure.PermitLimit = 50; // 50 requests
            configure.Window = TimeSpan.FromMinutes(1); // per minute
            configure.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
            configure.QueueLimit = 5;
        });

        options.AddFixedWindowLimiter("DocumentPolicy", configure =>
        {
            configure.PermitLimit = 10; // 10 document operations
            configure.Window = TimeSpan.FromMinutes(1); // per minute
            configure.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
            configure.QueueLimit = 2;
        });

        options.OnRejected = async (context, token) =>
        {
            context.HttpContext.Response.StatusCode = 429;
            await context.HttpContext.Response.WriteAsync("Rate limit exceeded. Please try again later.", token);
        };
    });

    // Add MediatR
    builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(SendMessageCommand).Assembly));

    // Add FluentValidation
    builder.Services.AddValidatorsFromAssemblyContaining<SendMessageCommand>();

    // Add AutoMapper
    builder.Services.AddAutoMapper(typeof(SendMessageCommand).Assembly);

    // Add Memory Cache
    builder.Services.AddMemoryCache();

    // Add HTTP Client for AI Services
    builder.Services.AddHttpClient<IAIAssistantService, EnhancedAIAssistantService>(client =>
    {
        client.Timeout = TimeSpan.FromMinutes(3);
    });

    // Add HTTP Client for Legal Research Integration
    builder.Services.AddHttpClient<ILegalResearchIntegrationService, LegalResearchIntegrationService>(client =>
    {
        client.Timeout = TimeSpan.FromMinutes(2);
    });

    // Register unified AI services
    builder.Services.AddScoped<IUnifiedLLMService, UnifiedLLMService>();
    builder.Services.AddScoped<IUnifiedEmbeddingService, UnifiedEmbeddingService>();

    // Register application services
    builder.Services.AddScoped<IAIAssistantService, EnhancedAIAssistantService>();
    builder.Services.AddScoped<IConversationService, ConversationManagementService>();
    builder.Services.AddScoped<ILegalResearchIntegrationService, LegalResearchIntegrationService>();

    // Add Entity Framework
    builder.Services.AddDbContext<AIAssistantDbContext>(options =>
    {
        var connectionString = builder.Configuration.GetConnectionString("PostgreSql")
            ?? "Host=localhost;Database=ai_assistant_db;Username=lexai_user;Password=lexai_password_2024!";
        options.UseNpgsql(connectionString);
    });

    // Register repositories
    builder.Services.AddScoped<IConversationRepository, ConversationRepository>();

    // Add SignalR for real-time chat
    builder.Services.AddSignalR(options =>
    {
        options.EnableDetailedErrors = builder.Environment.IsDevelopment();
        options.KeepAliveInterval = TimeSpan.FromSeconds(15);
        options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
    });

    // Configure CORS
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowedOrigins", policy =>
        {
            var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>()
                ?? new[] { "http://localhost:3000", "http://localhost:3001" };

            policy.WithOrigins(allowedOrigins)
                  .AllowAnyMethod()
                  .AllowAnyHeader()
                  .AllowCredentials();
        });
    });

    // Add Health Checks
    builder.Services.AddHealthChecks()
        .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy())
        .AddNpgSql(builder.Configuration.GetConnectionString("PostgreSql") ?? "Host=localhost;Database=ai_assistant_db;Username=lexai_user;Password=lexai_password_2024!");

    var app = builder.Build();

    // Configure the HTTP request pipeline
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "LexAI AI Assistant Service API v1");
            c.RoutePrefix = "swagger";
            c.DocumentTitle = "LexAI AI Assistant Service API Documentation";
        });
    }

    // Security headers
    app.Use(async (context, next) =>
    {
        context.Response.Headers["X-Content-Type-Options"] = "nosniff";
        context.Response.Headers["X-Frame-Options"] = "DENY";
        context.Response.Headers["X-XSS-Protection"] = "1; mode=block";
        context.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";

        if (!app.Environment.IsDevelopment())
        {
            context.Response.Headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains";
        }

        await next();
    });

    // Request logging middleware
    app.Use(async (context, next) =>
    {
        var correlationId = Guid.NewGuid().ToString();
        context.Items["CorrelationId"] = correlationId;
        context.Response.Headers["X-Correlation-ID"] = correlationId;

        Log.Information("Request {Method} {Path} started with correlation ID {CorrelationId}",
            context.Request.Method, context.Request.Path, correlationId);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        await next();

        stopwatch.Stop();

        Log.Information("Request {Method} {Path} completed in {ElapsedMs}ms with status {StatusCode} (Correlation ID: {CorrelationId})",
            context.Request.Method, context.Request.Path, stopwatch.ElapsedMilliseconds, context.Response.StatusCode, correlationId);
    });

    app.UseHttpsRedirection();
    app.UseCors("AllowedOrigins");
    app.UseRateLimiter();
    app.UseAuthentication();
    app.UseAuthorization();

    // Health checks endpoint
    app.MapHealthChecks("/health");

    // Map controllers
    app.MapControllers();

    // Map SignalR hubs
    // app.MapHub<ChatHub>("/chat-hub");

    Log.Information("LexAI AI Assistant Service started successfully");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "LexAI AI Assistant Service failed to start");
}
finally
{
    Log.CloseAndFlush();
}
