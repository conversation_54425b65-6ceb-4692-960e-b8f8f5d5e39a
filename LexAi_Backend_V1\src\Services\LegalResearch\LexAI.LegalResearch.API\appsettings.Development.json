{"ConnectionStrings": {"PostgreSql": "Host=localhost;Port=5433;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "AzureOpenAI": {"Endpoint": "https://lexai-az-openai.openai.azure.com/", "ApiKey": "4vl6Y7N8YV5D5oMUtEJYoYAlPc1tv1s0IPpbxsHrpe6zk2CPAGZQJQQJ99BFAC5T7U2XJ3w3AAABACOGv9Ha", "ChatDeploymentName": "lexai-gpt-4.1-nano", "EmbeddingDeploymentName": "text-embedding-3-small", "ApiVersion": "2024-02-01", "MaxTokens": 8000, "Temperature": 0.1, "TopP": 0.95, "FrequencyPenalty": 0, "PresencePenalty": 0, "EmbeddingDimension": "1536"}, "LLMService": {"DefaultProvider": "AzureOpenAI", "FallbackProvider": "Local", "MaxRetries": 3, "TimeoutSeconds": 120}, "OpenAI": {"ApiKey": "your-openai-api-key-fallback", "ChatModel": "gpt-4", "EmbeddingModel": "text-embedding-3-small", "MaxTokens": 4000, "Temperature": 0.7, "EmbeddingDimension": "1536"}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:3001"]}}