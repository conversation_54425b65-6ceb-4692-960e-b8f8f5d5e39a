2025-06-19 00:52:00.095 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-19 00:52:00.221 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:52:00.881 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-19 00:52:00.884 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-19 00:52:00.986 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:52:00.990 +04:00 [INF] Hosting environment: Development
2025-06-19 00:52:00.992 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-19 00:52:02.446 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-19 00:52:02.611 +04:00 [INF] Request GET / started with correlation ID 9318ae47-5970-4f9e-a220-749d81e4b1bd
2025-06-19 00:52:04.753 +04:00 [INF] Request GET / completed in 2137ms with status 404 (Correlation ID: 9318ae47-5970-4f9e-a220-749d81e4b1bd)
2025-06-19 00:52:04.764 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 2318.8265ms
2025-06-19 00:52:04.785 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-19 00:54:01.162 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 00:54:01.197 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID c6052e28-7127-48e5-9b15-62560b804b35
2025-06-19 00:54:01.243 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:54:01.249 +04:00 [INF] Request OPTIONS /api/chat/message completed in 12ms with status 204 (Correlation ID: c6052e28-7127-48e5-9b15-62560b804b35)
2025-06-19 00:54:01.256 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 94.1324ms
2025-06-19 00:54:01.261 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 83
2025-06-19 00:54:01.273 +04:00 [INF] Request POST /api/chat/message started with correlation ID 0ee5de57-4ed5-4926-962f-79dded08e273
2025-06-19 00:54:01.277 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:54:01.498 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:54:01.518 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 00:54:01.566 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 00:54:02.804 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:54:26.058 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur droit camerounais
2025-06-19 00:54:52.462 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur droit camerounais
2025-06-19 00:54:52.837 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 00:54:52.849 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 00:54:53.386 +04:00 [INF] Executed DbCommand (107ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-19 00:54:53.450 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur droit camerounais
2025-06-19 00:54:56.971 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 204, Cost: 0, Time: 1546ms
2025-06-19 00:54:57.068 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p21='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?' (DbType = Decimal), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = DateTime), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32), @p10='?', @p11='?', @p12='?', @p13='?', @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime), @p17='?', @p18='?', @p19='?' (DbType = Guid), @p20='?' (DbType = Int32), @p45='?' (DbType = Guid), @p22='?' (DbType = Double), @p23='?', @p24='?' (DbType = Guid), @p25='?' (DbType = DateTime), @p26='?', @p27='?' (DbType = DateTime), @p28='?', @p29='?' (DbType = Int32), @p30='?' (DbType = Int32), @p31='?', @p32='?' (DbType = Decimal), @p33='?' (DbType = Boolean), @p34='?' (DbType = Boolean), @p35='?', @p36='?', @p37='?' (DbType = Object), @p38='?' (DbType = Int32), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?' (DbType = DateTime), @p43='?', @p44='?', @p69='?' (DbType = Guid), @p46='?' (DbType = Double), @p47='?', @p48='?' (DbType = Guid), @p49='?' (DbType = DateTime), @p50='?', @p51='?' (DbType = DateTime), @p52='?', @p53='?' (DbType = Int32), @p54='?' (DbType = Int32), @p55='?', @p56='?' (DbType = Decimal), @p57='?' (DbType = Boolean), @p58='?' (DbType = Boolean), @p59='?', @p60='?', @p61='?' (DbType = Object), @p62='?' (DbType = Int32), @p63='?', @p64='?' (DbType = Int32), @p65='?' (DbType = Int32), @p66='?' (DbType = DateTime), @p67='?', @p68='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "CreatedAt" = @p0, "CreatedBy" = @p1, "DeletedAt" = @p2, "DeletedBy" = @p3, "EstimatedCost" = @p4, "IsArchived" = @p5, "IsDeleted" = @p6, "LastActivityAt" = @p7, "MessageCount" = @p8, "PrimaryDomain" = @p9, "SessionId" = @p10, "Status" = @p11, "Summary" = @p12, "Tags" = @p13, "Title" = @p14, "TotalTokensUsed" = @p15, "UpdatedAt" = @p16, "UpdatedBy" = @p17, "UserFeedback" = @p18, "UserId" = @p19, "UserRating" = @p20
WHERE "Id" = @p21;
UPDATE "Messages" SET "ConfidenceScore" = @p22, "Content" = @p23, "ConversationId" = @p24, "CreatedAt" = @p25, "CreatedBy" = @p26, "DeletedAt" = @p27, "DeletedBy" = @p28, "DetectedDomain" = @p29, "DetectedIntent" = @p30, "ErrorMessage" = @p31, "EstimatedCost" = @p32, "IsDeleted" = @p33, "IsEdited" = @p34, "MessageType" = @p35, "OriginalContent" = @p36, "ProcessingTime" = @p37, "Rating" = @p38, "Role" = @p39, "Status" = @p40, "TokensUsed" = @p41, "UpdatedAt" = @p42, "UpdatedBy" = @p43, "UserFeedback" = @p44
WHERE "Id" = @p45;
UPDATE "Messages" SET "ConfidenceScore" = @p46, "Content" = @p47, "ConversationId" = @p48, "CreatedAt" = @p49, "CreatedBy" = @p50, "DeletedAt" = @p51, "DeletedBy" = @p52, "DetectedDomain" = @p53, "DetectedIntent" = @p54, "ErrorMessage" = @p55, "EstimatedCost" = @p56, "IsDeleted" = @p57, "IsEdited" = @p58, "MessageType" = @p59, "OriginalContent" = @p60, "ProcessingTime" = @p61, "Rating" = @p62, "Role" = @p63, "Status" = @p64, "TokensUsed" = @p65, "UpdatedAt" = @p66, "UpdatedBy" = @p67, "UserFeedback" = @p68
WHERE "Id" = @p69;
2025-06-19 00:54:57.278 +04:00 [ERR] Error updating conversation: "71305698-22b2-4f57-8ed1-038eb2fbb47b"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.UpdateAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 94
2025-06-19 00:54:57.605 +04:00 [ERR] Error processing message for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.UpdateAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 94
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
2025-06-19 00:55:36.458 +04:00 [ERR] Error processing chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.UpdateAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 94
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
   at LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(ChatRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Controllers\ChatController.cs:line 86
2025-06-19 00:55:36.558 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 00:55:36.589 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 95006.8464ms
2025-06-19 00:55:36.594 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 00:55:36.600 +04:00 [INF] Request POST /api/chat/message completed in 95323ms with status 500 (Correlation ID: 0ee5de57-4ed5-4926-962f-79dded08e273)
2025-06-19 00:55:36.616 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null application/json; charset=utf-8 95355.5435ms
