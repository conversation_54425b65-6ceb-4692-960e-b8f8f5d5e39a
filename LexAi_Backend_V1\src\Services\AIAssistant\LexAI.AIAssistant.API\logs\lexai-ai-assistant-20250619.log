2025-06-19 20:41:00.511 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-19 20:41:01.761 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 20:41:03.613 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-19 20:41:04.182 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-19 20:41:04.484 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 20:41:04.743 +04:00 [INF] Hosting environment: Development
2025-06-19 20:41:04.782 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-19 20:41:04.954 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-19 20:41:05.269 +04:00 [INF] Request GET / started with correlation ID 87f6f7b2-b270-497e-9603-7d2c286c8723
2025-06-19 20:41:07.297 +04:00 [INF] Request GET / completed in 2021ms with status 404 (Correlation ID: 87f6f7b2-b270-497e-9603-7d2c286c8723)
2025-06-19 20:41:07.317 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 2364.2811ms
2025-06-19 20:41:07.341 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-19 20:41:51.317 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 20:41:51.372 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 5cef3ab5-24f9-4724-bf8e-b683d4ceb0c4
2025-06-19 20:41:51.381 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:41:51.388 +04:00 [INF] Request OPTIONS /api/chat/message completed in 12ms with status 204 (Correlation ID: 5cef3ab5-24f9-4724-bf8e-b683d4ceb0c4)
2025-06-19 20:41:51.403 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 86.5735ms
2025-06-19 20:41:51.408 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 94
2025-06-19 20:41:51.424 +04:00 [INF] Request POST /api/chat/message started with correlation ID eda9fca8-0dc0-49e1-853e-554956e42e3b
2025-06-19 20:41:51.429 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:41:51.572 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 20:41:51.588 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:41:51.625 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 20:41:51.687 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 20:41:51.913 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit du travail francais
2025-06-19 20:42:06.709 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 20:42:06.720 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 20:42:06.803 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit du travail francais
2025-06-19 20:42:07.156 +04:00 [INF] Executed DbCommand (49ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-19 20:42:07.184 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit du travail francais
2025-06-19 20:42:09.944 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 215, Cost: 0, Time: 1295ms
2025-06-19 20:42:10.194 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-19 20:42:10.294 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-19 20:42:10.372 +04:00 [ERR] Error saving conversation: "97e9302c-a2a1-4792-adf0-b5a5c629bd2d"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 193
2025-06-19 20:42:10.480 +04:00 [ERR] Error processing message for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 193
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
2025-06-19 20:42:10.578 +04:00 [ERR] Error processing chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 193
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
   at LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(ChatRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Controllers\ChatController.cs:line 86
2025-06-19 20:42:10.594 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 20:42:10.608 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 18972.2056ms
2025-06-19 20:42:10.610 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:42:10.614 +04:00 [INF] Request POST /api/chat/message completed in 19186ms with status 500 (Correlation ID: eda9fca8-0dc0-49e1-853e-554956e42e3b)
2025-06-19 20:42:10.631 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null application/json; charset=utf-8 19223.4539ms
2025-06-19 20:43:46.589 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 20:43:46.600 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID f47ae4a8-3be8-4de7-bb78-dbf742eaf255
2025-06-19 20:43:46.607 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:43:46.611 +04:00 [INF] Request OPTIONS /api/chat/message completed in 3ms with status 204 (Correlation ID: f47ae4a8-3be8-4de7-bb78-dbf742eaf255)
2025-06-19 20:43:46.620 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 30.9829ms
2025-06-19 20:43:46.624 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 95
2025-06-19 20:43:46.661 +04:00 [INF] Request POST /api/chat/message started with correlation ID d46d585c-8da7-4f88-ac49-7a50fad33192
2025-06-19 20:43:46.669 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:43:46.677 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 20:43:46.681 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:43:46.694 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 20:43:46.701 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 20:43:46.725 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit de travail en france
2025-06-19 20:43:46.845 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit de travail en france
2025-06-19 20:43:46.872 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-19 20:43:46.884 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": J'ai besoin d'aide pour une recherche juridique sur le droit de travail en france
2025-06-19 20:43:51.991 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 725, Cost: 0, Time: 3665ms
2025-06-19 20:43:51.997 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "72afdba1-5694-498b-979a-01c75cc0ffdc", Tokens: 725, Cost: 0
2025-06-19 20:43:52.003 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "72afdba1-5694-498b-979a-01c75cc0ffdc", Tokens: 725, Cost: 0
2025-06-19 20:43:52.009 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-19 20:43:52.070 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 5369.2032ms
2025-06-19 20:43:52.075 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:43:52.078 +04:00 [INF] Request POST /api/chat/message completed in 5409ms with status 200 (Correlation ID: d46d585c-8da7-4f88-ac49-7a50fad33192)
2025-06-19 20:43:52.084 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 5459.9353ms
2025-06-19 20:44:57.178 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 20:44:57.186 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 10243583-9fef-46e1-bc33-a033b2f17ddd
2025-06-19 20:44:57.191 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:44:57.193 +04:00 [INF] Request OPTIONS /api/chat/message completed in 2ms with status 204 (Correlation ID: 10243583-9fef-46e1-bc33-a033b2f17ddd)
2025-06-19 20:44:57.203 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 24.9386ms
2025-06-19 20:44:57.206 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 45
2025-06-19 20:44:57.219 +04:00 [INF] Request POST /api/chat/message started with correlation ID cc436afd-dc2e-43b3-87d2-1ac37033bc79
2025-06-19 20:44:57.223 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:44:57.225 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 20:44:57.229 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:44:57.231 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 20:44:57.239 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 20:44:57.247 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit du travail au cameroun
2025-06-19 20:44:57.399 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit du travail au cameroun
2025-06-19 20:44:57.418 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-19 20:44:57.431 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit du travail au cameroun
2025-06-19 20:45:03.320 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1084, Cost: 0, Time: 5083ms
2025-06-19 20:45:03.324 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "676af85c-5e65-4cf8-ae8a-c6ef2287a24b", Tokens: 1084, Cost: 0
2025-06-19 20:45:03.329 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "676af85c-5e65-4cf8-ae8a-c6ef2287a24b", Tokens: 1084, Cost: 0
2025-06-19 20:45:03.335 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-19 20:45:03.392 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 6156.3027ms
2025-06-19 20:45:03.395 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:45:03.399 +04:00 [INF] Request POST /api/chat/message completed in 6176ms with status 200 (Correlation ID: cc436afd-dc2e-43b3-87d2-1ac37033bc79)
2025-06-19 20:45:03.405 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 6199.488ms
2025-06-19 20:48:12.200 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 20:48:12.209 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 290a9e0b-27ef-4c4c-9e99-72cb3b304097
2025-06-19 20:48:12.213 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:48:12.215 +04:00 [INF] Request OPTIONS /api/chat/message completed in 2ms with status 204 (Correlation ID: 290a9e0b-27ef-4c4c-9e99-72cb3b304097)
2025-06-19 20:48:12.225 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 24.9686ms
2025-06-19 20:48:12.228 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 47
2025-06-19 20:48:12.240 +04:00 [INF] Request POST /api/chat/message started with correlation ID 2ba77f2c-dc3e-4d6d-8ef2-5af07e77b70a
2025-06-19 20:48:12.244 +04:00 [INF] CORS policy execution successful.
2025-06-19 20:48:12.245 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 20:48:12.248 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:48:12.251 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 20:48:12.254 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 20:48:12.258 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit sur le sport au cameroun
2025-06-19 20:48:12.261 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit sur le sport au cameroun
2025-06-19 20:48:12.291 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-19 20:48:12.300 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit sur le sport au cameroun
2025-06-19 20:48:18.702 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1002, Cost: 0, Time: 5132ms
2025-06-19 20:48:18.706 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "a5b73745-a286-49f8-8e8b-b58d97a2895c", Tokens: 1002, Cost: 0
2025-06-19 20:48:18.710 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "a5b73745-a286-49f8-8e8b-b58d97a2895c", Tokens: 1002, Cost: 0
2025-06-19 20:48:18.714 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-19 20:48:18.717 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 6463.1626ms
2025-06-19 20:48:18.719 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 20:48:18.721 +04:00 [INF] Request POST /api/chat/message completed in 6477ms with status 200 (Correlation ID: 2ba77f2c-dc3e-4d6d-8ef2-5af07e77b70a)
2025-06-19 20:48:18.726 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 6497.9409ms
