2025-06-19 21:32:52.049 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-19 21:32:52.323 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 21:32:52.754 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-19 21:32:52.755 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-19 21:32:52.829 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 21:32:52.834 +04:00 [INF] Hosting environment: Development
2025-06-19 21:32:52.839 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-19 21:32:53.645 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-19 21:32:53.867 +04:00 [INF] Request GET / started with correlation ID 5198e21b-6095-4ac8-93e3-825eb71cd2c1
2025-06-19 21:32:53.959 +04:00 [INF] Request GET / completed in 88ms with status 404 (Correlation ID: 5198e21b-6095-4ac8-93e3-825eb71cd2c1)
2025-06-19 21:32:53.968 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 330.6046ms
2025-06-19 21:32:53.992 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-19 21:33:29.292 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 21:33:29.292 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 21:33:29.330 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 2d0a3715-354f-452e-8c2e-24dae8b81352
2025-06-19 21:33:29.331 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 5cad4439-a31e-4b2c-8824-afb49815ecac
2025-06-19 21:33:29.388 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:33:29.388 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:33:29.398 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 15ms with status 204 (Correlation ID: 2d0a3715-354f-452e-8c2e-24dae8b81352)
2025-06-19 21:33:29.398 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 11ms with status 204 (Correlation ID: 5cad4439-a31e-4b2c-8824-afb49815ecac)
2025-06-19 21:33:29.404 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 112.0427ms
2025-06-19 21:33:29.409 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 117.3741ms
2025-06-19 21:33:29.411 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 21:33:29.434 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID fe7eb110-6b5f-4c79-b911-7a6be5cf90b8
2025-06-19 21:33:29.437 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:33:29.565 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:33:29.568 +04:00 [INF] Request GET /api/chat/conversations completed in 131ms with status 404 (Correlation ID: fe7eb110-6b5f-4c79-b911-7a6be5cf90b8)
2025-06-19 21:33:29.573 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 404 0 null 161.9869ms
2025-06-19 21:33:29.579 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 21:33:29.583 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/api/chat/conversations, Response status code: 404
2025-06-19 21:33:29.593 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID bbbc0e03-0857-474e-a5a1-fea46a74753a
2025-06-19 21:33:29.602 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:33:29.611 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:33:29.613 +04:00 [INF] Request GET /api/chat/conversations completed in 11ms with status 404 (Correlation ID: bbbc0e03-0857-474e-a5a1-fea46a74753a)
2025-06-19 21:33:29.618 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 404 0 null 38.8237ms
2025-06-19 21:33:29.633 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/api/chat/conversations, Response status code: 404
2025-06-19 21:33:59.561 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 21:33:59.577 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 84efbf3a-838c-4ffb-8123-a5018d58a12a
2025-06-19 21:33:59.586 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:33:59.591 +04:00 [INF] Request OPTIONS /api/chat/message completed in 6ms with status 204 (Correlation ID: 84efbf3a-838c-4ffb-8123-a5018d58a12a)
2025-06-19 21:33:59.597 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 36.4958ms
2025-06-19 21:33:59.601 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 53
2025-06-19 21:33:59.614 +04:00 [INF] Request POST /api/chat/message started with correlation ID 10666ff6-562a-484e-8fa8-f547dfc332bf
2025-06-19 21:33:59.617 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:33:59.634 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:33:59.648 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 21:33:59.698 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 21:33:59.773 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 21:34:00.037 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail a Accenture Maurice
2025-06-19 21:34:00.325 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail a Accenture Maurice
2025-06-19 21:34:01.593 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 21:34:01.621 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 21:34:02.954 +04:00 [INF] Executed DbCommand (155ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-19 21:34:03.015 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail a Accenture Maurice
2025-06-19 21:34:11.399 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1199, Cost: 0, Time: 6419ms
2025-06-19 21:34:11.655 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p30='?' (DbType = Guid), @p7='?' (DbType = Double), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?', @p17='?' (DbType = Decimal), @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Int32), @p24='?', @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p54='?' (DbType = Guid), @p31='?' (DbType = Double), @p32='?', @p33='?' (DbType = Guid), @p34='?' (DbType = DateTime), @p35='?', @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = Int32), @p39='?' (DbType = Int32), @p40='?', @p41='?' (DbType = Decimal), @p42='?' (DbType = Boolean), @p43='?' (DbType = Boolean), @p44='?', @p45='?', @p46='?' (DbType = Object), @p47='?' (DbType = Int32), @p48='?', @p49='?' (DbType = Int32), @p50='?' (DbType = Int32), @p51='?' (DbType = DateTime), @p52='?', @p53='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6;
UPDATE "Messages" SET "ConfidenceScore" = @p7, "Content" = @p8, "ConversationId" = @p9, "CreatedAt" = @p10, "CreatedBy" = @p11, "DeletedAt" = @p12, "DeletedBy" = @p13, "DetectedDomain" = @p14, "DetectedIntent" = @p15, "ErrorMessage" = @p16, "EstimatedCost" = @p17, "IsDeleted" = @p18, "IsEdited" = @p19, "MessageType" = @p20, "OriginalContent" = @p21, "ProcessingTime" = @p22, "Rating" = @p23, "Role" = @p24, "Status" = @p25, "TokensUsed" = @p26, "UpdatedAt" = @p27, "UpdatedBy" = @p28, "UserFeedback" = @p29
WHERE "Id" = @p30;
UPDATE "Messages" SET "ConfidenceScore" = @p31, "Content" = @p32, "ConversationId" = @p33, "CreatedAt" = @p34, "CreatedBy" = @p35, "DeletedAt" = @p36, "DeletedBy" = @p37, "DetectedDomain" = @p38, "DetectedIntent" = @p39, "ErrorMessage" = @p40, "EstimatedCost" = @p41, "IsDeleted" = @p42, "IsEdited" = @p43, "MessageType" = @p44, "OriginalContent" = @p45, "ProcessingTime" = @p46, "Rating" = @p47, "Role" = @p48, "Status" = @p49, "TokensUsed" = @p50, "UpdatedAt" = @p51, "UpdatedBy" = @p52, "UserFeedback" = @p53
WHERE "Id" = @p54;
2025-06-19 21:34:11.928 +04:00 [ERR] Error saving changes to database
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 210
2025-06-19 21:34:12.256 +04:00 [ERR] Error processing message for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 210
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 162
2025-06-19 21:34:12.611 +04:00 [ERR] Error processing chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 210
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 162
   at LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(ChatRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Controllers\ChatController.cs:line 86
2025-06-19 21:34:12.638 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 21:34:12.672 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 12961.8886ms
2025-06-19 21:34:12.677 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 21:34:12.683 +04:00 [INF] Request POST /api/chat/message completed in 13065ms with status 500 (Correlation ID: 10666ff6-562a-484e-8fa8-f547dfc332bf)
2025-06-19 21:34:12.697 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null application/json; charset=utf-8 13095.7265ms
2025-06-19 21:37:11.872 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 21:37:11.891 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID b0e6af6c-3904-4951-a02d-49a46f96535c
2025-06-19 21:37:11.899 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:37:11.906 +04:00 [INF] Request OPTIONS /api/chat/message completed in 7ms with status 204 (Correlation ID: b0e6af6c-3904-4951-a02d-49a46f96535c)
2025-06-19 21:37:11.921 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 49.1481ms
2025-06-19 21:37:11.924 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 33
2025-06-19 21:37:11.941 +04:00 [INF] Request POST /api/chat/message started with correlation ID c961b1bd-992e-4be8-8818-ab2e2c8422cc
2025-06-19 21:37:11.944 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:37:11.947 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:37:11.950 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 21:37:11.959 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 21:37:11.966 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 21:37:11.993 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Audit en entreprise
2025-06-19 21:37:12.009 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Audit en entreprise
2025-06-19 21:37:12.178 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-19 21:37:12.190 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Audit en entreprise
2025-06-19 21:37:17.709 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 858, Cost: 0, Time: 3965ms
2025-06-19 21:37:17.723 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p30='?' (DbType = Guid), @p7='?' (DbType = Double), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?', @p17='?' (DbType = Decimal), @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Int32), @p24='?', @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p54='?' (DbType = Guid), @p31='?' (DbType = Double), @p32='?', @p33='?' (DbType = Guid), @p34='?' (DbType = DateTime), @p35='?', @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = Int32), @p39='?' (DbType = Int32), @p40='?', @p41='?' (DbType = Decimal), @p42='?' (DbType = Boolean), @p43='?' (DbType = Boolean), @p44='?', @p45='?', @p46='?' (DbType = Object), @p47='?' (DbType = Int32), @p48='?', @p49='?' (DbType = Int32), @p50='?' (DbType = Int32), @p51='?' (DbType = DateTime), @p52='?', @p53='?'], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6;
UPDATE "Messages" SET "ConfidenceScore" = @p7, "Content" = @p8, "ConversationId" = @p9, "CreatedAt" = @p10, "CreatedBy" = @p11, "DeletedAt" = @p12, "DeletedBy" = @p13, "DetectedDomain" = @p14, "DetectedIntent" = @p15, "ErrorMessage" = @p16, "EstimatedCost" = @p17, "IsDeleted" = @p18, "IsEdited" = @p19, "MessageType" = @p20, "OriginalContent" = @p21, "ProcessingTime" = @p22, "Rating" = @p23, "Role" = @p24, "Status" = @p25, "TokensUsed" = @p26, "UpdatedAt" = @p27, "UpdatedBy" = @p28, "UserFeedback" = @p29
WHERE "Id" = @p30;
UPDATE "Messages" SET "ConfidenceScore" = @p31, "Content" = @p32, "ConversationId" = @p33, "CreatedAt" = @p34, "CreatedBy" = @p35, "DeletedAt" = @p36, "DeletedBy" = @p37, "DetectedDomain" = @p38, "DetectedIntent" = @p39, "ErrorMessage" = @p40, "EstimatedCost" = @p41, "IsDeleted" = @p42, "IsEdited" = @p43, "MessageType" = @p44, "OriginalContent" = @p45, "ProcessingTime" = @p46, "Rating" = @p47, "Role" = @p48, "Status" = @p49, "TokensUsed" = @p50, "UpdatedAt" = @p51, "UpdatedBy" = @p52, "UserFeedback" = @p53
WHERE "Id" = @p54;
2025-06-19 21:37:17.930 +04:00 [ERR] Error saving changes to database
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 210
2025-06-19 21:37:18.437 +04:00 [ERR] Error processing message for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 210
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken)
2025-06-19 21:37:18.897 +04:00 [ERR] Error processing chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 210
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken)
   at LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(ChatRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Controllers\ChatController.cs:line 86
2025-06-19 21:37:18.908 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 21:37:18.915 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 6949.3455ms
2025-06-19 21:37:18.918 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 21:37:18.920 +04:00 [INF] Request POST /api/chat/message completed in 6976ms with status 500 (Correlation ID: c961b1bd-992e-4be8-8818-ab2e2c8422cc)
2025-06-19 21:37:18.926 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null application/json; charset=utf-8 7001.4626ms
2025-06-19 21:37:41.549 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-19 21:37:41.559 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 2714b6a7-db81-4845-a1b5-63093b4bb614
2025-06-19 21:37:41.563 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:37:41.566 +04:00 [INF] Request OPTIONS /api/chat/message completed in 3ms with status 204 (Correlation ID: 2714b6a7-db81-4845-a1b5-63093b4bb614)
2025-06-19 21:37:41.574 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 25.1592ms
2025-06-19 21:37:41.580 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 34
2025-06-19 21:37:41.622 +04:00 [INF] Request POST /api/chat/message started with correlation ID 69c55e54-be0e-4f37-98b6-1a2e899d6537
2025-06-19 21:37:41.630 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:37:41.637 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:37:41.646 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 21:37:41.661 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 21:37:41.669 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 21:37:41.683 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Audit des entreprise
2025-06-19 21:37:41.706 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Audit des entreprise
2025-06-19 21:37:41.744 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-19 21:37:41.748 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Audit des entreprise
2025-06-19 21:37:47.136 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 879, Cost: 0, Time: 4581ms
2025-06-19 21:37:47.141 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "d73ccc1f-c24e-4b19-9e4f-a3917e2f3459", Tokens: 879, Cost: 0
2025-06-19 21:37:47.146 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "d73ccc1f-c24e-4b19-9e4f-a3917e2f3459", Tokens: 879, Cost: 0
2025-06-19 21:37:47.154 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-19 21:37:47.256 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 5590.4812ms
2025-06-19 21:37:47.262 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-19 21:37:47.266 +04:00 [INF] Request POST /api/chat/message completed in 5635ms with status 200 (Correlation ID: 69c55e54-be0e-4f37-98b6-1a2e899d6537)
2025-06-19 21:37:47.273 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 5692.9343ms
2025-06-19 21:38:12.843 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - null null
2025-06-19 21:38:12.852 +04:00 [INF] Request OPTIONS /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 started with correlation ID b319411c-aabc-43c4-9363-08d3a695350e
2025-06-19 21:38:12.858 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:38:12.860 +04:00 [INF] Request OPTIONS /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 completed in 2ms with status 204 (Correlation ID: b319411c-aabc-43c4-9363-08d3a695350e)
2025-06-19 21:38:12.864 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - 204 null null 21.0589ms
2025-06-19 21:38:12.866 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - application/json 12
2025-06-19 21:38:12.872 +04:00 [INF] Request POST /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 started with correlation ID d29265ff-53ca-4f90-b657-7fe0f1934de3
2025-06-19 21:38:12.874 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:38:12.875 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:38:12.877 +04:00 [INF] Request POST /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 completed in 3ms with status 404 (Correlation ID: d29265ff-53ca-4f90-b657-7fe0f1934de3)
2025-06-19 21:38:12.880 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - 404 0 null 13.9427ms
2025-06-19 21:38:12.885 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3, Response status code: 404
2025-06-19 21:38:37.824 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - null null
2025-06-19 21:38:37.832 +04:00 [INF] Request OPTIONS /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 started with correlation ID 7443d185-c414-47a1-b5c5-77d345c4b199
2025-06-19 21:38:37.837 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:38:37.841 +04:00 [INF] Request OPTIONS /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 completed in 3ms with status 204 (Correlation ID: 7443d185-c414-47a1-b5c5-77d345c4b199)
2025-06-19 21:38:37.846 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - 204 null null 22.1159ms
2025-06-19 21:38:37.849 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - application/json 12
2025-06-19 21:38:37.864 +04:00 [INF] Request POST /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 started with correlation ID 8cf3c988-73fe-4ac2-bb38-34d43e833125
2025-06-19 21:38:37.874 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:38:37.882 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:38:37.890 +04:00 [INF] Request POST /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 completed in 17ms with status 404 (Correlation ID: 8cf3c988-73fe-4ac2-bb38-34d43e833125)
2025-06-19 21:38:37.909 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - 404 0 null 60.6047ms
2025-06-19 21:38:37.931 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3, Response status code: 404
2025-06-19 21:44:34.306 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - null null
2025-06-19 21:44:34.312 +04:00 [INF] Request OPTIONS /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 started with correlation ID f658e986-5186-4049-8423-28d164aebebc
2025-06-19 21:44:34.316 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:44:34.318 +04:00 [INF] Request OPTIONS /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 completed in 2ms with status 204 (Correlation ID: f658e986-5186-4049-8423-28d164aebebc)
2025-06-19 21:44:34.322 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - 204 null null 15.1932ms
2025-06-19 21:44:34.324 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - application/json 13
2025-06-19 21:44:34.342 +04:00 [INF] Request POST /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 started with correlation ID 2cb78781-4f6d-4cc0-98a6-4e4e5c132eb8
2025-06-19 21:44:34.346 +04:00 [INF] CORS policy execution successful.
2025-06-19 21:44:34.350 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 21:44:34.356 +04:00 [INF] Request POST /api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 completed in 9ms with status 404 (Correlation ID: 2cb78781-4f6d-4cc0-98a6-4e4e5c132eb8)
2025-06-19 21:44:34.365 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3 - 404 0 null 40.3297ms
2025-06-19 21:44:34.372 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5003/api/chat/rate/108201e2-b46c-4e26-88bd-dc4b2a9272e3, Response status code: 404
2025-06-19 22:13:12.582 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-19 22:13:12.636 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 22:13:12.732 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-19 22:13:12.733 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-19 22:13:12.735 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 22:13:12.735 +04:00 [INF] Hosting environment: Development
2025-06-19 22:13:12.736 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-19 22:14:32.672 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:14:32.672 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-19 22:14:32.733 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID a5ef4f15-62b5-416c-8a99-b79c28e72736
2025-06-19 22:14:32.733 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 1fbaabf6-a060-4234-8f1e-1f488fb6e521
2025-06-19 22:14:32.744 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:14:32.744 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:14:32.746 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 3ms with status 204 (Correlation ID: 1fbaabf6-a060-4234-8f1e-1f488fb6e521)
2025-06-19 22:14:32.746 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 6ms with status 204 (Correlation ID: a5ef4f15-62b5-416c-8a99-b79c28e72736)
2025-06-19 22:14:32.755 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 88.363ms
2025-06-19 22:14:32.755 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 88.3599ms
2025-06-19 22:14:32.764 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:14:32.785 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID aed3ba98-c45a-4fb9-bbc5-9a9654456e7d
2025-06-19 22:14:32.790 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:14:32.861 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:14:32.866 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:14:32.884 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:14:32.892 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:14:34.665 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 22:14:34.679 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-19 22:14:36.301 +04:00 [INF] Executed DbCommand (119ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:14:36.687 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:14:36.734 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:14:36.834 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-19 22:14:36.838 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 3944.1998ms
2025-06-19 22:14:36.843 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 2993c0d5-999d-4944-ae58-7709266a4141
2025-06-19 22:14:36.845 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:14:36.847 +04:00 [INF] CORS policy execution successful.
2025-06-19 22:14:36.856 +04:00 [INF] Request GET /api/chat/conversations completed in 4067ms with status 200 (Correlation ID: aed3ba98-c45a-4fb9-bbc5-9a9654456e7d)
2025-06-19 22:14:36.863 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 22:14:36.873 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:14:36.878 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-19 22:14:36.886 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 4121.9406ms
2025-06-19 22:14:36.887 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 22:14:36.961 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:14:36.975 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-19 22:14:36.981 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-19 22:14:36.987 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 101.392ms
2025-06-19 22:14:36.993 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-19 22:14:36.996 +04:00 [INF] Request GET /api/chat/conversations completed in 148ms with status 200 (Correlation ID: 2993c0d5-999d-4944-ae58-7709266a4141)
2025-06-19 22:14:37.002 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 168.2424ms
2025-06-19 22:17:26.486 +04:00 [INF] Application is shutting down...
