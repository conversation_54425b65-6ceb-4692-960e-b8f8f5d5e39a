<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.Shared.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings">
            <summary>
            Configuration settings for PostgreSQL database connection
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings.ConnectionString">
            <summary>
            Database connection string
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings.MaxRetryAttempts">
            <summary>
            Maximum number of retry attempts for database operations
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings.CommandTimeout">
            <summary>
            Command timeout in seconds
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings.EnableSensitiveDataLogging">
            <summary>
            Enable sensitive data logging (for development only)
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.PostgreSqlSettings.EnableDetailedErrors">
            <summary>
            Enable detailed errors (for development only)
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings">
            <summary>
            Configuration settings for MongoDB connection
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.ConnectionString">
            <summary>
            MongoDB connection string
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.DatabaseName">
            <summary>
            Database name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.MaxConnectionPoolSize">
            <summary>
            Maximum connection pool size
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.ConnectTimeoutMs">
            <summary>
            Connection timeout in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.ServerSelectionTimeoutMs">
            <summary>
            Server selection timeout in milliseconds
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.MongoDbSettings.SocketTimeoutMs">
            <summary>
            Socket timeout in milliseconds
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.RedisSettings">
            <summary>
            Configuration settings for Redis cache
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.RedisSettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RedisSettings.ConnectionString">
            <summary>
            Redis connection string
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RedisSettings.Database">
            <summary>
            Default database number
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RedisSettings.DefaultExpirationMinutes">
            <summary>
            Default expiration time in minutes
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RedisSettings.KeyPrefix">
            <summary>
            Key prefix for cache entries
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RedisSettings.EnableCompression">
            <summary>
            Enable compression for cached values
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings">
            <summary>
            Configuration settings for RabbitMQ message broker
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.HostName">
            <summary>
            RabbitMQ host name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.Port">
            <summary>
            RabbitMQ port
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.UserName">
            <summary>
            Username for authentication
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.Password">
            <summary>
            Password for authentication
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.VirtualHost">
            <summary>
            Virtual host
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.LegalResearchExchange">
            <summary>
            Exchange name for legal research events
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.DocumentExchange">
            <summary>
            Exchange name for document events
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.NotificationExchange">
            <summary>
            Exchange name for notification events
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.RabbitMqSettings.AuditExchange">
            <summary>
            Exchange name for audit events
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.JwtSettings">
            <summary>
            Configuration settings for JWT authentication
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.JwtSettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.SecretKey">
            <summary>
            Secret key for signing JWT tokens
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.Issuer">
            <summary>
            Token issuer
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.Audience">
            <summary>
            Token audience
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.AccessTokenExpirationMinutes">
            <summary>
            Access token expiration time in minutes
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.RefreshTokenExpirationDays">
            <summary>
            Refresh token expiration time in days
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.ClockSkewMinutes">
            <summary>
            Clock skew tolerance in minutes
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.ValidateIssuer">
            <summary>
            Validate token issuer
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.ValidateAudience">
            <summary>
            Validate token audience
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.ValidateLifetime">
            <summary>
            Validate token lifetime
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.ValidateIssuerSigningKey">
            <summary>
            Validate issuer signing key
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.JwtSettings.RequireHttpsMetadata">
            <summary>
            Require HTTPS for token validation
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.OpenAISettings">
            <summary>
            Configuration settings for OpenAI integration
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.ApiKey">
            <summary>
            OpenAI API key
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.OrganizationId">
            <summary>
            OpenAI organization ID (optional)
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.DefaultModel">
            <summary>
            Default model for text completion
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.EmbeddingModel">
            <summary>
            Default model for embeddings
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.MaxTokens">
            <summary>
            Maximum tokens for completion requests
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.Temperature">
            <summary>
            Temperature for text generation (0.0 to 2.0)
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.TimeoutSeconds">
            <summary>
            Request timeout in seconds
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.MaxRetryAttempts">
            <summary>
            Maximum retry attempts for failed requests
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.OpenAISettings.BaseUrl">
            <summary>
            Base URL for OpenAI API (for Azure OpenAI or custom endpoints)
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings">
            <summary>
            Configuration settings for file storage
            </summary>
        </member>
        <member name="F:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.SectionName">
            <summary>
            Configuration section name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.Provider">
            <summary>
            Storage provider type (Local, AzureBlob, S3)
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.LocalPath">
            <summary>
            Base path for local file storage
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.AzureBlobConnectionString">
            <summary>
            Azure Blob Storage connection string
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.AzureBlobContainerName">
            <summary>
            Azure Blob Storage container name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.S3AccessKey">
            <summary>
            AWS S3 access key
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.S3SecretKey">
            <summary>
            AWS S3 secret key
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.S3BucketName">
            <summary>
            AWS S3 bucket name
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.S3Region">
            <summary>
            AWS S3 region
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.MaxFileSizeBytes">
            <summary>
            Maximum file size in bytes
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.AllowedExtensions">
            <summary>
            Allowed file extensions
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.EnableVirusScanning">
            <summary>
            Enable virus scanning
            </summary>
        </member>
        <member name="P:LexAI.Shared.Infrastructure.Configuration.FileStorageSettings.EnableEncryption">
            <summary>
            Enable file encryption at rest
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Services.UnifiedEmbeddingService">
            <summary>
            Service d'embedding unifié supportant OpenAI et modèles locaux
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedEmbeddingService.GenerateEmbeddingAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Génère un embedding pour un texte
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedEmbeddingService.GenerateBatchEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},System.Threading.CancellationToken)">
            <summary>
            Génère des embeddings pour plusieurs textes
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedEmbeddingService.IsAvailableAsync(System.Threading.CancellationToken)">
            <summary>
            Vérifie si le service d'embedding est disponible
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedEmbeddingService.GetServiceInfo">
            <summary>
            Obtient les informations sur le service d'embedding
            </summary>
        </member>
        <member name="T:LexAI.Shared.Infrastructure.Services.UnifiedLLMService">
            <summary>
            Service LLM unifié pour tous les modules LexAI
            Supporte OpenAI, Ollama, et modèles locaux avec fallback automatique
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(System.String,LexAI.Shared.Application.DTOs.LLM.LLMOptions,System.Threading.CancellationToken)">
            <summary>
            Envoie un prompt au LLM et retourne la réponse
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendConversationAsync(System.Collections.Generic.IEnumerable{LexAI.Shared.Application.DTOs.LLM.LLMMessage},LexAI.Shared.Application.DTOs.LLM.LLMOptions,System.Threading.CancellationToken)">
            <summary>
            Envoie une conversation au LLM
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync``1(System.String,System.String,LexAI.Shared.Application.DTOs.LLM.LLMOptions,System.Threading.CancellationToken)">
            <summary>
            Analyse un texte et extrait des informations structurées
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedLLMService.IsAvailableAsync(System.Threading.CancellationToken)">
            <summary>
            Vérifie si le service LLM est disponible
            </summary>
        </member>
        <member name="M:LexAI.Shared.Infrastructure.Services.UnifiedLLMService.GetModelInfo">
            <summary>
            Obtient les informations sur le modèle actuel
            </summary>
        </member>
    </members>
</doc>
