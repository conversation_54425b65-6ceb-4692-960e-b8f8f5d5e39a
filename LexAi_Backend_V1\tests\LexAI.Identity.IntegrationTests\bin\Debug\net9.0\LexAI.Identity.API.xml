<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.Identity.API</name>
    </assembly>
    <members>
        <member name="T:LexAI.Identity.API.Controllers.AuthController">
            <summary>
            Controller for authentication operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.#ctor(MediatR.IMediator,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.API.Controllers.AuthController})">
            <summary>
            Initializes a new instance of the AuthController
            </summary>
            <param name="mediator">MediatR mediator</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.Login(LexAI.Identity.Application.DTOs.LoginDto)">
            <summary>
            Authenticates a user and returns access and refresh tokens
            </summary>
            <param name="loginDto">Login credentials</param>
            <returns>Authentication response with tokens and user information</returns>
            <response code="200">Login successful</response>
            <response code="400">Invalid credentials or validation errors</response>
            <response code="401">Authentication failed</response>
            <response code="423">Account is locked</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.RefreshToken(LexAI.Identity.Application.DTOs.RefreshTokenDto)">
            <summary>
            Refreshes an access token using a refresh token
            </summary>
            <param name="refreshTokenDto">Refresh token request</param>
            <returns>New authentication response with fresh tokens</returns>
            <response code="200">Token refresh successful</response>
            <response code="400">Invalid refresh token</response>
            <response code="401">Refresh token expired or invalid</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.Logout(System.Boolean)">
            <summary>
            Logs out a user by revoking their refresh token(s)
            </summary>
            <param name="revokeAllTokens">Whether to revoke all user tokens or just the current one</param>
            <returns>Logout confirmation</returns>
            <response code="200">Logout successful</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Gets the current authenticated user's information
            </summary>
            <returns>Current user information</returns>
            <response code="200">User information retrieved successfully</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.ChangePassword(LexAI.Identity.Application.DTOs.ChangePasswordDto)">
            <summary>
            Changes the current user's password
            </summary>
            <param name="changePasswordDto">Password change request</param>
            <returns>Password change confirmation</returns>
            <response code="200">Password changed successfully</response>
            <response code="400">Invalid current password or validation errors</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.ForgotPassword(LexAI.Identity.Application.DTOs.ForgotPasswordDto)">
            <summary>
            Initiates a password reset process
            </summary>
            <param name="forgotPasswordDto">Forgot password request</param>
            <returns>Password reset initiation confirmation</returns>
            <response code="200">Password reset email sent (if email exists)</response>
            <response code="400">Invalid email format</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.ResetPassword(LexAI.Identity.Application.DTOs.ResetPasswordDto)">
            <summary>
            Resets a user's password using a reset token
            </summary>
            <param name="resetPasswordDto">Password reset request</param>
            <returns>Password reset confirmation</returns>
            <response code="200">Password reset successfully</response>
            <response code="400">Invalid token or validation errors</response>
        </member>
        <member name="M:LexAI.Identity.API.Controllers.AuthController.Register(LexAI.Identity.Application.DTOs.RegisterUserDto)">
            <summary>
            Registers a new user account (public registration)
            </summary>
            <param name="registerDto">User registration data</param>
            <returns>Created user information</returns>
            <response code="201">User registered successfully</response>
            <response code="400">Validation errors or user already exists</response>
            <response code="409">User with email already exists</response>
        </member>
    </members>
</doc>
