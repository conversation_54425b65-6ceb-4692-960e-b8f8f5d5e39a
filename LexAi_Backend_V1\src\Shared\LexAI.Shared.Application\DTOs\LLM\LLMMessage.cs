﻿
namespace LexAI.Shared.Application.DTOs.LLM
{

    /// <summary>
    /// Message pour les conversations LLM
    /// </summary>
    public class LLMMessage
    {
        public string Role { get; set; } = string.Empty; // "system", "user", "assistant"
        public string Content { get; set; } = string.Empty;
        public DateTime? Timestamp { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }
}
