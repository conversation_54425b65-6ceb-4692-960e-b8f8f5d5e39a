import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { DocumentAnalyzer } from '../../components/document-analysis/DocumentAnalyzer'
import { aiAssistantApi } from '../../services/api'

// Mock de l'API
vi.mock('../../services/api', () => ({
  aiAssistantApi: {
    analyzeDocument: vi.fn()
  }
}))

const mockAnalysisResponse = {
  analysis: "Ce contrat présente plusieurs clauses standard mais contient une clause de résiliation qui pourrait être problématique.",
  keyFindings: [
    "Clause de résiliation unilatérale sans préavis",
    "Pénalités de retard élevées (10% par mois)",
    "Absence de clause de force majeure"
  ],
  riskAssessment: "Risque moyen - Certaines clauses nécessitent une révision",
  recommendations: [
    "Ajouter une clause de préavis de 30 jours minimum",
    "Réduire les pénalités à un taux raisonnable (2-3% par mois)",
    "Inclure une clause de force majeure standard"
  ],
  citations: [
    {
      title: "Code civil - Article 1134",
      source: "Légifrance",
      excerpt: "Les conventions légalement formées tiennent lieu de loi à ceux qui les ont faites.",
      url: "https://www.legifrance.gouv.fr"
    }
  ],
  processingTimeMs: 2500,
  tokensUsed: 1250,
  estimatedCost: 0.025,
  documentName: "contrat-test.txt"
}

describe('DocumentAnalyzer', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render the document analyzer form', () => {
    render(<DocumentAnalyzer />)
    
    expect(screen.getByText('Analyse de Document Juridique')).toBeInTheDocument()
    expect(screen.getByText('Document à analyser')).toBeInTheDocument()
    expect(screen.getByText('Ou saisir le contenu directement')).toBeInTheDocument()
    expect(screen.getByText('Type de document (optionnel)')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /analyser le document/i })).toBeInTheDocument()
  })

  it('should show error when trying to analyze without content', async () => {
    render(<DocumentAnalyzer />)
    
    const analyzeButton = screen.getByRole('button', { name: /analyser le document/i })
    fireEvent.click(analyzeButton)
    
    await waitFor(() => {
      expect(screen.getByText('Veuillez fournir un document ou du contenu à analyser')).toBeInTheDocument()
    })
  })

  it('should analyze document content successfully', async () => {
    const mockApi = vi.mocked(aiAssistantApi.analyzeDocument)
    mockApi.mockResolvedValue(mockAnalysisResponse)

    render(<DocumentAnalyzer />)
    
    // Saisir du contenu
    const contentTextarea = screen.getByPlaceholderText('Collez ici le contenu du document à analyser...')
    fireEvent.change(contentTextarea, { 
      target: { value: 'Contrat de prestation de services entre A et B...' } 
    })

    // Sélectionner le type de document
    const typeSelect = screen.getByDisplayValue('Sélectionner un type')
    fireEvent.change(typeSelect, { target: { value: 'contract' } })

    // Lancer l'analyse
    const analyzeButton = screen.getByRole('button', { name: /analyser le document/i })
    fireEvent.click(analyzeButton)

    // Vérifier l'état de chargement
    await waitFor(() => {
      expect(screen.getByText('Analyse en cours...')).toBeInTheDocument()
    })

    // Attendre les résultats
    await waitFor(() => {
      expect(screen.getByText('Analyse du document')).toBeInTheDocument()
      expect(screen.getByText(mockAnalysisResponse.analysis)).toBeInTheDocument()
    })

    // Vérifier que l'API a été appelée avec les bons paramètres
    expect(mockApi).toHaveBeenCalledWith({
      documentName: 'Document texte',
      documentContent: 'Contrat de prestation de services entre A et B...',
      documentType: 'contract',
      focusAreas: undefined,
      context: undefined
    })
  })

  it('should display analysis results correctly', async () => {
    const mockApi = vi.mocked(aiAssistantApi.analyzeDocument)
    mockApi.mockResolvedValue(mockAnalysisResponse)

    render(<DocumentAnalyzer />)
    
    // Saisir du contenu et analyser
    const contentTextarea = screen.getByPlaceholderText('Collez ici le contenu du document à analyser...')
    fireEvent.change(contentTextarea, { 
      target: { value: 'Test content' } 
    })

    const analyzeButton = screen.getByRole('button', { name: /analyser le document/i })
    fireEvent.click(analyzeButton)

    // Attendre les résultats
    await waitFor(() => {
      // Vérifier l'analyse principale
      expect(screen.getByText('Analyse du document')).toBeInTheDocument()
      expect(screen.getByText(mockAnalysisResponse.analysis)).toBeInTheDocument()

      // Vérifier les points clés
      expect(screen.getByText('Points clés identifiés')).toBeInTheDocument()
      expect(screen.getByText('Clause de résiliation unilatérale sans préavis')).toBeInTheDocument()

      // Vérifier l'évaluation des risques
      expect(screen.getByText('Évaluation des risques')).toBeInTheDocument()
      expect(screen.getByText(mockAnalysisResponse.riskAssessment)).toBeInTheDocument()

      // Vérifier les recommandations
      expect(screen.getByText('Recommandations')).toBeInTheDocument()
      expect(screen.getByText('Ajouter une clause de préavis de 30 jours minimum')).toBeInTheDocument()

      // Vérifier les citations
      expect(screen.getByText('Sources et références')).toBeInTheDocument()
      expect(screen.getByText('Code civil - Article 1134')).toBeInTheDocument()

      // Vérifier les métadonnées
      expect(screen.getByText('Informations sur l\'analyse')).toBeInTheDocument()
      expect(screen.getByText('2500ms')).toBeInTheDocument()
      expect(screen.getByText('1250')).toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    const mockApi = vi.mocked(aiAssistantApi.analyzeDocument)
    mockApi.mockRejectedValue(new Error('Erreur de connexion à l\'API'))

    render(<DocumentAnalyzer />)
    
    // Saisir du contenu
    const contentTextarea = screen.getByPlaceholderText('Collez ici le contenu du document à analyser...')
    fireEvent.change(contentTextarea, { 
      target: { value: 'Test content' } 
    })

    // Lancer l'analyse
    const analyzeButton = screen.getByRole('button', { name: /analyser le document/i })
    fireEvent.click(analyzeButton)

    // Vérifier l'affichage de l'erreur
    await waitFor(() => {
      expect(screen.getByText('Erreur de connexion à l\'API')).toBeInTheDocument()
    })
  })

  it('should allow adding and removing focus areas', () => {
    render(<DocumentAnalyzer />)
    
    // Trouver le champ de saisie des domaines d'analyse
    const focusAreaInput = screen.getByPlaceholderText('Ajouter un domaine d\'analyse...')
    
    // Ajouter un domaine d'analyse
    fireEvent.change(focusAreaInput, { target: { value: 'Clauses de résiliation' } })
    fireEvent.keyDown(focusAreaInput, { key: 'Enter' })
    
    // Vérifier que le domaine a été ajouté
    expect(screen.getByText('Clauses de résiliation')).toBeInTheDocument()
    
    // Supprimer le domaine
    const removeButton = screen.getByRole('button', { name: '' }) // Bouton X
    fireEvent.click(removeButton)
    
    // Vérifier que le domaine a été supprimé
    expect(screen.queryByText('Clauses de résiliation')).not.toBeInTheDocument()
  })

  it('should call onAnalysisComplete callback when provided', async () => {
    const mockOnAnalysisComplete = vi.fn()
    const mockApi = vi.mocked(aiAssistantApi.analyzeDocument)
    mockApi.mockResolvedValue(mockAnalysisResponse)

    render(<DocumentAnalyzer onAnalysisComplete={mockOnAnalysisComplete} />)
    
    // Saisir du contenu et analyser
    const contentTextarea = screen.getByPlaceholderText('Collez ici le contenu du document à analyser...')
    fireEvent.change(contentTextarea, { 
      target: { value: 'Test content' } 
    })

    const analyzeButton = screen.getByRole('button', { name: /analyser le document/i })
    fireEvent.click(analyzeButton)

    // Vérifier que le callback a été appelé
    await waitFor(() => {
      expect(mockOnAnalysisComplete).toHaveBeenCalledWith(mockAnalysisResponse)
    })
  })

  it('should handle file upload', () => {
    render(<DocumentAnalyzer />)
    
    // Créer un fichier de test
    const file = new File(['contenu du contrat'], 'contrat.txt', { type: 'text/plain' })
    
    // Simuler l'upload
    const fileInput = screen.getByRole('textbox', { hidden: true }) // input[type="file"]
    fireEvent.change(fileInput, { target: { files: [file] } })
    
    // Vérifier que le nom du fichier est affiché
    expect(screen.getByText('contrat.txt')).toBeInTheDocument()
  })
})
