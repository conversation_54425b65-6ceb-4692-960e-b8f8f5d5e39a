using LexAI.DocumentAnalysis.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service de stockage local des documents
/// </summary>
public class DocumentStorageService : IDocumentStorageService
{
    private readonly ILogger<DocumentStorageService> _logger;
    private readonly string _storageBasePath;

    public DocumentStorageService(
        IConfiguration configuration,
        ILogger<DocumentStorageService> logger)
    {
        _logger = logger;
        _storageBasePath = configuration["DocumentStorage:BasePath"] ?? 
                          Path.Combine(Directory.GetCurrentDirectory(), "storage", "documents");
        
        // Créer le dossier de stockage s'il n'existe pas
        Directory.CreateDirectory(_storageBasePath);
        
        _logger.LogInformation("Document storage initialized at: {StoragePath}", _storageBasePath);
    }

    /// <summary>
    /// Stocke un document et retourne son hash et chemin
    /// </summary>
    public async Task<(string Hash, string StoragePath)> StoreDocumentAsync(
        byte[] documentContent, 
        string originalFileName, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Générer un hash unique pour le document
            var hash = GenerateDocumentHash(documentContent, originalFileName);
            
            // Créer le chemin de stockage avec l'extension originale
            var extension = Path.GetExtension(originalFileName);
            var fileName = $"{hash}{extension}";
            var storagePath = Path.Combine(_storageBasePath, fileName);
            
            // Vérifier si le fichier existe déjà
            if (File.Exists(storagePath))
            {
                _logger.LogDebug("Document already exists: {Hash}", hash);
                return (hash, storagePath);
            }
            
            // Sauvegarder le fichier
            await File.WriteAllBytesAsync(storagePath, documentContent, cancellationToken);
            
            _logger.LogInformation("Document stored successfully: {Hash} -> {Path}", hash, storagePath);
            
            return (hash, storagePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing document: {FileName}", originalFileName);
            throw;
        }
    }

    /// <summary>
    /// Récupère un document par son hash
    /// </summary>
    public async Task<byte[]?> GetDocumentAsync(
        string documentHash, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Chercher le fichier avec ce hash (peu importe l'extension)
            var files = Directory.GetFiles(_storageBasePath, $"{documentHash}.*");
            
            if (files.Length == 0)
            {
                _logger.LogWarning("Document not found: {Hash}", documentHash);
                return null;
            }
            
            var filePath = files[0]; // Prendre le premier fichier trouvé
            
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Document file does not exist: {Path}", filePath);
                return null;
            }
            
            var content = await File.ReadAllBytesAsync(filePath, cancellationToken);
            
            _logger.LogDebug("Document retrieved successfully: {Hash}", documentHash);
            
            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving document: {Hash}", documentHash);
            return null;
        }
    }

    /// <summary>
    /// Supprime un document par son hash
    /// </summary>
    public async Task<bool> DeleteDocumentAsync(
        string documentHash, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Chercher le fichier avec ce hash
            var files = Directory.GetFiles(_storageBasePath, $"{documentHash}.*");
            
            if (files.Length == 0)
            {
                _logger.LogWarning("Document not found for deletion: {Hash}", documentHash);
                return false;
            }
            
            foreach (var file in files)
            {
                File.Delete(file);
                _logger.LogInformation("Document deleted: {Path}", file);
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting document: {Hash}", documentHash);
            return false;
        }
    }

    /// <summary>
    /// Vérifie si un document existe
    /// </summary>
    public Task<bool> DocumentExistsAsync(
        string documentHash, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var files = Directory.GetFiles(_storageBasePath, $"{documentHash}.*");
            return Task.FromResult(files.Length > 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking document existence: {Hash}", documentHash);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// Obtient les informations d'un document stocké
    /// </summary>
    public Task<DocumentStorageInfo?> GetDocumentInfoAsync(
        string documentHash, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var files = Directory.GetFiles(_storageBasePath, $"{documentHash}.*");
            
            if (files.Length == 0)
            {
                return Task.FromResult<DocumentStorageInfo?>(null);
            }
            
            var filePath = files[0];
            var fileInfo = new FileInfo(filePath);
            
            var info = new DocumentStorageInfo
            {
                Hash = documentHash,
                FilePath = filePath,
                FileName = fileInfo.Name,
                FileSize = fileInfo.Length,
                CreatedAt = fileInfo.CreationTimeUtc,
                LastModified = fileInfo.LastWriteTimeUtc
            };
            
            return Task.FromResult<DocumentStorageInfo?>(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting document info: {Hash}", documentHash);
            return Task.FromResult<DocumentStorageInfo?>(null);
        }
    }

    /// <summary>
    /// Génère un hash unique pour un document
    /// </summary>
    private string GenerateDocumentHash(byte[] content, string fileName)
    {
        using var sha256 = SHA256.Create();
        
        // Combiner le contenu du fichier et le nom pour créer un hash unique
        var combinedData = content.Concat(Encoding.UTF8.GetBytes(fileName)).ToArray();
        var hashBytes = sha256.ComputeHash(combinedData);
        
        // Convertir en string hexadécimal
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }
}
