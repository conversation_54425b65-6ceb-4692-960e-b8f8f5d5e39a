using AutoFixture;
using AutoFixture.AutoMoq;
using FluentAssertions;
using LexAI.LegalResearch.Application.Commands;
using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.Entities;
using LexAI.LegalResearch.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace LexAI.LegalResearch.UnitTests.Application.Commands;

/// <summary>
/// Unit tests for PerformSearchCommandHandler
/// </summary>
public class PerformSearchCommandHandlerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<ILegalSearchService> _mockSearchService;
    private readonly Mock<ISearchQueryRepository> _mockQueryRepository;
    private readonly Mock<ILogger<PerformSearchCommandHandler>> _mockLogger;
    private readonly PerformSearchCommandHandler _handler;

    public PerformSearchCommandHandlerTests()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        _mockSearchService = _fixture.Freeze<Mock<ILegalSearchService>>();
        _mockQueryRepository = _fixture.Freeze<Mock<ISearchQueryRepository>>();
        _mockLogger = _fixture.Freeze<Mock<ILogger<PerformSearchCommandHandler>>>();
        
        _handler = new PerformSearchCommandHandler(
            _mockSearchService.Object,
            _mockQueryRepository.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldReturnSearchResponse()
    {
        // Arrange
        var request = CreateValidSearchRequest();
        var command = new PerformSearchCommand { Request = request };
        var expectedResponse = CreateValidSearchResponse();

        _mockSearchService
            .Setup(x => x.SearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _mockQueryRepository
            .Setup(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockQueryRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Query.Should().Be(request.Query);
        result.Results.Should().HaveCount(expectedResponse.Results.Count);
        result.ExecutionTimeMs.Should().BeGreaterThan(0);

        // Verify repository interactions
        _mockQueryRepository.Verify(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockQueryRepository.Verify(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithHybridSearchMethod_ShouldCallHybridSearch()
    {
        // Arrange
        var request = CreateValidSearchRequest();
        request.Method = SearchMethod.Hybrid;
        var command = new PerformSearchCommand { Request = request };
        var expectedResponse = CreateValidSearchResponse();

        _mockSearchService
            .Setup(x => x.HybridSearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _mockQueryRepository
            .Setup(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockQueryRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockSearchService.Verify(x => x.HybridSearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockSearchService.Verify(x => x.SearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithSearchServiceException_ShouldMarkQueryAsFailedAndRethrow()
    {
        // Arrange
        var request = CreateValidSearchRequest();
        var command = new PerformSearchCommand { Request = request };
        var expectedException = new InvalidOperationException("Search service error");

        _mockSearchService
            .Setup(x => x.SearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        _mockQueryRepository
            .Setup(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockQueryRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act & Assert
        var action = async () => await _handler.Handle(command, CancellationToken.None);
        await action.Should().ThrowAsync<InvalidOperationException>().WithMessage("Search service error");

        // Verify that query was marked as failed
        _mockQueryRepository.Verify(x => x.UpdateAsync(
            It.Is<SearchQuery>(q => q.Status == SearchStatus.Failed && q.ErrorMessage == "Search service error"),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithFilters_ShouldSetFiltersOnQuery()
    {
        // Arrange
        var request = CreateValidSearchRequest();
        request.DomainFilter = LegalDomain.Labor;
        request.TypeFilter = DocumentType.Law;
        request.LanguageFilter = "fr";
        request.DateFilter = new DateRangeDto
        {
            StartDate = new DateTime(2020, 1, 1),
            EndDate = new DateTime(2024, 12, 31)
        };

        var command = new PerformSearchCommand { Request = request };
        var expectedResponse = CreateValidSearchResponse();

        _mockSearchService
            .Setup(x => x.SearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _mockQueryRepository
            .Setup(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockQueryRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Verify that filters were set on the query
        _mockQueryRepository.Verify(x => x.AddAsync(
            It.Is<SearchQuery>(q => 
                q.DomainFilter == LegalDomain.Labor &&
                q.TypeFilter == DocumentType.Law &&
                q.LanguageFilter == "fr" &&
                q.DateFilter != null),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogSearchInformation()
    {
        // Arrange
        var request = CreateValidSearchRequest();
        var command = new PerformSearchCommand { Request = request };
        var expectedResponse = CreateValidSearchResponse();

        _mockSearchService
            .Setup(x => x.SearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _mockQueryRepository
            .Setup(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockQueryRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Performing legal search for query")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Search completed successfully")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithSearchParameters_ShouldSetParametersOnQuery()
    {
        // Arrange
        var request = CreateValidSearchRequest();
        request.Method = SearchMethod.Semantic;
        request.Limit = 50;
        request.MinRelevanceScore = 0.8;
        request.IncludeHighlights = true;

        var command = new PerformSearchCommand { Request = request };
        var expectedResponse = CreateValidSearchResponse();

        _mockSearchService
            .Setup(x => x.SearchAsync(It.IsAny<SearchRequestDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        _mockQueryRepository
            .Setup(x => x.AddAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockQueryRepository
            .Setup(x => x.UpdateAsync(It.IsAny<SearchQuery>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Verify that parameters were set correctly
        _mockQueryRepository.Verify(x => x.AddAsync(
            It.Is<SearchQuery>(q => 
                q.Parameters.Method == SearchMethod.Semantic &&
                q.Parameters.Limit == 50 &&
                q.Parameters.MinRelevanceScore == 0.8 &&
                q.Parameters.IncludeHighlights == true),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    private SearchRequestDto CreateValidSearchRequest()
    {
        return new SearchRequestDto
        {
            Query = "contrat de travail CDI",
            UserId = Guid.NewGuid(),
            SessionId = "test-session-123",
            Method = SearchMethod.Semantic,
            Limit = 20,
            MinRelevanceScore = 0.7,
            IncludeHighlights = true
        };
    }

    private SearchResponseDto CreateValidSearchResponse()
    {
        return new SearchResponseDto
        {
            QueryId = Guid.NewGuid(),
            Query = "contrat de travail CDI",
            ProcessedQuery = "contrat travail cdi",
            Results = new List<SearchResultDto>
            {
                new SearchResultDto
                {
                    DocumentId = Guid.NewGuid(),
                    Title = "Code du travail - Article L1221-1",
                    Summary = "Le contrat de travail à durée indéterminée...",
                    RelevanceScore = 0.95,
                    SimilarityScore = 0.92,
                    DocumentType = DocumentType.Law,
                    LegalDomain = LegalDomain.Labor,
                    Source = new DocumentSourceDto
                    {
                        Name = "Légifrance",
                        Url = "https://legifrance.gouv.fr/test",
                        Type = SourceType.Official,
                        Authority = AuthorityLevel.National,
                        Jurisdiction = "France",
                        ReliabilityScore = 1.0
                    },
                    Rank = 1
                }
            },
            TotalResults = 1,
            ExecutionTimeMs = 250,
            Method = SearchMethod.Semantic,
            Intent = QueryIntent.Information,
            QualityScore = 0.89,
            IsCached = false
        };
    }
}
