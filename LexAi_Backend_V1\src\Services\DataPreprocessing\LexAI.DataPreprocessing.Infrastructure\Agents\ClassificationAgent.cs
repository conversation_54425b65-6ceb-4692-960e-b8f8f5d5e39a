using LexAI.DataPreprocessing.Application.DTOs;
using LexAI.DataPreprocessing.Application.Interfaces;
using LexAI.DataPreprocessing.Domain.Entities;
using LexAI.DataPreprocessing.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace LexAI.DataPreprocessing.Infrastructure.Agents;

/// <summary>
/// Document classification agent implementation
/// </summary>
public class ClassificationAgent : IClassificationAgent
{
    private readonly IClassificationService _classificationService;
    private readonly ILogger<ClassificationAgent> _logger;

    /// <summary>
    /// Agent name
    /// </summary>
    public string AgentName => "ClassificationAgent";

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType AgentType => AgentType.Classification;

    /// <summary>
    /// Initializes a new instance of the ClassificationAgent
    /// </summary>
    /// <param name="classificationService">Classification service</param>
    /// <param name="logger">Logger</param>
    public ClassificationAgent(
        IClassificationService classificationService,
        ILogger<ClassificationAgent> logger)
    {
        _classificationService = classificationService;
        _logger = logger;
    }

    /// <summary>
    /// Classifies a document into legal domains
    /// </summary>
    /// <param name="document">Document to classify</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Classification result</returns>
    public async Task<ClassificationResultDto> ClassifyDocumentAsync(Document document, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting classification for document {DocumentId}: {FileName}",
            document.Id, document.FileName);

        var stopwatch = Stopwatch.StartNew();
        var result = new ClassificationResultDto
        {
            AgentName = AgentName,
            Success = false
        };

        try
        {
            // Validate document has extracted text
            if (string.IsNullOrWhiteSpace(document.ExtractedText))
            {
                var error = "Document has no extracted text for classification";
                result.Errors.Add(error);
                _logger.LogWarning("Cannot classify document {DocumentId}: {Error}", document.Id, error);
                return result;
            }

            // Perform classification
            var classificationResult = await _classificationService.ClassifyTextAsync(document.ExtractedText, cancellationToken);

            if (!classificationResult.Success)
            {
                var error = classificationResult.ErrorMessage ?? "Classification failed";
                result.Errors.Add(error);
                _logger.LogWarning("Classification failed for document {DocumentId}: {Error}", document.Id, error);
                return result;
            }

            // Set classification results
            result.DetectedDomain = classificationResult.DetectedDomain;
            result.Confidence = classificationResult.Confidence;
            result.DomainScores = classificationResult.DomainScores;
            result.Success = true;

            // Extract keywords in parallel
            var keywordsTask = ExtractKeywordsAsync(document.ExtractedText, cancellationToken);
            var entitiesTask = ExtractNamedEntitiesAsync(document.ExtractedText, cancellationToken);

            await Task.WhenAll(keywordsTask, entitiesTask);

            result.Keywords = (await keywordsTask).ToList();
            result.NamedEntities = (await entitiesTask).Select(e => MapToNamedEntityDto(e)).ToList();

            stopwatch.Stop();
            result.ClassificationTime = stopwatch.Elapsed;

            _logger.LogInformation("Classification completed for document {DocumentId}. " +
                "Domain: {Domain}, Confidence: {Confidence:F2}, Keywords: {KeywordCount}, Entities: {EntityCount}, Time: {Time}ms",
                document.Id, result.DetectedDomain, result.Confidence, result.Keywords.Count, result.NamedEntities.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.ClassificationTime = stopwatch.Elapsed;
            result.Errors.Add($"Classification failed: {ex.Message}");

            _logger.LogError(ex, "Error classifying document {DocumentId}", document.Id);
            return result;
        }
    }

    /// <summary>
    /// Gets classification confidence for a document
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Domain confidence scores</returns>
    public async Task<Dictionary<LegalDomain, double>> GetDomainConfidenceAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return new Dictionary<LegalDomain, double>();

            var classificationResult = await _classificationService.ClassifyTextAsync(text, cancellationToken);

            return classificationResult.Success
                ? classificationResult.DomainScores
                : new Dictionary<LegalDomain, double>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting domain confidence for text");
            return new Dictionary<LegalDomain, double>();
        }
    }

    /// <summary>
    /// Extracts keywords from document text
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extracted keywords</returns>
    public async Task<IEnumerable<string>> ExtractKeywordsAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return Enumerable.Empty<string>();

            var keywords = await _classificationService.ExtractKeywordsAsync(text, 20, cancellationToken);

            _logger.LogDebug("Extracted {KeywordCount} keywords from text", keywords.Count());

            return keywords;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting keywords from text");
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// Extracts named entities from document text
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Named entities</returns>
    public async Task<IEnumerable<Domain.ValueObjects.NamedEntity>> ExtractNamedEntitiesAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(text))
                return Enumerable.Empty<Domain.ValueObjects.NamedEntity>();

            var entities = await _classificationService.ExtractNamedEntitiesAsync(text, cancellationToken);

            _logger.LogDebug("Extracted {EntityCount} named entities from text", entities.Count());

            return entities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting named entities from text");
            return Enumerable.Empty<Domain.ValueObjects.NamedEntity>();
        }
    }

    private static NamedEntityDto MapToNamedEntityDto(Domain.ValueObjects.NamedEntity entity)
    {
        return new NamedEntityDto
        {
            Text = entity.Text,
            Type = entity.Type,
            StartPosition = entity.StartPosition,
            EndPosition = entity.EndPosition,
            Confidence = entity.Confidence,
            Metadata = new Dictionary<string, object>(entity.Metadata)
        };
    }
}

/// <summary>
/// Chunking agent implementation
/// </summary>
public class ChunkingAgent : IChunkingAgent
{
    private readonly IChunkingService _chunkingService;
    private readonly ILogger<ChunkingAgent> _logger;

    /// <summary>
    /// Agent name
    /// </summary>
    public string AgentName => "ChunkingAgent";

    /// <summary>
    /// Agent type
    /// </summary>
    public AgentType AgentType => AgentType.Chunking;

    /// <summary>
    /// Supported chunking strategies
    /// </summary>
    public IEnumerable<ChunkingStrategy> SupportedStrategies => _chunkingService.SupportedStrategies;

    /// <summary>
    /// Initializes a new instance of the ChunkingAgent
    /// </summary>
    /// <param name="chunkingService">Chunking service</param>
    /// <param name="logger">Logger</param>
    public ChunkingAgent(
        IChunkingService chunkingService,
        ILogger<ChunkingAgent> logger)
    {
        _chunkingService = chunkingService;
        _logger = logger;
    }

    /// <summary>
    /// Chunks a document into smaller pieces
    /// </summary>
    /// <param name="document">Document to chunk</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chunking result</returns>
    public async Task<ChunkingResultDto> ChunkDocumentAsync(
        Document document,
        ChunkingConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting chunking for document {DocumentId} with strategy {Strategy}",
            document.Id, configuration.Strategy);

        var stopwatch = Stopwatch.StartNew();
        var result = new ChunkingResultDto
        {
            AgentName = AgentName,
            Strategy = configuration.Strategy,
            Success = false
        };

        try
        {
            // Validate document has extracted text
            if (string.IsNullOrWhiteSpace(document.ExtractedText))
            {
                var error = "Document has no extracted text for chunking";
                result.Errors.Add(error);
                _logger.LogWarning("Cannot chunk document {DocumentId}: {Error}", document.Id, error);
                return result;
            }

            // Validate configuration
            var validation = ValidateConfiguration(configuration);
            if (!validation.IsValid)
            {
                result.Errors.AddRange(validation.Errors);
                result.Warnings.AddRange(validation.Warnings);
                _logger.LogWarning("Invalid chunking configuration for document {DocumentId}: {Errors}",
                    document.Id, string.Join(", ", validation.Errors));
                return result;
            }

            // Perform chunking
            var textChunks = await _chunkingService.ChunkTextAsync(document.ExtractedText, configuration, cancellationToken);
            var chunkList = textChunks.ToList();

            if (!chunkList.Any())
            {
                var error = "No chunks were created from the document";
                result.Errors.Add(error);
                _logger.LogWarning("No chunks created for document {DocumentId}", document.Id);
                return result;
            }

            // Create document chunks
            var documentChunks = new List<DocumentChunkDto>();
            var sequenceNumber = 0;

            foreach (var textChunk in chunkList)
            {
                var chunk = DocumentChunk.Create(
                    document.Id,
                    sequenceNumber++,
                    textChunk.Text,
                    textChunk.Type,
                    textChunk.StartPosition,
                    textChunk.EndPosition,
                    AgentName);

                // Add chunk metadata
                var updatedMetadata = chunk.Metadata;
                foreach (var metadata in textChunk.Metadata)
                {
                    updatedMetadata = updatedMetadata.WithCustomMetadata(metadata.Key, metadata.Value);
                }
                // Note: We need to update the chunk with the new metadata
                // This will be handled in the infrastructure layer when saving

                documentChunks.Add(MapToDocumentChunkDto(chunk));
            }

            // Set result properties
            result.Chunks = documentChunks;
            result.TotalChunks = documentChunks.Count;
            result.AverageChunkSize = documentChunks.Any()
                ? (int)documentChunks.Average(c => c.CharacterCount)
                : 0;
            result.Success = true;

            stopwatch.Stop();
            result.ChunkingTime = stopwatch.Elapsed;

            _logger.LogInformation("Chunking completed for document {DocumentId}. " +
                "Chunks: {ChunkCount}, Average size: {AverageSize}, Time: {Time}ms",
                document.Id, result.TotalChunks, result.AverageChunkSize, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.ChunkingTime = stopwatch.Elapsed;
            result.Errors.Add($"Chunking failed: {ex.Message}");

            _logger.LogError(ex, "Error chunking document {DocumentId}", document.Id);
            return result;
        }
    }

    /// <summary>
    /// Estimates the number of chunks for a document
    /// </summary>
    /// <param name="text">Document text</param>
    /// <param name="configuration">Chunking configuration</param>
    /// <returns>Estimated chunk count</returns>
    public int EstimateChunkCount(string text, ChunkingConfiguration configuration)
    {
        try
        {
            return _chunkingService.EstimateChunkCount(text, configuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating chunk count");
            return 0;
        }
    }

    /// <summary>
    /// Validates chunking configuration
    /// </summary>
    /// <param name="configuration">Configuration to validate</param>
    /// <returns>Validation result</returns>
    public ValidationResult ValidateConfiguration(ChunkingConfiguration configuration)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // Check if strategy is supported
            if (!SupportedStrategies.Contains(configuration.Strategy))
            {
                result.IsValid = false;
                result.Errors.Add($"Chunking strategy {configuration.Strategy} is not supported");
            }

            // Validate chunk sizes
            if (configuration.MaxChunkSize <= 0)
            {
                result.IsValid = false;
                result.Errors.Add("Maximum chunk size must be positive");
            }

            if (configuration.MinChunkSize <= 0)
            {
                result.IsValid = false;
                result.Errors.Add("Minimum chunk size must be positive");
            }

            if (configuration.MinChunkSize >= configuration.MaxChunkSize)
            {
                result.IsValid = false;
                result.Errors.Add("Minimum chunk size must be less than maximum chunk size");
            }

            if (configuration.OverlapSize < 0)
            {
                result.IsValid = false;
                result.Errors.Add("Overlap size cannot be negative");
            }

            if (configuration.OverlapSize >= configuration.MaxChunkSize)
            {
                result.IsValid = false;
                result.Errors.Add("Overlap size must be less than maximum chunk size");
            }

            // Add warnings for potentially problematic configurations
            if (configuration.MaxChunkSize > 4000)
            {
                result.Warnings.Add("Large chunk size may impact embedding quality");
            }

            if (configuration.OverlapSize > configuration.MaxChunkSize * 0.5)
            {
                result.Warnings.Add("Large overlap size may create redundant chunks");
            }

            if (configuration.MinChunkSize < 50)
            {
                result.Warnings.Add("Very small minimum chunk size may create low-quality chunks");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating chunking configuration");
            result.IsValid = false;
            result.Errors.Add($"Configuration validation failed: {ex.Message}");
        }

        return result;
    }

    private static DocumentChunkDto MapToDocumentChunkDto(DocumentChunk chunk)
    {
        return new DocumentChunkDto
        {
            Id = chunk.Id,
            DocumentId = chunk.DocumentId,
            SequenceNumber = chunk.SequenceNumber,
            Content = chunk.Content,
            Type = chunk.Type,
            StartPosition = chunk.StartPosition,
            EndPosition = chunk.EndPosition,
            TokenCount = chunk.TokenCount,
            CharacterCount = chunk.CharacterCount,
            QualityScore = chunk.QualityScore,
            ImportanceScore = chunk.ImportanceScore,
            Keywords = new List<string>(chunk.Keywords),
            NamedEntities = chunk.NamedEntities.Select(e => new NamedEntityDto
            {
                Text = e.Text,
                Type = e.Type,
                StartPosition = e.StartPosition,
                EndPosition = e.EndPosition,
                Confidence = e.Confidence,
                Metadata = new Dictionary<string, object>(e.Metadata)
            }).ToList(),
            DomainRelevance = new Dictionary<LegalDomain, double>(chunk.DomainRelevance),
            IsVectorized = chunk.IsVectorized,
            VectorId = chunk.VectorId
        };
    }
}
