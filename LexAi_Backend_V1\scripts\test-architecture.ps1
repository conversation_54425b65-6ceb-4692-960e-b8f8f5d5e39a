﻿# LexAI Architecture Test Script
# This script validates that the LexAI microservices architecture is working correctly

param(
    [switch]$Verbose,
    [switch]$Infrastructure,
    [switch]$Services,
    [switch]$All
)

$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Blue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-Infrastructure {
    Write-ColorOutput "🧪 Testing Infrastructure Services..." $Blue
    
    $tests = @()
    
    # Test PostgreSQL
    try {
        $pgResult = docker exec lexai-postgres pg_isready -U lexai_user -d lexai_db
        if ($pgResult -like "*accepting connections*") {
            $tests += @{ Name = "PostgreSQL"; Status = "✅ PASS"; Details = "Database accepting connections" }
        } else {
            $tests += @{ Name = "PostgreSQL"; Status = "❌ FAIL"; Details = $pgResult }
        }
    }
    catch {
        $tests += @{ Name = "PostgreSQL"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test MongoDB
    try {
        $mongoResult = docker exec lexai-mongodb mongosh --eval "db.adminCommand('ping')" --quiet
        if ($mongoResult -like "*ok*") {
            $tests += @{ Name = "MongoDB"; Status = "✅ PASS"; Details = "Database responding to ping" }
        } else {
            $tests += @{ Name = "MongoDB"; Status = "❌ FAIL"; Details = $mongoResult }
        }
    }
    catch {
        $tests += @{ Name = "MongoDB"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test Redis
    try {
        $redisResult = docker exec lexai-redis redis-cli ping
        if ($redisResult -eq "PONG") {
            $tests += @{ Name = "Redis"; Status = "✅ PASS"; Details = "Cache responding to ping" }
        } else {
            $tests += @{ Name = "Redis"; Status = "❌ FAIL"; Details = $redisResult }
        }
    }
    catch {
        $tests += @{ Name = "Redis"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test RabbitMQ
    try {
        $rabbitResult = docker exec lexai-rabbitmq rabbitmq-diagnostics ping
        if ($rabbitResult -like "*Ping succeeded*") {
            $tests += @{ Name = "RabbitMQ"; Status = "✅ PASS"; Details = "Message broker responding" }
        } else {
            $tests += @{ Name = "RabbitMQ"; Status = "❌ FAIL"; Details = $rabbitResult }
        }
    }
    catch {
        $tests += @{ Name = "RabbitMQ"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    return $tests
}

function Test-ApiGateway {
    Write-ColorOutput "🧪 Testing API Gateway..." $Blue
    
    $tests = @()
    $baseUrl = "http://localhost:8080"
    
    # Test ping endpoint
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/gateway/ping" -Method Get -TimeoutSec 10
        if ($response.message -eq "Pong") {
            $tests += @{ Name = "Gateway Ping"; Status = "✅ PASS"; Details = "Ping endpoint responding correctly" }
        } else {
            $tests += @{ Name = "Gateway Ping"; Status = "❌ FAIL"; Details = "Unexpected response: $($response.message)" }
        }
    }
    catch {
        $tests += @{ Name = "Gateway Ping"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test info endpoint
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/gateway/info" -Method Get -TimeoutSec 10
        if ($response.name -eq "LexAI API Gateway") {
            $tests += @{ Name = "Gateway Info"; Status = "✅ PASS"; Details = "Info endpoint responding with correct data" }
        } else {
            $tests += @{ Name = "Gateway Info"; Status = "❌ FAIL"; Details = "Unexpected response format" }
        }
    }
    catch {
        $tests += @{ Name = "Gateway Info"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test health endpoint
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 10
        $tests += @{ Name = "Health Check"; Status = "✅ PASS"; Details = "Health endpoint accessible" }
    }
    catch {
        $tests += @{ Name = "Health Check"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test Swagger endpoint
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/swagger/index.html" -Method Get -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            $tests += @{ Name = "Swagger UI"; Status = "✅ PASS"; Details = "Swagger documentation accessible" }
        } else {
            $tests += @{ Name = "Swagger UI"; Status = "❌ FAIL"; Details = "Status code: $($response.StatusCode)" }
        }
    }
    catch {
        $tests += @{ Name = "Swagger UI"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    # Test metrics endpoint
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/gateway/metrics" -Method Get -TimeoutSec 10
        if ($response.uptime) {
            $tests += @{ Name = "Gateway Metrics"; Status = "✅ PASS"; Details = "Metrics endpoint providing data" }
        } else {
            $tests += @{ Name = "Gateway Metrics"; Status = "❌ FAIL"; Details = "No metrics data returned" }
        }
    }
    catch {
        $tests += @{ Name = "Gateway Metrics"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    return $tests
}

function Test-DatabaseConnections {
    Write-ColorOutput "🧪 Testing Database Connections..." $Blue
    
    $tests = @()
    
    # Test PostgreSQL databases
    $databases = @("identity_db", "legal_research_db", "client_management_db", "document_analysis_db", "document_generator_db", "ai_assistant_db")
    
    foreach ($db in $databases) {
        try {
            $result = docker exec lexai-postgres psql -U lexai_user -d $db -c "SELECT 1;" -t
            if ($result -like "*1*") {
                $tests += @{ Name = "PostgreSQL $db"; Status = "✅ PASS"; Details = "Database accessible and responding" }
            } else {
                $tests += @{ Name = "PostgreSQL $db"; Status = "❌ FAIL"; Details = "Database not responding correctly" }
            }
        }
        catch {
            $tests += @{ Name = "PostgreSQL $db"; Status = "❌ FAIL"; Details = $_.Exception.Message }
        }
    }
    
    # Test MongoDB collections
    try {
        $result = docker exec lexai-mongodb mongosh lexai_documents --eval "db.legal_documents.countDocuments()" --quiet
        if ($result -match "\d+") {
            $tests += @{ Name = "MongoDB Collections"; Status = "✅ PASS"; Details = "Collections accessible" }
        } else {
            $tests += @{ Name = "MongoDB Collections"; Status = "❌ FAIL"; Details = "Collections not accessible" }
        }
    }
    catch {
        $tests += @{ Name = "MongoDB Collections"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    return $tests
}

function Test-RabbitMQQueues {
    Write-ColorOutput "🧪 Testing RabbitMQ Queues..." $Blue
    
    $tests = @()
    
    try {
        # Test queue creation and basic functionality
        $result = docker exec lexai-rabbitmq rabbitmqctl list_queues -p lexai_vhost
        if ($result -like "*legal.research.queries*") {
            $tests += @{ Name = "RabbitMQ Queues"; Status = "✅ PASS"; Details = "Queues created and accessible" }
        } else {
            $tests += @{ Name = "RabbitMQ Queues"; Status = "❌ FAIL"; Details = "Expected queues not found" }
        }
    }
    catch {
        $tests += @{ Name = "RabbitMQ Queues"; Status = "❌ FAIL"; Details = $_.Exception.Message }
    }
    
    return $tests
}

function Test-DockerServices {
    Write-ColorOutput "🧪 Testing Docker Services..." $Blue
    
    $tests = @()
    
    # Get service status
    $services = docker-compose ps --format "table {{.Name}}\t{{.State}}"
    
    $expectedServices = @("lexai-postgres", "lexai-mongodb", "lexai-redis", "lexai-rabbitmq", "lexai-api-gateway")
    
    foreach ($service in $expectedServices) {
        if ($services -like "*$service*Up*") {
            $tests += @{ Name = "Docker $service"; Status = "✅ PASS"; Details = "Service running" }
        } else {
            $tests += @{ Name = "Docker $service"; Status = "❌ FAIL"; Details = "Service not running or not found" }
        }
    }
    
    return $tests
}

function Show-TestResults {
    param([array]$TestResults)
    
    $passCount = ($TestResults | Where-Object { $_.Status -like "*PASS*" }).Count
    $failCount = ($TestResults | Where-Object { $_.Status -like "*FAIL*" }).Count
    $totalCount = $TestResults.Count
    
    Write-Host ""
    Write-ColorOutput "📊 Test Results Summary" $Blue
    Write-ColorOutput "======================" $Blue
    
    foreach ($test in $TestResults) {
        if ($Verbose) {
            Write-Host "$($test.Status) $($test.Name) - $($test.Details)"
        } else {
            Write-Host "$($test.Status) $($test.Name)"
        }
    }
    
    Write-Host ""
    Write-ColorOutput "Total Tests: $totalCount" $Blue
    Write-ColorOutput "Passed: $passCount" $Green
    if ($failCount -gt 0) {
        Write-ColorOutput "Failed: $failCount" $Red
    } else {
        Write-ColorOutput "Failed: $failCount" $Green
    }
    
    $successRate = [math]::Round(($passCount / $totalCount) * 100, 2)
    Write-ColorOutput "Success Rate: $successRate%" $(if ($successRate -eq 100) { $Green } elseif ($successRate -ge 80) { $Yellow } else { $Red })
    
    return $failCount -eq 0
}

# Main script logic
if (-not $Infrastructure -and -not $Services -and -not $All) {
    $All = $true
}

Write-ColorOutput "🧪 LexAI Architecture Test Suite" $Blue
Write-ColorOutput "=================================" $Blue
Write-Host ""

$allTests = @()

if ($Infrastructure -or $All) {
    $allTests += Test-Infrastructure
    $allTests += Test-DatabaseConnections
    $allTests += Test-RabbitMQQueues
    $allTests += Test-DockerServices
}

if ($Services -or $All) {
    $allTests += Test-ApiGateway
}

$success = Show-TestResults -TestResults $allTests

if ($success) {
    Write-Host ""
    Write-ColorOutput "🎉 All tests passed! Your LexAI architecture is working correctly." $Green
    exit 0
} else {
    Write-Host ""
    Write-ColorOutput "❌ Some tests failed. Please check the logs and fix the issues." $Red
    Write-ColorOutput "💡 Use 'docker-compose logs -f' to see detailed logs." $Yellow
    exit 1
}
