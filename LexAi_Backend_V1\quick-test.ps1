# Script simple pour tester la compilation des services
Write-Host "Test de compilation des services LexAI..." -ForegroundColor Green

$services = @(
    "src/Services/DocumentAnalysis/LexAI.DocumentAnalysis.API",
    "src/Services/AIAssistant/LexAI.AIAssistant.API", 
    "src/Services/LegalResearch/LexAI.LegalResearch.API"
)

$results = @()

foreach ($service in $services) {
    $serviceName = Split-Path $service -Leaf
    Write-Host "Testing: $serviceName" -ForegroundColor Blue
    
    try {
        $output = dotnet build $service --verbosity quiet 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "OK: $serviceName" -ForegroundColor Green
            $results += @{ Service = $serviceName; Status = "OK" }
        } else {
            Write-Host "FAILED: $serviceName" -ForegroundColor Red
            $results += @{ Service = $serviceName; Status = "FAILED" }
        }
    } catch {
        Write-Host "ERROR: $serviceName - $($_.Exception.Message)" -ForegroundColor Red
        $results += @{ Service = $serviceName; Status = "ERROR" }
    }
}

Write-Host "`nRESULTATS:" -ForegroundColor Cyan
foreach ($result in $results) {
    $color = if ($result.Status -eq "OK") { "Green" } else { "Red" }
    Write-Host "  $($result.Service): $($result.Status)" -ForegroundColor $color
}

$successCount = ($results | Where-Object { $_.Status -eq "OK" }).Count
Write-Host "`nServices OK: $successCount/$($results.Count)" -ForegroundColor White
