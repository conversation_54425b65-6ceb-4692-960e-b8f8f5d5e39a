# LexAI .gitignore

# =============================================================================
# Environment and Configuration Files
# =============================================================================
.env
.env.local
.env.production
.env.staging
appsettings.Development.json
appsettings.Production.json
appsettings.Staging.json
secrets.json

# =============================================================================
# .NET Core / .NET 9
# =============================================================================
bin/
obj/
out/
*.user
*.suo
*.cache
*.docstates
*.userprefs
*.pidb
*.booproj
*.svd
*.pdb
*.opendb
*.VC.db

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017/2019/2022 cache/options directory
.vs/
.vscode/
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono auto generated files
mono_crash.*

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017 cache/options directory
.vs/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# Benchmark Results
BenchmarkDotNet.Artifacts/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# =============================================================================
# Entity Framework
# =============================================================================
*.edmx.diagram
*.edmx.sql
migrations/

# =============================================================================
# Docker
# =============================================================================
.dockerignore
docker-compose.override.yml
docker-compose.production.yml
docker-compose.staging.yml

# =============================================================================
# Database Files
# =============================================================================
*.mdf
*.ldf
*.ndf
*.db
*.sqlite
*.sqlite3

# =============================================================================
# Logs and Temporary Files
# =============================================================================
logs/
*.log
*.log.*
temp/
tmp/
*.tmp
*.temp

# =============================================================================
# File Uploads and Storage
# =============================================================================
uploads/
files/
documents/
storage/
wwwroot/uploads/

# =============================================================================
# Package Managers
# =============================================================================
# NuGet
*.nupkg
*.snupkg
.nuget/
packages/
!packages/build/
!packages/repositories.config

# NPM (if using for frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================================================================
# IDE and Editor Files
# =============================================================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains Rider
.idea/
*.sln.iml

# Sublime Text
*.sublime-workspace
*.sublime-project

# =============================================================================
# Operating System Files
# =============================================================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Security and Sensitive Files
# =============================================================================
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
secrets/
private/
confidential/

# =============================================================================
# Test Coverage and Reports
# =============================================================================
coverage/
*.coverage
*.coveragexml
TestResults/
test-results/
coverage.json
coverage.xml
*.trx

# =============================================================================
# Backup Files
# =============================================================================
*.bak
*.backup
*.old
*.orig
*.swp
*.swo
*~

# =============================================================================
# Application Specific
# =============================================================================
# AI Model files (if storing locally)
models/
*.model
*.pkl
*.joblib

# Generated documentation
docs/api/
docs/generated/

# Temporary AI processing files
ai-temp/
processing/

# Legal document cache
legal-cache/
document-cache/
