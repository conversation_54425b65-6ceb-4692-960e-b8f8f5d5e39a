using LexAI.AIAssistant.Application.DTOs;
using LexAI.AIAssistant.Application.Interfaces;
using LexAI.AIAssistant.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using LexAI.Shared.Application.DTOs.LLM;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.Interfaces;
using LexAI.Shared.Application.Utils;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace LexAI.AIAssistant.Infrastructure.Services;

/// <summary>
/// Service d'assistant IA amélioré utilisant le service LLM unifié
/// </summary>
public class EnhancedAIAssistantService : IAIAssistantService
{
    private readonly IUnifiedLLMService _llmService;
    private readonly ILegalResearchIntegrationService _legalResearchService;
    private readonly ILogger<EnhancedAIAssistantService> _logger;
    private readonly IConfiguration _configuration;

    public EnhancedAIAssistantService(
        IUnifiedLLMService llmService,
        ILegalResearchIntegrationService legalResearchService,
        IConfiguration configuration,
        ILogger<EnhancedAIAssistantService> logger)
    {
        _llmService = llmService;
        _legalResearchService = legalResearchService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Envoie un message à l'assistant IA et obtient une réponse
    /// </summary>
    public async Task<ChatResponseDto> SendMessageAsync(ChatRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing enhanced chat request for user {UserId}: {Message}",
            request.UserId, request.Message);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // Analyser l'intention et le domaine du message
            var messageAnalysis = await AnalyzeMessageWithLLMAsync(request.Message, cancellationToken);

            // Effectuer une recherche juridique si nécessaire
            var relatedDocuments = new List<LegalDocumentSummaryDto>();
            var citations = new List<CitationDto>();

            if (request.IncludeLegalResearch && messageAnalysis.RequiresLegalResearch)
            {
                _logger.LogDebug("Performing legal research for message: {Message}", request.Message);

                relatedDocuments = (await _legalResearchService.SearchLegalDocumentsAsync(
                    request.Message,
                    messageAnalysis.Domain,
                    5,
                    cancellationToken)).ToList();

                // Convertir les documents en citations
                citations = relatedDocuments.Select(doc => new CitationDto
                {
                    Id = Guid.NewGuid(),
                    Type = CitationType.LegalDocument,
                    Title = doc.Title,
                    Url = doc.Url ?? string.Empty,
                    Source = doc.Source,
                    PublicationDate = doc.PublicationDate,
                    RelevanceScore = doc.RelevanceScore,
                    Excerpt = doc.Summary
                }).ToList();
            }

            // Construire le contexte pour l'IA
            var context = request.Context ?? new ConversationContextDto();
            var messages = BuildConversationMessages(context, messageAnalysis, relatedDocuments, request.Message, request);

            // Options LLM basées sur le contexte
            var llmOptions = CreateLLMOptions(context, messageAnalysis);

            // Envoyer la conversation au LLM
            var llmResponse = await _llmService.SendConversationAsync(messages, llmOptions, cancellationToken);

            if (!llmResponse.Success)
            {
                throw new InvalidOperationException($"LLM service error: {llmResponse.Error}");
            }

            stopwatch.Stop();

            // Générer des questions de suivi si un ID de conversation est fourni
            var followUpQuestions = new List<string>();
            if (request.ConversationId.HasValue)
            {
                followUpQuestions = (await GenerateFollowUpQuestionsAsync(
                    request.ConversationId.Value, llmResponse.Content, cancellationToken)).ToList();
            }

            // Construire la réponse
            var chatResponse = new ChatResponseDto
            {
                ConversationId = request.ConversationId ?? Guid.NewGuid(),
                Response = llmResponse.Content,
                MessageId = Guid.NewGuid(),
                ResponseType = DetermineResponseType(messageAnalysis.Intent),
                DetectedIntent = messageAnalysis.Intent,
                DetectedDomain = messageAnalysis.Domain,
                ConfidenceScore = messageAnalysis.ConfidenceScore,
                Citations = citations,
                RelatedDocuments = relatedDocuments,
                FollowUpQuestions = followUpQuestions,
                ProcessingTimeMs = llmResponse.ProcessingTimeMs,
                TokensUsed = llmResponse.TokensUsed,
                EstimatedCost = llmResponse.EstimatedCost,
                Quality = AssessResponseQuality(llmResponse.Content, messageAnalysis),
                IsCached = false
            };

            _logger.LogInformation("Enhanced chat request processed successfully. Model: {Model}, Tokens: {TokensUsed}, Cost: {Cost}, Time: {Time}ms",
                llmResponse.ModelUsed, llmResponse.TokensUsed, llmResponse.EstimatedCost, llmResponse.ProcessingTimeMs);

            return chatResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing enhanced chat request");
            throw;
        }
    }

    /// <summary>
    /// Continue une conversation existante
    /// </summary>
    public async Task<ChatResponseDto> ContinueConversationAsync(Guid conversationId, string message, CancellationToken cancellationToken = default)
    {
        var request = new ChatRequestDto
        {
            Message = message,
            ConversationId = conversationId,
            UserId = Guid.Empty, // Sera défini par l'appelant
            SessionId = conversationId.ToString(),
            IncludeLegalResearch = true
        };

        return await SendMessageAsync(request, cancellationToken);
    }

    /// <summary>
    /// Analyse un document juridique
    /// </summary>
    public async Task<DocumentAnalysisResponseDto> AnalyzeDocumentAsync(DocumentAnalysisRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Analyzing document with enhanced LLM: {DocumentName}", request.DocumentName);

        var analysisPrompt = BuildDocumentAnalysisPrompt(request);
        var llmOptions = LLMServiceExtensions.ForLegalAnalysis(8000);

        var llmResponse = await _llmService.SendPromptAsync(analysisPrompt, llmOptions, cancellationToken);

        if (!llmResponse.Success)
        {
            throw new InvalidOperationException($"Document analysis failed: {llmResponse.Error}");
        }

        // Analyser la réponse structurée
        var structuredAnalysis = await _llmService.AnalyzeStructuredAsync<DocumentAnalysisStructure>(
            llmResponse.Content,
            "Extrayez les éléments clés de cette analyse de document en JSON avec les champs: keyFindings, riskAssessment, recommendations",
            llmOptions,
            cancellationToken);

        return new DocumentAnalysisResponseDto
        {
            DocumentName = request.DocumentName,
            Analysis = llmResponse.Content,
            KeyFindings = structuredAnalysis?.KeyFindings ?? ExtractKeyFindings(llmResponse.Content),
            RiskAssessment = structuredAnalysis?.RiskAssessment ?? ExtractRiskAssessment(llmResponse.Content),
            Recommendations = structuredAnalysis?.Recommendations ?? ExtractRecommendations(llmResponse.Content),
            Citations = new List<CitationDto>(), // À implémenter avec la recherche juridique
            ProcessingTimeMs = llmResponse.ProcessingTimeMs,
            TokensUsed = llmResponse.TokensUsed,
            EstimatedCost = llmResponse.EstimatedCost
        };
    }

    /// <summary>
    /// Effectue une recherche juridique et fournit des insights
    /// </summary>
    public async Task<LegalResearchResponseDto> PerformLegalResearchAsync(LegalResearchRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Performing enhanced legal research: {Query}", request.Query);

        // Rechercher des documents pertinents
        var documents = await _legalResearchService.SearchLegalDocumentsAsync(
            request.Query, request.Domain, 10, cancellationToken);

        var researchPrompt = BuildLegalResearchPrompt(request, documents);
        var llmOptions = LLMServiceExtensions.ForLegalResearch(6000);

        var llmResponse = await _llmService.SendPromptAsync(researchPrompt, llmOptions, cancellationToken);

        if (!llmResponse.Success)
        {
            throw new InvalidOperationException($"Legal research failed: {llmResponse.Error}");
        }

        return new LegalResearchResponseDto
        {
            Query = request.Query,
            Research = llmResponse.Content,
            KeyInsights = ExtractKeyInsights(llmResponse.Content),
            RelevantDocuments = documents.ToList(),
            Citations = documents.Select(doc => new CitationDto
            {
                Id = Guid.NewGuid(),
                Type = CitationType.LegalDocument,
                Title = doc.Title,
                Source = doc.Source,
                Url = doc.Url,
                RelevanceScore = doc.RelevanceScore,
                Excerpt = doc.Summary
            }).ToList(),
            ProcessingTimeMs = llmResponse.ProcessingTimeMs,
            TokensUsed = llmResponse.TokensUsed,
            EstimatedCost = llmResponse.EstimatedCost
        };
    }

    /// <summary>
    /// Génère un document juridique basé sur les exigences de l'utilisateur
    /// </summary>
    public async Task<DocumentGenerationResponseDto> GenerateDocumentAsync(DocumentGenerationRequestDto request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating document with enhanced LLM: {DocumentType}", request.DocumentType);

        var generationPrompt = BuildDocumentGenerationPrompt(request);
        var llmOptions = LLMServiceExtensions.ForDocumentGeneration(8000);

        var llmResponse = await _llmService.SendPromptAsync(generationPrompt, llmOptions, cancellationToken);

        if (!llmResponse.Success)
        {
            throw new InvalidOperationException($"Document generation failed: {llmResponse.Error}");
        }

        return new DocumentGenerationResponseDto
        {
            DocumentType = request.DocumentType,
            GeneratedContent = llmResponse.Content,
            Metadata = new Dictionary<string, object>
            {
                ["GeneratedAt"] = DateTime.UtcNow,
                ["ModelUsed"] = llmResponse.ModelUsed,
                ["TokensUsed"] = llmResponse.TokensUsed,
                ["EstimatedCost"] = llmResponse.EstimatedCost
            },
            ProcessingTimeMs = llmResponse.ProcessingTimeMs,
            TokensUsed = llmResponse.TokensUsed,
            EstimatedCost = llmResponse.EstimatedCost
        };
    }

    /// <summary>
    /// Résume une conversation
    /// </summary>
    public async Task<ConversationSummaryDto> SummarizeConversationAsync(Guid conversationId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Summarizing conversation with enhanced LLM: {ConversationId}", conversationId);

        // Pour l'instant, retourner un placeholder
        // Dans une implémentation complète, récupérer l'historique de la conversation et la résumer
        return new ConversationSummaryDto
        {
            ConversationId = conversationId,
            Summary = "Résumé de conversation sera implémenté avec l'historique des conversations",
            KeyTopics = new List<string> { "Consultation juridique", "Révision de document" },
            MainConclusions = new List<string> { "Recherche supplémentaire nécessaire" },
            GeneratedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Analyse l'intention du message avec le LLM
    /// </summary>
    private async Task<MessageAnalysisDto> AnalyzeMessageWithLLMAsync(string message, CancellationToken cancellationToken)
    {
        var analysisPrompt = $@"Analysez ce message juridique et extrayez les informations suivantes en JSON:
{{
    ""intent"": ""Information|Research|Advice|DocumentAnalysis"",
    ""domain"": ""Commercial|Labor|Family|Criminal|Administrative|null"",
    ""requiresLegalResearch"": true/false,
    ""confidenceScore"": 0.0-1.0,
    ""entities"": [""entité1"", ""entité2""],
    ""complexity"": 0.0-1.0
}}

Message: {message}";

        var llmOptions = LLMServiceExtensions.ForClassification(1000);
        var analysis = await _llmService.AnalyzeStructuredAsync<MessageAnalysisStructure>(
            message, analysisPrompt, llmOptions, cancellationToken);

        if (analysis == null)
        {
            // Fallback vers l'analyse simple
            return new MessageAnalysisDto
            {
                Message = message,
                Intent = DetectIntent(message),
                Domain = DetectDomain(message),
                Entities = ExtractEntitiesAsDto(message),
                ConfidenceScore = 0.5,
                SuggestedResponseType = MessageType.Answer
            };
        }

        return new MessageAnalysisDto
        {
            Message = message,
            Intent = Enum.Parse<MessageIntent>(analysis.Intent, true),
            Domain = analysis.Domain != null ? Enum.Parse<LegalDomain>(analysis.Domain, true) : null,
            Entities = analysis.Entities?.Select(e => new ExtractedEntityDto
            {
                Text = e,
                Type = "ENTITY",
                Confidence = 0.8
            }).ToList() ?? new List<ExtractedEntityDto>(),
            ConfidenceScore = analysis.ConfidenceScore,
            SuggestedResponseType = MessageType.Answer
        };
    }

    // Classes pour l'analyse structurée
    private class MessageAnalysisStructure
    {
        public string Intent { get; set; } = string.Empty;
        public string? Domain { get; set; }
        public bool RequiresLegalResearch { get; set; }
        public double ConfidenceScore { get; set; }
        public List<string>? Entities { get; set; }
        public double Complexity { get; set; }
    }

    private class DocumentAnalysisStructure
    {
        public List<string> KeyFindings { get; set; } = new();
        public string RiskAssessment { get; set; } = string.Empty;
        public List<string> Recommendations { get; set; } = new();
    }

    // Méthodes utilitaires (à implémenter)
    private List<LLMMessage> BuildConversationMessages(ConversationContextDto context, MessageAnalysisDto analysis, List<LegalDocumentSummaryDto> documents, string userMessage, ChatRequestDto request)
    {
        var messages = new List<LLMMessage>();

        // Message système
        var systemPrompt = BuildSystemPrompt(context, analysis, documents);
        messages.Add(new LLMMessage { Role = "system", Content = systemPrompt });

        // Ajouter l'historique des messages récents si disponible
        if (request.Metadata?.ContainsKey("RecentMessages") == true &&
            request.Metadata["RecentMessages"] is List<MessageDto> recentMessages)
        {
            foreach (var message in recentMessages)
            {
                var role = message.Role == MessageRole.User ? "user" : "assistant";
                messages.Add(new LLMMessage { Role = role, Content = message.Content });
            }
        }

        // Message utilisateur actuel
        messages.Add(new LLMMessage { Role = "user", Content = userMessage });

        return messages;
    }

    private LLMOptions CreateLLMOptions(ConversationContextDto context, MessageAnalysisDto analysis)
    {
        return new LLMOptions
        {
            MaxTokens = context.MaxTokens,
            Temperature = context.Temperature,
            TopP = 1.0,
            FrequencyPenalty = 0.1,
            PresencePenalty = 0.1
        };
    }

    // Méthodes utilitaires existantes (simplifiées pour l'exemple)
    private static MessageIntent DetectIntent(string message) => MessageIntent.Information;
    private static LegalDomain? DetectDomain(string message) => null;
    private static List<ExtractedEntityDto> ExtractEntitiesAsDto(string message) => new();
    private static MessageType DetermineResponseType(MessageIntent intent) => MessageType.Answer;
    private static ResponseQuality AssessResponseQuality(string response, MessageAnalysisDto analysis) => ResponseQuality.High;
    private static List<string> ExtractKeyFindings(string content) => new();
    private static string ExtractRiskAssessment(string content) => string.Empty;
    private static List<string> ExtractRecommendations(string content) => new();
    private static List<string> ExtractKeyInsights(string content) => new();
    private async Task<IEnumerable<string>> GenerateFollowUpQuestionsAsync(Guid conversationId, string response, CancellationToken cancellationToken) => new List<string>();
    private string BuildSystemPrompt(ConversationContextDto context, MessageAnalysisDto analysis, List<LegalDocumentSummaryDto> documents) => LLMSystemPrompts.LegalAnalysis;
    private string BuildDocumentAnalysisPrompt(DocumentAnalysisRequestDto request) => $"Analysez ce document: {request.DocumentContent}";
    private string BuildLegalResearchPrompt(LegalResearchRequestDto request, IEnumerable<LegalDocumentSummaryDto> documents) => $"Recherche sur: {request.Query}";
    private string BuildDocumentGenerationPrompt(DocumentGenerationRequestDto request) => $"Générez un {request.DocumentType}: {request.Requirements}";
}
