version: '3.8'

services:
  # PostgreSQL pour AIAssistant
  aiassistant-postgres:
    image: postgres:16-alpine
    container_name: lexai-aiassistant-postgres
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres_admin_2024!
    ports:
      - "5436:5432"  # Port différent pour éviter les conflits
    volumes:
      - aiassistant_postgres_data:/var/lib/postgresql/data
      - ./scripts/init-databases.sql:/docker-entrypoint-initdb.d/init-databases.sql
    networks:
      - lexai-aiassistant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
  
volumes:
  aiassistant_postgres_data:
    driver: local

networks:
  lexai-aiassistant-network:
    driver: bridge
