namespace LexAI.DocumentAnalysis.Infrastructure.Configuration;

/// <summary>
/// Configuration pour Azure Document Intelligence
/// </summary>
public class AzureDocumentIntelligenceSettings
{
    public const string SectionName = "Azure:DocumentIntelligence";

    /// <summary>
    /// Endpoint Azure Document Intelligence
    /// </summary>
    public string Endpoint { get; set; } = string.Empty;

    /// <summary>
    /// Clé API Azure Document Intelligence
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;

    /// <summary>
    /// Version de l'API à utiliser
    /// </summary>
    public string ApiVersion { get; set; } = "2023-07-31";

    /// <summary>
    /// Modèle par défaut pour l'extraction de texte
    /// </summary>
    public string DefaultModel { get; set; } = "prebuilt-read";

    /// <summary>
    /// Modèle pour l'analyse de documents
    /// </summary>
    public string DocumentAnalysisModel { get; set; } = "prebuilt-document";

    /// <summary>
    /// Modèle pour l'analyse de contrats
    /// </summary>
    public string ContractAnalysisModel { get; set; } = "prebuilt-contract";

    /// <summary>
    /// Timeout en secondes pour les requêtes
    /// </summary>
    public int TimeoutSeconds { get; set; } = 120;

    /// <summary>
    /// Taille maximale de fichier en MB
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 50;

    /// <summary>
    /// Types de fichiers supportés
    /// </summary>
    public string[] SupportedFileTypes { get; set; } = new[]
    {
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "text/plain",
        "image/jpeg",
        "image/png",
        "image/tiff"
    };

    /// <summary>
    /// Activer le retry automatique
    /// </summary>
    public bool EnableRetry { get; set; } = true;

    /// <summary>
    /// Nombre maximum de tentatives
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Délai entre les tentatives en millisecondes
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Activer la mise en cache des résultats
    /// </summary>
    public bool EnableCaching { get; set; } = true;

    /// <summary>
    /// Durée de cache en minutes
    /// </summary>
    public int CacheDurationMinutes { get; set; } = 60;

    /// <summary>
    /// Activer les logs détaillés
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Valide la configuration
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(Endpoint) && 
               !string.IsNullOrEmpty(ApiKey) &&
               Uri.TryCreate(Endpoint, UriKind.Absolute, out _);
    }

    /// <summary>
    /// Obtient le modèle approprié selon le type de document
    /// </summary>
    public string GetModelForDocumentType(string documentType)
    {
        return documentType.ToLowerInvariant() switch
        {
            "contract" => ContractAnalysisModel,
            "lease" => ContractAnalysisModel,
            "employment" => ContractAnalysisModel,
            "partnership" => ContractAnalysisModel,
            "nda" => ContractAnalysisModel,
            "terms" => DocumentAnalysisModel,
            _ => DefaultModel
        };
    }

    /// <summary>
    /// Vérifie si le type de fichier est supporté
    /// </summary>
    public bool IsFileTypeSupported(string contentType)
    {
        return SupportedFileTypes.Contains(contentType, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Obtient la taille maximale en bytes
    /// </summary>
    public long GetMaxFileSizeBytes()
    {
        return MaxFileSizeMB * 1024L * 1024L;
    }
}
