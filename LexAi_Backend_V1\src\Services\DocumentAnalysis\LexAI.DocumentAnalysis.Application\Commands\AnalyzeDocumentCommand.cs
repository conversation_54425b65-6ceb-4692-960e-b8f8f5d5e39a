using LexAI.DocumentAnalysis.Application.DTOs;
using MediatR;

namespace LexAI.DocumentAnalysis.Application.Commands;

/// <summary>
/// Commande pour analyser un document juridique
/// </summary>
public class AnalyzeDocumentCommand : IRequest<DocumentAnalysisResponseDto>
{
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public byte[] DocumentContent { get; set; } = Array.Empty<byte>();
    public string? DocumentUrl { get; set; }
    public Guid UserId { get; set; }
    public AnalysisOptions Options { get; set; } = new();
}

/// <summary>
/// Handler pour la commande d'analyse de document
/// </summary>
public class AnalyzeDocumentCommandHandler : IRequestHandler<AnalyzeDocumentCommand, DocumentAnalysisResponseDto>
{
    private readonly IDocumentAnalysisService _documentAnalysisService;
    private readonly ILogger<AnalyzeDocumentCommandHandler> _logger;

    public AnalyzeDocumentCommandHandler(
        IDocumentAnalysisService documentAnalysisService,
        ILogger<AnalyzeDocumentCommandHandler> logger)
    {
        _documentAnalysisService = documentAnalysisService;
        _logger = logger;
    }

    public async Task<DocumentAnalysisResponseDto> Handle(
        AnalyzeDocumentCommand request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting document analysis for user {UserId}, document: {DocumentName}", 
            request.UserId, request.DocumentName);

        try
        {
            var analysisRequest = new DocumentAnalysisRequestDto
            {
                DocumentName = request.DocumentName,
                DocumentType = request.DocumentType,
                DocumentContent = request.DocumentContent,
                DocumentUrl = request.DocumentUrl,
                UserId = request.UserId,
                Options = request.Options
            };

            var result = await _documentAnalysisService.AnalyzeDocumentAsync(analysisRequest, cancellationToken);

            _logger.LogInformation("Document analysis completed successfully for document: {DocumentName}, Analysis ID: {AnalysisId}", 
                request.DocumentName, result.Id);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing document {DocumentName} for user {UserId}", 
                request.DocumentName, request.UserId);
            throw;
        }
    }
}

/// <summary>
/// Commande pour régénérer une analyse
/// </summary>
public class RegenerateAnalysisCommand : IRequest<DocumentAnalysisResponseDto>
{
    public Guid AnalysisId { get; set; }
    public Guid UserId { get; set; }
    public AnalysisOptions? NewOptions { get; set; }
}

/// <summary>
/// Handler pour la régénération d'analyse
/// </summary>
public class RegenerateAnalysisCommandHandler : IRequestHandler<RegenerateAnalysisCommand, DocumentAnalysisResponseDto>
{
    private readonly IDocumentAnalysisService _documentAnalysisService;
    private readonly ILogger<RegenerateAnalysisCommandHandler> _logger;

    public RegenerateAnalysisCommandHandler(
        IDocumentAnalysisService documentAnalysisService,
        ILogger<RegenerateAnalysisCommandHandler> logger)
    {
        _documentAnalysisService = documentAnalysisService;
        _logger = logger;
    }

    public async Task<DocumentAnalysisResponseDto> Handle(
        RegenerateAnalysisCommand request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Regenerating analysis {AnalysisId} for user {UserId}", 
            request.AnalysisId, request.UserId);

        try
        {
            var result = await _documentAnalysisService.RegenerateAnalysisAsync(
                request.AnalysisId, 
                request.NewOptions, 
                cancellationToken);

            _logger.LogInformation("Analysis regenerated successfully: {AnalysisId}", request.AnalysisId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating analysis {AnalysisId} for user {UserId}", 
                request.AnalysisId, request.UserId);
            throw;
        }
    }
}

/// <summary>
/// Commande pour supprimer une analyse
/// </summary>
public class DeleteAnalysisCommand : IRequest<bool>
{
    public Guid AnalysisId { get; set; }
    public Guid UserId { get; set; }
}

/// <summary>
/// Handler pour la suppression d'analyse
/// </summary>
public class DeleteAnalysisCommandHandler : IRequestHandler<DeleteAnalysisCommand, bool>
{
    private readonly IDocumentAnalysisService _documentAnalysisService;
    private readonly ILogger<DeleteAnalysisCommandHandler> _logger;

    public DeleteAnalysisCommandHandler(
        IDocumentAnalysisService documentAnalysisService,
        ILogger<DeleteAnalysisCommandHandler> logger)
    {
        _documentAnalysisService = documentAnalysisService;
        _logger = logger;
    }

    public async Task<bool> Handle(
        DeleteAnalysisCommand request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Deleting analysis {AnalysisId} for user {UserId}", 
            request.AnalysisId, request.UserId);

        try
        {
            var result = await _documentAnalysisService.DeleteAnalysisAsync(
                request.AnalysisId, 
                request.UserId, 
                cancellationToken);

            if (result)
            {
                _logger.LogInformation("Analysis deleted successfully: {AnalysisId}", request.AnalysisId);
            }
            else
            {
                _logger.LogWarning("Analysis not found or not authorized to delete: {AnalysisId}", request.AnalysisId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting analysis {AnalysisId} for user {UserId}", 
                request.AnalysisId, request.UserId);
            throw;
        }
    }
}
