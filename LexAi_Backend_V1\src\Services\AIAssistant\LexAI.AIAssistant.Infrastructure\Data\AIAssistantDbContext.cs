using LexAI.AIAssistant.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace LexAI.AIAssistant.Infrastructure.Data;

/// <summary>
/// DbContext pour le service AI Assistant
/// </summary>
public class AIAssistantDbContext : DbContext
{
    public AIAssistantDbContext(DbContextOptions<AIAssistantDbContext> options) : base(options)
    {
    }

    // DbSets pour les entités
    public DbSet<Conversation> Conversations { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<ChatSession> ChatSessions { get; set; }
    public DbSet<UserPreference> UserPreferences { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration de l'entité Conversation
        modelBuilder.Entity<Conversation>(entity =>
        {
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .IsRequired();
                
            entity.Property(e => e.UserId)
                .IsRequired();
                
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(200);
                
            entity.Property(e => e.Summary)
                .HasMaxLength(1000);
                
            entity.Property(e => e.Status)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.CreatedAt)
                .IsRequired();
                
            entity.Property(e => e.UpdatedAt)
                .IsRequired();

            // Configuration des propriétés JSON
            entity.Property(e => e.Tags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>());

            // Ignorer les propriétés complexes pour l'instant
            entity.Ignore(e => e.Context);

            // Relations
            entity.HasMany(e => e.Messages)
                .WithOne(m => m.Conversation)
                .HasForeignKey(m => m.ConversationId)
                .OnDelete(DeleteBehavior.Cascade);

            // Index
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => new { e.UserId, e.Status });
        });

        // Configuration de l'entité Message
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .IsRequired();
                
            entity.Property(e => e.ConversationId)
                .IsRequired();
                
            entity.Property(e => e.Content)
                .IsRequired();
                
            entity.Property(e => e.Role)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.MessageType)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.CreatedAt)
                .IsRequired();

            // Configuration des propriétés JSON - Ignorer pour l'instant

            // Ignorer les propriétés complexes pour l'instant
            entity.Ignore(e => e.Citations);
            entity.Ignore(e => e.Attachments);
            entity.Ignore(e => e.Metadata);

            // Index
            entity.HasIndex(e => e.ConversationId);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.Role);
        });

        // Configuration de l'entité ChatSession
        modelBuilder.Entity<ChatSession>(entity =>
        {
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .IsRequired();
                
            entity.Property(e => e.UserId)
                .IsRequired();
                
            entity.Property(e => e.SessionToken)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.Status)
                .IsRequired()
                .HasConversion<string>();
                
            entity.Property(e => e.CreatedAt)
                .IsRequired();
                
            entity.Property(e => e.ExpiresAt)
                .IsRequired();

            // Configuration des propriétés JSON
            entity.Property(e => e.Context)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

            // Index
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.SessionToken).IsUnique();
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.ExpiresAt);
        });

        // Configuration de l'entité UserPreference
        modelBuilder.Entity<UserPreference>(entity =>
        {
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .IsRequired();
                
            entity.Property(e => e.UserId)
                .IsRequired();
                
            entity.Property(e => e.PreferenceKey)
                .IsRequired()
                .HasMaxLength(100);
                
            entity.Property(e => e.PreferenceValue)
                .IsRequired();
                
            entity.Property(e => e.CreatedAt)
                .IsRequired();
                
            entity.Property(e => e.UpdatedAt)
                .IsRequired();

            // Index
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => new { e.UserId, e.PreferenceKey }).IsUnique();
        });
    }
}
