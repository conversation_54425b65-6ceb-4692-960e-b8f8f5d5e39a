// Types pour le service Legal Research

export interface SearchRequest {
  query: string
  domain?: string
  jurisdiction?: string
  documentTypes?: string[]
  maxResults?: number
  includeHighlights?: boolean
}

export interface SearchResult {
  id: string
  title: string
  content: string
  source: string
  url?: string
  relevanceScore: number
  highlights?: string[]
  metadata: SearchResultMetadata
}

export interface SearchResultMetadata {
  documentType: string
  jurisdiction: string
  date?: string
  author?: string
  court?: string
  caseNumber?: string
  tags?: string[]
}

export interface SearchResponse {
  results: SearchResult[]
  totalCount: number
  query: string
  searchTime: number
  suggestions?: string[]
}

export interface DocumentSearchRequest {
  query: string
  filters?: SearchFilters
}

export interface CaseSearchRequest {
  query: string
  filters?: CaseFilters
}

export interface SearchFilters {
  documentType?: string[]
  jurisdiction?: string[]
  dateFrom?: string
  dateTo?: string
  author?: string[]
  tags?: string[]
}

export interface CaseFilters extends SearchFilters {
  court?: string[]
  caseType?: string[]
  outcome?: string[]
}

export interface AdvancedSearchRequest {
  queries: SearchQuery[]
  operator: 'AND' | 'OR'
  filters?: SearchFilters
  maxResults?: number
}

export interface SearchQuery {
  field: string
  value: string
  operator: 'contains' | 'exact' | 'starts_with' | 'ends_with'
}

export interface SearchSuggestion {
  text: string
  type: 'query' | 'filter' | 'document'
  score: number
}

export interface SearchHistoryItem {
  id: string
  query: string
  timestamp: string
  resultCount: number
  filters?: SearchFilters
}

export interface SearchAnalytics {
  sessionId: string
  totalSearches: number
  averageResultCount: number
  mostSearchedTerms: string[]
  searchTrends: SearchTrend[]
}

export interface SearchTrend {
  term: string
  count: number
  period: string
}

export interface SearchFeedback {
  searchId: string
  relevantResults: string[]
  irrelevantResults: string[]
  rating: number
  comments?: string
}

export interface SimilarDocumentsRequest {
  documentId: string
  maxResults?: number
}
