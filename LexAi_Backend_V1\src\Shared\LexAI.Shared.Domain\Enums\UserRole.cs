namespace LexAI.Shared.Domain.Enums;

/// <summary>
/// Enumeration of user roles in the LexAI system
/// </summary>
public enum UserRole
{
    /// <summary>
    /// System administrator with full access
    /// </summary>
    Administrator = 1,

    /// <summary>
    /// Senior lawyer with management capabilities
    /// </summary>
    SeniorLawyer = 2,

    /// <summary>
    /// Regular lawyer with standard access
    /// </summary>
    Lawyer = 3,

    /// <summary>
    /// Legal assistant with limited access
    /// </summary>
    LegalAssistant = 4,

    /// <summary>
    /// Client with restricted access to their own data
    /// </summary>
    Client = 5,

    /// <summary>
    /// Guest user with minimal access
    /// </summary>
    Guest = 6
}

/// <summary>
/// Enumeration of document types in the legal system
/// </summary>
public enum DocumentType
{
    /// <summary>
    /// Legal contract document
    /// </summary>
    Contract = 1,

    /// <summary>
    /// Formal legal letter
    /// </summary>
    Letter = 2,

    /// <summary>
    /// Legal notice or demand letter
    /// </summary>
    Notice = 3,

    /// <summary>
    /// Company statutes or bylaws
    /// </summary>
    Statutes = 4,

    /// <summary>
    /// Legal brief or memorandum
    /// </summary>
    Brief = 5,

    /// <summary>
    /// Court pleading document
    /// </summary>
    Pleading = 6,

    /// <summary>
    /// Legal opinion or advice
    /// </summary>
    Opinion = 7,

    /// <summary>
    /// Power of attorney document
    /// </summary>
    PowerOfAttorney = 8,

    /// <summary>
    /// Will or testament
    /// </summary>
    Will = 9,

    /// <summary>
    /// Other legal document type
    /// </summary>
    Other = 99
}

/// <summary>
/// Enumeration of case or matter status
/// </summary>
public enum CaseStatus
{
    /// <summary>
    /// New case, not yet started
    /// </summary>
    New = 1,

    /// <summary>
    /// Case is currently being worked on
    /// </summary>
    InProgress = 2,

    /// <summary>
    /// Case is on hold or paused
    /// </summary>
    OnHold = 3,

    /// <summary>
    /// Waiting for client response or action
    /// </summary>
    WaitingForClient = 4,

    /// <summary>
    /// Waiting for court or third party
    /// </summary>
    WaitingForCourt = 5,

    /// <summary>
    /// Case has been completed successfully
    /// </summary>
    Completed = 6,

    /// <summary>
    /// Case has been closed without completion
    /// </summary>
    Closed = 7,

    /// <summary>
    /// Case has been cancelled
    /// </summary>
    Cancelled = 8
}

/// <summary>
/// Enumeration of legal practice areas
/// </summary>
public enum PracticeArea
{
    /// <summary>
    /// Corporate and business law
    /// </summary>
    Corporate = 1,

    /// <summary>
    /// Employment and labor law
    /// </summary>
    Employment = 2,

    /// <summary>
    /// Real estate law
    /// </summary>
    RealEstate = 3,

    /// <summary>
    /// Family law
    /// </summary>
    Family = 4,

    /// <summary>
    /// Criminal law
    /// </summary>
    Criminal = 5,

    /// <summary>
    /// Civil litigation
    /// </summary>
    CivilLitigation = 6,

    /// <summary>
    /// Intellectual property law
    /// </summary>
    IntellectualProperty = 7,

    /// <summary>
    /// Tax law
    /// </summary>
    Tax = 8,

    /// <summary>
    /// Immigration law
    /// </summary>
    Immigration = 9,

    /// <summary>
    /// Personal injury law
    /// </summary>
    PersonalInjury = 10,

    /// <summary>
    /// Contract law
    /// </summary>
    Contract = 11,

    /// <summary>
    /// Other practice area
    /// </summary>
    Other = 99
}
