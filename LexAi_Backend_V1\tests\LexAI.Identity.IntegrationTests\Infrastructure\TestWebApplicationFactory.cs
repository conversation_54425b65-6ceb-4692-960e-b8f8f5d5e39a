using LexAI.Identity.Infrastructure.Data;
using LexAI.Shared.Infrastructure.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestPlatform.TestHost;
using Testcontainers.PostgreSql;
using Xunit;

namespace LexAI.Identity.IntegrationTests.Infrastructure;

/// <summary>
/// Custom WebApplicationFactory for integration tests
/// </summary>
public class TestWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly PostgreSqlContainer _dbContainer;

    /// <summary>
    /// Initializes a new instance of the TestWebApplicationFactory
    /// </summary>
    public TestWebApplicationFactory()
    {
        _dbContainer = new PostgreSqlBuilder()
            .WithImage("postgres:16-alpine")
            .WithDatabase("lexai_identity_test")
            .WithUsername("test_user")
            .WithPassword("test_password")
            .WithCleanUp(true)
            .Build();
    }

    /// <summary>
    /// Gets the database connection string for tests
    /// </summary>
    public string ConnectionString => _dbContainer.GetConnectionString();

    /// <summary>
    /// Configures the web host for testing
    /// </summary>
    /// <param name="builder">Web host builder</param>
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddJsonFile("appsettings.Test.json", optional: false);
        });

        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<IdentityDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add test database context
            services.AddDbContext<IdentityDbContext>(options =>
            {
                options.UseNpgsql(ConnectionString);
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });

            // Configure test JWT settings
            services.Configure<JwtSettings>(options =>
            {
                options.SecretKey = "test-secret-key-that-is-at-least-32-characters-long-for-testing";
                options.Issuer = "LexAI-Test";
                options.Audience = "LexAI-Test-Users";
                options.AccessTokenExpirationMinutes = 60;
                options.RefreshTokenExpirationDays = 7;
                options.ValidateIssuer = false;
                options.ValidateAudience = false;
                options.ValidateLifetime = true;
                options.ValidateIssuerSigningKey = true;
                options.RequireHttpsMetadata = false;
            });

            // Configure logging for tests
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Warning);
            });
        });

        builder.UseEnvironment("Test");
    }

    /// <summary>
    /// Initializes the test environment
    /// </summary>
    public async Task InitializeAsync()
    {
        await _dbContainer.StartAsync();

        // Create and migrate the database
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<IdentityDbContext>();
        await context.Database.EnsureCreatedAsync();
    }

    /// <summary>
    /// Cleans up the test environment
    /// </summary>
    public new async Task DisposeAsync()
    {
        await _dbContainer.StopAsync();
        await base.DisposeAsync();
    }

    /// <summary>
    /// Resets the database to a clean state
    /// </summary>
    public async Task ResetDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<IdentityDbContext>();
        
        // Clear all data
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE \"RefreshTokens\" CASCADE");
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE \"UserPermissions\" CASCADE");
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE \"AuditEntries\" CASCADE");
        await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE \"Users\" CASCADE");
    }

    /// <summary>
    /// Seeds the database with test data
    /// </summary>
    public async Task SeedDatabaseAsync()
    {
        using var scope = Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<IdentityDbContext>();

        // Add test users if they don't exist
        if (!await context.Users.AnyAsync())
        {
            var testUsers = new[]
            {
                LexAI.Identity.Domain.Entities.User.Create(
                    "<EMAIL>",
                    "Admin",
                    "User",
                    LexAI.Shared.Domain.Enums.UserRole.Administrator,
                    "system"),
                LexAI.Identity.Domain.Entities.User.Create(
                    "<EMAIL>",
                    "John",
                    "Lawyer",
                    LexAI.Shared.Domain.Enums.UserRole.Lawyer,
                    "system"),
                LexAI.Identity.Domain.Entities.User.Create(
                    "<EMAIL>",
                    "Jane",
                    "Client",
                    LexAI.Shared.Domain.Enums.UserRole.Client,
                    "system")
            };

            foreach (var user in testUsers)
            {
                user.SetPassword("TestPassword123!", "system");
                user.VerifyEmail("system");
            }

            context.Users.AddRange(testUsers);
            await context.SaveChangesAsync();
        }
    }
}
