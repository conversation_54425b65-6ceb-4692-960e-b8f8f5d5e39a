namespace LexAI.DocumentAnalysis.Application.Interfaces;

/// <summary>
/// Interface pour le service de stockage des documents
/// </summary>
public interface IDocumentStorageService
{
    /// <summary>
    /// Stocke un document et retourne son hash et chemin
    /// </summary>
    Task<(string Hash, string StoragePath)> StoreDocumentAsync(
        byte[] documentContent, 
        string originalFileName, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Récupère un document par son hash
    /// </summary>
    Task<byte[]?> GetDocumentAsync(
        string documentHash, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime un document par son hash
    /// </summary>
    Task<bool> DeleteDocumentAsync(
        string documentHash, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si un document existe
    /// </summary>
    Task<bool> DocumentExistsAsync(
        string documentHash, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les informations d'un document stocké
    /// </summary>
    Task<DocumentStorageInfo?> GetDocumentInfoAsync(
        string documentHash, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Informations sur un document stocké
/// </summary>
public class DocumentStorageInfo
{
    public string Hash { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime LastModified { get; set; }
}
