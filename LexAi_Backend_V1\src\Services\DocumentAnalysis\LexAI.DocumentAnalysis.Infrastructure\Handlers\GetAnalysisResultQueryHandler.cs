using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Queries;
using LexAI.DocumentAnalysis.Domain.Enums;
using LexAI.DocumentAnalysis.Infrastructure.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Handlers;

/// <summary>
/// Handler pour la query GetAnalysisResultQuery
/// </summary>
public class GetAnalysisResultQueryHandler : IRequestHandler<GetAnalysisResultQuery, DocumentAnalysisResponseDto?>
{
    private readonly DocumentAnalysisDbContext _context;
    private readonly ILogger<GetAnalysisResultQueryHandler> _logger;

    public GetAnalysisResultQueryHandler(
        DocumentAnalysisDbContext context,
        ILogger<GetAnalysisResultQueryHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<DocumentAnalysisResponseDto?> Handle(
        GetAnalysisResultQuery request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving analysis result: {AnalysisId} for user: {UserId}", 
            request.AnalysisId, request.UserId);

        try
        {
            var analysis = await _context.DocumentAnalysisResults
                .Include(a => a.Clauses)
                .Include(a => a.Risks)
                .Include(a => a.Recommendations)
                .Include(a => a.Entities)
                .Include(a => a.Citations)
                .FirstOrDefaultAsync(a => a.Id == request.AnalysisId && a.UserId == request.UserId, 
                    cancellationToken);

            if (analysis == null)
            {
                _logger.LogWarning("Analysis not found: {AnalysisId} for user: {UserId}", 
                    request.AnalysisId, request.UserId);
                return null;
            }

            return new DocumentAnalysisResponseDto
            {
                Id = analysis.Id,
                DocumentName = analysis.DocumentName,
                DocumentType = analysis.DocumentType,
                Status = analysis.Status.ToString(),
                AnalysisContent = analysis.AnalysisContent,
                ConfidenceScore = analysis.ConfidenceScore,
                ProcessingTimeMs = analysis.ProcessingTimeMs,
                TokensUsed = analysis.TokensUsed,
                EstimatedCost = analysis.EstimatedCost,
                ModelUsed = analysis.ModelUsed,
                UserId = analysis.UserId,
                AnalyzedAt = analysis.AnalyzedAt,
                
                // Mapper les entités liées
                Clauses = analysis.Clauses.Select(c => new ClauseAnalysisDto
                {
                    Id = c.Id,
                    ClauseText = c.ClauseText,
                    ClauseType = c.ClauseType,
                    Analysis = c.Analysis,
                    RiskLevel = c.RiskLevel.ToString(),
                    ConfidenceScore = c.ConfidenceScore,
                    SuggestedRevision = c.SuggestedRevision,
                    Tags = c.Tags,
                    StartPosition = c.StartPosition,
                    EndPosition = c.EndPosition
                }).ToList(),

                Risks = analysis.Risks.Select(r => new RiskAssessmentDto
                {
                    Id = r.Id,
                    RiskType = r.RiskType,
                    Description = r.Description,
                    Severity = r.Severity.ToString(),
                    Probability = r.Probability,
                    Impact = r.Impact,
                    Mitigation = r.Mitigation,
                    AffectedClauses = r.AffectedClauses,
                    Confidence = r.Confidence
                }).ToList(),

                Recommendations = analysis.Recommendations.Select(r => new DocumentRecommendationDto
                {
                    Id = r.Id,
                    Type = r.Type,
                    Title = r.Title,
                    Description = r.Description,
                    Priority = r.Priority.ToString(),
                    SuggestedAction = r.SuggestedAction,
                    LegalBasis = r.LegalBasis,
                    RelatedClauses = r.RelatedClauses,
                    Impact = r.Impact
                }).ToList(),

                Entities = analysis.Entities.Select(e => new ExtractedEntityDto
                {
                    Id = e.Id,
                    Text = e.Text,
                    Type = e.Type,
                    ConfidenceScore = e.ConfidenceScore,
                    StartPosition = e.StartPosition,
                    EndPosition = e.EndPosition,
                    NormalizedValue = e.NormalizedValue,
                    Metadata = e.Metadata
                }).ToList(),

                Citations = analysis.Citations.Select(c => new DocumentCitationDto
                {
                    Id = c.Id,
                    Type = c.Type,
                    Title = c.Title,
                    Source = c.Source,
                    Url = c.Url,
                    Reference = c.Reference,
                    RelevanceScore = c.RelevanceScore,
                    Context = c.Context
                }).ToList(),

                Summary = new DocumentSummaryDto
                {
                    ExecutiveSummary = analysis.ExecutiveSummary ?? string.Empty,
                    KeyPoints = analysis.KeyPoints?.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>(),
                    OverallRiskLevel = analysis.OverallRiskLevel ?? "Unknown"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analysis result: {AnalysisId} for user: {UserId}", 
                request.AnalysisId, request.UserId);
            throw;
        }
    }
}
