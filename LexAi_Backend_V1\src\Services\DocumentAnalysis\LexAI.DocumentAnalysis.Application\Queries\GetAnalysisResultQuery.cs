using LexAI.DocumentAnalysis.Application.DTOs;
using MediatR;

namespace LexAI.DocumentAnalysis.Application.Queries;

/// <summary>
/// Query pour récupérer le résultat d'une analyse par ID
/// </summary>
public class GetAnalysisResultQuery : IRequest<DocumentAnalysisResponseDto?>
{
    public Guid AnalysisId { get; set; }
    public Guid UserId { get; set; }

    public GetAnalysisResultQuery(Guid analysisId, Guid userId)
    {
        AnalysisId = analysisId;
        UserId = userId;
    }
}
