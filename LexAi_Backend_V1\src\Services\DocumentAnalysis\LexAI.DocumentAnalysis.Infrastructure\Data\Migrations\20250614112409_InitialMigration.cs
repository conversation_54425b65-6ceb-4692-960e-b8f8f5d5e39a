﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LexAI.DocumentAnalysis.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "document_analysis_results",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DocumentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OriginalContent = table.Column<string>(type: "text", nullable: false),
                    ExtractedText = table.Column<string>(type: "text", nullable: false),
                    AnalysisContent = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    ConfidenceScore = table.Column<double>(type: "double precision", precision: 5, scale: 4, nullable: false),
                    ProcessingTimeMs = table.Column<int>(type: "integer", nullable: false),
                    TokensUsed = table.Column<int>(type: "integer", nullable: false),
                    EstimatedCost = table.Column<decimal>(type: "numeric(10,4)", precision: 10, scale: 4, nullable: false),
                    ModelUsed = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    AnalyzedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_document_analysis_results", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "clause_analyses",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentAnalysisResultId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClauseText = table.Column<string>(type: "text", nullable: false),
                    ClauseType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Analysis = table.Column<string>(type: "text", nullable: false),
                    RiskLevel = table.Column<string>(type: "text", nullable: false),
                    ConfidenceScore = table.Column<double>(type: "double precision", precision: 5, scale: 4, nullable: false),
                    StartPosition = table.Column<int>(type: "integer", nullable: false),
                    EndPosition = table.Column<int>(type: "integer", nullable: false),
                    SuggestedRevision = table.Column<string>(type: "text", nullable: true),
                    Tags = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_clause_analyses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_clause_analyses_document_analysis_results_DocumentAnalysisR~",
                        column: x => x.DocumentAnalysisResultId,
                        principalTable: "document_analysis_results",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "document_citations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentAnalysisResultId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Source = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Url = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Reference = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    RelevanceScore = table.Column<double>(type: "double precision", precision: 5, scale: 4, nullable: false),
                    Context = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_document_citations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_document_citations_document_analysis_results_DocumentAnalys~",
                        column: x => x.DocumentAnalysisResultId,
                        principalTable: "document_analysis_results",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "document_recommendations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentAnalysisResultId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Priority = table.Column<string>(type: "text", nullable: false),
                    SuggestedAction = table.Column<string>(type: "text", nullable: true),
                    LegalBasis = table.Column<string>(type: "text", nullable: true),
                    RelatedClauses = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_document_recommendations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_document_recommendations_document_analysis_results_Document~",
                        column: x => x.DocumentAnalysisResultId,
                        principalTable: "document_analysis_results",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "extracted_entities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentAnalysisResultId = table.Column<Guid>(type: "uuid", nullable: false),
                    Text = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Type = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ConfidenceScore = table.Column<double>(type: "double precision", precision: 5, scale: 4, nullable: false),
                    StartPosition = table.Column<int>(type: "integer", nullable: false),
                    EndPosition = table.Column<int>(type: "integer", nullable: false),
                    NormalizedValue = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_extracted_entities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_extracted_entities_document_analysis_results_DocumentAnalys~",
                        column: x => x.DocumentAnalysisResultId,
                        principalTable: "document_analysis_results",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "risk_assessments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentAnalysisResultId = table.Column<Guid>(type: "uuid", nullable: false),
                    RiskType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Severity = table.Column<string>(type: "text", nullable: false),
                    Probability = table.Column<double>(type: "double precision", precision: 5, scale: 4, nullable: false),
                    Impact = table.Column<string>(type: "text", nullable: false),
                    Mitigation = table.Column<string>(type: "text", nullable: false),
                    AffectedClauses = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    xmin = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_risk_assessments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_risk_assessments_document_analysis_results_DocumentAnalysis~",
                        column: x => x.DocumentAnalysisResultId,
                        principalTable: "document_analysis_results",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_clause_analyses_DocumentAnalysisResultId",
                table: "clause_analyses",
                column: "DocumentAnalysisResultId");

            migrationBuilder.CreateIndex(
                name: "IX_clause_analyses_RiskLevel",
                table: "clause_analyses",
                column: "RiskLevel");

            migrationBuilder.CreateIndex(
                name: "IX_document_analysis_results_AnalyzedAt",
                table: "document_analysis_results",
                column: "AnalyzedAt");

            migrationBuilder.CreateIndex(
                name: "IX_document_analysis_results_Status",
                table: "document_analysis_results",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_document_analysis_results_UserId",
                table: "document_analysis_results",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_document_analysis_results_UserId_Status",
                table: "document_analysis_results",
                columns: new[] { "UserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_document_citations_DocumentAnalysisResultId",
                table: "document_citations",
                column: "DocumentAnalysisResultId");

            migrationBuilder.CreateIndex(
                name: "IX_document_citations_Type",
                table: "document_citations",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_document_recommendations_DocumentAnalysisResultId",
                table: "document_recommendations",
                column: "DocumentAnalysisResultId");

            migrationBuilder.CreateIndex(
                name: "IX_document_recommendations_Priority",
                table: "document_recommendations",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_extracted_entities_DocumentAnalysisResultId",
                table: "extracted_entities",
                column: "DocumentAnalysisResultId");

            migrationBuilder.CreateIndex(
                name: "IX_extracted_entities_Type",
                table: "extracted_entities",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_risk_assessments_DocumentAnalysisResultId",
                table: "risk_assessments",
                column: "DocumentAnalysisResultId");

            migrationBuilder.CreateIndex(
                name: "IX_risk_assessments_Severity",
                table: "risk_assessments",
                column: "Severity");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "clause_analyses");

            migrationBuilder.DropTable(
                name: "document_citations");

            migrationBuilder.DropTable(
                name: "document_recommendations");

            migrationBuilder.DropTable(
                name: "extracted_entities");

            migrationBuilder.DropTable(
                name: "risk_assessments");

            migrationBuilder.DropTable(
                name: "document_analysis_results");
        }
    }
}
