import  { useState } from 'react'
import { Outlet } from 'react-router-dom'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { cn } from '../../lib/utils'
import { useThemeStore } from '../../store/themeStore'

export function Layout() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { theme } = useThemeStore()

  return (
    <div className={cn(
      "h-screen flex overflow-hidden",
      theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100'
    )}>
      {/* Sidebar for desktop */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <Sidebar />
      </div>

      {/* Sidebar for mobile */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            onClick={() => setSidebarOpen(false)}
          />
          <div className={cn(
            "relative flex w-full max-w-xs flex-1 flex-col",
            theme === 'dark' ? 'bg-gray-900' : 'bg-white'
          )}>
            <Sidebar />
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 overflow-y-auto">
          <div className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <Outlet />
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
