2025-06-14 13:28:32.061 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 13:28:32.167 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:28:32.528 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 13:28:32.531 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 13:28:32.869 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:28:32.871 +04:00 [INF] Hosting environment: Development
2025-06-14 13:28:32.873 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 13:28:33.452 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 13:28:33.636 +04:00 [INF] Request GET / started with correlation ID 557f8c75-e47d-46c4-a137-4b1f26dbd3fc
2025-06-14 13:28:35.587 +04:00 [INF] Request GET / completed in 1943ms with status 404 (Correlation ID: 557f8c75-e47d-46c4-a137-4b1f26dbd3fc)
2025-06-14 13:28:35.607 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 2165.7471ms
2025-06-14 13:28:35.639 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 13:35:53.250 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 13:35:53.298 +04:00 [INF] Request OPTIONS /api/v1/documents/analyses started with correlation ID 2125841c-4d1d-42a2-8997-a018d9428f0c
2025-06-14 13:35:53.312 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:35:53.322 +04:00 [INF] Request OPTIONS /api/v1/documents/analyses completed in 17ms with status 204 (Correlation ID: 2125841c-4d1d-42a2-8997-a018d9428f0c)
2025-06-14 13:35:53.332 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 82.0083ms
2025-06-14 13:35:53.340 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 13:35:53.389 +04:00 [INF] Request GET /api/v1/documents/analyses started with correlation ID e9d1c3c5-0367-4866-833b-639a981c3896
2025-06-14 13:35:53.393 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:35:53.528 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 13:35:53.572 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-14 13:35:53.578 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-14 13:35:53.581 +04:00 [INF] Request GET /api/v1/documents/analyses completed in 189ms with status 404 (Correlation ID: e9d1c3c5-0367-4866-833b-639a981c3896)
2025-06-14 13:35:53.588 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 404 0 null 248.7739ms
2025-06-14 13:35:53.597 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/api/v1/documents/analyses, Response status code: 404
2025-06-14 13:52:15.336 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 13:52:15.358 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID 9edc83db-64a8-425e-b36b-f575e264205b
2025-06-14 13:52:15.362 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:52:15.366 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 3ms with status 204 (Correlation ID: 9edc83db-64a8-425e-b36b-f575e264205b)
2025-06-14 13:52:15.374 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 37.276ms
2025-06-14 13:52:15.376 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 13:52:15.384 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID 69754b6b-d87e-48ce-bd77-8ac3a3bf86a4
2025-06-14 13:52:15.389 +04:00 [INF] CORS policy execution successful.
2025-06-14 13:52:15.393 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 13:52:15.399 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-14 13:52:15.402 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-14 13:52:15.404 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 16ms with status 404 (Correlation ID: 69754b6b-d87e-48ce-bd77-8ac3a3bf86a4)
2025-06-14 13:52:15.408 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 31.6383ms
2025-06-14 13:52:15.412 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 14:00:58.873 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 14:00:58.970 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 14:00:59.190 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 14:00:59.192 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 14:00:59.237 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 14:00:59.277 +04:00 [INF] Hosting environment: Development
2025-06-14 14:00:59.314 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 14:00:59.755 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 14:00:59.940 +04:00 [INF] Request GET / started with correlation ID 91cebdff-3593-463e-9463-d66ab7f570a7
2025-06-14 14:01:01.891 +04:00 [INF] Request GET / completed in 1947ms with status 404 (Correlation ID: 91cebdff-3593-463e-9463-d66ab7f570a7)
2025-06-14 14:01:01.901 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 2148.7601ms
2025-06-14 14:01:01.915 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 15:19:54.008 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 240
2025-06-14 15:24:08.547 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 240
2025-06-14 15:25:22.185 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 240
2025-06-14 15:28:14.899 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 15:28:14.974 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:28:15.296 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 15:28:15.298 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 15:28:15.408 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:28:15.412 +04:00 [INF] Hosting environment: Development
2025-06-14 15:28:15.415 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 15:28:15.894 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 15:28:16.146 +04:00 [INF] Request GET / started with correlation ID 63f7ab52-3037-40d4-8411-82875f1778a9
2025-06-14 15:28:16.249 +04:00 [INF] Request GET / completed in 96ms with status 404 (Correlation ID: 63f7ab52-3037-40d4-8411-82875f1778a9)
2025-06-14 15:28:16.265 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 375.3517ms
2025-06-14 15:28:16.287 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 15:30:57.469 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 15:30:57.553 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID 2a7724c5-ec74-4e00-80f1-31ee8df93eb3
2025-06-14 15:30:57.569 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:57.571 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 4ms with status 204 (Correlation ID: 2a7724c5-ec74-4e00-80f1-31ee8df93eb3)
2025-06-14 15:30:57.574 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 104.9395ms
2025-06-14 15:30:57.576 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 15:30:57.585 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID 93689a9e-9900-43ed-bab3-c0d2339bb4e0
2025-06-14 15:30:57.589 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:30:57.726 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 15:30:57.772 +04:00 [WRN] JWT Authentication failed: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-14 15:30:57.776 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10517: Signature validation failed. The token's kid is missing. Keys tried: 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey, KeyId: '', InternalId: 'rFmIU1l0L9yZXYZun5Sznow9GqTNDD5WfZi1z_x9RaM'. , KeyId: 
'. Number of keys in TokenValidationParameters: '1'. 
Number of keys in Configuration: '0'. 
Exceptions caught:
 '[PII of type 'System.String' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'.
token: '[PII of type 'Microsoft.IdentityModel.JsonWebTokens.JsonWebToken' is hidden. For more details, see https://aka.ms/IdentityModel/PII.]'. See https://aka.ms/IDX10503 for details.
2025-06-14 15:30:57.777 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 189ms with status 404 (Correlation ID: 93689a9e-9900-43ed-bab3-c0d2339bb4e0)
2025-06-14 15:30:57.782 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 205.5555ms
2025-06-14 15:30:57.787 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
