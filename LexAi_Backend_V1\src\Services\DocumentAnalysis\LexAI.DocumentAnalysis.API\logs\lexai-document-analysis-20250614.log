2025-06-14 19:30:47.260 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 19:30:47.358 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 19:30:47.611 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 19:30:47.613 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 19:30:47.884 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 19:30:47.886 +04:00 [INF] Hosting environment: Development
2025-06-14 19:30:47.889 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 19:30:49.496 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 19:30:49.941 +04:00 [INF] Request GET / started with correlation ID 40624184-c6ae-4492-a09f-2c37e4afccc5
2025-06-14 19:30:50.169 +04:00 [INF] Request GET / completed in 219ms with status 404 (Correlation ID: 40624184-c6ae-4492-a09f-2c37e4afccc5)
2025-06-14 19:30:50.232 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 739.8196ms
2025-06-14 19:30:50.257 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 19:31:13.927 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 19:31:14.043 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID c0e96f88-fea7-4833-8876-858b4141f42f
2025-06-14 19:31:14.090 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:31:14.103 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 21ms with status 204 (Correlation ID: c0e96f88-fea7-4833-8876-858b4141f42f)
2025-06-14 19:31:14.125 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 197.8361ms
2025-06-14 19:31:14.134 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundarypfTLRnAvRBTfzfAS 599108
2025-06-14 19:31:14.178 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 47a9f17d-0e13-4e29-8491-872d808d69b4
2025-06-14 19:31:14.189 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:31:14.343 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 2:57:30 PM', Current time (UTC): '6/14/2025 3:31:14 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 19:31:14.441 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 2:57:30 PM', Current time (UTC): '6/14/2025 3:31:14 PM'.
2025-06-14 19:31:14.451 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 2:57:30 PM', Current time (UTC): '6/14/2025 3:31:14 PM'.
2025-06-14 19:31:14.467 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 19:31:14.488 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 19:31:14.493 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 305ms with status 401 (Correlation ID: 47a9f17d-0e13-4e29-8491-872d808d69b4)
2025-06-14 19:31:14.501 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 401 0 null 366.8007ms
2025-06-14 19:31:17.078 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundaryNY0x4meEAZk37Ua8 599108
2025-06-14 19:31:17.083 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 7bd08215-a4ff-43d7-afca-982e56c90455
2025-06-14 19:31:17.086 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:31:17.105 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 19:31:17.111 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 19:31:17.165 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 19:31:17.299 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 19:31:17.362 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 19:31:56.948 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 19:33:26.442 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 19:33:26.470 +04:00 [INF] Starting comprehensive document analysis for: Cdd_test.pdf
2025-06-14 19:33:26.492 +04:00 [INF] Starting Azure Document Intelligence extraction for document type: pdf
2025-06-14 19:34:37.405 +04:00 [INF] Azure extraction completed successfully. Processing time: 5346ms, Pages: 2
2025-06-14 19:35:08.967 +04:00 [INF] Starting clause analysis for document type: pdf
2025-06-14 19:35:10.276 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:35:10.289 +04:00 [INF] Clause analysis completed. Found 0 clauses
2025-06-14 19:36:16.730 +04:00 [INF] Starting risk assessment for document type: pdf
2025-06-14 19:36:17.046 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:36:17.366 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:36:17.377 +04:00 [INF] Risk assessment completed. Identified 0 risks
2025-06-14 19:36:46.172 +04:00 [INF] Generating recommendations for document type: pdf
2025-06-14 19:36:46.522 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:36:46.838 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:36:46.847 +04:00 [INF] Generated 0 recommendations
2025-06-14 19:37:10.981 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:37:42.294 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:38:04.284 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:40:46.246 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 19:41:00.408 +04:00 [INF] Document analysis completed successfully. Processing time: 447833ms, Tokens: 0
2025-06-14 19:41:12.183 +04:00 [INF] Document analysis completed successfully for document: Cdd_test.pdf, Analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 19:41:12.186 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 19:41:12.192 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-14 19:41:12.200 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 595023.5363ms
2025-06-14 19:41:12.203 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 19:41:12.205 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 595119ms with status 200 (Correlation ID: 7bd08215-a4ff-43d7-afca-982e56c90455)
2025-06-14 19:41:12.209 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 595131.0553ms
2025-06-14 19:42:14.558 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 19:42:14.576 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID c4fd8eb7-9faf-4fc9-9a3e-d62e7079876f
2025-06-14 19:42:14.579 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:42:14.580 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: c4fd8eb7-9faf-4fc9-9a3e-d62e7079876f)
2025-06-14 19:42:14.584 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 26.111ms
2025-06-14 19:42:14.586 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 19:42:14.599 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 4d78f71a-e53c-4cf8-ba08-dc9b37e6a9d3
2025-06-14 19:42:14.604 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:42:14.607 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 19:42:14.614 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 19:42:14.632 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 19:42:14.648 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 19:42:14.653 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 19:42:14.696 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:42:15.014 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:42:16.064 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 19:42:16.112 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 19:42:16.126 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 19:42:16.132 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 19:42:16.134 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 19:42:17.289 +04:00 [INF] Executed DbCommand (102ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-14 19:42:17.611 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 19:42:17.704 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentName", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."OriginalContent", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", d1.xmin, r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", r.xmin, d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0.xmin
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentName", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."OriginalContent", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId", d.xmin
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-14 19:42:17.732 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:42:17.738 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 19:42:17.746 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 3102.3445ms
2025-06-14 19:42:17.749 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 19:42:17.751 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 3147ms with status 200 (Correlation ID: 4d78f71a-e53c-4cf8-ba08-dc9b37e6a9d3)
2025-06-14 19:42:17.767 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 3180.5226ms
2025-06-14 19:50:27.519 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 19:50:27.537 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 0e56da83-0e19-4d96-bfff-0a5c7d25e733
2025-06-14 19:50:27.542 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:50:27.547 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 5ms with status 204 (Correlation ID: 0e56da83-0e19-4d96-bfff-0a5c7d25e733)
2025-06-14 19:50:27.557 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 37.8873ms
2025-06-14 19:50:27.560 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 19:50:27.585 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID bcfbb9ee-d837-44b5-b85b-e9d5f32bc039
2025-06-14 19:50:27.596 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:50:27.603 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 19:50:27.611 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 19:50:27.615 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 19:50:27.622 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 19:50:27.628 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 19:50:27.635 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:50:27.639 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:50:27.701 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-14 19:50:27.718 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentName", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."OriginalContent", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", d1.xmin, r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", r.xmin, d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0.xmin
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentName", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."OriginalContent", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId", d.xmin
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-14 19:50:27.725 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:50:27.727 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 19:50:27.731 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 113.3134ms
2025-06-14 19:50:27.734 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 19:50:27.736 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 140ms with status 200 (Correlation ID: bcfbb9ee-d837-44b5-b85b-e9d5f32bc039)
2025-06-14 19:50:27.741 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 180.5714ms
2025-06-14 19:52:56.343 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 19:52:56.356 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 42901afb-e333-4f3e-a040-ae603d900b3e
2025-06-14 19:52:56.363 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:52:56.367 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 4ms with status 204 (Correlation ID: 42901afb-e333-4f3e-a040-ae603d900b3e)
2025-06-14 19:52:56.384 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 41.3192ms
2025-06-14 19:52:56.390 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 19:52:56.489 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 951279a1-a3bf-4ee2-8b5e-930f61650958
2025-06-14 19:52:56.495 +04:00 [INF] CORS policy execution successful.
2025-06-14 19:52:56.498 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 19:52:56.501 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 19:52:56.503 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 19:52:56.509 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 19:52:56.511 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 19:53:05.218 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:54:44.832 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:56:30.130 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-14 19:56:33.546 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentName", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."OriginalContent", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", d1.xmin, r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", r.xmin, d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0.xmin
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentName", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."OriginalContent", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId", d.xmin
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-14 19:56:46.617 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 19:56:46.620 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 19:56:46.622 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 230114.1724ms
2025-06-14 19:56:46.624 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 19:56:46.626 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 230132ms with status 200 (Correlation ID: 951279a1-a3bf-4ee2-8b5e-930f61650958)
2025-06-14 19:56:46.629 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 230238.9148ms
2025-06-14 20:08:52.974 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 20:08:52.986 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID f54714f8-1a15-4ae8-9ffa-d81b7c0613a4
2025-06-14 20:08:52.993 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:08:52.995 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 2ms with status 204 (Correlation ID: f54714f8-1a15-4ae8-9ffa-d81b7c0613a4)
2025-06-14 20:08:53.000 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 25.8407ms
2025-06-14 20:08:53.014 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundary8gC2tGzAIeFGBFKA 599108
2025-06-14 20:08:53.112 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID b4e1df32-d4b0-4cf9-8003-4d2e5c1a3d65
2025-06-14 20:08:53.115 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:08:53.118 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:08:53.122 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:08:53.125 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:08:53.131 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:08:53.135 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:09:04.227 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 20:09:04.254 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 20:09:04.286 +04:00 [INF] Returning cached analysis for document: Cdd_test.pdf
2025-06-14 20:09:04.288 +04:00 [INF] Document analysis completed successfully for document: Cdd_test.pdf, Analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:09:04.290 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:09:04.292 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-14 20:09:04.293 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 11164.0194ms
2025-06-14 20:09:04.295 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:09:04.296 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 11180ms with status 200 (Correlation ID: b4e1df32-d4b0-4cf9-8003-4d2e5c1a3d65)
2025-06-14 20:09:04.299 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 11285.5534ms
2025-06-14 20:09:12.467 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate - null null
2025-06-14 20:09:12.480 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate started with correlation ID 63c7749c-14ef-4db3-9d1a-1de5b025892b
2025-06-14 20:09:12.488 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:09:12.494 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate completed in 6ms with status 204 (Correlation ID: 63c7749c-14ef-4db3-9d1a-1de5b025892b)
2025-06-14 20:09:12.506 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate - 204 null null 39.5407ms
2025-06-14 20:09:12.511 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate - application/json 0
2025-06-14 20:09:12.528 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate started with correlation ID 874ab09d-347d-48be-a96b-56221072c71b
2025-06-14 20:09:12.537 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:09:12.542 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:09:12.548 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis (LexAI.DocumentAnalysis.API)'
2025-06-14 20:09:12.567 +04:00 [INF] Route matched with {action = "RegenerateAnalysis", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] RegenerateAnalysis(System.Guid, LexAI.DocumentAnalysis.Application.DTOs.AnalysisOptions, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:09:12.576 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:09:12.579 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:09:12.589 +04:00 [INF] Regenerating analysis "8948abb9-c9f0-4757-82d0-9b016e028d23" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:09:12.601 +04:00 [INF] Regenerating analysis "8948abb9-c9f0-4757-82d0-9b016e028d23" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:09:12.605 +04:00 [INF] Regenerating analysis: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:09:12.726 +04:00 [ERR] Error regenerating analysis "8948abb9-c9f0-4757-82d0-9b016e028d23" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
System.NotImplementedException: Regeneration will be implemented with database integration
   at LexAI.DocumentAnalysis.Infrastructure.Services.DocumentAnalysisService.RegenerateAnalysisAsync(Guid analysisId, AnalysisOptions newOptions, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Infrastructure\Services\DocumentAnalysisService.cs:line 231
   at LexAI.DocumentAnalysis.Application.Commands.RegenerateAnalysisCommandHandler.Handle(RegenerateAnalysisCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Application\Commands\AnalyzeDocumentCommand.cs:line 107
2025-06-14 20:09:12.827 +04:00 [ERR] Error regenerating analysis "8948abb9-c9f0-4757-82d0-9b016e028d23"
System.NotImplementedException: Regeneration will be implemented with database integration
   at LexAI.DocumentAnalysis.Infrastructure.Services.DocumentAnalysisService.RegenerateAnalysisAsync(Guid analysisId, AnalysisOptions newOptions, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Infrastructure\Services\DocumentAnalysisService.cs:line 231
   at LexAI.DocumentAnalysis.Application.Commands.RegenerateAnalysisCommandHandler.Handle(RegenerateAnalysisCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Application\Commands\AnalyzeDocumentCommand.cs:line 107
   at LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis(Guid analysisId, AnalysisOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Controllers\DocumentAnalysisController.cs:line 219
2025-06-14 20:09:12.835 +04:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-06-14 20:09:12.839 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis (LexAI.DocumentAnalysis.API) in 264.1517ms
2025-06-14 20:09:12.843 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis (LexAI.DocumentAnalysis.API)'
2025-06-14 20:09:12.847 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate completed in 309ms with status 500 (Correlation ID: 874ab09d-347d-48be-a96b-56221072c71b)
2025-06-14 20:09:12.854 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/8948abb9-c9f0-4757-82d0-9b016e028d23/regenerate - 500 null application/json; charset=utf-8 343.0549ms
2025-06-14 20:10:24.329 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 20:10:24.352 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 06a577a3-53ba-4894-b80b-828f68e5a081
2025-06-14 20:10:24.356 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:10:24.359 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 2ms with status 204 (Correlation ID: 06a577a3-53ba-4894-b80b-828f68e5a081)
2025-06-14 20:10:24.369 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 39.5327ms
2025-06-14 20:10:24.372 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 20:10:24.382 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 9f63faf2-5e32-4a45-b4d5-bc06fbdbb686
2025-06-14 20:10:24.389 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:10:24.393 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:10:24.398 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 20:10:24.403 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:10:24.410 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:10:24.412 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:10:28.375 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:10:30.878 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:10:32.358 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-14 20:10:32.367 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentName", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."OriginalContent", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", d1.xmin, r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", r.xmin, d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0.xmin
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentName", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."OriginalContent", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId", d.xmin
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-14 20:10:48.235 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:10:48.237 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 20:10:48.240 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 23831.6465ms
2025-06-14 20:10:48.243 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 20:10:48.246 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 23857ms with status 200 (Correlation ID: 9f63faf2-5e32-4a45-b4d5-bc06fbdbb686)
2025-06-14 20:10:48.250 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 23878.353ms
2025-06-14 20:11:02.685 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 20:11:02.693 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 3860a28b-c030-4c59-b603-fac7f579f772
2025-06-14 20:11:02.701 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:11:02.706 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 4ms with status 204 (Correlation ID: 3860a28b-c030-4c59-b603-fac7f579f772)
2025-06-14 20:11:02.712 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 27.6534ms
2025-06-14 20:11:02.721 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundaryqc3ef2NpZbeBBXIE 599108
2025-06-14 20:11:02.788 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID b3c767e4-8cfb-4311-91a0-7488cb481058
2025-06-14 20:11:02.791 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:11:02.793 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:11:02.795 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:11:02.797 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:11:02.804 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:11:02.806 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:11:10.042 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 20:11:56.819 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 20:12:10.402 +04:00 [INF] Returning cached analysis for document: Cdd_test.pdf
2025-06-14 20:12:10.474 +04:00 [INF] Document analysis completed successfully for document: Cdd_test.pdf, Analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:12:15.244 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:12:15.248 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-14 20:12:15.252 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 72448.6514ms
2025-06-14 20:12:15.255 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:12:15.258 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 72467ms with status 200 (Correlation ID: b3c767e4-8cfb-4311-91a0-7488cb481058)
2025-06-14 20:12:15.262 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 72541.6934ms
2025-06-14 20:13:49.692 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 20:13:49.714 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID d1826a8d-5dc9-4caf-88bd-95f08a268d13
2025-06-14 20:13:49.716 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:13:49.718 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 1ms with status 204 (Correlation ID: d1826a8d-5dc9-4caf-88bd-95f08a268d13)
2025-06-14 20:13:49.722 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 29.1267ms
2025-06-14 20:13:49.726 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundaryAYQn5AuaxaSCUAse 599108
2025-06-14 20:13:49.778 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID c139d109-ebd0-4e84-8755-f9e9e936e37d
2025-06-14 20:13:49.782 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:13:49.785 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:13:49.788 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:13:49.790 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:13:49.796 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:13:49.798 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:13:53.037 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 20:13:57.004 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-14 20:14:27.488 +04:00 [INF] Returning cached analysis for document: Cdd_test.pdf
2025-06-14 20:15:30.768 +04:00 [INF] Document analysis completed successfully for document: Cdd_test.pdf, Analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:15:41.370 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "8948abb9-c9f0-4757-82d0-9b016e028d23"
2025-06-14 20:16:02.979 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-14 20:16:02.991 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 133195.9821ms
2025-06-14 20:16:03.002 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:16:03.005 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 133223ms with status 200 (Correlation ID: c139d109-ebd0-4e84-8755-f9e9e936e37d)
2025-06-14 20:16:03.010 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 133283.693ms
2025-06-14 20:20:32.444 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 20:20:32.471 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID e40c015d-e66c-49db-9f9b-7ec68ab54d9f
2025-06-14 20:20:32.480 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:20:32.484 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 4ms with status 204 (Correlation ID: e40c015d-e66c-49db-9f9b-7ec68ab54d9f)
2025-06-14 20:20:32.491 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 47.0106ms
2025-06-14 20:20:32.496 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundary5cv3soFXlyvjPd3F 599647
2025-06-14 20:20:32.557 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 3c6a6fcb-521d-44c6-8c5e-1ae7b9b2d417
2025-06-14 20:20:32.561 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:20:32.564 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:20:32.566 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:20:32.569 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:20:32.573 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:20:32.574 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:20:42.372 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_cmr1.pdf
2025-06-14 20:21:14.278 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_cmr1.pdf
2025-06-14 20:21:59.499 +04:00 [INF] Starting comprehensive document analysis for: Cdd_test_cmr1.pdf
2025-06-14 20:26:27.749 +04:00 [INF] Starting Azure Document Intelligence extraction for document type: pdf
2025-06-14 20:28:09.079 +04:00 [INF] Azure extraction completed successfully. Processing time: 4939ms, Pages: 2
2025-06-14 20:29:10.169 +04:00 [INF] Starting clause analysis for document type: pdf
2025-06-14 20:38:45.895 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:38:45.955 +04:00 [INF] Clause analysis completed. Found 0 clauses
2025-06-14 20:39:06.978 +04:00 [INF] Starting risk assessment for document type: pdf
2025-06-14 20:39:24.497 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:39:27.954 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:39:27.975 +04:00 [INF] Risk assessment completed. Identified 0 risks
2025-06-14 20:39:32.825 +04:00 [INF] Generating recommendations for document type: pdf
2025-06-14 20:40:01.239 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:40:39.726 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:40:39.734 +04:00 [INF] Generated 0 recommendations
2025-06-14 20:40:49.711 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:40:54.576 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:40:57.539 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:41:00.758 +04:00 [ERR] Error sending prompt to LLM
Microsoft.SemanticKernel.HttpOperationException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
 ---> System.ClientModel.ClientResultException: HTTP 404 (: DeploymentNotFound)

The API deployment for this resource does not exist. If you created the deployment within the last 5 minutes, please wait a moment and try again.
   at OpenAI.ClientPipelineExtensions.ProcessMessageAsync(ClientPipeline pipeline, PipelineMessage message, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(BinaryContent content, RequestOptions options)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   --- End of inner exception stack trace ---
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.GetChatCompletionResultAsync(IChatCompletionService chatCompletion, Kernel kernel, PromptRenderingResult promptRenderingResult, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunctionFromPrompt.InvokeCoreAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.<>c__DisplayClass32_0.<<InvokeAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.SemanticKernel.Kernel.InvokeFilterOrFunctionAsync(NonNullCollection`1 functionFilters, Func`2 functionCallback, FunctionInvocationContext context, Int32 index)
   at Microsoft.SemanticKernel.Kernel.OnFunctionInvocationAsync(KernelFunction function, KernelArguments arguments, FunctionResult functionResult, Boolean isStreaming, Func`2 functionCallback, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.KernelFunction.InvokeAsync(Kernel kernel, KernelArguments arguments, CancellationToken cancellationToken)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.SendPromptAsync(String prompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 47
2025-06-14 20:41:00.772 +04:00 [INF] Document analysis completed successfully. Processing time: 1142888ms, Tokens: 0
2025-06-14 20:41:00.783 +04:00 [INF] Document analysis completed successfully for document: Cdd_test_cmr1.pdf, Analysis ID: "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b"
2025-06-14 20:41:00.784 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b"
2025-06-14 20:41:00.786 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-14 20:41:00.788 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 1228215.7177ms
2025-06-14 20:41:00.789 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 20:41:00.791 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 1228230ms with status 200 (Correlation ID: 3c6a6fcb-521d-44c6-8c5e-1ae7b9b2d417)
2025-06-14 20:41:00.795 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 1228299.3166ms
2025-06-14 20:42:12.507 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate - null null
2025-06-14 20:42:12.513 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate started with correlation ID 67475cbe-ae44-47da-aa18-413fda0f1e6b
2025-06-14 20:42:12.516 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:42:12.519 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate completed in 2ms with status 204 (Correlation ID: 67475cbe-ae44-47da-aa18-413fda0f1e6b)
2025-06-14 20:42:12.528 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate - 204 null null 20.9732ms
2025-06-14 20:42:12.534 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate - application/json 0
2025-06-14 20:42:12.550 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate started with correlation ID 699fedf1-b5e4-4968-aab3-47a11c4f5b3c
2025-06-14 20:42:12.553 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:42:12.557 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 4:31:16 PM', Current time (UTC): '6/14/2025 4:42:12 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 20:42:12.563 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 4:31:16 PM', Current time (UTC): '6/14/2025 4:42:12 PM'.
2025-06-14 20:42:12.564 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 4:31:16 PM', Current time (UTC): '6/14/2025 4:42:12 PM'.
2025-06-14 20:42:12.567 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 20:42:12.570 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 20:42:12.572 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate completed in 18ms with status 401 (Correlation ID: 699fedf1-b5e4-4968-aab3-47a11c4f5b3c)
2025-06-14 20:42:12.574 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate - 401 0 null 40.8051ms
2025-06-14 20:42:13.042 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate - application/json 0
2025-06-14 20:42:13.047 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate started with correlation ID 383926fc-d9b4-4fa5-97d0-e76e1ea54525
2025-06-14 20:42:13.048 +04:00 [INF] CORS policy execution successful.
2025-06-14 20:42:13.050 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 20:42:13.052 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis (LexAI.DocumentAnalysis.API)'
2025-06-14 20:42:13.054 +04:00 [INF] Route matched with {action = "RegenerateAnalysis", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] RegenerateAnalysis(System.Guid, LexAI.DocumentAnalysis.Application.DTOs.AnalysisOptions, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 20:42:13.058 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 20:42:13.059 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 20:42:20.420 +04:00 [INF] Regenerating analysis "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:43:55.222 +04:00 [INF] Regenerating analysis "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 20:46:09.417 +04:00 [INF] Regenerating analysis: "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b"
2025-06-14 20:46:09.517 +04:00 [ERR] Error regenerating analysis "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
System.NotImplementedException: Regeneration will be implemented with database integration
   at LexAI.DocumentAnalysis.Infrastructure.Services.DocumentAnalysisService.RegenerateAnalysisAsync(Guid analysisId, AnalysisOptions newOptions, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Infrastructure\Services\DocumentAnalysisService.cs:line 231
   at LexAI.DocumentAnalysis.Application.Commands.RegenerateAnalysisCommandHandler.Handle(RegenerateAnalysisCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Application\Commands\AnalyzeDocumentCommand.cs:line 107
2025-06-14 20:46:09.554 +04:00 [ERR] Error regenerating analysis "e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b"
System.NotImplementedException: Regeneration will be implemented with database integration
   at LexAI.DocumentAnalysis.Infrastructure.Services.DocumentAnalysisService.RegenerateAnalysisAsync(Guid analysisId, AnalysisOptions newOptions, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Infrastructure\Services\DocumentAnalysisService.cs:line 231
   at LexAI.DocumentAnalysis.Application.Commands.RegenerateAnalysisCommandHandler.Handle(RegenerateAnalysisCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.Application\Commands\AnalyzeDocumentCommand.cs:line 107
   at LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis(Guid analysisId, AnalysisOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Controllers\DocumentAnalysisController.cs:line 219
2025-06-14 20:46:09.557 +04:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-06-14 20:46:09.559 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis (LexAI.DocumentAnalysis.API) in 236502.5604ms
2025-06-14 20:46:09.562 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.RegenerateAnalysis (LexAI.DocumentAnalysis.API)'
2025-06-14 20:46:09.564 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate completed in 236516ms with status 500 (Correlation ID: 383926fc-d9b4-4fa5-97d0-e76e1ea54525)
2025-06-14 20:46:09.568 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/e44e0d7f-4e80-4f45-ba23-f8fda6a93d4b/regenerate - 500 null application/json; charset=utf-8 236526.3378ms
