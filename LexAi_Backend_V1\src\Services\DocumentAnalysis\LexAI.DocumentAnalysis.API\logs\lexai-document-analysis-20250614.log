2025-06-14 15:53:32.309 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 15:53:32.434 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:53:32.963 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 15:53:32.964 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 15:53:33.036 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:53:33.039 +04:00 [INF] Hosting environment: Development
2025-06-14 15:53:33.040 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 15:53:33.828 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 15:53:33.980 +04:00 [INF] Request GET / started with correlation ID f506c35d-9d09-4c41-b208-b88103928e98
2025-06-14 15:53:34.062 +04:00 [INF] Request GET / completed in 77ms with status 404 (Correlation ID: f506c35d-9d09-4c41-b208-b88103928e98)
2025-06-14 15:53:34.077 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 247.8438ms
2025-06-14 15:53:34.101 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 15:55:24.649 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 15:55:24.735 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID 9e34696f-20dd-4801-9909-13129fb7c4db
2025-06-14 15:55:24.767 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:55:24.775 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 18ms with status 204 (Correlation ID: 9e34696f-20dd-4801-9909-13129fb7c4db)
2025-06-14 15:55:24.787 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 138.0875ms
2025-06-14 15:55:24.792 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 15:55:24.807 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID f54707ba-892f-4239-bdcc-34206ae2180c
2025-06-14 15:55:24.815 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:55:24.916 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:55:24.921 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 108ms with status 404 (Correlation ID: f54707ba-892f-4239-bdcc-34206ae2180c)
2025-06-14 15:55:24.927 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 135.3915ms
2025-06-14 15:55:24.942 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 15:56:18.211 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger - null null
2025-06-14 15:56:18.271 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger - 301 0 null 60.0319ms
2025-06-14 15:56:18.281 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/index.html - null null
2025-06-14 15:56:18.350 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/index.html - 200 null text/html;charset=utf-8 69.1593ms
2025-06-14 15:56:18.370 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/swagger-ui.css - null null
2025-06-14 15:56:18.377 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/index.css - null null
2025-06-14 15:56:18.383 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/swagger-ui-bundle.js - null null
2025-06-14 15:56:18.443 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/index.js - null null
2025-06-14 15:56:18.443 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/swagger-ui-standalone-preset.js - null null
2025-06-14 15:56:18.453 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 15:56:18.490 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-14 15:56:18.486 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/index.js - 200 null application/javascript;charset=utf-8 42.4487ms
2025-06-14 15:56:18.509 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/index.css - 200 202 text/css 132.1353ms
2025-06-14 15:56:18.529 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/_framework/aspnetcore-browser-refresh.js - 200 16537 application/javascript; charset=utf-8 76.0699ms
2025-06-14 15:56:18.537 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-14 15:56:18.539 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-14 15:56:18.561 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/swagger-ui.css - 200 152035 text/css 191.3093ms
2025-06-14 15:56:18.562 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 118.9396ms
2025-06-14 15:56:18.571 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/_vs/browserLink - null null
2025-06-14 15:56:18.624 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-14 15:56:18.630 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 251.597ms
2025-06-14 15:56:18.698 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/_vs/browserLink - 200 null text/javascript; charset=UTF-8 127.3967ms
2025-06-14 15:56:18.769 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/v1/swagger.json - null null
2025-06-14 15:56:18.791 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/favicon-32x32.png - null null
2025-06-14 15:56:18.797 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-14 15:56:18.799 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/favicon-32x32.png - 200 628 image/png 8.0844ms
2025-06-14 15:56:18.854 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 84.774ms
2025-06-14 16:00:06.114 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis - null null
2025-06-14 16:00:06.121 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 5f5a02cd-b10f-4b9f-8641-52516a72baa6
2025-06-14 16:00:06.156 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 16:00:06.167 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 16:00:06.171 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 46ms with status 401 (Correlation ID: 5f5a02cd-b10f-4b9f-8641-52516a72baa6)
2025-06-14 16:00:06.175 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis - 401 0 null 61.1538ms
2025-06-14 16:01:12.727 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 16:01:12.739 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID ecdcda4e-2106-4369-b600-740c88118d40
2025-06-14 16:01:12.746 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:12.750 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 3ms with status 204 (Correlation ID: ecdcda4e-2106-4369-b600-740c88118d40)
2025-06-14 16:01:12.756 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 28.9604ms
2025-06-14 16:01:12.759 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 16:01:12.772 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID 0e62fc9b-9d31-44b2-b0fc-2501a5843749
2025-06-14 16:01:12.774 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:12.779 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:01:12.787 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 12ms with status 404 (Correlation ID: 0e62fc9b-9d31-44b2-b0fc-2501a5843749)
2025-06-14 16:01:12.798 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 38.4259ms
2025-06-14 16:01:12.818 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 16:01:33.395 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 16:01:33.404 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID d4733858-1d5d-4d6a-930c-0f6b41a67e8a
2025-06-14 16:01:33.413 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:33.420 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 7ms with status 204 (Correlation ID: d4733858-1d5d-4d6a-930c-0f6b41a67e8a)
2025-06-14 16:01:33.433 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 37.7554ms
2025-06-14 16:01:33.437 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 16:01:33.455 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID 6767d55f-a05d-4924-8aa3-aad9a2dade7c
2025-06-14 16:01:33.458 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:33.459 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:01:33.463 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 5ms with status 404 (Correlation ID: 6767d55f-a05d-4924-8aa3-aad9a2dade7c)
2025-06-14 16:01:33.472 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 34.9369ms
2025-06-14 16:01:33.484 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 16:01:59.816 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:01:59.828 +04:00 [INF] Request OPTIONS /api/v1/documents/analyses started with correlation ID 198b3e97-ee41-4579-8cde-1d54398aa8f2
2025-06-14 16:01:59.833 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:59.839 +04:00 [INF] Request OPTIONS /api/v1/documents/analyses completed in 5ms with status 204 (Correlation ID: 198b3e97-ee41-4579-8cde-1d54398aa8f2)
2025-06-14 16:01:59.859 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 41.5262ms
2025-06-14 16:01:59.868 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:01:59.883 +04:00 [INF] Request GET /api/v1/documents/analyses started with correlation ID 107cd39c-4f37-49ea-ac01-613f9e13be8d
2025-06-14 16:01:59.887 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:59.891 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:01:59.900 +04:00 [INF] Request GET /api/v1/documents/analyses completed in 12ms with status 404 (Correlation ID: 107cd39c-4f37-49ea-ac01-613f9e13be8d)
2025-06-14 16:01:59.907 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 404 0 null 38.5944ms
2025-06-14 16:01:59.920 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/api/v1/documents/analyses, Response status code: 404
2025-06-14 16:04:36.785 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:04:36.806 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyses started with correlation ID f55e9b28-a917-4335-b427-6e3354a30ff3
2025-06-14 16:04:36.813 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:04:36.815 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyses completed in 2ms with status 204 (Correlation ID: f55e9b28-a917-4335-b427-6e3354a30ff3)
2025-06-14 16:04:36.819 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 34.0306ms
2025-06-14 16:04:36.822 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:04:36.832 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/analyses started with correlation ID dc8d5557-011d-4275-b6ce-8cf86cc33e6b
2025-06-14 16:04:36.835 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:04:36.837 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:04:36.839 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/analyses completed in 3ms with status 404 (Correlation ID: dc8d5557-011d-4275-b6ce-8cf86cc33e6b)
2025-06-14 16:04:36.848 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 404 0 null 25.8068ms
2025-06-14 16:04:36.870 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/api/v1/DocumentAnalysis/analyses, Response status code: 404
2025-06-14 16:09:56.845 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:09:56.855 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID e644faca-38bb-42d8-8c55-47bd9db2b488
2025-06-14 16:09:56.862 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:09:56.867 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 4ms with status 204 (Correlation ID: e644faca-38bb-42d8-8c55-47bd9db2b488)
2025-06-14 16:09:56.880 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 35.0056ms
2025-06-14 16:09:56.885 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:09:56.913 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID c08bc597-1200-47c4-ba9e-b2bf96f0d9a9
2025-06-14 16:09:56.921 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:09:56.927 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:09:56.940 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 16:09:57.026 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:10:01.748 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 16:10:01.754 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 16:10:01.770 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 4730.3941ms
2025-06-14 16:10:01.772 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 16:10:01.774 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 4853ms with status 200 (Correlation ID: c08bc597-1200-47c4-ba9e-b2bf96f0d9a9)
2025-06-14 16:10:01.779 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 4893.2809ms
2025-06-14 16:11:11.185 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 16:11:11.210 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID d8a2b275-effc-479c-94f0-c38e85504811
2025-06-14 16:11:11.213 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:11:11.215 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 1ms with status 204 (Correlation ID: d8a2b275-effc-479c-94f0-c38e85504811)
2025-06-14 16:11:11.220 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 34.2593ms
2025-06-14 16:11:11.225 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 16:11:11.241 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 5a15ebe2-da3a-4f96-93f7-bd7cf7896636
2025-06-14 16:11:11.247 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:11:11.249 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:11:11.263 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:11:11.304 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:11:11.323 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 16:11:11.327 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 16:11:11.329 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 18.0571ms
2025-06-14 16:11:11.332 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:11:11.333 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 86ms with status 400 (Correlation ID: 5a15ebe2-da3a-4f96-93f7-bd7cf7896636)
2025-06-14 16:11:11.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 112.5349ms
2025-06-14 16:48:22.589 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 16:48:22.705 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 16:48:22.953 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 16:48:22.956 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 16:48:22.998 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 16:48:23.001 +04:00 [INF] Hosting environment: Development
2025-06-14 16:48:23.007 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 16:48:23.723 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 16:48:23.889 +04:00 [INF] Request GET / started with correlation ID 6c27514c-6c7f-448f-b5e2-3cfbcb4a65dd
2025-06-14 16:48:23.989 +04:00 [INF] Request GET / completed in 93ms with status 404 (Correlation ID: 6c27514c-6c7f-448f-b5e2-3cfbcb4a65dd)
2025-06-14 16:48:24.000 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 279.0694ms
2025-06-14 16:48:24.017 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 16:49:11.796 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:49:11.847 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID ae2ffc1f-b298-439a-a075-f236e16e624f
2025-06-14 16:49:11.879 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:49:11.887 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 14ms with status 204 (Correlation ID: ae2ffc1f-b298-439a-a075-f236e16e624f)
2025-06-14 16:49:11.894 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 98.8332ms
2025-06-14 16:49:11.905 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:49:11.942 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID e264398c-2fdd-4654-8e4b-b5f999530199
2025-06-14 16:49:11.949 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:49:12.017 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 12:30:21 PM', Current time (UTC): '6/14/2025 12:49:12 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 16:49:12.074 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 12:30:21 PM', Current time (UTC): '6/14/2025 12:49:12 PM'.
2025-06-14 16:49:12.086 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 12:30:21 PM', Current time (UTC): '6/14/2025 12:49:12 PM'.
2025-06-14 16:49:12.102 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 16:49:12.118 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 16:49:12.123 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 174ms with status 401 (Correlation ID: e264398c-2fdd-4654-8e4b-b5f999530199)
2025-06-14 16:49:12.133 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 401 0 null 228.3534ms
2025-06-14 16:49:13.502 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:49:13.509 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID e803c138-a1e4-41ea-b563-a59736fd8c73
2025-06-14 16:49:13.512 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:49:13.525 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:49:13.532 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 16:49:13.557 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:49:13.933 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 16:49:14.111 +04:00 [ERR] Error configuring primary LLM model: OpenAI
System.InvalidOperationException: OpenAI API key is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 280
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 234
2025-06-14 16:49:18.777 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 5209.7195ms
2025-06-14 16:49:18.780 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 16:49:20.128 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: OpenAI API key is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 280
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 234
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService..ctor(IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 27
   at InvokeStub_UnifiedLLMService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_6(IServiceProvider provider) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 211
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method10(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_11>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 282
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__10>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 267
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-14 16:49:20.159 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 500 null text/plain; charset=utf-8 6657.3776ms
2025-06-14 16:50:19.297 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 16:50:19.305 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 4357376f-2eb9-4b8e-89bd-c505e394083b
2025-06-14 16:50:19.310 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:50:19.313 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 2ms with status 204 (Correlation ID: 4357376f-2eb9-4b8e-89bd-c505e394083b)
2025-06-14 16:50:19.324 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 27.8065ms
2025-06-14 16:50:19.331 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 16:50:19.345 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID a698a621-0d17-446f-8a93-f25bc023695b
2025-06-14 16:50:19.348 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:50:19.351 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:50:19.357 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:50:19.379 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:50:19.385 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 16:50:19.422 +04:00 [ERR] Error configuring primary LLM model: OpenAI
System.InvalidOperationException: OpenAI API key is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 280
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 234
2025-06-14 16:50:41.659 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 22274.8898ms
2025-06-14 16:50:41.663 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:50:43.319 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: OpenAI API key is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 280
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 234
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService..ctor(IConfiguration configuration, ILogger`1 logger) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 27
   at InvokeStub_UnifiedLLMService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_6(IServiceProvider provider) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 211
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method18(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_11>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 282
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__10>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 267
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-14 16:50:43.339 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 500 null text/plain; charset=utf-8 24008.384ms
2025-06-14 16:58:22.030 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 16:58:22.038 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 2798ae67-4941-4685-8b89-1cfc9ac8585c
2025-06-14 16:58:22.047 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:58:22.050 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 4ms with status 204 (Correlation ID: 2798ae67-4941-4685-8b89-1cfc9ac8585c)
2025-06-14 16:58:22.061 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 30.5943ms
2025-06-14 16:58:22.079 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 16:58:22.125 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 1548dade-2233-46c5-9b79-d01d3a650acd
2025-06-14 16:58:22.127 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:58:22.131 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:58:22.133 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:58:22.135 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:58:22.142 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 17:04:17.958 +04:00 [ERR] Error configuring primary LLM model: OpenAI
System.InvalidOperationException: OpenAI API key is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel()
2025-06-14 17:04:19.921 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 357781.0734ms
2025-06-14 17:04:19.933 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 17:04:21.013 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: OpenAI API key is required
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.ConfigureOpenAI(IKernelBuilder builder)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.CreateKernel()
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService..ctor(IConfiguration configuration, ILogger`1 logger)
   at InvokeStub_UnifiedLLMService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_6(IServiceProvider provider) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 211
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method18(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at Program.<>c.<<<Main>$>b__0_11>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 282
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__10>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 267
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-14 17:04:21.061 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 500 null text/plain; charset=utf-8 358981.8745ms
2025-06-14 17:04:56.177 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 17:04:56.289 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 17:04:56.676 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 17:04:56.679 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 17:04:56.864 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 17:04:56.866 +04:00 [INF] Hosting environment: Development
2025-06-14 17:04:56.868 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 17:04:57.239 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 17:04:57.402 +04:00 [INF] Request GET / started with correlation ID d3e5b1e2-31d3-4ff9-98da-d32475fba290
2025-06-14 17:04:57.467 +04:00 [INF] Request GET / completed in 58ms with status 404 (Correlation ID: d3e5b1e2-31d3-4ff9-98da-d32475fba290)
2025-06-14 17:04:57.480 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 244.9713ms
2025-06-14 17:04:57.502 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 17:05:36.625 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 17:05:36.645 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID f62b60d5-39cf-4f01-896f-269659fde045
2025-06-14 17:05:36.657 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:05:36.665 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 13ms with status 204 (Correlation ID: f62b60d5-39cf-4f01-896f-269659fde045)
2025-06-14 17:05:36.673 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 47.7159ms
2025-06-14 17:05:36.679 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 17:05:36.690 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID f799772e-2546-423d-9559-cc5791e77277
2025-06-14 17:05:36.693 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:05:36.746 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 17:05:36.761 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 17:05:36.783 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 17:05:36.847 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 17:05:59.795 +04:00 [INF] Configured OpenAI LLM with model: gpt-4.1-nano-2025-04-14
2025-06-14 17:06:28.037 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:06:28.107 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:06:28.117 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:06:28.125 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 17:06:28.145 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 51356.7933ms
2025-06-14 17:06:28.148 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 17:06:28.153 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 51460ms with status 200 (Correlation ID: f799772e-2546-423d-9559-cc5791e77277)
2025-06-14 17:06:28.156 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 51477.4775ms
2025-06-14 17:06:46.361 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 17:06:46.367 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID ca3dd2eb-c423-420e-b620-dfe5061698c2
2025-06-14 17:06:46.372 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:06:46.374 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 1ms with status 204 (Correlation ID: ca3dd2eb-c423-420e-b620-dfe5061698c2)
2025-06-14 17:06:46.383 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 21.361ms
2025-06-14 17:06:46.389 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 17:06:46.398 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID bb6cb6a3-0508-415d-b093-debcb13d2a60
2025-06-14 17:06:46.402 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:06:46.408 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 17:06:46.411 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 17:06:46.427 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 17:06:46.434 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 17:06:50.467 +04:00 [INF] Configured OpenAI LLM with model: gpt-4.1-nano-2025-04-14
2025-06-14 17:06:50.482 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 17:06:50.487 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 17:06:50.489 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 4057.8862ms
2025-06-14 17:06:50.492 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 17:06:50.493 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 4091ms with status 400 (Correlation ID: bb6cb6a3-0508-415d-b093-debcb13d2a60)
2025-06-14 17:06:50.497 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 4108.115ms
2025-06-14 17:56:32.258 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 17:56:32.359 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 17:56:32.605 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 17:56:32.606 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 17:56:32.874 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 17:56:32.878 +04:00 [INF] Hosting environment: Development
2025-06-14 17:56:32.881 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 17:56:33.392 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 17:56:33.597 +04:00 [INF] Request GET / started with correlation ID 8ffa2590-c31d-44fb-aa69-1ab5030ede40
2025-06-14 17:56:36.943 +04:00 [INF] Request GET / completed in 3341ms with status 404 (Correlation ID: 8ffa2590-c31d-44fb-aa69-1ab5030ede40)
2025-06-14 17:56:36.960 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 3571.7453ms
2025-06-14 17:56:36.986 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 17:57:26.503 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 17:57:26.579 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 10ee815c-ff35-4020-9ac9-e5c4fdd9e640
2025-06-14 17:57:26.609 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:26.619 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 19ms with status 204 (Correlation ID: 10ee815c-ff35-4020-9ac9-e5c4fdd9e640)
2025-06-14 17:57:26.626 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 123.2371ms
2025-06-14 17:57:26.634 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 17:57:26.645 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID f65596ad-c239-48f2-a5db-ecfd1ffcd216
2025-06-14 17:57:26.650 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:26.924 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 1:49:13 PM', Current time (UTC): '6/14/2025 1:57:26 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-14 17:57:27.036 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 1:49:13 PM', Current time (UTC): '6/14/2025 1:57:26 PM'.
2025-06-14 17:57:27.050 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 1:49:13 PM', Current time (UTC): '6/14/2025 1:57:26 PM'.
2025-06-14 17:57:27.073 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 17:57:27.092 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 17:57:27.097 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 449ms with status 401 (Correlation ID: f65596ad-c239-48f2-a5db-ecfd1ffcd216)
2025-06-14 17:57:27.108 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 401 0 null 474.0721ms
2025-06-14 17:57:30.549 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 17:57:30.562 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 5795be45-9386-4bdd-b20a-0ea66a6c604e
2025-06-14 17:57:30.567 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:30.603 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 17:57:30.616 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 17:57:30.695 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 17:57:31.722 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 17:57:33.020 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 17:57:33.651 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:57:35.091 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:57:36.307 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-14 17:57:36.370 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 17:57:36.387 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 17:57:36.392 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 17:57:36.395 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-14 17:57:37.929 +04:00 [INF] Executed DbCommand (151ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-14 17:57:38.394 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 17:57:38.533 +04:00 [INF] Executed DbCommand (35ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentName", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."OriginalContent", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", d1.xmin, r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", r.xmin, d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0.xmin
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentName", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."OriginalContent", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId", d.xmin
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-14 17:57:38.586 +04:00 [INF] Retrieved 0 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 17:57:38.605 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 17:57:38.647 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 7867.3556ms
2025-06-14 17:57:38.653 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 17:57:38.661 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 8093ms with status 200 (Correlation ID: 5795be45-9386-4bdd-b20a-0ea66a6c604e)
2025-06-14 17:57:38.760 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 8210.6713ms
2025-06-14 17:57:56.777 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 17:57:56.788 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID b7cf80c3-1486-478d-a2d8-57f6f0cf0f11
2025-06-14 17:57:56.797 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:56.800 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 3ms with status 204 (Correlation ID: b7cf80c3-1486-478d-a2d8-57f6f0cf0f11)
2025-06-14 17:57:56.815 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 37.8847ms
2025-06-14 17:57:56.821 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 17:57:56.848 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 8d9e4dec-dde4-46b2-96b7-98d4228081f3
2025-06-14 17:57:56.853 +04:00 [INF] CORS policy execution successful.
2025-06-14 17:57:56.862 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 17:57:56.914 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 17:57:56.961 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 17:57:56.970 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 17:57:56.972 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 17:57:56.995 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 17:57:56.999 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 17:57:57.003 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 36.0482ms
2025-06-14 17:57:57.006 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 17:57:57.009 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 156ms with status 400 (Correlation ID: 8d9e4dec-dde4-46b2-96b7-98d4228081f3)
2025-06-14 17:57:57.015 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 193.7496ms
2025-06-14 18:08:40.698 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 18:08:40.749 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 0d26c228-8b1e-4eb5-9555-6edddfee8d54
2025-06-14 18:08:40.759 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:08:40.765 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 6ms with status 204 (Correlation ID: 0d26c228-8b1e-4eb5-9555-6edddfee8d54)
2025-06-14 18:08:40.781 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 82.7344ms
2025-06-14 18:08:40.795 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 18:08:40.815 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID f2b6fb16-0aef-4644-a884-c3531e31338c
2025-06-14 18:08:40.818 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:08:40.819 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 18:08:40.824 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:08:40.826 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 18:08:40.835 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 18:08:40.839 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 18:09:14.723 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 18:09:14.731 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 18:09:14.733 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 33901.7085ms
2025-06-14 18:09:14.735 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:09:14.738 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 33920ms with status 400 (Correlation ID: f2b6fb16-0aef-4644-a884-c3531e31338c)
2025-06-14 18:09:14.744 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 33948.3318ms
2025-06-14 18:13:46.795 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 18:13:46.809 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID e62a818b-bfea-4853-91c5-4c01292ec49f
2025-06-14 18:13:46.818 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:13:46.822 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 3ms with status 204 (Correlation ID: e62a818b-bfea-4853-91c5-4c01292ec49f)
2025-06-14 18:13:46.834 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 39.4072ms
2025-06-14 18:13:46.839 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 18:13:46.849 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID af6a3369-96ac-48ca-bc8b-c012a1f295e1
2025-06-14 18:13:46.852 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:13:46.853 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 18:13:46.855 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:13:46.856 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 18:13:46.860 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 18:13:46.862 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 18:14:13.210 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 18:14:13.232 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 18:14:13.234 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 26374.4542ms
2025-06-14 18:14:13.236 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:14:13.237 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 26385ms with status 400 (Correlation ID: af6a3369-96ac-48ca-bc8b-c012a1f295e1)
2025-06-14 18:14:13.243 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 26403.5568ms
2025-06-14 18:15:16.929 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 18:15:16.937 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 0adf849f-726a-4087-bd14-c0d202cc1753
2025-06-14 18:15:16.943 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:15:16.946 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 2ms with status 204 (Correlation ID: 0adf849f-726a-4087-bd14-c0d202cc1753)
2025-06-14 18:15:16.955 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 26.0384ms
2025-06-14 18:15:16.959 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 18:15:16.976 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID b55544eb-62db-49de-bec9-11aa06c2eb5f
2025-06-14 18:15:16.979 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:15:16.981 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 18:15:16.984 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:15:16.987 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 18:15:16.994 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 18:15:16.997 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 18:15:31.180 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 18:15:31.190 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 18:15:31.199 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 14205.5228ms
2025-06-14 18:15:31.206 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:15:31.212 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 14233ms with status 400 (Correlation ID: b55544eb-62db-49de-bec9-11aa06c2eb5f)
2025-06-14 18:15:31.222 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 14262.751ms
2025-06-14 18:23:29.216 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 18:23:29.231 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 6cc9ca8b-f6d8-49e9-8f30-d23b01874e38
2025-06-14 18:23:29.235 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:23:29.238 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 3ms with status 204 (Correlation ID: 6cc9ca8b-f6d8-49e9-8f30-d23b01874e38)
2025-06-14 18:23:29.246 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 29.3775ms
2025-06-14 18:23:29.252 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 18:23:29.273 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID ec41059f-9a8e-4216-bbb9-354c3718d4cc
2025-06-14 18:23:29.277 +04:00 [INF] CORS policy execution successful.
2025-06-14 18:23:29.280 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 18:23:29.283 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:23:29.286 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 18:23:29.291 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-14 18:23:29.292 +04:00 [INF] Configured Azure OpenAI LLM with deployment: gpt-4.1-nano-2025-04-14 at https://lexai-az-openai.openai.azure.com/
2025-06-14 18:23:33.708 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 18:23:33.716 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 18:23:33.725 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 4434.6806ms
2025-06-14 18:23:33.730 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 18:23:33.735 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 4458ms with status 400 (Correlation ID: ec41059f-9a8e-4216-bbb9-354c3718d4cc)
2025-06-14 18:23:33.747 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 4495.2299ms
