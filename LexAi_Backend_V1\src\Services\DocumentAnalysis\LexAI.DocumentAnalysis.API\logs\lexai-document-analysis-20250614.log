2025-06-14 15:53:32.309 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-14 15:53:32.434 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 15:53:32.963 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-14 15:53:32.964 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-14 15:53:33.036 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 15:53:33.039 +04:00 [INF] Hosting environment: Development
2025-06-14 15:53:33.040 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-14 15:53:33.828 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-14 15:53:33.980 +04:00 [INF] Request GET / started with correlation ID f506c35d-9d09-4c41-b208-b88103928e98
2025-06-14 15:53:34.062 +04:00 [INF] Request GET / completed in 77ms with status 404 (Correlation ID: f506c35d-9d09-4c41-b208-b88103928e98)
2025-06-14 15:53:34.077 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 247.8438ms
2025-06-14 15:53:34.101 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-14 15:55:24.649 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 15:55:24.735 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID 9e34696f-20dd-4801-9909-13129fb7c4db
2025-06-14 15:55:24.767 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:55:24.775 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 18ms with status 204 (Correlation ID: 9e34696f-20dd-4801-9909-13129fb7c4db)
2025-06-14 15:55:24.787 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 138.0875ms
2025-06-14 15:55:24.792 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 15:55:24.807 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID f54707ba-892f-4239-bdcc-34206ae2180c
2025-06-14 15:55:24.815 +04:00 [INF] CORS policy execution successful.
2025-06-14 15:55:24.916 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 15:55:24.921 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 108ms with status 404 (Correlation ID: f54707ba-892f-4239-bdcc-34206ae2180c)
2025-06-14 15:55:24.927 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 135.3915ms
2025-06-14 15:55:24.942 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 15:56:18.211 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger - null null
2025-06-14 15:56:18.271 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger - 301 0 null 60.0319ms
2025-06-14 15:56:18.281 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/index.html - null null
2025-06-14 15:56:18.350 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/index.html - 200 null text/html;charset=utf-8 69.1593ms
2025-06-14 15:56:18.370 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/swagger-ui.css - null null
2025-06-14 15:56:18.377 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/index.css - null null
2025-06-14 15:56:18.383 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/swagger-ui-bundle.js - null null
2025-06-14 15:56:18.443 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/index.js - null null
2025-06-14 15:56:18.443 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/swagger-ui-standalone-preset.js - null null
2025-06-14 15:56:18.453 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 15:56:18.490 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-14 15:56:18.486 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/index.js - 200 null application/javascript;charset=utf-8 42.4487ms
2025-06-14 15:56:18.509 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/index.css - 200 202 text/css 132.1353ms
2025-06-14 15:56:18.529 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/_framework/aspnetcore-browser-refresh.js - 200 16537 application/javascript; charset=utf-8 76.0699ms
2025-06-14 15:56:18.537 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-14 15:56:18.539 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-14 15:56:18.561 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/swagger-ui.css - 200 152035 text/css 191.3093ms
2025-06-14 15:56:18.562 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 118.9396ms
2025-06-14 15:56:18.571 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/_vs/browserLink - null null
2025-06-14 15:56:18.624 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-14 15:56:18.630 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 251.597ms
2025-06-14 15:56:18.698 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/_vs/browserLink - 200 null text/javascript; charset=UTF-8 127.3967ms
2025-06-14 15:56:18.769 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/v1/swagger.json - null null
2025-06-14 15:56:18.791 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/swagger/favicon-32x32.png - null null
2025-06-14 15:56:18.797 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-14 15:56:18.799 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/favicon-32x32.png - 200 628 image/png 8.0844ms
2025-06-14 15:56:18.854 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 84.774ms
2025-06-14 16:00:06.114 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis - null null
2025-06-14 16:00:06.121 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 5f5a02cd-b10f-4b9f-8641-52516a72baa6
2025-06-14 16:00:06.156 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 16:00:06.167 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-14 16:00:06.171 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 46ms with status 401 (Correlation ID: 5f5a02cd-b10f-4b9f-8641-52516a72baa6)
2025-06-14 16:00:06.175 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis - 401 0 null 61.1538ms
2025-06-14 16:01:12.727 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 16:01:12.739 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID ecdcda4e-2106-4369-b600-740c88118d40
2025-06-14 16:01:12.746 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:12.750 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 3ms with status 204 (Correlation ID: ecdcda4e-2106-4369-b600-740c88118d40)
2025-06-14 16:01:12.756 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 28.9604ms
2025-06-14 16:01:12.759 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 16:01:12.772 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID 0e62fc9b-9d31-44b2-b0fc-2501a5843749
2025-06-14 16:01:12.774 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:12.779 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:01:12.787 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 12ms with status 404 (Correlation ID: 0e62fc9b-9d31-44b2-b0fc-2501a5843749)
2025-06-14 16:01:12.798 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 38.4259ms
2025-06-14 16:01:12.818 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 16:01:33.395 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - null null
2025-06-14 16:01:33.404 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze started with correlation ID d4733858-1d5d-4d6a-930c-0f6b41a67e8a
2025-06-14 16:01:33.413 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:33.420 +04:00 [INF] Request OPTIONS /api/v1/documents/analyze completed in 7ms with status 204 (Correlation ID: d4733858-1d5d-4d6a-930c-0f6b41a67e8a)
2025-06-14 16:01:33.433 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyze - 204 null null 37.7554ms
2025-06-14 16:01:33.437 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - application/json 2
2025-06-14 16:01:33.455 +04:00 [INF] Request POST /api/v1/documents/analyze started with correlation ID 6767d55f-a05d-4924-8aa3-aad9a2dade7c
2025-06-14 16:01:33.458 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:33.459 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:01:33.463 +04:00 [INF] Request POST /api/v1/documents/analyze completed in 5ms with status 404 (Correlation ID: 6767d55f-a05d-4924-8aa3-aad9a2dade7c)
2025-06-14 16:01:33.472 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/documents/analyze - 404 0 null 34.9369ms
2025-06-14 16:01:33.484 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:5004/api/v1/documents/analyze, Response status code: 404
2025-06-14 16:01:59.816 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:01:59.828 +04:00 [INF] Request OPTIONS /api/v1/documents/analyses started with correlation ID 198b3e97-ee41-4579-8cde-1d54398aa8f2
2025-06-14 16:01:59.833 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:59.839 +04:00 [INF] Request OPTIONS /api/v1/documents/analyses completed in 5ms with status 204 (Correlation ID: 198b3e97-ee41-4579-8cde-1d54398aa8f2)
2025-06-14 16:01:59.859 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 41.5262ms
2025-06-14 16:01:59.868 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:01:59.883 +04:00 [INF] Request GET /api/v1/documents/analyses started with correlation ID 107cd39c-4f37-49ea-ac01-613f9e13be8d
2025-06-14 16:01:59.887 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:01:59.891 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:01:59.900 +04:00 [INF] Request GET /api/v1/documents/analyses completed in 12ms with status 404 (Correlation ID: 107cd39c-4f37-49ea-ac01-613f9e13be8d)
2025-06-14 16:01:59.907 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/documents/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 404 0 null 38.5944ms
2025-06-14 16:01:59.920 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/api/v1/documents/analyses, Response status code: 404
2025-06-14 16:04:36.785 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:04:36.806 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyses started with correlation ID f55e9b28-a917-4335-b427-6e3354a30ff3
2025-06-14 16:04:36.813 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:04:36.815 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyses completed in 2ms with status 204 (Correlation ID: f55e9b28-a917-4335-b427-6e3354a30ff3)
2025-06-14 16:04:36.819 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 34.0306ms
2025-06-14 16:04:36.822 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:04:36.832 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/analyses started with correlation ID dc8d5557-011d-4275-b6ce-8cf86cc33e6b
2025-06-14 16:04:36.835 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:04:36.837 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:04:36.839 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/analyses completed in 3ms with status 404 (Correlation ID: dc8d5557-011d-4275-b6ce-8cf86cc33e6b)
2025-06-14 16:04:36.848 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/analyses?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 404 0 null 25.8068ms
2025-06-14 16:04:36.870 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/api/v1/DocumentAnalysis/analyses, Response status code: 404
2025-06-14 16:09:56.845 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-14 16:09:56.855 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID e644faca-38bb-42d8-8c55-47bd9db2b488
2025-06-14 16:09:56.862 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:09:56.867 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 4ms with status 204 (Correlation ID: e644faca-38bb-42d8-8c55-47bd9db2b488)
2025-06-14 16:09:56.880 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 35.0056ms
2025-06-14 16:09:56.885 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-14 16:09:56.913 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID c08bc597-1200-47c4-ba9e-b2bf96f0d9a9
2025-06-14 16:09:56.921 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:09:56.927 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:09:56.940 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 16:09:57.026 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:10:01.748 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-14 16:10:01.754 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-14 16:10:01.770 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 4730.3941ms
2025-06-14 16:10:01.772 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-14 16:10:01.774 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 4853ms with status 200 (Correlation ID: c08bc597-1200-47c4-ba9e-b2bf96f0d9a9)
2025-06-14 16:10:01.779 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 4893.2809ms
2025-06-14 16:11:11.185 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-14 16:11:11.210 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID d8a2b275-effc-479c-94f0-c38e85504811
2025-06-14 16:11:11.213 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:11:11.215 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 1ms with status 204 (Correlation ID: d8a2b275-effc-479c-94f0-c38e85504811)
2025-06-14 16:11:11.220 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 34.2593ms
2025-06-14 16:11:11.225 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - application/json 2
2025-06-14 16:11:11.241 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 5a15ebe2-da3a-4f96-93f7-bd7cf7896636
2025-06-14 16:11:11.247 +04:00 [INF] CORS policy execution successful.
2025-06-14 16:11:11.249 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-14 16:11:11.263 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:11:11.304 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-14 16:11:11.323 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: null
2025-06-14 16:11:11.327 +04:00 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-14 16:11:11.329 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 18.0571ms
2025-06-14 16:11:11.332 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-14 16:11:11.333 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 86ms with status 400 (Correlation ID: 5a15ebe2-da3a-4f96-93f7-bd7cf7896636)
2025-06-14 16:11:11.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 400 null application/json; charset=utf-8 112.5349ms
