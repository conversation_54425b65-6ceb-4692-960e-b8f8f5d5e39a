﻿using LexAI.Shared.Application.DTOs.Embedding;

namespace LexAI.Shared.Application.Utils
{
    /// <summary>
    /// Utilitaires pour la gestion des embeddings
    /// </summary>
    public static class EmbeddingUtils
    {
        /// <summary>
        /// Sérialise un embedding en base64
        /// </summary>
        public static string SerializeEmbedding(float[] embedding)
        {
            var bytes = new byte[embedding.Length * sizeof(float)];
            Buffer.BlockCopy(embedding, 0, bytes, 0, bytes.Length);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// Désérialise un embedding depuis base64
        /// </summary>
        public static float[] DeserializeEmbedding(string base64Embedding)
        {
            var bytes = Convert.FromBase64String(base64Embedding);
            var embedding = new float[bytes.Length / sizeof(float)];
            Buffer.BlockCopy(bytes, 0, embedding, 0, bytes.Length);
            return embedding;
        }

        /// <summary>
        /// Compresse un embedding (quantification simple)
        /// </summary>
        public static byte[] CompressEmbedding(float[] embedding, float scale = 127f)
        {
            return embedding.Select(x => (byte)Math.Max(0, Math.Min(255, (x * scale) + 128))).ToArray();
        }

        /// <summary>
        /// Décompresse un embedding
        /// </summary>
        public static float[] DecompressEmbedding(byte[] compressedEmbedding, float scale = 127f)
        {
            return compressedEmbedding.Select(b => (b - 128f) / scale).ToArray();
        }

        /// <summary>
        /// Calcule les statistiques d'un ensemble d'embeddings
        /// </summary>
        public static EmbeddingStatistics CalculateStatistics(IEnumerable<float[]> embeddings)
        {
            var embeddingsList = embeddings.ToList();
            if (!embeddingsList.Any())
                return new EmbeddingStatistics();

            var dimensions = embeddingsList.First().Length;
            var means = new double[dimensions];
            var variances = new double[dimensions];

            // Calculer les moyennes
            for (int i = 0; i < dimensions; i++)
            {
                means[i] = embeddingsList.Average(e => e[i]);
            }

            // Calculer les variances
            for (int i = 0; i < dimensions; i++)
            {
                variances[i] = embeddingsList.Average(e => Math.Pow(e[i] - means[i], 2));
            }

            return new EmbeddingStatistics
            {
                Count = embeddingsList.Count,
                Dimensions = dimensions,
                Means = means,
                Variances = variances,
                StandardDeviations = variances.Select(Math.Sqrt).ToArray()
            };
        }
    }

}
