{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.application\\handlers\\getuseranalysesqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.application\\handlers\\getuseranalysesqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\repositories\\documentanalysisrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\repositories\\documentanalysisrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.infrastructure\\services\\unifiedllmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|solutionrelative:src\\shared\\lexai.shared.infrastructure\\services\\unifiedllmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\data\\documentanalysisdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\data\\documentanalysisdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\recommendationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\recommendationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\riskassessmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\riskassessmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\clauseanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\clauseanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\azuredocumentextractionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\azuredocumentextractionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentanalysiscacheservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentanalysiscacheservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.application\\commands\\analyzedocumentcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.application\\commands\\analyzedocumentcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\controllers\\documentanalysiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\controllers\\documentanalysiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\configuration\\azuredocumentintelligencesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\configuration\\azuredocumentintelligencesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.infrastructure\\configuration\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|solutionrelative:src\\shared\\lexai.shared.infrastructure\\configuration\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "UnifiedLLMService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Services\\UnifiedLLMService.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Infrastructure\\Services\\UnifiedLLMService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Services\\UnifiedLLMService.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Infrastructure\\Services\\UnifiedLLMService.cs", "ViewState": "AgIAAEMBAAAAAAAAAAAiwFABAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:32:35.078Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "GetUserAnalysesQueryHandler.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAmwFcAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:21:23.061Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DocumentAnalysisRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAmwFsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:54:34.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "IdentityDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ViewState": "AgIAAB4BAAAAAAAAAAAMwCoBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:56:19.406Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DocumentStorageService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T21:10:07.236Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DocumentAnalysisDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "ViewState": "AgIAABgAAAAAAAAAAAAuwC8AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T21:11:39.552Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DocumentAnalysisService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "ViewState": "AgIAAP0BAAAAAAAAAAAuwA8CAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T16:07:32.7Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "RiskAssessmentService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "ViewState": "AgIAADcAAAAAAAAAAAApwEIAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:21:23.015Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "RecommendationService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "ViewState": "AgIAABoAAAAAAAAAAAD4vyUAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:21:23.026Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ClauseAnalysisService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "ViewState": "AgIAACkAAAAAAAAAAAAQwDwAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T18:22:35.736Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "DocumentAnalysisCacheService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisCacheService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisCacheService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisCacheService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisCacheService.cs", "ViewState": "AgIAAFUBAAAAAAAAAAAAAGUBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:19:55.98Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "AnalyzeDocumentCommand.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Commands\\AnalyzeDocumentCommand.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Commands\\AnalyzeDocumentCommand.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Commands\\AnalyzeDocumentCommand.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Commands\\AnalyzeDocumentCommand.cs", "ViewState": "AgIAACgAAAAAAAAAAAAgwDcAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T16:11:46.386Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AzureDocumentExtractionService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAQwI4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:56:19.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "DocumentAnalysisController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAAAGIAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T14:07:51.184Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "ViewState": "AgIAAEMAAAAAAAAAAAAAAFEAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T13:33:40.076Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "ViewState": "AgIAABIAAAAAAAAAAAAAADwAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T13:36:00.083Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "AzureDocumentIntelligenceSettings.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAAAFYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:19:52.331Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "JwtSettings.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAuwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:11:06.657Z", "EditorCaption": ""}]}]}]}