{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\conversation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\conversation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\chatsession.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\chatsession.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.infrastructure\\middleware\\jwtvalidationmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|solutionrelative:src\\shared\\lexai.shared.infrastructure\\middleware\\jwtvalidationmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\data\\aiassistantdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\data\\aiassistantdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.application\\commands\\searchcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C285458E-9FD9-42D3-88DC-EC9E4EC939F0}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\LexAI.LegalResearch.Application.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.application\\commands\\searchcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\LexAI.LegalResearch.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.domain\\entities\\documentchunk.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAA4EAF2-8662-495C-AC3C-FDBA17D8053A}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\LexAI.LegalResearch.Domain.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.domain\\entities\\documentchunk.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\LexAI.LegalResearch.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.infrastructure\\repositories\\searchqueryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\LexAI.LegalResearch.Infrastructure.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.infrastructure\\repositories\\searchqueryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\LexAI.LegalResearch.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.infrastructure\\repositories\\legaldocumentrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B0FAA6F0-A8BF-47F0-97EA-ECCD431F1C51}|src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\LexAI.LegalResearch.Infrastructure.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.infrastructure\\repositories\\legaldocumentrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.application\\handlers\\getuseranalysesqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.application\\handlers\\getuseranalysesqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\controllers\\documentanalysiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\controllers\\documentanalysiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.application\\handlers\\getanalysisresultqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2D9F005E-F801-4E76-8B5C-13588CF401D6}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\LexAI.DocumentAnalysis.Application.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.application\\handlers\\getanalysisresultqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\repositories\\conversationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\repositories\\conversationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.application\\commands\\chatcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.application\\commands\\chatcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\repositories\\documentanalysisrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\repositories\\documentanalysisrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\documentstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\data\\documentanalysisdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\data\\documentanalysisdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{32E1C28C-F033-49F3-A461-116855927092}|src\\Services\\Identity\\LexAI.Identity.Infrastructure\\LexAI.Identity.Infrastructure.csproj|solutionrelative:src\\services\\identity\\lexai.identity.infrastructure\\data\\identitydbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\recommendationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\recommendationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\riskassessmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\riskassessmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\clauseanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\clauseanalysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\azuredocumentextractionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\services\\azuredocumentextractionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\configuration\\azuredocumentintelligencesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\configuration\\azuredocumentintelligencesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\shared\\lexai.shared.infrastructure\\configuration\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2B3C4D5-E6F7-8901-1234-567890123456}|src\\Shared\\LexAI.Shared.Infrastructure\\LexAI.Shared.Infrastructure.csproj|solutionrelative:src\\shared\\lexai.shared.infrastructure\\configuration\\jwtsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "ViewState": "AgIAADwAAAAAAAAAAAAAABkAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T13:33:40.076Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-17T22:03:11.7Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "ViewState": "AgIAAAkAAAAAAAAAAAAAABwAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-17T20:56:15.761Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Conversation.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:49:19.656Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ChatSession.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\ChatSession.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\ChatSession.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\ChatSession.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\ChatSession.cs", "ViewState": "AgIAACEAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:49:05.892Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "JwtValidationMiddleware.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Middleware\\JwtValidationMiddleware.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Infrastructure\\Middleware\\JwtValidationMiddleware.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Middleware\\JwtValidationMiddleware.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Infrastructure\\Middleware\\JwtValidationMiddleware.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:42:24.141Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AIAssistantDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Data\\AIAssistantDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Data\\AIAssistantDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Data\\AIAssistantDbContext.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Data\\AIAssistantDbContext.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAAAGUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:35:48.276Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SearchCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Application\\Commands\\SearchCommands.cs", "ViewState": "AgIAANYAAAAAAAAAAAAuwA8BAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:15:42.163Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "DocumentChunk.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\Entities\\DocumentChunk.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\Entities\\DocumentChunk.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\Entities\\DocumentChunk.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Domain\\Entities\\DocumentChunk.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T21:15:26.744Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "LegalDocumentRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\LegalDocumentRepository.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\LegalDocumentRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\LegalDocumentRepository.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\LegalDocumentRepository.cs", "ViewState": "AgIAAI4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T19:16:44.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "SearchQueryRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\SearchQueryRepository.cs", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\SearchQueryRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\SearchQueryRepository.cs", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.Infrastructure\\Repositories\\SearchQueryRepository.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAuwK0AAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T19:17:11.333Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DocumentAnalysisController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Controllers\\DocumentAnalysisController.cs", "ViewState": "AgIAAKEAAAAAAAAAAAAAALAAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T14:07:51.184Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "GetAnalysisResultQueryHandler.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetAnalysisResultQueryHandler.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetAnalysisResultQueryHandler.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetAnalysisResultQueryHandler.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetAnalysisResultQueryHandler.cs", "ViewState": "AgIAAHQAAAAAAAAAAAAUwHoAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T19:55:53.309Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "DocumentAnalysisService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentAnalysisService.cs", "ViewState": "AgIAAGUCAAAAAAAAAAAmwG4CAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T16:07:32.7Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "GetUserAnalysesQueryHandler.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Application\\Handlers\\GetUserAnalysesQueryHandler.cs", "ViewState": "AgIAAD4AAAAAAAAAAAAAAE4AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:21:23.061Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "ViewState": "AgIAAFQAAAAAAAAAAAAAADwAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T13:36:00.083Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "ConversationRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "ViewState": "AgIAAIYAAAAAAAAAAAAAAIwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T17:10:35.382Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "ChatCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "ViewState": "AgIAAJIAAAAAAAAAAAAAAHwAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T18:50:13.51Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "DocumentStorageService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\DocumentStorageService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T21:10:07.236Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "IdentityDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.Infrastructure\\Data\\IdentityDbContext.cs", "ViewState": "AgIAAB4BAAAAAAAAAAAMwCoBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:56:19.406Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "DocumentAnalysisRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Repositories\\DocumentAnalysisRepository.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAmwGgAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:54:34.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "DocumentAnalysisDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "ViewState": "AgIAABgAAAAAAAAAAAAuwC8AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T21:11:39.552Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "RiskAssessmentService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RiskAssessmentService.cs", "ViewState": "AgIAADcAAAAAAAAAAAApwEIAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:21:23.015Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "RecommendationService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\RecommendationService.cs", "ViewState": "AgIAABoAAAAAAAAAAAD4vyUAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:21:23.026Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "ClauseAnalysisService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\ClauseAnalysisService.cs", "ViewState": "AgIAACkAAAAAAAAAAAAQwDwAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T18:22:35.736Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "AzureDocumentExtractionService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Services\\AzureDocumentExtractionService.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAQwI4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T13:56:19.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "AzureDocumentIntelligenceSettings.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Configuration\\AzureDocumentIntelligenceSettings.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAAAFYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:19:52.331Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "JwtSettings.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "RelativeDocumentMoniker": "src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "RelativeToolTip": "src\\Shared\\LexAI.Shared.Infrastructure\\Configuration\\JwtSettings.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAuwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T17:11:06.657Z", "EditorCaption": ""}]}]}]}