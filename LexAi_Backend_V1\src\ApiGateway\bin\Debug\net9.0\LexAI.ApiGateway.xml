<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.ApiGateway</name>
    </assembly>
    <members>
        <member name="T:LexAI.ApiGateway.Controllers.GatewayController">
            <summary>
            Contrôleur pour les informations de l'API Gateway
            </summary>
        </member>
        <member name="M:LexAI.ApiGateway.Controllers.GatewayController.#ctor(Microsoft.Extensions.Logging.ILogger{LexAI.ApiGateway.Controllers.GatewayController},Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Initialise une nouvelle instance du contrôleur Gateway
            </summary>
            <param name="logger">Logger pour les traces</param>
            <param name="configuration">Configuration de l'application</param>
        </member>
        <member name="M:LexAI.ApiGateway.Controllers.GatewayController.GetInfo">
            <summary>
            Obtient les informations de version et de santé de l'API Gateway
            </summary>
            <returns>Informations sur l'API Gateway</returns>
            <response code="200">Informations récupérées avec succès</response>
        </member>
        <member name="M:LexAI.ApiGateway.Controllers.GatewayController.GetServices">
            <summary>
            Obtient la liste des services disponibles via l'API Gateway
            </summary>
            <returns>Liste des services et leurs endpoints</returns>
            <response code="200">Liste des services récupérée avec succès</response>
        </member>
        <member name="M:LexAI.ApiGateway.Controllers.GatewayController.Ping">
            <summary>
            Endpoint de vérification de santé simple
            </summary>
            <returns>Statut de santé</returns>
            <response code="200">Service en bonne santé</response>
        </member>
        <member name="M:LexAI.ApiGateway.Controllers.GatewayController.GetMetrics">
            <summary>
            Obtient les métriques de base de l'API Gateway
            </summary>
            <returns>Métriques de performance</returns>
            <response code="200">Métriques récupérées avec succès</response>
        </member>
        <member name="T:LexAI.ApiGateway.Controllers.GatewayInfoResponse">
            <summary>
            Réponse contenant les informations de l'API Gateway
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.Name">
            <summary>
            Nom de l'API Gateway
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.Version">
            <summary>
            Version de l'API Gateway
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.BuildDate">
            <summary>
            Date de build
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.Environment">
            <summary>
            Environnement d'exécution
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.Status">
            <summary>
            Statut de santé
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.Timestamp">
            <summary>
            Timestamp de la réponse
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayInfoResponse.Services">
            <summary>
            Liste des services disponibles
            </summary>
        </member>
        <member name="T:LexAI.ApiGateway.Controllers.ServiceInfo">
            <summary>
            Informations sur un service
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.ServiceInfo.Name">
            <summary>
            Nom du service
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.ServiceInfo.Description">
            <summary>
            Description du service
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.ServiceInfo.BaseUrl">
            <summary>
            URL de base du service
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.ServiceInfo.Version">
            <summary>
            Version du service
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.ServiceInfo.SwaggerUrl">
            <summary>
            URL de la documentation Swagger
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.ServiceInfo.HealthCheckUrl">
            <summary>
            URL du health check
            </summary>
        </member>
        <member name="T:LexAI.ApiGateway.Controllers.PingResponse">
            <summary>
            Réponse du ping
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.PingResponse.Message">
            <summary>
            Message de réponse
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.PingResponse.Timestamp">
            <summary>
            Timestamp de la réponse
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.PingResponse.Version">
            <summary>
            Version du service
            </summary>
        </member>
        <member name="T:LexAI.ApiGateway.Controllers.GatewayMetrics">
            <summary>
            Métriques de l'API Gateway
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayMetrics.Uptime">
            <summary>
            Temps de fonctionnement
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayMetrics.MemoryUsage">
            <summary>
            Utilisation mémoire en bytes
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayMetrics.ProcessorCount">
            <summary>
            Nombre de processeurs
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayMetrics.WorkingSet">
            <summary>
            Working set en bytes
            </summary>
        </member>
        <member name="P:LexAI.ApiGateway.Controllers.GatewayMetrics.Timestamp">
            <summary>
            Timestamp des métriques
            </summary>
        </member>
    </members>
</doc>
