# Script de test automatisé pour les endpoints API
param(
    [string]$BaseUrl = "https://localhost:61113",
    [string]$Token = "",
    [string]$TestFile = ""
)

Write-Host "🧪 Test automatisé des endpoints API Data Preprocessing" -ForegroundColor Green

if ([string]::IsNullOrEmpty($Token)) {
    Write-Error "❌ Token JWT requis. Utilisez -Token 'votre_token_jwt'"
    exit 1
}

if ([string]::IsNullOrEmpty($TestFile) -or !(Test-Path $TestFile)) {
    Write-Warning "⚠️ Fichier de test non spécifié ou introuvable. Création d'un fichier de test..."
    
    # Créer un fichier de test simple
    $TestFile = "test-document.txt"
    $testContent = @"
CONTRAT DE TRAVAIL

Article 1 - Objet du contrat
Le présent contrat a pour objet l'engagement de M. Jean DUPONT en qualité d'employé au sein de la société ABC.

Article 2 - Durée du contrat
Le présent contrat est conclu pour une durée indéterminée à compter du 1er janvier 2025.

Article 3 - Rémunération
La rémunération mensuelle brute est fixée à 3000 euros.

Article 4 - Lieu de travail
Le lieu de travail est situé au 123 Rue de la Paix, 75001 Paris.
"@
    
    Set-Content -Path $TestFile -Value $testContent -Encoding UTF8
    Write-Host "✅ Fichier de test créé: $TestFile" -ForegroundColor Green
}

$headers = @{
    "Authorization" = "Bearer $Token"
    "Accept" = "application/json"
}

$testResults = @()

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Method,
        [string]$Url,
        [hashtable]$Headers,
        [object]$Body = $null,
        [string]$ContentType = "application/json"
    )
    
    Write-Host "🔍 Test: $Name" -ForegroundColor Cyan
    
    try {
        $requestParams = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
            TimeoutSec = 30
        }
        
        if ($Body) {
            if ($ContentType -eq "application/json") {
                $requestParams.Body = $Body | ConvertTo-Json -Depth 10
                $requestParams.ContentType = $ContentType
            } else {
                $requestParams.Body = $Body
                $requestParams.ContentType = $ContentType
            }
        }
        
        $response = Invoke-WebRequest @requestParams
        
        $result = @{
            Name = $Name
            Status = "✅ PASS"
            StatusCode = $response.StatusCode
            ResponseTime = "N/A"
            Details = "Success"
        }
        
        Write-Host "  ✅ $($response.StatusCode) - Success" -ForegroundColor Green
        
        if ($response.Content) {
            try {
                $jsonResponse = $response.Content | ConvertFrom-Json
                $result.Response = $jsonResponse
                
                # Afficher quelques détails de la réponse
                if ($jsonResponse.documentId) {
                    Write-Host "  📄 Document ID: $($jsonResponse.documentId)" -ForegroundColor Gray
                }
                if ($jsonResponse.fileName) {
                    Write-Host "  📁 File Name: $($jsonResponse.fileName)" -ForegroundColor Gray
                }
                if ($jsonResponse.status) {
                    Write-Host "  📊 Status: $($jsonResponse.status)" -ForegroundColor Gray
                }
            } catch {
                $result.Response = $response.Content
            }
        }
        
        return $result
    }
    catch {
        $result = @{
            Name = $Name
            Status = "❌ FAIL"
            StatusCode = $_.Exception.Response.StatusCode.value__
            Details = $_.Exception.Message
        }
        
        Write-Host "  ❌ $($result.StatusCode) - $($result.Details)" -ForegroundColor Red
        return $result
    }
}

Write-Host ""
Write-Host "📋 Démarrage des tests..." -ForegroundColor Yellow

# Test 1: Health Check
$testResults += Test-Endpoint -Name "Health Check" -Method "GET" -Url "$BaseUrl/health" -Headers @{}

# Test 2: Swagger
$testResults += Test-Endpoint -Name "Swagger UI" -Method "GET" -Url "$BaseUrl/swagger" -Headers @{}

# Test 3: Get User Documents (liste vide initialement)
$testResults += Test-Endpoint -Name "Get User Documents" -Method "GET" -Url "$BaseUrl/api/documents" -Headers $headers

# Test 4: Upload Document
Write-Host "📤 Préparation de l'upload..." -ForegroundColor Yellow

# Créer le contenu multipart pour l'upload
$boundary = [System.Guid]::NewGuid().ToString()
$fileBytes = [System.IO.File]::ReadAllBytes($TestFile)
$fileName = [System.IO.Path]::GetFileName($TestFile)

$bodyLines = @(
    "--$boundary",
    'Content-Disposition: form-data; name="file"; filename="' + $fileName + '"',
    'Content-Type: text/plain',
    '',
    [System.Text.Encoding]::UTF8.GetString($fileBytes),
    "--$boundary",
    'Content-Disposition: form-data; name="metadata"',
    '',
    '{"source": "api-test", "category": "legal"}',
    "--$boundary--"
)

$bodyString = $bodyLines -join "`r`n"
$bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($bodyString)

try {
    $uploadHeaders = $headers.Clone()
    $uploadHeaders["Content-Type"] = "multipart/form-data; boundary=$boundary"
    
    $uploadResponse = Invoke-WebRequest -Uri "$BaseUrl/api/documents/upload" -Method POST -Headers $uploadHeaders -Body $bodyBytes -UseBasicParsing -TimeoutSec 60
    
    $uploadResult = @{
        Name = "Upload Document"
        Status = "✅ PASS"
        StatusCode = $uploadResponse.StatusCode
        Details = "Upload successful"
    }
    
    $uploadData = $uploadResponse.Content | ConvertFrom-Json
    $documentId = $uploadData.documentId
    
    Write-Host "  ✅ $($uploadResponse.StatusCode) - Upload successful" -ForegroundColor Green
    Write-Host "  📄 Document ID: $documentId" -ForegroundColor Gray
    
    $testResults += $uploadResult
    
    # Test 5: Get Specific Document
    if ($documentId) {
        Start-Sleep -Seconds 2  # Attendre que le document soit sauvegardé
        $testResults += Test-Endpoint -Name "Get Specific Document" -Method "GET" -Url "$BaseUrl/api/documents/$documentId" -Headers $headers
        
        # Test 6: Get Processing Status
        $testResults += Test-Endpoint -Name "Get Processing Status" -Method "GET" -Url "$BaseUrl/api/documents/$documentId/status" -Headers $headers
        
        # Test 7: Start Processing
        $processingConfig = @{
            chunkingStrategy = "Semantic"
            chunkSize = 1000
            overlapSize = 100
            embeddingModel = "OpenAISmall"
            vectorDatabase = "MongoDB"
            enableQualityAssurance = $true
        }
        
        $testResults += Test-Endpoint -Name "Start Processing" -Method "POST" -Url "$BaseUrl/api/documents/$documentId/process" -Headers $headers -Body $processingConfig
    }
    
} catch {
    $uploadResult = @{
        Name = "Upload Document"
        Status = "❌ FAIL"
        StatusCode = $_.Exception.Response.StatusCode.value__
        Details = $_.Exception.Message
    }
    
    Write-Host "  ❌ Upload failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += $uploadResult
}

# Test 8: Test avec un ID invalide (doit retourner 404)
$fakeId = "00000000-0000-0000-0000-000000000000"
$testResults += Test-Endpoint -Name "Get Non-existent Document" -Method "GET" -Url "$BaseUrl/api/documents/$fakeId" -Headers $headers

Write-Host ""
Write-Host "📊 Résultats des tests:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

$passCount = 0
$failCount = 0

foreach ($result in $testResults) {
    $status = if ($result.Status.StartsWith("✅")) { 
        $passCount++
        $result.Status 
    } else { 
        $failCount++
        $result.Status 
    }
    
    Write-Host "$($result.Name): $status" -ForegroundColor $(if ($result.Status.StartsWith("✅")) { "Green" } else { "Red" })
    
    if ($result.StatusCode) {
        Write-Host "  Status Code: $($result.StatusCode)" -ForegroundColor Gray
    }
    
    if ($result.Details -and $result.Details -ne "Success") {
        Write-Host "  Details: $($result.Details)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "📈 Résumé:" -ForegroundColor Yellow
Write-Host "  ✅ Tests réussis: $passCount" -ForegroundColor Green
Write-Host "  ❌ Tests échoués: $failCount" -ForegroundColor Red
Write-Host "  📊 Taux de réussite: $([math]::Round(($passCount / $testResults.Count) * 100, 2))%" -ForegroundColor Cyan

# Nettoyer le fichier de test créé
if (Test-Path "test-document.txt") {
    Remove-Item "test-document.txt" -Force
    Write-Host "🧹 Fichier de test nettoyé" -ForegroundColor Gray
}

Write-Host ""
if ($failCount -eq 0) {
    Write-Host "🎉 Tous les tests sont passés avec succès !" -ForegroundColor Green
} else {
    Write-Host "⚠️ Certains tests ont échoué. Vérifiez les logs pour plus de détails." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "💡 Pour tester avec Hangfire:" -ForegroundColor Cyan
Write-Host "  1. Ouvrez http://localhost:5001/hangfire" -ForegroundColor White
Write-Host "  2. Connectez-vous avec admin/admin123" -ForegroundColor White
Write-Host "  3. Surveillez les jobs de traitement" -ForegroundColor White
