using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace LexAI.Identity.Infrastructure.Data;

/// <summary>
/// Design-time factory for creating IdentityDbContext instances during migrations
/// </summary>
public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<IdentityDbContext>
{
    /// <summary>
    /// Creates a new instance of IdentityDbContext for design-time operations
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Configured IdentityDbContext instance</returns>
    public IdentityDbContext CreateDbContext(string[] args)
    {
        // Build configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        // Get connection string
        var connectionString = configuration.GetConnectionString("PostgreSql") 
            ?? "Host=localhost;Port=5433;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!";

        // Configure DbContext options
        var optionsBuilder = new DbContextOptionsBuilder<IdentityDbContext>();
        optionsBuilder.UseNpgsql(connectionString, options =>
        {
            options.MigrationsAssembly(typeof(IdentityDbContext).Assembly.FullName);
        });

        return new IdentityDbContext(optionsBuilder.Options);
    }
}
