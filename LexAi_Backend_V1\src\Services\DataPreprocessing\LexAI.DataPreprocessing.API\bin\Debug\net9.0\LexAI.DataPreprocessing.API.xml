<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.DataPreprocessing.API</name>
    </assembly>
    <members>
        <member name="T:LexAI.DataPreprocessing.API.Controllers.DocumentsController">
            <summary>
            Controller for document processing operations
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.#ctor(MediatR.IMediator,LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent,LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.API.Controllers.DocumentsController})">
            <summary>
            Initializes a new instance of the DocumentsController
            </summary>
            <param name="mediator">MediatR mediator</param>
            <param name="orchestrationAgent">Orchestration agent</param>
            <param name="documentRepository">Document repository</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument(Microsoft.AspNetCore.Http.IFormFile,System.String)">
            <summary>
            Uploads a document for processing
            </summary>
            <param name="file">Document file</param>
            <param name="metadata">Optional metadata as JSON string</param>
            <returns>Upload response</returns>
            <response code="200">Document uploaded successfully</response>
            <response code="400">Invalid request</response>
            <response code="401">User not authenticated</response>
            <response code="413">File too large</response>
            <response code="429">Rate limit exceeded</response>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.ProcessDocument(System.Guid,LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto)">
            <summary>
            Processes a document through the pipeline
            </summary>
            <param name="documentId">Document ID to process</param>
            <param name="configuration">Processing configuration</param>
            <returns>Processing result</returns>
            <response code="200">Document processed successfully</response>
            <response code="400">Invalid request</response>
            <response code="404">Document not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.RetryDocumentProcessing(System.Guid,LexAI.DataPreprocessing.API.Controllers.RetryProcessingRequestDto)">
            <summary>
            Retries failed document processing
            </summary>
            <param name="documentId">Document ID to retry</param>
            <param name="request">Retry request</param>
            <returns>Processing result</returns>
            <response code="200">Document retry completed</response>
            <response code="400">Invalid request</response>
            <response code="404">Document not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetProcessingStatus(System.Guid)">
            <summary>
            Gets processing status for a document
            </summary>
            <param name="documentId">Document ID</param>
            <returns>Processing status</returns>
            <response code="200">Status retrieved successfully</response>
            <response code="404">Document not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.CancelDocumentProcessing(System.Guid,LexAI.DataPreprocessing.API.Controllers.CancelProcessingRequestDto)">
            <summary>
            Cancels document processing
            </summary>
            <param name="documentId">Document ID to cancel</param>
            <param name="request">Cancellation request</param>
            <returns>Cancellation result</returns>
            <response code="200">Processing cancelled successfully</response>
            <response code="400">Invalid request</response>
            <response code="404">Document not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument(System.Guid)">
            <summary>
            Gets a specific document by ID
            </summary>
            <param name="documentId">Document ID</param>
            <returns>Document details</returns>
            <response code="200">Document retrieved successfully</response>
            <response code="404">Document not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments(System.Int32,System.Int32)">
            <summary>
            Gets user's documents
            </summary>
            <param name="limit">Maximum number of documents</param>
            <param name="offset">Offset for pagination</param>
            <returns>User documents</returns>
            <response code="200">Documents retrieved successfully</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="T:LexAI.DataPreprocessing.API.Controllers.RetryProcessingRequestDto">
            <summary>
            Retry processing request DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.RetryProcessingRequestDto.FromStep">
            <summary>
            Step to retry from
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.API.Controllers.CancelProcessingRequestDto">
            <summary>
            Cancel processing request DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.CancelProcessingRequestDto.Reason">
            <summary>
            Cancellation reason
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto">
            <summary>
            Document summary DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.FileSize">
            <summary>
            File size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.MimeType">
            <summary>
            MIME type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.Status">
            <summary>
            Processing status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.CreatedBy">
            <summary>
            User who created the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.DetectedDomain">
            <summary>
            Detected legal domain
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.ClassificationConfidence">
            <summary>
            Classification confidence
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.ChunkCount">
            <summary>
            Number of chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.TotalTokens">
            <summary>
            Total tokens
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.IsVectorized">
            <summary>
            Whether vectorized
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.VectorDatabase">
            <summary>
            Vector database
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.ProcessingTime">
            <summary>
            Processing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.CreatedAt">
            <summary>
            Created timestamp
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto.UpdatedAt">
            <summary>
            Updated timestamp
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.API.DesignTimeDataPreprocessingDbContextFactory">
            <summary>
            Design-time factory for creating DataPreprocessingDbContext instances during migrations
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.API.DesignTimeDataPreprocessingDbContextFactory.CreateDbContext(System.String[])">
            <summary>
            Creates a new instance of DataPreprocessingDbContext for design-time operations
            </summary>
            <param name="args">Command line arguments</param>
            <returns>Configured DataPreprocessingDbContext instance</returns>
        </member>
        <member name="T:BackgroundJobs">
            <summary>
            Background jobs for the data preprocessing service
            </summary>
        </member>
        <member name="M:BackgroundJobs.CleanupFailedDocuments">
            <summary>
            Cleanup failed documents older than 7 days
            </summary>
        </member>
        <member name="M:BackgroundJobs.GenerateProcessingStatistics">
            <summary>
            Generate processing statistics
            </summary>
        </member>
        <member name="T:JwtSettings">
            <summary>
            JWT settings configuration
            </summary>
        </member>
    </members>
</doc>
