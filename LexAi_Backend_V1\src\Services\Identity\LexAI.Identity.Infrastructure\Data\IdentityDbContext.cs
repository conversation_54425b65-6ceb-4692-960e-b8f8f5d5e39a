using LexAI.Identity.Domain.Entities;
using LexAI.Shared.Domain.Common;
using Microsoft.EntityFrameworkCore;

namespace LexAI.Identity.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for Identity service
/// </summary>
public class IdentityDbContext : DbContext
{
    /// <summary>
    /// Users DbSet
    /// </summary>
    public DbSet<User> Users { get; set; } = null!;

    /// <summary>
    /// Refresh tokens DbSet
    /// </summary>
    public DbSet<RefreshToken> RefreshTokens { get; set; } = null!;

    /// <summary>
    /// User permissions DbSet
    /// </summary>
    public DbSet<UserPermission> UserPermissions { get; set; } = null!;

    /// <summary>
    /// Audit entries DbSet
    /// </summary>
    public DbSet<AuditEntry> AuditEntries { get; set; } = null!;

    /// <summary>
    /// Initializes a new instance of the IdentityDbContext
    /// </summary>
    /// <param name="options">DbContext options</param>
    public IdentityDbContext(DbContextOptions<IdentityDbContext> options) : base(options)
    {
         // Ajout d'un correctif pour éviter la vérification de xmin sur les entités Added
        ChangeTracker.Tracked += (sender, e) =>
        {
            if (e.Entry.Metadata.FindProperty("xmin") != null && e.Entry.State == EntityState.Added)
            {
                e.Entry.Property("xmin").IsTemporary = true;
            }
        };
    }

    /// <summary>
    /// Configures the model that was discovered by convention from the entity types
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure User entity
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("Users");
            entity.HasKey(e => e.Id);

            // Configure Email value object
            entity.OwnsOne(e => e.Email, email =>
            {
                email.Property(e => e.Value)
                    .HasColumnName("Email")
                    .HasMaxLength(254)
                    .IsRequired();

                email.HasIndex(e => e.Value)
                    .IsUnique()
                    .HasDatabaseName("IX_Users_Email");
            });

            // Configure PhoneNumber value object
            entity.OwnsOne(e => e.PhoneNumber, phone =>
            {
                phone.Property(e => e.Value)
                    .HasColumnName("PhoneNumber")
                    .HasMaxLength(20);

                phone.Property(e => e.CountryCode)
                    .HasColumnName("PhoneCountryCode")
                    .HasMaxLength(5);
            });

            // Configure properties
            entity.Property(e => e.FirstName)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.LastName)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.PasswordHash)
                .HasMaxLength(255)
                .IsRequired();

            entity.Property(e => e.Role)
                .HasConversion<string>()
                .HasMaxLength(20)
                .IsRequired();

            entity.Property(e => e.PreferredLanguage)
                .HasMaxLength(10)
                .HasDefaultValue("fr-FR");

            entity.Property(e => e.TimeZone)
                .HasMaxLength(50)
                .HasDefaultValue("Europe/Paris");

            entity.Property(e => e.ProfilePictureUrl)
                .HasMaxLength(500);

            entity.Property(e => e.LastLoginIpAddress)
                .HasMaxLength(45); // IPv6 max length

            // Configure audit properties
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(50);

            entity.Property(e => e.UpdatedBy)
                .HasMaxLength(50);

            entity.Property(e => e.DeletedBy)
                .HasMaxLength(50);

            // Configure indexes
            entity.HasIndex(e => e.Role)
                .HasDatabaseName("IX_Users_Role");

            entity.HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_Users_IsActive");

            entity.HasIndex(e => e.IsLocked)
                .HasDatabaseName("IX_Users_IsLocked");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("IX_Users_CreatedAt");

            // Configure relationships
            entity.HasMany(e => e.RefreshTokens)
                .WithOne(rt => rt.User)
                .HasForeignKey(rt => rt.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.Permissions)
                .WithOne(p => p.User)
                .HasForeignKey(p => p.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.AuditEntries)
                .WithOne()
                .HasForeignKey(ae => ae.EntityId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure global query filter for soft delete
            entity.HasQueryFilter(e => !e.IsDeleted);
        });

        // Configure RefreshToken entity
        modelBuilder.Entity<RefreshToken>(entity =>
        {
            entity.ToTable("RefreshTokens");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.Token)
                .HasMaxLength(255)
                .IsRequired();

            entity.Property(e => e.IpAddress)
                .HasMaxLength(45);

            entity.Property(e => e.UserAgent)
                .HasMaxLength(500);

            entity.Property(e => e.RevokedBy)
                .HasMaxLength(50);

            entity.Property(e => e.RevocationReason)
                .HasMaxLength(200);

            // Configure indexes
            entity.HasIndex(e => e.Token)
                .IsUnique()
                .HasDatabaseName("IX_RefreshTokens_Token");

            entity.HasIndex(e => e.UserId)
                .HasDatabaseName("IX_RefreshTokens_UserId");

            entity.HasIndex(e => e.ExpiresAt)
                .HasDatabaseName("IX_RefreshTokens_ExpiresAt");

            entity.HasIndex(e => e.IsRevoked)
                .HasDatabaseName("IX_RefreshTokens_IsRevoked");
        });

        // Configure UserPermission entity
        modelBuilder.Entity<UserPermission>(entity =>
        {
            entity.ToTable("UserPermissions");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.Permission)
                .HasMaxLength(100)
                .IsRequired();

            entity.Property(e => e.Resource)
                .HasMaxLength(100);

            entity.Property(e => e.Action)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.GrantedBy)
                .HasMaxLength(50)
                .IsRequired();

            // Configure indexes
            entity.HasIndex(e => new { e.UserId, e.Permission, e.Resource, e.Action })
                .IsUnique()
                .HasDatabaseName("IX_UserPermissions_Unique");

            entity.HasIndex(e => e.ExpiresAt)
                .HasDatabaseName("IX_UserPermissions_ExpiresAt");
        });

        // Configure AuditEntry entity
        modelBuilder.Entity<AuditEntry>(entity =>
        {
            entity.ToTable("AuditEntries");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.EntityType)
                .HasMaxLength(100)
                .IsRequired();

            entity.Property(e => e.Action)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.UserId)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.Changes)
                .HasColumnType("jsonb");

            entity.Property(e => e.IpAddress)
                .HasMaxLength(45);

            entity.Property(e => e.UserAgent)
                .HasMaxLength(500);

            // Configure indexes
            entity.HasIndex(e => e.EntityId)
                .HasDatabaseName("IX_AuditEntries_EntityId");

            entity.HasIndex(e => e.EntityType)
                .HasDatabaseName("IX_AuditEntries_EntityType");

            entity.HasIndex(e => e.Action)
                .HasDatabaseName("IX_AuditEntries_Action");

            entity.HasIndex(e => e.UserId)
                .HasDatabaseName("IX_AuditEntries_UserId");

            entity.HasIndex(e => e.Timestamp)
                .HasDatabaseName("IX_AuditEntries_Timestamp");
        });

        // Pour toutes les entités héritant de BaseEntity
        foreach (var entityType in modelBuilder.Model.GetEntityTypes()
            .Where(t => typeof(BaseEntity).IsAssignableFrom(t.ClrType)))
        {
            // On ne configure xmin comme propriété de concurrence que pour les entités qui ne sont pas ajoutées (Added)
            modelBuilder.Entity(entityType.ClrType)
                .Property<uint>("xmin")
                .IsConcurrencyToken()
                .HasColumnName("xmin")
                .ValueGeneratedOnAddOrUpdate();
        }

        base.OnModelCreating(modelBuilder);

    }

    /// <summary>
    /// Saves all changes made in this context to the database
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of state entries written to the database</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps for auditable entities
        var entries = ChangeTracker.Entries<BaseEntity>();
        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    // Correctif : si l'entité est une AuditEntry et que xmin == 0 (donc nouvellement ajoutée), on force l'état à Added
                    if (entry.Entity is AuditEntry auditEntry && entry.Property("xmin").CurrentValue is uint xmin && xmin == 0)
                    {
                        entry.State = EntityState.Added;
                    }
                    break;
            }
        }
        return await base.SaveChangesAsync(cancellationToken);
    }
}
