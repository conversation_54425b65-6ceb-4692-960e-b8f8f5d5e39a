import { BaseApiService } from '../api/base'
import type { 
  DocumentAnalysisRequest,
  DocumentAnalysisResponse,
  DocumentAnalysisListRequest,
  DocumentAnalysisListResponse,
  AnalysisOptions
} from './types'

const DOCUMENT_ANALYSIS_API_BASE_URL = import.meta.env.VITE_DOCUMENT_ANALYSIS_API_URL || 'http://localhost:51405'

class DocumentAnalysisApiService extends BaseApiService {
  constructor() {
    super(DOCUMENT_ANALYSIS_API_BASE_URL)
  }

  /**
   * Analyse un document juridique complet
   * CORRECTION: Utilise la méthode upload() qui gère correctement FormData
   */
  async analyzeDocument(request: DocumentAnalysisRequest): Promise<DocumentAnalysisResponse> {
    const formData = new FormData()

    // Nom du champ attendu par le backend
    formData.append('DocumentFile', request.documentFile)

    if (request.documentName) {
      formData.append('DocumentName', request.documentName)
    }

    if (request.options) {
      // Conversion camelCase vers PascalCase pour le backend
      const backendOptions = this.mapOptionsToBackend(request.options)
      formData.append('Options', JSON.stringify(backendOptions))
    }

    // Debug log pour vérifier le contenu
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('DocumentAnalysis Request:', {
        fileName: request.documentFile.name,
        fileSize: request.documentFile.size,
        documentName: request.documentName,
        options: request.options,
        formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
          key,
          value: value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value
        }))
      })
    }

    // Utilise la méthode upload() qui gère correctement FormData
    return this.upload('/api/v1/DocumentAnalysis/analyze', formData)
  }

  /**
   * Obtient le résultat d'une analyse par ID
   */
  async getAnalysisResult(analysisId: string): Promise<DocumentAnalysisResponse> {
    return this.get(`/api/v1/DocumentAnalysis/${analysisId}`)
  }

  /**
   * Obtient la liste des analyses pour l'utilisateur connecté
   */
  async getUserAnalyses(request: DocumentAnalysisListRequest = {}): Promise<DocumentAnalysisListResponse> {
    const params = new URLSearchParams()

    if (request.documentType) params.append('documentType', request.documentType)
    if (request.status) params.append('status', request.status)
    if (request.fromDate) params.append('fromDate', request.fromDate)
    if (request.toDate) params.append('toDate', request.toDate)
    if (request.page) params.append('page', request.page.toString())
    if (request.pageSize) params.append('pageSize', request.pageSize.toString())
    if (request.sortBy) params.append('sortBy', request.sortBy)
    if (request.sortDescending !== undefined) params.append('sortDescending', request.sortDescending.toString())

    return this.get(`/api/v1/DocumentAnalysis?${params.toString()}`)
  }

  /**
   * Régénère une analyse existante avec de nouvelles options
   */
  async regenerateAnalysis(analysisId: string, options?: AnalysisOptions): Promise<DocumentAnalysisResponse> {
    const analysisOptions = options || this.getDefaultOptions()
    const backendOptions = this.mapOptionsToBackend(analysisOptions)
    return this.post(`/api/v1/DocumentAnalysis/${analysisId}/regenerate`, backendOptions)
  }

  /**
   * Supprime une analyse
   */
  async deleteAnalysis(analysisId: string): Promise<void> {
    return this.delete(`/api/v1/DocumentAnalysis/${analysisId}`)
  }

  /**
   * Options d'analyse prédéfinies
   */
  getDefaultOptions(): AnalysisOptions {
    return {
      extractClauses: true,
      performRiskAssessment: true,
      generateRecommendations: true,
      extractEntities: true,
      findCitations: true,
      useAzureDocumentIntelligence: true,
      language: 'fr'
    }
  }

  /**
   * Options d'analyse rapide (moins de fonctionnalités)
   */
  getQuickAnalysisOptions(): AnalysisOptions {
    return {
      extractClauses: true,
      performRiskAssessment: true,
      generateRecommendations: false,
      extractEntities: false,
      findCitations: false,
      useAzureDocumentIntelligence: true,
      language: 'fr'
    }
  }

  /**
   * Options d'analyse complète (toutes les fonctionnalités)
   */
  getComprehensiveAnalysisOptions(): AnalysisOptions {
    return {
      extractClauses: true,
      performRiskAssessment: true,
      generateRecommendations: true,
      extractEntities: true,
      findCitations: true,
      useAzureDocumentIntelligence: true,
      language: 'fr'
    }
  }

  /**
   * Convertit les options frontend (camelCase) vers le format backend (PascalCase)
   */
  private mapOptionsToBackend(options: AnalysisOptions): any {
    return {
      ExtractClauses: options.extractClauses ?? true,
      PerformRiskAssessment: options.performRiskAssessment ?? true,
      GenerateRecommendations: options.generateRecommendations ?? true,
      ExtractEntities: options.extractEntities ?? true,
      FindCitations: options.findCitations ?? true,
      UseAzureDocumentIntelligence: options.useAzureDocumentIntelligence ?? true,
      SpecificAnalysisType: options.specificAnalysisType,
      FocusAreas: options.focusAreas || [],
      Language: options.language || 'fr'
    }
  }
}

export const documentAnalysisApi = new DocumentAnalysisApiService()
