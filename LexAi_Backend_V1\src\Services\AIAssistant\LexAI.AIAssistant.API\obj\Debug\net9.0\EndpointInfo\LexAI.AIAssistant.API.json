{"openapi": "3.0.1", "info": {"title": "LexAI AI Assistant Service API", "description": "Service d'assistant IA juridique pour LexAI", "contact": {"name": "LexAI Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Chat/message": {"post": {"tags": ["Cha<PERSON>"], "summary": "Sends a message to the AI assistant", "requestBody": {"description": "Chat request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}}}, "responses": {"200": {"description": "Message processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Chat/conversations/{conversationId}/messages": {"post": {"tags": ["Cha<PERSON>"], "summary": "Continues an existing conversation", "parameters": [{"name": "conversationId", "in": "path", "description": "Conversation ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Message request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContinueConversationRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContinueConversationRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContinueConversationRequestDto"}}}}, "responses": {"200": {"description": "Message processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Conversation not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Chat/analyze-document": {"post": {"tags": ["Cha<PERSON>"], "summary": "Analyzes a legal document", "requestBody": {"description": "Document analysis request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisRequestDto"}}}}, "responses": {"200": {"description": "Document analyzed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentAnalysisResponseDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Chat/legal-research": {"post": {"tags": ["Cha<PERSON>"], "summary": "Performs legal research", "requestBody": {"description": "Legal research request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LegalResearchRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LegalResearchRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LegalResearchRequestDto"}}}}, "responses": {"200": {"description": "Research completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LegalResearchResponseDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Chat/generate-document": {"post": {"tags": ["Cha<PERSON>"], "summary": "Generates a legal document", "requestBody": {"description": "Document generation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentGenerationRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DocumentGenerationRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DocumentGenerationRequestDto"}}}}, "responses": {"200": {"description": "Document generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentGenerationResponseDto"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Chat/messages/{messageId}/rate": {"post": {"tags": ["Cha<PERSON>"], "summary": "Rates a message", "parameters": [{"name": "messageId", "in": "path", "description": "Message ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Rating request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RateMessageRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RateMessageRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RateMessageRequestDto"}}}}, "responses": {"200": {"description": "Message rated successfully", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Message not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}}, "components": {"schemas": {"AIModelType": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "AttachmentType": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "ChatRequestDto": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "sessionId": {"type": "string", "nullable": true}, "conversationId": {"type": "string", "format": "uuid", "nullable": true}, "context": {"$ref": "#/components/schemas/ConversationContextDto"}, "includeLegalResearch": {"type": "boolean"}, "includeCitations": {"type": "boolean"}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/MessageAttachmentDto"}, "nullable": true}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ChatResponseDto": {"type": "object", "properties": {"conversationId": {"type": "string", "format": "uuid"}, "response": {"type": "string", "nullable": true}, "messageId": {"type": "string", "format": "uuid"}, "responseType": {"$ref": "#/components/schemas/MessageType"}, "detectedIntent": {"$ref": "#/components/schemas/MessageIntent"}, "detectedDomain": {"$ref": "#/components/schemas/LegalDomain"}, "confidenceScore": {"type": "number", "format": "double", "nullable": true}, "citations": {"type": "array", "items": {"$ref": "#/components/schemas/CitationDto"}, "nullable": true}, "relatedDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/LegalDocumentSummaryDto"}, "nullable": true}, "followUpQuestions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}, "tokensUsed": {"type": "integer", "format": "int32"}, "estimatedCost": {"type": "number", "format": "double"}, "quality": {"$ref": "#/components/schemas/ResponseQuality"}, "isCached": {"type": "boolean"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "CitationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"$ref": "#/components/schemas/CitationType"}, "title": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "format": "date-time", "nullable": true}, "relevanceScore": {"type": "number", "format": "double"}, "excerpt": {"type": "string", "nullable": true}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "CitationType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "ContinueConversationRequestDto": {"type": "object", "properties": {"message": {"type": "string", "description": "User message", "nullable": true}}, "additionalProperties": false, "description": "Continue conversation request DTO"}, "ConversationContextDto": {"type": "object", "properties": {"modelType": {"$ref": "#/components/schemas/AIModelType"}, "mode": {"$ref": "#/components/schemas/ConversationMode"}, "systemPrompt": {"type": "string", "nullable": true}, "maxTokens": {"type": "integer", "format": "int32"}, "temperature": {"type": "number", "format": "double"}, "includeLegalResearch": {"type": "boolean"}, "includeCitations": {"type": "boolean"}, "language": {"type": "string", "nullable": true}, "jurisdiction": {"type": "string", "nullable": true}, "userRole": {"type": "string", "nullable": true}, "preferences": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ConversationMode": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "DocumentAnalysisRequestDto": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "documentName": {"type": "string", "nullable": true}, "documentContent": {"type": "string", "nullable": true}, "documentType": {"type": "string", "nullable": true}, "focusAreas": {"type": "array", "items": {"type": "string"}, "nullable": true}, "context": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DocumentAnalysisResponseDto": {"type": "object", "properties": {"documentName": {"type": "string", "nullable": true}, "analysis": {"type": "string", "nullable": true}, "keyFindings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "riskAssessment": {"type": "string", "nullable": true}, "recommendations": {"type": "array", "items": {"type": "string"}, "nullable": true}, "citations": {"type": "array", "items": {"$ref": "#/components/schemas/CitationDto"}, "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}, "tokensUsed": {"type": "integer", "format": "int32"}, "estimatedCost": {"type": "number", "format": "double"}}, "additionalProperties": false}, "DocumentGenerationRequestDto": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "documentType": {"type": "string", "nullable": true}, "requirements": {"type": "string", "nullable": true}, "template": {"type": "string", "nullable": true}, "jurisdiction": {"type": "string", "nullable": true}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "DocumentGenerationResponseDto": {"type": "object", "properties": {"documentType": {"type": "string", "nullable": true}, "generatedContent": {"type": "string", "nullable": true}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}, "tokensUsed": {"type": "integer", "format": "int32"}, "estimatedCost": {"type": "number", "format": "double"}}, "additionalProperties": false}, "LegalDocumentSummaryDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "format": "date-time", "nullable": true}, "relevanceScore": {"type": "number", "format": "double"}, "domain": {"$ref": "#/components/schemas/LegalDomain"}}, "additionalProperties": false}, "LegalDomain": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], "type": "integer", "format": "int32"}, "LegalResearchRequestDto": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "query": {"type": "string", "nullable": true}, "domain": {"$ref": "#/components/schemas/LegalDomain"}, "jurisdiction": {"type": "string", "nullable": true}, "depth": {"$ref": "#/components/schemas/ResearchDepth"}, "includeCaseLaw": {"type": "boolean"}, "includeLegislation": {"type": "boolean"}, "includeAcademicSources": {"type": "boolean"}, "maxResults": {"type": "integer", "format": "int32"}, "includeHighlights": {"type": "boolean"}}, "additionalProperties": false}, "LegalResearchResponseDto": {"type": "object", "properties": {"query": {"type": "string", "nullable": true}, "research": {"type": "string", "nullable": true}, "keyInsights": {"type": "array", "items": {"type": "string"}, "nullable": true}, "relevantDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/LegalDocumentSummaryDto"}, "nullable": true}, "citations": {"type": "array", "items": {"$ref": "#/components/schemas/CitationDto"}, "nullable": true}, "processingTimeMs": {"type": "integer", "format": "int64"}, "tokensUsed": {"type": "integer", "format": "int32"}, "estimatedCost": {"type": "number", "format": "double"}, "generatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "MessageAttachmentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fileName": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "mimeType": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/AttachmentType"}, "url": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "uploadedAt": {"type": "string", "format": "date-time"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "MessageIntent": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "type": "integer", "format": "int32"}, "MessageType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8], "type": "integer", "format": "int32"}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "RateMessageRequestDto": {"type": "object", "properties": {"rating": {"type": "integer", "description": "Rating (1-5)", "format": "int32"}, "feedback": {"type": "string", "description": "Optional feedback", "nullable": true}}, "additionalProperties": false, "description": "Rate message request DTO"}, "ResearchDepth": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "ResponseQuality": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}