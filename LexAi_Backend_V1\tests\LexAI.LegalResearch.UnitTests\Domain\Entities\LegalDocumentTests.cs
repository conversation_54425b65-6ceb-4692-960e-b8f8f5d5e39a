using FluentAssertions;
using LexAI.LegalResearch.Domain.Entities;
using LexAI.LegalResearch.Domain.ValueObjects;
using Xunit;

namespace LexAI.LegalResearch.UnitTests.Domain.Entities;

/// <summary>
/// Unit tests for LegalDocument entity
/// </summary>
public class LegalDocumentTests
{
    [Fact]
    public void Create_WithValidParameters_ShouldCreateDocument()
    {
        // Arrange
        var title = "Code du travail - Article L1221-1";
        var content = "Le contrat de travail à durée indéterminée est la forme normale et générale de la relation de travail.";
        var type = DocumentType.Law;
        var domain = LegalDomain.Labor;
        var source = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/codes/article_lc/LEGIARTI000006900785");
        var createdBy = "system";

        // Act
        var document = LegalDocument.Create(title, content, type, domain, source, createdBy);

        // Assert
        document.Should().NotBeNull();
        document.Id.Should().NotBeEmpty();
        document.Title.Should().Be(title);
        document.Content.Should().Be(content);
        document.Type.Should().Be(type);
        document.Domain.Should().Be(domain);
        document.Source.Should().Be(source);
        document.CreatedBy.Should().Be(createdBy);
        document.Status.Should().Be(DocumentStatus.Draft);
        document.Priority.Should().Be(DocumentPriority.Normal);
        document.Language.Should().Be("fr");
        document.IsIndexed.Should().BeFalse();
        document.Summary.Should().NotBeEmpty();
        document.Metadata.Should().NotBeNull();
        document.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidTitle_ShouldThrowArgumentException(string invalidTitle)
    {
        // Arrange
        var content = "Valid content";
        var type = DocumentType.Law;
        var domain = LegalDomain.Labor;
        var source = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");
        var createdBy = "system";

        // Act & Assert
        var action = () => LegalDocument.Create(invalidTitle, content, type, domain, source, createdBy);
        action.Should().Throw<ArgumentException>().WithMessage("Title cannot be empty*");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void Create_WithInvalidContent_ShouldThrowArgumentException(string invalidContent)
    {
        // Arrange
        var title = "Valid title";
        var type = DocumentType.Law;
        var domain = LegalDomain.Labor;
        var source = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");
        var createdBy = "system";

        // Act & Assert
        var action = () => LegalDocument.Create(title, invalidContent, type, domain, source, createdBy);
        action.Should().Throw<ArgumentException>().WithMessage("Content cannot be empty*");
    }

    [Fact]
    public void UpdateContent_WithValidContent_ShouldUpdateContentAndResetIndexing()
    {
        // Arrange
        var document = CreateValidDocument();
        document.MarkAsIndexed();
        var newContent = "Updated content for the legal document.";
        var updatedBy = "user123";

        // Act
        document.UpdateContent(newContent, updatedBy);

        // Assert
        document.Content.Should().Be(newContent);
        document.UpdatedBy.Should().Be(updatedBy);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        document.IsIndexed.Should().BeFalse();
        document.IndexedAt.Should().BeNull();
        document.Chunks.Should().BeEmpty();
        document.Summary.Should().Be(newContent); // Short content becomes full summary
    }

    [Fact]
    public void UpdateContent_WithLongContent_ShouldTruncateSummary()
    {
        // Arrange
        var document = CreateValidDocument();
        var longContent = new string('A', 600); // 600 characters
        var updatedBy = "user123";

        // Act
        document.UpdateContent(longContent, updatedBy);

        // Assert
        document.Content.Should().Be(longContent);
        document.Summary.Should().HaveLength(503); // 500 + "..."
        document.Summary.Should().EndWith("...");
    }

    [Fact]
    public void UpdateMetadata_WithValidData_ShouldUpdateMetadata()
    {
        // Arrange
        var document = CreateValidDocument();
        var newTitle = "Updated Title";
        var newType = DocumentType.Regulation;
        var newDomain = LegalDomain.Administrative;
        var publicationDate = new DateTime(2023, 1, 1);
        var effectiveDate = new DateTime(2023, 2, 1);
        var updatedBy = "user123";

        // Act
        document.UpdateMetadata(newTitle, newType, newDomain, publicationDate, effectiveDate, updatedBy);

        // Assert
        document.Title.Should().Be(newTitle);
        document.Type.Should().Be(newType);
        document.Domain.Should().Be(newDomain);
        document.PublicationDate.Should().Be(publicationDate);
        document.EffectiveDate.Should().Be(effectiveDate);
        document.UpdatedBy.Should().Be(updatedBy);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void AddTags_WithValidTags_ShouldAddNormalizedTags()
    {
        // Arrange
        var document = CreateValidDocument();
        var tags = new[] { "Contract", "EMPLOYMENT", "  labor  ", "Contract" }; // Duplicates and mixed case

        // Act
        document.AddTags(tags);

        // Assert
        document.Tags.Should().HaveCount(3);
        document.Tags.Should().Contain("contract");
        document.Tags.Should().Contain("employment");
        document.Tags.Should().Contain("labor");
    }

    [Fact]
    public void RemoveTags_WithExistingTags_ShouldRemoveTags()
    {
        // Arrange
        var document = CreateValidDocument();
        document.AddTags("contract", "employment", "labor");

        // Act
        document.RemoveTags("employment", "nonexistent");

        // Assert
        document.Tags.Should().HaveCount(2);
        document.Tags.Should().Contain("contract");
        document.Tags.Should().Contain("labor");
        document.Tags.Should().NotContain("employment");
    }

    [Fact]
    public void AddKeywords_WithValidKeywords_ShouldAddNormalizedKeywords()
    {
        // Arrange
        var document = CreateValidDocument();
        var keywords = new[] { "CDI", "contrat", "  TRAVAIL  ", "cdi" }; // Duplicates and mixed case

        // Act
        document.AddKeywords(keywords);

        // Assert
        document.Keywords.Should().HaveCount(3);
        document.Keywords.Should().Contain("cdi");
        document.Keywords.Should().Contain("contrat");
        document.Keywords.Should().Contain("travail");
    }

    [Fact]
    public void AddReference_WithValidReference_ShouldAddReference()
    {
        // Arrange
        var document = CreateValidDocument();
        var referencedDocId = Guid.NewGuid();
        var reference = DocumentReference.Create(referencedDocId, "Related", "Related document");

        // Act
        document.AddReference(reference);

        // Assert
        document.References.Should().HaveCount(1);
        document.References.First().Should().Be(reference);
    }

    [Fact]
    public void AddReference_WithDuplicateReference_ShouldNotAddDuplicate()
    {
        // Arrange
        var document = CreateValidDocument();
        var referencedDocId = Guid.NewGuid();
        var reference1 = DocumentReference.Create(referencedDocId, "Related", "First reference");
        var reference2 = DocumentReference.Create(referencedDocId, "Related", "Second reference");

        // Act
        document.AddReference(reference1);
        document.AddReference(reference2);

        // Assert
        document.References.Should().HaveCount(1);
    }

    [Fact]
    public void MarkAsIndexed_ShouldSetIndexedProperties()
    {
        // Arrange
        var document = CreateValidDocument();

        // Act
        document.MarkAsIndexed();

        // Assert
        document.IsIndexed.Should().BeTrue();
        document.IndexedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void RecordAccess_ShouldIncrementAccessCountAndUpdateTimestamp()
    {
        // Arrange
        var document = CreateValidDocument();
        var initialAccessCount = document.AccessCount;

        // Act
        document.RecordAccess();

        // Assert
        document.AccessCount.Should().Be(initialAccessCount + 1);
        document.LastAccessedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void UpdateStatus_WithValidStatus_ShouldUpdateStatus()
    {
        // Arrange
        var document = CreateValidDocument();
        var newStatus = DocumentStatus.Published;
        var updatedBy = "user123";

        // Act
        document.UpdateStatus(newStatus, updatedBy);

        // Assert
        document.Status.Should().Be(newStatus);
        document.UpdatedBy.Should().Be(updatedBy);
        document.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SetFileInfo_WithValidInfo_ShouldSetFileProperties()
    {
        // Arrange
        var document = CreateValidDocument();
        var filePath = "/documents/test.pdf";
        var fileSize = 1024L;
        var fileHash = "abc123";

        // Act
        document.SetFileInfo(filePath, fileSize, fileHash);

        // Assert
        document.FilePath.Should().Be(filePath);
        document.FileSize.Should().Be(fileSize);
        document.FileHash.Should().Be(fileHash);
    }

    [Fact]
    public void IsActive_WithPublishedStatusAndNoExpiration_ShouldReturnTrue()
    {
        // Arrange
        var document = CreateValidDocument();
        document.UpdateStatus(DocumentStatus.Published, "system");

        // Act
        var isActive = document.IsActive();

        // Assert
        isActive.Should().BeTrue();
    }

    [Fact]
    public void IsActive_WithDraftStatus_ShouldReturnFalse()
    {
        // Arrange
        var document = CreateValidDocument(); // Default status is Draft

        // Act
        var isActive = document.IsActive();

        // Assert
        isActive.Should().BeFalse();
    }

    [Fact]
    public void IsExpired_WithFutureExpirationDate_ShouldReturnFalse()
    {
        // Arrange
        var document = CreateValidDocument();
        // Set expiration date to future using reflection or by updating metadata
        var futureDate = DateTime.UtcNow.AddDays(30);

        // Act
        var isExpired = document.IsExpired();

        // Assert
        isExpired.Should().BeFalse();
    }

    [Fact]
    public void IsExpired_WithNoExpirationDate_ShouldReturnFalse()
    {
        // Arrange
        var document = CreateValidDocument();

        // Act
        var isExpired = document.IsExpired();

        // Assert
        isExpired.Should().BeFalse();
    }

    private static LegalDocument CreateValidDocument()
    {
        var title = "Test Document";
        var content = "Test content for the legal document.";
        var type = DocumentType.Law;
        var domain = LegalDomain.Labor;
        var source = DocumentSource.CreateLegifrance("https://legifrance.gouv.fr/test");
        var createdBy = "system";

        return LegalDocument.Create(title, content, type, domain, source, createdBy);
    }
}
