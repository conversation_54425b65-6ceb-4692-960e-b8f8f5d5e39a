# 🚀 Guide de Démarrage Rapide - LexAI

Ce guide vous permettra de démarrer rapidement l'architecture microservices LexAI en mode développement.

## ✅ Prérequis

Avant de commencer, assurez-vous d'avoir installé :

- **.NET 9 SDK** - [Télécharger ici](https://dotnet.microsoft.com/download/dotnet/9.0)
- **Docker Desktop** - [Télécharger ici](https://www.docker.com/products/docker-desktop)
- **Git** pour cloner le repository
- **Visual Studio 2022** ou **VS Code** (recommandé)

## 🔧 Installation et Configuration

### 1. <PERSON><PERSON><PERSON> le Repository

```bash
git clone <votre-repository-url>
cd LexAi_Backend_V1
```

### 2. Configurer les Variables d'Environnement

```bash
# Copier le fichier d'exemple
cp .env.example .env

# Éditer le fichier .env avec vos clés API
# Minimum requis pour démarrer :
# OPENAI_API_KEY=your-openai-api-key-here
```

### 3. Démarrer l'Infrastructure

```bash
# Démarrer PostgreSQL, MongoDB, Redis, RabbitMQ
docker-compose up -d postgres mongodb redis rabbitmq

# Vérifier que tous les services sont démarrés
docker-compose ps
```

### 4. Attendre que l'Infrastructure soit Prête

```bash
# Vérifier les logs pour s'assurer que tout est prêt
docker-compose logs -f

# Attendre environ 30-60 secondes pour l'initialisation complète
```

### 5. Restaurer les Packages NuGet

```bash
# Restaurer tous les packages de la solution
dotnet restore
```

### 6. Construire la Solution

```bash
# Construire toute la solution
dotnet build
```

## 🏃‍♂️ Démarrage des Services

### Option 1: Démarrage avec Docker (Recommandé)

```bash
# Construire et démarrer tous les services
docker-compose up --build

# Ou en arrière-plan
docker-compose up -d --build
```

### Option 2: Démarrage Manuel pour le Développement

```bash
# Terminal 1 - API Gateway
dotnet run --project src/ApiGateway

# Terminal 2 - Identity Service (quand il sera créé)
# dotnet run --project src/Services/Identity/LexAI.Identity.API

# Terminal 3 - Legal Research Service (quand il sera créé)
# dotnet run --project src/Services/LegalResearch/LexAI.LegalResearch.API
```

## 🧪 Vérification de l'Installation

### 1. Vérifier l'API Gateway

```bash
# Test de ping
curl http://localhost:8080/api/gateway/ping

# Informations du gateway
curl http://localhost:8080/api/gateway/info

# Liste des services
curl http://localhost:8080/api/gateway/services
```

### 2. Vérifier les Health Checks

```bash
# Health check global
curl http://localhost:8080/health

# Health check détaillé
curl http://localhost:8080/health/ready
```

### 3. Accéder à la Documentation Swagger

Ouvrez votre navigateur et allez à :
- **API Gateway**: http://localhost:8080/swagger
- **Documentation complète**: http://localhost:8080/swagger/index.html

### 4. Vérifier les Bases de Données

```bash
# PostgreSQL
docker exec -it lexai-postgres psql -U lexai_user -d lexai_db -c "\l"

# MongoDB
docker exec -it lexai-mongodb mongosh --eval "show dbs"

# Redis
docker exec -it lexai-redis redis-cli ping

# RabbitMQ Management UI
# Ouvrir http://localhost:15672
# Username: lexai_user
# Password: lexai_rabbitmq_password_2024!
```

## 📊 Interfaces de Monitoring

### RabbitMQ Management
- **URL**: http://localhost:15672
- **Username**: lexai_user
- **Password**: lexai_rabbitmq_password_2024!

### Logs en Temps Réel

```bash
# Logs de tous les services
docker-compose logs -f

# Logs d'un service spécifique
docker-compose logs -f api-gateway
docker-compose logs -f postgres
docker-compose logs -f mongodb
```

## 🔍 Tests de Base

### 1. Test de l'API Gateway

```bash
# Test de connectivité
curl -X GET "http://localhost:8080/api/gateway/ping" \
  -H "accept: application/json"

# Test des métriques
curl -X GET "http://localhost:8080/api/gateway/metrics" \
  -H "accept: application/json"
```

### 2. Test des Bases de Données

```bash
# Test PostgreSQL
docker exec lexai-postgres pg_isready -U lexai_user -d lexai_db

# Test MongoDB
docker exec lexai-mongodb mongosh --eval "db.adminCommand('ping')"

# Test Redis
docker exec lexai-redis redis-cli ping
```

## 🛠️ Développement

### Structure des Projets

```
src/
├── ApiGateway/              # ✅ Créé - Point d'entrée principal
├── Services/
│   ├── Identity/           # 🚧 En cours - Service d'authentification
│   ├── LegalResearch/      # ⏳ À créer - Service de recherche juridique
│   ├── AIAssistant/        # ⏳ À créer - Service chatbot IA
│   ├── DocumentAnalysis/   # ⏳ À créer - Service d'analyse de documents
│   ├── DocumentGenerator/  # ⏳ À créer - Service de génération de documents
│   └── ClientManagement/   # ⏳ À créer - Service de gestion clients
└── Shared/                 # ✅ Créé - Bibliothèques partagées
    ├── Domain/            # ✅ Créé - Entités communes
    └── Infrastructure/    # ✅ Créé - Services d'infrastructure
```

### Commandes Utiles

```bash
# Reconstruire un service spécifique
docker-compose build api-gateway

# Redémarrer un service
docker-compose restart api-gateway

# Voir les logs d'un service
docker-compose logs -f api-gateway

# Arrêter tous les services
docker-compose down

# Arrêter et supprimer les volumes (⚠️ Perte de données)
docker-compose down -v
```

## 🐛 Dépannage

### Problèmes Courants

1. **Port déjà utilisé**
   ```bash
   # Vérifier les ports utilisés
   netstat -tulpn | grep :8080
   
   # Arrêter le processus si nécessaire
   sudo kill -9 <PID>
   ```

2. **Problème de connexion à la base de données**
   ```bash
   # Vérifier que PostgreSQL est démarré
   docker-compose ps postgres
   
   # Redémarrer PostgreSQL
   docker-compose restart postgres
   ```

3. **Problème de permissions Docker**
   ```bash
   # Sur Linux, ajouter l'utilisateur au groupe docker
   sudo usermod -aG docker $USER
   
   # Redémarrer la session
   ```

### Logs de Debug

```bash
# Activer les logs détaillés
export ASPNETCORE_ENVIRONMENT=Development

# Logs avec plus de détails
docker-compose logs -f --tail=100
```

## 📚 Prochaines Étapes

1. **Créer le Service Identity** - Authentification et gestion des utilisateurs
2. **Créer le Service Legal Research** - Recherche juridique avec IA
3. **Créer le Service AI Assistant** - Chatbot juridique
4. **Implémenter les tests unitaires et d'intégration**
5. **Configurer le CI/CD**

## 🆘 Support

Si vous rencontrez des problèmes :

1. Vérifiez les logs : `docker-compose logs -f`
2. Vérifiez la documentation dans `/docs/`
3. Vérifiez les issues GitHub
4. Contactez l'équipe de développement

## 🎉 Félicitations !

Vous avez maintenant une architecture microservices LexAI fonctionnelle ! 

L'API Gateway est accessible à http://localhost:8080 et la documentation Swagger à http://localhost:8080/swagger.
