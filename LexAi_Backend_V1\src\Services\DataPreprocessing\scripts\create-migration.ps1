﻿# Script pour créer une migration Entity Framework
param(
    [Parameter(Mandatory=$true)]
    [string]$MigrationName
)

Write-Host "Création de la migration '$MigrationName' pour le service Data Preprocessing..." -ForegroundColor Green

# Naviguer vers le répertoire du projet API
$apiProject = "src/Services/DataPreprocessing/LexAI.DataPreprocessing.API"
$infrastructureProject = "src/Services/DataPreprocessing/LexAI.DataPreprocessing.Infrastructure"

if (!(Test-Path $apiProject)) {
    Write-Error "Le projet API n'existe pas : $apiProject"
    exit 1
}

if (!(Test-Path $infrastructureProject)) {
    Write-Error "Le projet Infrastructure n'existe pas : $infrastructureProject"
    exit 1
}

try {
    # Créer la migration
    Write-Host "Création de la migration..." -ForegroundColor Yellow
    dotnet ef migrations add $MigrationName `
        --project $infrastructureProject `
        --startup-project $apiProject `
        --output-dir Data/Migrations

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Migration '$MigrationName' créée avec succès !" -ForegroundColor Green
        Write-Host ""
        Write-Host "Pour appliquer la migration :" -ForegroundColor Cyan
        Write-Host "  dotnet ef database update --project $infrastructureProject --startup-project $apiProject" -ForegroundColor White
        Write-Host ""
        Write-Host "Pour générer un script SQL :" -ForegroundColor Cyan
        Write-Host "  dotnet ef migrations script --project $infrastructureProject --startup-project $apiProject" -ForegroundColor White
    } else {
        Write-Error "Erreur lors de la création de la migration"
        exit 1
    }
}
catch {
    Write-Error "Erreur lors de la création de la migration : $_"
    exit 1
}
