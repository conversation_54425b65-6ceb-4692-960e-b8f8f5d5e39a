2025-06-19 00:30:52.981 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-19 00:30:53.209 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:30:54.200 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-19 00:30:55.088 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-19 00:30:57.016 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-19 00:30:57.143 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:30:57.145 +04:00 [INF] Hosting environment: Development
2025-06-19 00:30:57.193 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-19 00:30:57.486 +04:00 [INF] Request GET / started with correlation ID 22df19b1-b9bb-4d6f-a3d6-bfdaba307887
2025-06-19 00:30:57.696 +04:00 [INF] Request GET / completed in 161ms with status 404 (Correlation ID: 22df19b1-b9bb-4d6f-a3d6-bfdaba307887)
2025-06-19 00:30:57.713 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 700.0959ms
2025-06-19 00:30:57.893 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-19 00:31:35.916 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:31:35.916 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:31:35.974 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 45badb80-230a-43b0-a859-588489243d6b
2025-06-19 00:31:35.974 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID a5e3d2ee-0dff-4779-9321-a8aef6f6636a
2025-06-19 00:31:36.018 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:31:36.018 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:31:36.027 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 10ms with status 204 (Correlation ID: a5e3d2ee-0dff-4779-9321-a8aef6f6636a)
2025-06-19 00:31:36.027 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 15ms with status 204 (Correlation ID: 45badb80-230a-43b0-a859-588489243d6b)
2025-06-19 00:31:36.035 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 118.2638ms
2025-06-19 00:31:36.042 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 137.7254ms
2025-06-19 00:31:36.043 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:31:36.064 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID d4fe793b-71f1-4ed3-b04f-b7295abe61b9
2025-06-19 00:31:36.069 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:31:36.183 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:31:36.192 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:31:36.219 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:31:36.267 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:31:36.851 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:31:36.855 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:31:36.861 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:31:36.878 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 650.771ms
2025-06-19 00:31:36.880 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:31:36.881 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:31:36.885 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID d4ba5fa3-e3f6-4bb6-b737-71000b42ccbd
2025-06-19 00:31:36.886 +04:00 [INF] Request GET /api/v1/search/history completed in 817ms with status 200 (Correlation ID: d4fe793b-71f1-4ed3-b04f-b7295abe61b9)
2025-06-19 00:31:36.888 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:31:36.891 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 848.0598ms
2025-06-19 00:31:36.894 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:31:36.898 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:31:36.899 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:31:36.904 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:31:36.908 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:31:36.909 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:31:36.910 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:31:36.912 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 8.7134ms
2025-06-19 00:31:36.914 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:31:36.915 +04:00 [INF] Request GET /api/v1/search/history completed in 27ms with status 200 (Correlation ID: d4ba5fa3-e3f6-4bb6-b737-71000b42ccbd)
2025-06-19 00:31:36.918 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 37.9981ms
2025-06-19 00:35:52.216 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:35:52.298 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:35:52.305 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 103a6213-73dd-4115-9874-17ccdf5b6f5c
2025-06-19 00:35:52.309 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID ac397781-996d-4d1b-a632-9e28f08e9025
2025-06-19 00:35:52.311 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:35:52.313 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:35:52.315 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 3ms with status 204 (Correlation ID: 103a6213-73dd-4115-9874-17ccdf5b6f5c)
2025-06-19 00:35:52.316 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 2ms with status 204 (Correlation ID: ac397781-996d-4d1b-a632-9e28f08e9025)
2025-06-19 00:35:52.323 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 107.2731ms
2025-06-19 00:35:52.329 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 31.1954ms
2025-06-19 00:35:52.329 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:35:52.360 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID b7534c1f-b54b-4dbb-9854-83430ba7a7e0
2025-06-19 00:35:52.363 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:35:52.365 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:35:52.371 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:35:52.373 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:35:52.386 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:35:52.396 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:35:52.400 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:35:52.404 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:35:52.408 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 29.672ms
2025-06-19 00:35:52.411 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:35:52.413 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:35:52.419 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 5b397eb4-16e4-46c3-b088-b095ac3cf9a8
2025-06-19 00:35:52.419 +04:00 [INF] Request GET /api/v1/search/history completed in 56ms with status 200 (Correlation ID: b7534c1f-b54b-4dbb-9854-83430ba7a7e0)
2025-06-19 00:35:52.423 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:35:52.427 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 98.0855ms
2025-06-19 00:35:52.433 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:35:52.461 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:35:52.469 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:35:52.477 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:35:52.480 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:35:52.482 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:35:52.486 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:35:52.489 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 12.068ms
2025-06-19 00:35:52.495 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:35:52.499 +04:00 [INF] Request GET /api/v1/search/history completed in 77ms with status 200 (Correlation ID: 5b397eb4-16e4-46c3-b088-b095ac3cf9a8)
2025-06-19 00:35:52.505 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 93.531ms
2025-06-19 00:36:14.478 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-19 00:36:14.492 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 761cedee-f4a7-44de-b9f7-a23a300c694e
2025-06-19 00:36:14.502 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:36:14.509 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 7ms with status 204 (Correlation ID: 761cedee-f4a7-44de-b9f7-a23a300c694e)
2025-06-19 00:36:14.522 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 43.2405ms
2025-06-19 00:36:14.529 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/v1/search/perform - application/json 64
2025-06-19 00:36:14.556 +04:00 [INF] Request POST /api/v1/search/perform started with correlation ID 9cef1a8d-b4bd-492c-8072-6fe3b435e6f1
2025-06-19 00:36:14.565 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:36:14.570 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:36:14.579 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 00:36:14.597 +04:00 [INF] Route matched with {action = "Search", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.LegalResearch.Application.DTOs.SearchResponseDto]] Search(LexAI.LegalResearch.Application.DTOs.SearchRequestDto) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:36:14.603 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:36:14.694 +04:00 [INF] Search request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Cheque ceci
2025-06-19 00:36:14.712 +04:00 [INF] Performing legal search for query: Cheque ceci
2025-06-19 00:36:14.722 +04:00 [INF] Adding new search query: "05e031f5-9b67-4c38-b0d4-9ab97518b169"
2025-06-19 00:36:14.727 +04:00 [INF] Successfully added search query: "05e031f5-9b67-4c38-b0d4-9ab97518b169"
2025-06-19 00:36:14.732 +04:00 [INF] Updating search query: "05e031f5-9b67-4c38-b0d4-9ab97518b169"
2025-06-19 00:36:14.735 +04:00 [INF] Successfully updated search query: "05e031f5-9b67-4c38-b0d4-9ab97518b169"
2025-06-19 00:36:14.741 +04:00 [INF] Performing hybrid search for query: Cheque ceci
2025-06-19 00:36:14.765 +04:00 [INF] Performing semantic search for query: Cheque ceci
2025-06-19 00:36:17.040 +04:00 [INF] Start processing HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/lexai-embedding-ada-002/embeddings?*
2025-06-19 00:36:17.048 +04:00 [INF] Sending HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/lexai-embedding-ada-002/embeddings?*
2025-06-19 00:36:17.744 +04:00 [INF] Received HTTP response headers after 690.4783ms - 404
2025-06-19 00:36:17.750 +04:00 [INF] End processing HTTP request after 716.6933ms - 404
2025-06-19 00:36:17.866 +04:00 [ERR] HTTP error generating embedding
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
2025-06-19 00:36:18.092 +04:00 [ERR] Error performing semantic search for query: Cheque ceci
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 111
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
2025-06-19 00:36:18.293 +04:00 [ERR] Error performing hybrid search for query: Cheque ceci
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 111
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
2025-06-19 00:36:18.469 +04:00 [ERR] Search failed for query: Cheque ceci
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 111
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
2025-06-19 00:36:18.554 +04:00 [INF] Updating search query: "05e031f5-9b67-4c38-b0d4-9ab97518b169"
2025-06-19 00:36:18.557 +04:00 [INF] Successfully updated search query: "05e031f5-9b67-4c38-b0d4-9ab97518b169"
2025-06-19 00:36:18.651 +04:00 [ERR] Error performing search for query: Cheque ceci
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 111
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 156
2025-06-19 00:36:18.846 +04:00 [ERR] Error performing search for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 111
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 156
   at LexAI.LegalResearch.API.Controllers.SearchController.Search(SearchRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Controllers\SearchController.cs:line 86
2025-06-19 00:36:18.864 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 00:36:18.901 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API) in 4298.3572ms
2025-06-19 00:36:18.910 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 00:36:18.916 +04:00 [INF] Request POST /api/v1/search/perform completed in 4351ms with status 500 (Correlation ID: 9cef1a8d-b4bd-492c-8072-6fe3b435e6f1)
2025-06-19 00:36:18.926 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/v1/search/perform - 500 null application/json; charset=utf-8 4395.7677ms
