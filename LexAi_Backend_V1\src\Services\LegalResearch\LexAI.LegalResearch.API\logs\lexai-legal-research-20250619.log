2025-06-19 00:52:02.554 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-19 00:52:02.645 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:52:03.014 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-19 00:52:03.018 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-19 00:52:03.086 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:52:03.091 +04:00 [INF] Hosting environment: Development
2025-06-19 00:52:03.093 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-19 00:52:04.182 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-19 00:52:04.414 +04:00 [INF] Request GET / started with correlation ID d75a151c-defd-4ee1-8b54-86b4347799d8
2025-06-19 00:52:05.955 +04:00 [INF] Request GET / completed in 1530ms with status 404 (Correlation ID: d75a151c-defd-4ee1-8b54-86b4347799d8)
2025-06-19 00:52:05.964 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 1786.3327ms
2025-06-19 00:52:05.988 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-19 00:52:25.502 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:52:25.502 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:52:25.537 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 11334e44-9378-4a37-99a5-fe9358155853
2025-06-19 00:52:25.538 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID eb9b9b47-d432-4858-b045-eb11314812ed
2025-06-19 00:52:25.589 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:25.589 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:25.598 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 16ms with status 204 (Correlation ID: 11334e44-9378-4a37-99a5-fe9358155853)
2025-06-19 00:52:25.598 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 10ms with status 204 (Correlation ID: eb9b9b47-d432-4858-b045-eb11314812ed)
2025-06-19 00:52:25.606 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 103.822ms
2025-06-19 00:52:25.612 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 110.1885ms
2025-06-19 00:52:25.614 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:52:25.640 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 08a9bf29-c07e-4e97-b984-0650e4552325
2025-06-19 00:52:25.643 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:25.782 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:52:25.795 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:25.835 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:52:26.898 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:52:28.136 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.141 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.148 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:52:28.167 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 2322.0706ms
2025-06-19 00:52:28.167 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:52:28.170 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:28.175 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 780ab9fd-0933-4d08-b6d2-4c328a965f23
2025-06-19 00:52:28.176 +04:00 [INF] Request GET /api/v1/search/history completed in 2533ms with status 200 (Correlation ID: 08a9bf29-c07e-4e97-b984-0650e4552325)
2025-06-19 00:52:28.178 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:28.181 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 2567.2262ms
2025-06-19 00:52:28.188 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:52:28.192 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:28.195 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:52:28.198 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:52:28.202 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.203 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.205 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:52:28.208 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 10.1154ms
2025-06-19 00:52:28.209 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:28.211 +04:00 [INF] Request GET /api/v1/search/history completed in 33ms with status 200 (Correlation ID: 780ab9fd-0933-4d08-b6d2-4c328a965f23)
2025-06-19 00:52:28.214 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 46.1572ms
2025-06-19 00:52:47.345 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-19 00:52:47.355 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 3c593c07-b744-4522-945c-7862ad4aeacb
2025-06-19 00:52:47.361 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:47.363 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 2ms with status 204 (Correlation ID: 3c593c07-b744-4522-945c-7862ad4aeacb)
2025-06-19 00:52:47.367 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 22.3698ms
2025-06-19 00:52:47.369 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/v1/search/perform - application/json 58
2025-06-19 00:52:47.380 +04:00 [INF] Request POST /api/v1/search/perform started with correlation ID aefacf22-cd75-4128-9ee8-40d9ebc5f085
2025-06-19 00:52:47.392 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:47.398 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:52:47.412 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 00:52:47.425 +04:00 [INF] Route matched with {action = "Search", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.LegalResearch.Application.DTOs.SearchResponseDto]] Search(LexAI.LegalResearch.Application.DTOs.SearchRequestDto) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:52:47.448 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:52:47.545 +04:00 [INF] Search request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": testo
2025-06-19 00:52:47.560 +04:00 [INF] Performing legal search for query: testo
2025-06-19 00:52:47.571 +04:00 [INF] Adding new search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.575 +04:00 [INF] Successfully added search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.578 +04:00 [INF] Updating search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.580 +04:00 [INF] Successfully updated search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.589 +04:00 [INF] Performing hybrid search for query: testo
2025-06-19 00:52:47.619 +04:00 [INF] Performing semantic search for query: testo
2025-06-19 00:52:50.047 +04:00 [INF] Start processing HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/lexai-gpt-4.1-nano/embeddings?*
2025-06-19 00:52:50.053 +04:00 [INF] Sending HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/lexai-gpt-4.1-nano/embeddings?*
2025-06-19 00:52:50.732 +04:00 [INF] Received HTTP response headers after 668.7054ms - 400
2025-06-19 00:52:50.739 +04:00 [INF] End processing HTTP request after 694.5378ms - 400
2025-06-19 00:52:50.934 +04:00 [ERR] HTTP error generating embedding
System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
2025-06-19 00:52:51.262 +04:00 [ERR] Error performing semantic search for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
2025-06-19 00:52:51.503 +04:00 [ERR] Error performing hybrid search for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
2025-06-19 00:52:51.753 +04:00 [ERR] Search failed for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
2025-06-19 00:52:51.924 +04:00 [INF] Updating search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:51.928 +04:00 [INF] Successfully updated search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:52.045 +04:00 [ERR] Error performing search for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 156
2025-06-19 00:52:52.310 +04:00 [ERR] Error performing search for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 156
   at LexAI.LegalResearch.API.Controllers.SearchController.Search(SearchRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Controllers\SearchController.cs:line 86
2025-06-19 00:52:52.329 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 00:52:52.383 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API) in 4953.6643ms
2025-06-19 00:52:52.396 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 00:52:52.410 +04:00 [INF] Request POST /api/v1/search/perform completed in 5018ms with status 500 (Correlation ID: aefacf22-cd75-4128-9ee8-40d9ebc5f085)
2025-06-19 00:52:52.430 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/v1/search/perform - 500 null application/json; charset=utf-8 5060.2223ms
2025-06-19 01:05:37.664 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-19 01:05:37.751 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 01:05:38.562 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-19 01:05:38.566 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-19 01:05:38.670 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 01:05:38.672 +04:00 [INF] Hosting environment: Development
2025-06-19 01:05:38.674 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-19 01:05:39.531 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-19 01:05:39.878 +04:00 [INF] Request GET / started with correlation ID 45688589-8600-42d6-8f61-071ec89c4612
2025-06-19 01:05:40.030 +04:00 [INF] Request GET / completed in 145ms with status 404 (Correlation ID: 45688589-8600-42d6-8f61-071ec89c4612)
2025-06-19 01:05:40.042 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 516.6775ms
2025-06-19 01:05:40.071 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-19 01:08:50.933 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 01:08:50.933 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 01:08:51.121 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 5f479a5d-f473-4daf-9b15-f1769de221c1
2025-06-19 01:08:51.121 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 54b7bda9-6d20-4ccc-b325-8cd2319bc138
2025-06-19 01:08:51.129 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:08:51.130 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:08:51.134 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 6ms with status 204 (Correlation ID: 54b7bda9-6d20-4ccc-b325-8cd2319bc138)
2025-06-19 01:08:51.134 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 10ms with status 204 (Correlation ID: 5f479a5d-f473-4daf-9b15-f1769de221c1)
2025-06-19 01:08:51.138 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 205.0003ms
2025-06-19 01:08:51.142 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 216.9537ms
2025-06-19 01:08:51.143 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 01:08:51.175 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 35c6b315-8dcb-478c-9a27-93c8c5a55a02
2025-06-19 01:08:51.185 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:08:51.322 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 01:08:51.340 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 01:08:51.383 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 01:08:51.476 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 01:08:51.693 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 01:08:51.700 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 01:08:51.730 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 01:08:51.807 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 01:08:51.813 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 415.2268ms
2025-06-19 01:08:51.817 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID a601c724-7939-48e7-a8bb-829e303b18c6
2025-06-19 01:08:51.819 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 01:08:51.821 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:08:51.826 +04:00 [INF] Request GET /api/v1/search/history completed in 640ms with status 200 (Correlation ID: 35c6b315-8dcb-478c-9a27-93c8c5a55a02)
2025-06-19 01:08:51.831 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 687.6787ms
2025-06-19 01:08:51.831 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 01:08:51.840 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 01:08:51.862 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 01:08:51.870 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 01:08:51.873 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 01:08:51.875 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 01:08:51.879 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 01:08:51.884 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 14.0268ms
2025-06-19 01:08:51.886 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 01:08:51.892 +04:00 [INF] Request GET /api/v1/search/history completed in 70ms with status 200 (Correlation ID: a601c724-7939-48e7-a8bb-829e303b18c6)
2025-06-19 01:08:51.897 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 89.2315ms
2025-06-19 01:09:00.521 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-19 01:09:00.530 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 23d3fa8d-6de3-4dab-b1e6-6d70b464286a
2025-06-19 01:09:00.536 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:09:00.539 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 2ms with status 204 (Correlation ID: 23d3fa8d-6de3-4dab-b1e6-6d70b464286a)
2025-06-19 01:09:00.543 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 22.1927ms
2025-06-19 01:09:00.547 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/v1/search/perform - application/json 58
2025-06-19 01:09:00.566 +04:00 [INF] Request POST /api/v1/search/perform started with correlation ID aca0245c-b6e7-4a91-9296-618a9632d71f
2025-06-19 01:09:00.584 +04:00 [INF] CORS policy execution successful.
2025-06-19 01:09:00.587 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 01:09:00.593 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 01:09:00.602 +04:00 [INF] Route matched with {action = "Search", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.LegalResearch.Application.DTOs.SearchResponseDto]] Search(LexAI.LegalResearch.Application.DTOs.SearchRequestDto) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 01:09:00.612 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 01:09:00.702 +04:00 [INF] Search request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": total
2025-06-19 01:09:00.716 +04:00 [INF] Performing legal search for query: total
2025-06-19 01:09:00.724 +04:00 [INF] Adding new search query: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b"
2025-06-19 01:09:00.728 +04:00 [INF] Successfully added search query: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b"
2025-06-19 01:09:00.732 +04:00 [INF] Updating search query: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b"
2025-06-19 01:09:00.734 +04:00 [INF] Successfully updated search query: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b"
2025-06-19 01:09:00.740 +04:00 [INF] Performing hybrid search for query: total
2025-06-19 01:09:00.756 +04:00 [INF] Performing semantic search for query: total
2025-06-19 01:09:02.652 +04:00 [INF] Start processing HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?*
2025-06-19 01:09:02.657 +04:00 [INF] Sending HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?*
2025-06-19 01:09:03.366 +04:00 [INF] Received HTTP response headers after 697.5503ms - 404
2025-06-19 01:09:03.376 +04:00 [INF] End processing HTTP request after 725.9074ms - 404
2025-06-19 01:09:03.500 +04:00 [ERR] HTTP error generating embedding
System.Net.Http.HttpRequestException: Response status code does not indicate success: 404 (DeploymentNotFound).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
2025-06-19 01:09:03.586 +04:00 [WRN] Embedding deployment not found or bad request, returning default embedding vector. Error: Response status code does not indicate success: 404 (DeploymentNotFound).
2025-06-19 01:09:03.636 +04:00 [INF] Performing vector similarity search with limit 40 and threshold 0.4
2025-06-19 01:09:03.650 +04:00 [INF] Start processing HTTP request POST http://localhost:5001/api/search/vector
2025-06-19 01:09:03.656 +04:00 [INF] Sending HTTP request POST http://localhost:5001/api/search/vector
2025-06-19 01:09:07.756 +04:00 [ERR] Error performing vector similarity search
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:5001)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.InjectNewHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|4_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at LexAI.LegalResearch.Infrastructure.Services.QdrantVectorDatabaseService.SearchSimilarAsync(Single[] queryVector, Int32 limit, Double threshold, Dictionary`2 filters, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\QdrantVectorDatabaseService.cs:line 93
2025-06-19 01:09:07.769 +04:00 [INF] Semantic search completed. Results: 0, Time: 7007ms
2025-06-19 01:09:07.772 +04:00 [INF] Hybrid search completed. Results: 0, Time: 7029ms
2025-06-19 01:09:07.775 +04:00 [INF] Search completed successfully. Query ID: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b", Results: 0, Time: 7037ms
2025-06-19 01:09:07.777 +04:00 [INF] Updating search query: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b"
2025-06-19 01:09:07.778 +04:00 [INF] Successfully updated search query: "0e8cbd43-0c50-44cf-b6f3-d77580edb35b"
2025-06-19 01:09:07.779 +04:00 [INF] Search completed for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Results: 0, Time: 7029ms
2025-06-19 01:09:07.783 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.LegalResearch.Application.DTOs.SearchResponseDto'.
2025-06-19 01:09:07.819 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API) in 7213.6289ms
2025-06-19 01:09:07.823 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 01:09:07.826 +04:00 [INF] Request POST /api/v1/search/perform completed in 7241ms with status 200 (Correlation ID: aca0245c-b6e7-4a91-9296-618a9632d71f)
2025-06-19 01:09:07.836 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/v1/search/perform - 200 null application/json; charset=utf-8 7289.146ms
2025-06-19 19:58:39.964 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-19 19:58:40.125 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 19:58:40.757 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-19 19:58:40.784 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-19 19:58:40.865 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 19:58:40.870 +04:00 [INF] Hosting environment: Development
2025-06-19 19:58:40.872 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-19 19:58:42.117 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-19 19:58:42.540 +04:00 [INF] Request GET / started with correlation ID df7934ac-c22b-428d-ab28-56dbcb68521c
2025-06-19 19:58:42.820 +04:00 [INF] Request GET / completed in 272ms with status 404 (Correlation ID: df7934ac-c22b-428d-ab28-56dbcb68521c)
2025-06-19 19:58:42.836 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 721.5242ms
2025-06-19 19:58:42.866 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
