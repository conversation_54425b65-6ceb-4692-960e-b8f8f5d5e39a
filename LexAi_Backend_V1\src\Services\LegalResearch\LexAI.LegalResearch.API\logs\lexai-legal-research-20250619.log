2025-06-19 00:52:02.554 +04:00 [INF] LexAI Legal Research Service started successfully
2025-06-19 00:52:02.645 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:52:03.014 +04:00 [INF] Now listening on: https://localhost:5002
2025-06-19 00:52:03.018 +04:00 [INF] Now listening on: http://localhost:58324
2025-06-19 00:52:03.086 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:52:03.091 +04:00 [INF] Hosting environment: Development
2025-06-19 00:52:03.093 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API
2025-06-19 00:52:04.182 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/ - null null
2025-06-19 00:52:04.414 +04:00 [INF] Request GET / started with correlation ID d75a151c-defd-4ee1-8b54-86b4347799d8
2025-06-19 00:52:05.955 +04:00 [INF] Request GET / completed in 1530ms with status 404 (Correlation ID: d75a151c-defd-4ee1-8b54-86b4347799d8)
2025-06-19 00:52:05.964 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/ - 404 0 null 1786.3327ms
2025-06-19 00:52:05.988 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5002/, Response status code: 404
2025-06-19 00:52:25.502 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:52:25.502 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - null null
2025-06-19 00:52:25.537 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID 11334e44-9378-4a37-99a5-fe9358155853
2025-06-19 00:52:25.538 +04:00 [INF] Request OPTIONS /api/v1/search/history started with correlation ID eb9b9b47-d432-4858-b045-eb11314812ed
2025-06-19 00:52:25.589 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:25.589 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:25.598 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 16ms with status 204 (Correlation ID: 11334e44-9378-4a37-99a5-fe9358155853)
2025-06-19 00:52:25.598 +04:00 [INF] Request OPTIONS /api/v1/search/history completed in 10ms with status 204 (Correlation ID: eb9b9b47-d432-4858-b045-eb11314812ed)
2025-06-19 00:52:25.606 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 103.822ms
2025-06-19 00:52:25.612 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/history - 204 null null 110.1885ms
2025-06-19 00:52:25.614 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:52:25.640 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 08a9bf29-c07e-4e97-b984-0650e4552325
2025-06-19 00:52:25.643 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:25.782 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:52:25.795 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:25.835 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:52:26.898 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:52:28.136 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.141 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.148 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:52:28.167 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 2322.0706ms
2025-06-19 00:52:28.167 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5002/api/v1/search/history - application/json null
2025-06-19 00:52:28.170 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:28.175 +04:00 [INF] Request GET /api/v1/search/history started with correlation ID 780ab9fd-0933-4d08-b6d2-4c328a965f23
2025-06-19 00:52:28.176 +04:00 [INF] Request GET /api/v1/search/history completed in 2533ms with status 200 (Correlation ID: 08a9bf29-c07e-4e97-b984-0650e4552325)
2025-06-19 00:52:28.178 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:28.181 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 2567.2262ms
2025-06-19 00:52:28.188 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:52:28.192 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:28.195 +04:00 [INF] Route matched with {action = "GetSearchHistory", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto]]] GetSearchHistory(Int32, Int32) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:52:28.198 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:52:28.202 +04:00 [INF] Getting search history for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.203 +04:00 [INF] Retrieved 2 search history entries for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-19 00:52:28.205 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[LexAI.LegalResearch.Application.DTOs.SearchHistoryDto, LexAI.LegalResearch.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-19 00:52:28.208 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API) in 10.1154ms
2025-06-19 00:52:28.209 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.GetSearchHistory (LexAI.LegalResearch.API)'
2025-06-19 00:52:28.211 +04:00 [INF] Request GET /api/v1/search/history completed in 33ms with status 200 (Correlation ID: 780ab9fd-0933-4d08-b6d2-4c328a965f23)
2025-06-19 00:52:28.214 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5002/api/v1/search/history - 200 null application/json; charset=utf-8 46.1572ms
2025-06-19 00:52:47.345 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - null null
2025-06-19 00:52:47.355 +04:00 [INF] Request OPTIONS /api/v1/search/perform started with correlation ID 3c593c07-b744-4522-945c-7862ad4aeacb
2025-06-19 00:52:47.361 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:47.363 +04:00 [INF] Request OPTIONS /api/v1/search/perform completed in 2ms with status 204 (Correlation ID: 3c593c07-b744-4522-945c-7862ad4aeacb)
2025-06-19 00:52:47.367 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5002/api/v1/search/perform - 204 null null 22.3698ms
2025-06-19 00:52:47.369 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5002/api/v1/search/perform - application/json 58
2025-06-19 00:52:47.380 +04:00 [INF] Request POST /api/v1/search/perform started with correlation ID aefacf22-cd75-4128-9ee8-40d9ebc5f085
2025-06-19 00:52:47.392 +04:00 [INF] CORS policy execution successful.
2025-06-19 00:52:47.398 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-19 00:52:47.412 +04:00 [INF] Executing endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 00:52:47.425 +04:00 [INF] Route matched with {action = "Search", controller = "Search"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.LegalResearch.Application.DTOs.SearchResponseDto]] Search(LexAI.LegalResearch.Application.DTOs.SearchRequestDto) on controller LexAI.LegalResearch.API.Controllers.SearchController (LexAI.LegalResearch.API).
2025-06-19 00:52:47.448 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-19 00:52:47.545 +04:00 [INF] Search request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": testo
2025-06-19 00:52:47.560 +04:00 [INF] Performing legal search for query: testo
2025-06-19 00:52:47.571 +04:00 [INF] Adding new search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.575 +04:00 [INF] Successfully added search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.578 +04:00 [INF] Updating search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.580 +04:00 [INF] Successfully updated search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:47.589 +04:00 [INF] Performing hybrid search for query: testo
2025-06-19 00:52:47.619 +04:00 [INF] Performing semantic search for query: testo
2025-06-19 00:52:50.047 +04:00 [INF] Start processing HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/lexai-gpt-4.1-nano/embeddings?*
2025-06-19 00:52:50.053 +04:00 [INF] Sending HTTP request POST https://lexai-az-openai.openai.azure.com/openai/deployments/lexai-gpt-4.1-nano/embeddings?*
2025-06-19 00:52:50.732 +04:00 [INF] Received HTTP response headers after 668.7054ms - 400
2025-06-19 00:52:50.739 +04:00 [INF] End processing HTTP request after 694.5378ms - 400
2025-06-19 00:52:50.934 +04:00 [ERR] HTTP error generating embedding
System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
2025-06-19 00:52:51.262 +04:00 [ERR] Error performing semantic search for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
2025-06-19 00:52:51.503 +04:00 [ERR] Error performing hybrid search for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
2025-06-19 00:52:51.753 +04:00 [ERR] Search failed for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
2025-06-19 00:52:51.924 +04:00 [INF] Updating search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:51.928 +04:00 [INF] Successfully updated search query: "a1df5cd8-d3d1-40e1-a651-633032a0732f"
2025-06-19 00:52:52.045 +04:00 [ERR] Error performing search for query: testo
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 156
2025-06-19 00:52:52.310 +04:00 [ERR] Error performing search for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
System.InvalidOperationException: Failed to generate embedding due to network error
 ---> System.Net.Http.HttpRequestException: Response status code does not indicate success: 400 (Bad Request).
   at System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode()
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 93
   --- End of inner exception stack trace ---
   at LexAI.LegalResearch.Infrastructure.Services.OpenAIEmbeddingService.GenerateEmbeddingAsync(String text, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\OpenAIEmbeddingService.cs:line 119
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.SearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 77
   at LexAI.LegalResearch.Infrastructure.Services.LegalSearchService.HybridSearchAsync(SearchRequestDto request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Infrastructure\Services\LegalSearchService.cs:line 227
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 104
   at LexAI.LegalResearch.Application.Commands.PerformSearchCommandHandler.Handle(PerformSearchCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.Application\Commands\SearchCommands.cs:line 156
   at LexAI.LegalResearch.API.Controllers.SearchController.Search(SearchRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\LegalResearch\LexAI.LegalResearch.API\Controllers\SearchController.cs:line 86
2025-06-19 00:52:52.329 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-19 00:52:52.383 +04:00 [INF] Executed action LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API) in 4953.6643ms
2025-06-19 00:52:52.396 +04:00 [INF] Executed endpoint 'LexAI.LegalResearch.API.Controllers.SearchController.Search (LexAI.LegalResearch.API)'
2025-06-19 00:52:52.410 +04:00 [INF] Request POST /api/v1/search/perform completed in 5018ms with status 500 (Correlation ID: aefacf22-cd75-4128-9ee8-40d9ebc5f085)
2025-06-19 00:52:52.430 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5002/api/v1/search/perform - 500 null application/json; charset=utf-8 5060.2223ms
