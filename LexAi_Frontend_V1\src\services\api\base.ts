import { ApiException, type RequestOptions } from './types'

// Configuration par défaut pour fetch
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// Fonction utilitaire pour créer les headers avec authentification
const createAuthHeaders = (token?: string | null, contentType = true): HeadersInit => {
  const headers: HeadersInit = {}
  
  if (contentType) {
    headers['Content-Type'] = 'application/json'
    headers['Accept'] = 'application/json'
  }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  return headers
}

// Fonction utilitaire pour gérer les réponses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`
    let errorDetails: any = null

    try {
      const errorData = await response.json()
      errorMessage = errorData.detail || errorData.message || errorMessage
      errorDetails = errorData
    } catch {
      // Si on ne peut pas parser le JSON, on garde le message par défaut
    }

    throw new ApiException(errorMessage, response.status, errorDetails)
  }

  // Gérer les réponses vides (204 No Content)
  if (response.status === 204) {
    return {} as T
  }

  try {
    return await response.json()
  } catch {
    throw new ApiException('Invalid JSON response', response.status)
  }
}

// Service API de base
export class BaseApiService {
  protected baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  private getToken(): string | null {
    return localStorage.getItem('accessToken')
  }

  // GET request
  async get<T>(endpoint: string, options?: RequestOptions): Promise<T> {
    const token = this.getToken()

    // Debug log
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('API GET Request:', {
        url: `${this.baseUrl}${endpoint}`,
        hasToken: !!token,
        token: token ? `${token.substring(0, 20)}...` : null
      })
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: createAuthHeaders(token),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // POST request
  async post<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<T> {
    const token = this.getToken()

    // Si data est FormData, ne pas définir Content-Type
    const isFormData = data instanceof FormData
    const headers = createAuthHeaders(token, !isFormData)

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: isFormData ? data : (data ? JSON.stringify(data) : undefined),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // PUT request
  async put<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<T> {
    const token = this.getToken()

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<T> {
    const token = this.getToken()

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PATCH',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // DELETE request
  async delete<T>(endpoint: string, options?: RequestOptions): Promise<T> {
    const token = this.getToken()

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: createAuthHeaders(token),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // Upload de fichier avec FormData
  async upload<T>(endpoint: string, formData: FormData, options?: RequestOptions): Promise<T> {
    const token = this.getToken()
    const headers: HeadersInit = {}
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    // Ne pas définir Content-Type pour FormData, le navigateur le fait automatiquement

    // Debug log
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('API Upload Request:', {
        url: `${this.baseUrl}${endpoint}`,
        hasToken: !!token,
        formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
          key,
          value: value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value
        }))
      })
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
      ...options,
    })

    return handleResponse<T>(response)
  }
}
