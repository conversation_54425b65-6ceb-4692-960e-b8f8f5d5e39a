# Script PowerShell pour tester l'intégration complète LexAI
# Auteur: LexAI Team
# Version: 1.0

Write-Host "🧪 Test d'intégration LexAI..." -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:8080"
$services = @{
    "PostgreSQL" = "localhost:5432"
    "MongoDB" = "localhost:27017"
    "Redis" = "localhost:6379"
    "RabbitMQ" = "localhost:5672"
    "Qdrant" = "localhost:6333"
}

# Fonction pour tester la connectivité
function Test-ServiceConnectivity {
    param(
        [string]$ServiceName,
        [string]$Host,
        [int]$Port,
        [int]$TimeoutSeconds = 5
    )
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect($Host, $Port, $null, $null)
        $wait = $asyncResult.AsyncWaitHandle.WaitOne($TimeoutSeconds * 1000, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            Write-Host "✅ $ServiceName ($Host`:$Port)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $ServiceName ($Host`:$Port) - Timeout" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $ServiceName ($Host`:$Port) - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    } finally {
        if ($tcpClient) {
            $tcpClient.Close()
        }
    }
}

# Fonction pour tester une API HTTP
function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$Description,
        [hashtable]$Headers = @{},
        [string]$Method = "GET"
    )
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -TimeoutSec 10
        Write-Host "✅ $Description" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ $Description - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 1: Connectivité des services
Write-Host "`n🔌 Test de connectivité des services..." -ForegroundColor Cyan
$connectivityResults = @{}

foreach ($service in $services.GetEnumerator()) {
    $hostPort = $service.Value.Split(':')
    $host = $hostPort[0]
    $port = [int]$hostPort[1]
    $connectivityResults[$service.Key] = Test-ServiceConnectivity -ServiceName $service.Key -Host $host -Port $port
}

# Test 2: APIs de santé
Write-Host "`n🏥 Test des APIs de santé..." -ForegroundColor Cyan

$healthEndpoints = @(
    @{Url="http://localhost:6333/health"; Description="Qdrant Health"},
    @{Url="http://localhost:15672/api/overview"; Description="RabbitMQ Management"; Headers=@{Authorization="Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("lexai_user:lexai_rabbitmq_password_2024!"))}},
    @{Url="http://localhost:8081"; Description="Mongo Express"}
)

$healthResults = @{}
foreach ($endpoint in $healthEndpoints) {
    $healthResults[$endpoint.Description] = Test-HttpEndpoint -Url $endpoint.Url -Description $endpoint.Description -Headers $endpoint.Headers
}

# Test 3: Test des bases de données
Write-Host "`n💾 Test des bases de données..." -ForegroundColor Cyan

# Test PostgreSQL
try {
    # Nécessite le module PostgreSQL PowerShell (optionnel)
    Write-Host "🐘 Test PostgreSQL..." -ForegroundColor Yellow
    # Ici on pourrait ajouter un test de connexion PostgreSQL
    Write-Host "ℹ️  PostgreSQL - Test manuel requis" -ForegroundColor Yellow
} catch {
    Write-Host "⚠️  PostgreSQL - Module PowerShell non disponible" -ForegroundColor Yellow
}

# Test MongoDB via API REST
try {
    Write-Host "🍃 Test MongoDB..." -ForegroundColor Yellow
    # Test via Mongo Express
    $mongoTest = Test-HttpEndpoint -Url "http://localhost:8081" -Description "MongoDB via Mongo Express"
} catch {
    Write-Host "⚠️  MongoDB - Test via Mongo Express échoué" -ForegroundColor Yellow
}

# Test Redis
try {
    Write-Host "🔴 Test Redis..." -ForegroundColor Yellow
    # Ici on pourrait ajouter un test de connexion Redis
    Write-Host "ℹ️  Redis - Test manuel requis" -ForegroundColor Yellow
} catch {
    Write-Host "⚠️  Redis - Test échoué" -ForegroundColor Yellow
}

# Test 4: Test Qdrant spécifique
Write-Host "`n🔍 Test Qdrant..." -ForegroundColor Cyan

try {
    # Test de création d'une collection
    $qdrantCollectionTest = @{
        vectors = @{
            size = 384
            distance = "Cosine"
        }
    }
    
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    # Créer une collection de test
    try {
        Invoke-RestMethod -Uri "http://localhost:6333/collections/test_collection" -Method PUT -Body ($qdrantCollectionTest | ConvertTo-Json) -Headers $headers -TimeoutSec 10
        Write-Host "✅ Qdrant - Création de collection" -ForegroundColor Green
        
        # Supprimer la collection de test
        Invoke-RestMethod -Uri "http://localhost:6333/collections/test_collection" -Method DELETE -Headers $headers -TimeoutSec 10
        Write-Host "✅ Qdrant - Suppression de collection" -ForegroundColor Green
    } catch {
        Write-Host "❌ Qdrant - Test de collection échoué: $($_.Exception.Message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Qdrant - Test général échoué" -ForegroundColor Red
}

# Test 5: Test du service d'embedding local
Write-Host "`n🤖 Test du service d'embedding local..." -ForegroundColor Cyan

$embeddingServicePath = Join-Path (Split-Path -Parent (Split-Path -Parent $MyInvocation.MyCommand.Path)) "LexAi_Local_Embeding"
if (Test-Path $embeddingServicePath) {
    Write-Host "📁 Service d'embedding trouvé: $embeddingServicePath" -ForegroundColor Green
    
    # Vérifier si le service est en cours d'exécution
    $embeddingProcess = Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*embedding*" }
    if ($embeddingProcess) {
        Write-Host "✅ Service d'embedding en cours d'exécution" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Service d'embedding non démarré" -ForegroundColor Yellow
        Write-Host "💡 Démarrez le service avec: cd '$embeddingServicePath' && python app.py" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Service d'embedding non trouvé" -ForegroundColor Red
}

# Résumé des tests
Write-Host "`n📊 Résumé des tests:" -ForegroundColor Cyan

$totalTests = 0
$passedTests = 0

# Comptage des tests de connectivité
foreach ($result in $connectivityResults.Values) {
    $totalTests++
    if ($result) { $passedTests++ }
}

# Comptage des tests de santé
foreach ($result in $healthResults.Values) {
    $totalTests++
    if ($result) { $passedTests++ }
}

$successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)

Write-Host "   ✅ Tests réussis: $passedTests/$totalTests ($successRate%)" -ForegroundColor Green

if ($successRate -ge 80) {
    Write-Host "`n🎉 Infrastructure LexAI opérationnelle !" -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Host "`n⚠️  Infrastructure LexAI partiellement opérationnelle" -ForegroundColor Yellow
    Write-Host "💡 Vérifiez les services en échec et redémarrez si nécessaire" -ForegroundColor Cyan
} else {
    Write-Host "`n❌ Infrastructure LexAI non opérationnelle" -ForegroundColor Red
    Write-Host "💡 Vérifiez la configuration et redémarrez l'infrastructure" -ForegroundColor Cyan
}

# Recommandations
Write-Host "`n💡 Recommandations:" -ForegroundColor Cyan
Write-Host "   1. Vérifiez que tous les services Docker sont démarrés: docker compose ps" -ForegroundColor White
Write-Host "   2. Consultez les logs en cas d'erreur: docker compose logs [service]" -ForegroundColor White
Write-Host "   3. Redémarrez un service spécifique: docker compose restart [service]" -ForegroundColor White
Write-Host "   4. Configurez votre clé OpenAI dans le fichier .env" -ForegroundColor White
Write-Host "   5. Démarrez le service d'embedding local si nécessaire" -ForegroundColor White

Write-Host "`n✅ Test d'intégration terminé !" -ForegroundColor Green
