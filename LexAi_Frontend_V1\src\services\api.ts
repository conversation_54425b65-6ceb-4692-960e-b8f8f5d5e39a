// ⚠️ DEPRECATED: Ce fichier est conservé pour la compatibilité
// Utilisez les nouveaux services modulaires dans les dossiers spécialisés :
// - identity/ pour l'authentification et les utilisateurs
// - documentAnalysis/ pour l'analyse de documents
// - dataProcessing/ pour le traitement de documents
// - aiAssistant/ pour l'assistant IA
// - legalResearch/ pour la recherche juridique

// Imports des nouveaux services modulaires
export { authApi } from './identity/auth.api'
export { usersApi } from './identity/users.api'
export { documentAnalysisApi } from './documentAnalysis/analysis.api'
export { documentsApi } from './dataProcessing/documents.api'
export { chatApi } from './aiAssistant/chat.api'
export { searchApi } from './legalResearch/search.api'

// Imports des types
export type { ApiError } from './api/types'
export { ApiException } from './api/types'
export { BaseApiService } from './api/base'

// Import des intercepteurs pour l'auto-refresh des tokens
import './api/interceptors'

// Configuration de base de l'API
const IDENTITY_API_BASE_URL = import.meta.env.VITE_IDENTITY_API_URL || 'http://localhost:5000'
const DATAPROCESSING_API_BASE_URL = import.meta.env.VITE_DATAPROCESSING_API_URL || 'http://localhost:5001'
const LEGAL_RESEARCH_API_BASE_URL = import.meta.env.VITE_LEGAL_RESEARCH_API_URL || 'http://localhost:51403'
const AI_ASSISTANT_API_BASE_URL = import.meta.env.VITE_AI_ASSISTANT_API_URL || 'http://localhost:51404'
const DOCUMENT_ANALYSIS_API_BASE_URL = import.meta.env.VITE_DOCUMENT_ANALYSIS_API_URL || 'http://localhost:51405'

// Exports des URLs de base pour compatibilité
export const API_URLS = {
  IDENTITY: IDENTITY_API_BASE_URL,
  DATAPROCESSING: DATAPROCESSING_API_BASE_URL,
  LEGAL_RESEARCH: LEGAL_RESEARCH_API_BASE_URL,
  AI_ASSISTANT: AI_ASSISTANT_API_BASE_URL,
  DOCUMENT_ANALYSIS: DOCUMENT_ANALYSIS_API_BASE_URL,
}

// ⚠️ DEPRECATED: Utilisez les nouveaux services modulaires
// Classe de compatibilité - utilisez BaseApiService à la place
export { BaseApiService as ApiService } from './api/base'