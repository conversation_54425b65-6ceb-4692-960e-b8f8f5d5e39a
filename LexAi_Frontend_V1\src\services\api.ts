import { useAuthStore } from '../store/authStore'

// Configuration de base de l'API
const IDENTITY_API_BASE_URL = import.meta.env.VITE_IDENTITY_API_URL || 'http://localhost:5000'
const DATAPROCESSING_API_BASE_URL = import.meta.env.VITE_DATAPROCESSING_API_URL || 'http://localhost:5001'
const LEGAL_RESEARCH_API_BASE_URL = import.meta.env.VITE_LEGAL_RESEARCH_API_URL || 'http://localhost:51403'
const AI_ASSISTANT_API_BASE_URL = import.meta.env.VITE_AI_ASSISTANT_API_URL || 'http://localhost:51404'
const DOCUMENT_ANALYSIS_API_BASE_URL = import.meta.env.VITE_DOCUMENT_ANALYSIS_API_URL || 'http://localhost:51405'

// Interface pour les erreurs API
interface ApiError {
  message: string
  status: number
  details?: any
}

// Classe d'erreur personnalisée
export class ApiException extends Error {
  status: number
  details?: any

  constructor(message: string, status: number, details?: any) {
    super(message)
    this.name = 'ApiException'
    this.status = status
    this.details = details
  }
}

// Configuration par défaut pour fetch
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// Fonction utilitaire pour créer les headers avec authentification
const createAuthHeaders = (token?: string): HeadersInit => {
  const headers: HeadersInit = { ...defaultHeaders }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  return headers
}

// Fonction utilitaire pour gérer les réponses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`
    let errorDetails: any = null

    try {
      const errorData = await response.json()
      errorMessage = errorData.detail || errorData.message || errorMessage
      errorDetails = errorData
    } catch {
      // Si on ne peut pas parser le JSON, on garde le message par défaut
    }

    throw new ApiException(errorMessage, response.status, errorDetails)
  }

  // Gérer les réponses vides (204 No Content)
  if (response.status === 204) {
    return {} as T
  }

  try {
    return await response.json()
  } catch {
    throw new ApiException('Invalid JSON response', response.status)
  }
}

// Service API principal
export class ApiService {
  private baseUrl: string

  constructor(baseUrl: string = IDENTITY_API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  // GET request
  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    // Debug log
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('API GET Request:', {
        url: `${this.baseUrl}${endpoint}`,
        hasToken: !!token,
        token: token ? `${token.substring(0, 20)}...` : null
      })
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: createAuthHeaders(token),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // POST request
  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // PUT request
  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PATCH',
      headers: createAuthHeaders(token),
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })

    return handleResponse<T>(response)
  }

  // DELETE request
  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const token = localStorage.getItem('accessToken')

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: createAuthHeaders(token),
      ...options,
    })

    return handleResponse<T>(response)
  }

  // Upload de fichier
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<T> {
    const token = localStorage.getItem('accessToken')
    const formData = new FormData()

    formData.append('file', file)

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
      })
    }

    const headers: HeadersInit = {}
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    })

    return handleResponse<T>(response)
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/auth/refresh`, {
      method: 'POST',
      headers: defaultHeaders,
      body: JSON.stringify({ refreshToken }),
    })

    return handleResponse(response)
  }
}

// Instance par défaut du service API
export const apiService = new ApiService()

// Variables globales pour éviter les appels simultanés de refresh
let isRefreshing = false
let refreshPromise: Promise<any> | null = null

// Intercepteur pour gérer automatiquement le refresh token
const originalFetch = window.fetch
window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const response = await originalFetch(input, init)

  // Si on reçoit une erreur 401 (Unauthorized) et qu'on a un refresh token
  if (response.status === 401) {
    const authStore = useAuthStore.getState()

    if (authStore.refreshToken && typeof input === 'string' && !input.includes('/refresh')) {
      try {
        // Si un refresh est déjà en cours, attendre qu'il se termine
        if (isRefreshing && refreshPromise) {
          await refreshPromise

          // Retry avec le nouveau token
          const newToken = localStorage.getItem('accessToken')
          if (newToken) {
            const newHeaders = {
              ...init?.headers,
              'Authorization': `Bearer ${newToken}`
            }

            return originalFetch(input, {
              ...init,
              headers: newHeaders
            })
          }
        }

        // Démarrer le processus de refresh
        if (!isRefreshing) {
          isRefreshing = true
          refreshPromise = apiService.refreshToken(authStore.refreshToken)
            .then((refreshResponse) => {
              // Mettre à jour les tokens dans le store
              authStore.setTokens(refreshResponse.accessToken, refreshResponse.refreshToken)
              return refreshResponse
            })
            .finally(() => {
              isRefreshing = false
              refreshPromise = null
            })
        }

        const refreshResponse = await refreshPromise

        // Retry la requête originale avec le nouveau token
        const newHeaders = {
          ...init?.headers,
          'Authorization': `Bearer ${refreshResponse.accessToken}`
        }

        return originalFetch(input, {
          ...init,
          headers: newHeaders
        })
      } catch (refreshError) {
        // Si le refresh échoue, déconnecter l'utilisateur
        isRefreshing = false
        refreshPromise = null
        authStore.logout()
        window.location.href = '/login'
      }
    }
  }

  return response
}

// Services spécialisés
export const authApi = {
  login: (email: string, password: string) =>
    apiService.post('/api/auth/login', { email, password }),

  register: (userData: any) =>
    apiService.post('/api/auth/register', userData),

  logout: () =>
    apiService.post('/api/auth/logout'),

  refreshToken: (refreshToken: string) =>
    apiService.post('/api/auth/refresh', { refreshToken }),

  forgotPassword: (email: string) =>
    apiService.post('/api/auth/forgot-password', { email }),

  resetPassword: (token: string, newPassword: string) =>
    apiService.post('/api/auth/reset-password', { token, newPassword }),

  changePassword: (currentPassword: string, newPassword: string) =>
    apiService.post('/api/auth/change-password', { currentPassword, newPassword }),

  getCurrentUser: () =>
    apiService.get('/api/auth/me'),
}

export const usersApi = {
  getUsers: (limit?: number, offset?: number) =>
    apiService.get(`/api/users?${new URLSearchParams({
      ...(limit && { limit: limit.toString() }),
      ...(offset && { offset: offset.toString() })
    })}`),

  getUser: (id: string) =>
    apiService.get(`/api/users/${id}`),

  createUser: (userData: any) =>
    apiService.post('/api/users', userData),

  updateUser: (id: string, userData: any) =>
    apiService.put(`/api/users/${id}`, userData),

  deleteUser: (id: string) =>
    apiService.delete(`/api/users/${id}`),
}

// Services pour les différents microservices
const dataProcessingService = new ApiService(DATAPROCESSING_API_BASE_URL)
const legalResearchService = new ApiService(LEGAL_RESEARCH_API_BASE_URL)
const aiAssistantService = new ApiService(AI_ASSISTANT_API_BASE_URL)
const documentAnalysisService = new ApiService(DOCUMENT_ANALYSIS_API_BASE_URL)

export const documentsApi = {
  uploadDocument: (formData: FormData) => {
    const token = localStorage.getItem('accessToken')
    const headers: HeadersInit = {}
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    // Ne pas définir Content-Type pour FormData, le navigateur le fait automatiquement

    // Debug log
    if (import.meta.env.VITE_DEBUG_API === 'true') {
      console.log('Document Upload Request:', {
        url: `${DATAPROCESSING_API_BASE_URL}/api/documents/upload`,
        hasToken: !!token,
        formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
          key,
          value: value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value
        }))
      })
    }

    return fetch(`${DATAPROCESSING_API_BASE_URL}/api/documents/upload`, {
      method: 'POST',
      headers,
      body: formData,
    }).then(handleResponse)
  },

  getUserDocuments: (limit?: number, offset?: number) =>
    dataProcessingService.get(`/api/documents?${new URLSearchParams({
      ...(limit && { limit: limit.toString() }),
      ...(offset && { offset: offset.toString() })
    })}`),

  getDocument: (id: string) =>
    dataProcessingService.get(`/api/documents/${id}`),

  processDocument: (id: string, configuration: any) =>
    dataProcessingService.post(`/api/documents/${id}/process`, configuration),

  getProcessingStatus: (id: string) =>
    dataProcessingService.get(`/api/documents/${id}/status`),

  deleteDocument: (id: string) =>
    dataProcessingService.delete(`/api/documents/${id}`),
}

export const userApi = {
  getCurrentUser: () =>
    apiService.get('/api/users/me'),

  updateProfile: (userData: any) =>
    apiService.put('/api/users/me', userData),

  changePassword: (currentPassword: string, newPassword: string) =>
    apiService.post('/api/users/change-password', { currentPassword, newPassword }),

  uploadAvatar: (file: File) =>
    apiService.upload('/api/users/avatar', file),
}

export const aiAssistantApi = {
  sendMessage: (message: string, conversationId?: string) =>
    aiAssistantService.post('/api/v1/chat/send', { message, conversationId }),

  getConversations: () =>
    aiAssistantService.get('/api/v1/chat/conversations'),

  getConversation: (id: string) =>
    aiAssistantService.get(`/api/v1/chat/conversations/${id}`),

  deleteConversation: (id: string) =>
    aiAssistantService.delete(`/api/v1/chat/conversations/${id}`),

  // Analyse de documents
  analyzeDocument: (documentData: {
    documentName: string;
    documentContent: string;
    documentType?: string;
    focusAreas?: string[];
    context?: string;
  }) =>
    aiAssistantService.post('/api/v1/analysis/document', documentData),

  // Génération de documents
  generateDocument: (documentData: {
    documentType: string;
    requirements: string;
    template?: string;
    jurisdiction?: string;
    parameters?: Record<string, any>;
  }) =>
    aiAssistantService.post('/api/v1/generation/document', documentData),

  // Recherche juridique
  performLegalResearch: (researchData: {
    query: string;
    domain?: string;
    jurisdiction?: string;
    maxResults?: number;
  }) =>
    aiAssistantService.post('/api/v1/research/search', researchData),

  // Évaluation des messages
  rateMessage: (messageId: string, rating: number, feedback?: string) =>
    aiAssistantService.post(`/api/v1/chat/rate/${messageId}`, { rating, feedback }),
}

export const searchApi = {
  // Recherche juridique principale
  performSearch: (searchData: {
    query: string;
    domain?: string;
    jurisdiction?: string;
    documentTypes?: string[];
    maxResults?: number;
    includeHighlights?: boolean;
  }) =>
    legalResearchService.post('/api/v1/search/perform', searchData),

  // Recherche de documents
  searchDocuments: (query: string, filters?: any) =>
    legalResearchService.post('/api/v1/search/documents', { query, filters }),

  // Recherche de jurisprudences
  searchCases: (query: string, filters?: any) =>
    legalResearchService.post('/api/v1/search/cases', { query, filters }),

  // Suggestions de recherche
  getSearchSuggestions: (query: string) =>
    legalResearchService.get(`/api/v1/search/suggestions?q=${encodeURIComponent(query)}`),

  // Recherche avancée
  advancedSearch: (searchData: any) =>
    legalResearchService.post('/api/v1/search/advanced', searchData),

  // Historique de recherche
  getSearchHistory: () =>
    legalResearchService.get('/api/v1/search/history'),

  // Analytics de recherche
  getSearchAnalytics: (sessionId?: string) =>
    legalResearchService.get(`/api/v1/search/analytics${sessionId ? `?sessionId=${sessionId}` : ''}`),

  // Feedback sur les résultats
  provideFeedback: (searchId: string, feedback: {
    relevantResults: string[];
    irrelevantResults: string[];
    rating: number;
    comments?: string;
  }) =>
    legalResearchService.post(`/api/v1/search/feedback/${searchId}`, feedback),

  // Documents similaires
  getSimilarDocuments: (documentId: string, maxResults?: number) =>
    legalResearchService.get(`/api/v1/search/similar/${documentId}?maxResults=${maxResults || 5}`),
}

// API pour le traitement de documents
export const documentProcessingApi = {
  // Upload et traitement
  uploadDocument: (file: File, configuration?: any) => {
    const formData = new FormData();
    formData.append('file', file);
    if (configuration) {
      formData.append('configuration', JSON.stringify(configuration));
    }
    return apiService.upload('/api/documents/upload', formData);
  },

  // Traitement manuel
  processDocument: (documentId: string, configuration: any) =>
    apiService.post(`/api/documents/${documentId}/process`, configuration),

  // Statut de traitement
  getProcessingStatus: (documentId: string) =>
    apiService.get(`/api/documents/${documentId}/status`),

  // Liste des documents
  getDocuments: (filters?: any) =>
    apiService.get('/api/documents', { params: filters }),

  // Détails d'un document
  getDocument: (documentId: string) =>
    apiService.get(`/api/documents/${documentId}`),

  // Retry traitement
  retryProcessing: (documentId: string, fromStep?: string) =>
    apiService.post(`/api/documents/${documentId}/retry`, { fromStep }),

  // Annuler traitement
  cancelProcessing: (documentId: string) =>
    apiService.post(`/api/documents/${documentId}/cancel`),

  // Supprimer document
  deleteDocument: (documentId: string) =>
    apiService.delete(`/api/documents/${documentId}`),
}

// API pour l'analyse de documents
export const documentAnalysisApi = {
  // Analyser un document
  analyzeDocument: (file: File, options?: {
    extractClauses?: boolean;
    performRiskAssessment?: boolean;
    generateRecommendations?: boolean;
    extractEntities?: boolean;
    findCitations?: boolean;
    language?: string;
  }) => {
    const formData = new FormData();
    formData.append('file', file);
    if (options) {
      formData.append('options', JSON.stringify(options));
    }
    return documentAnalysisService.post('/api/v1/documents/analyze', formData);
  },

  // Récupérer une analyse
  getAnalysis: (analysisId: string) =>
    documentAnalysisService.get(`/api/v1/documents/${analysisId}/analysis`),

  // Récupérer les clauses d'un document
  getClauses: (analysisId: string) =>
    documentAnalysisService.get(`/api/v1/documents/${analysisId}/clauses`),

  // Récupérer les risques identifiés
  getRisks: (analysisId: string) =>
    documentAnalysisService.get(`/api/v1/documents/${analysisId}/risks`),

  // Récupérer les recommandations
  getRecommendations: (analysisId: string) =>
    documentAnalysisService.get(`/api/v1/documents/${analysisId}/recommendations`),

  // Récupérer les entités extraites
  getEntities: (analysisId: string) =>
    documentAnalysisService.get(`/api/v1/documents/${analysisId}/entities`),

  // Récupérer le résumé d'un document
  getSummary: (analysisId: string) =>
    documentAnalysisService.get(`/api/v1/documents/${analysisId}/summary`),

  // Lister les analyses d'un utilisateur
  getUserAnalyses: (limit?: number, offset?: number) =>
    documentAnalysisService.get(`/api/v1/documents/analyses?${new URLSearchParams({
      ...(limit && { limit: limit.toString() }),
      ...(offset && { offset: offset.toString() })
    })}`),
}

// API pour l'assistant IA (nouvelle version avec microservice dédié)
export const aiAssistantServiceApi = {
  // Démarrer une nouvelle conversation
  startConversation: (initialMessage?: string) =>
    aiAssistantService.post('/api/v1/chat/conversations', {
      initialMessage: initialMessage || 'Bonjour, comment puis-je vous aider ?'
    }),

  // Envoyer un message
  sendMessage: (conversationId: string, message: string, context?: any) =>
    aiAssistantService.post(`/api/v1/chat/conversations/${conversationId}/messages`, {
      message,
      context
    }),

  // Récupérer l'historique d'une conversation
  getConversation: (conversationId: string) =>
    aiAssistantService.get(`/api/v1/chat/conversations/${conversationId}`),

  // Lister les conversations
  getConversations: (limit?: number, offset?: number) =>
    aiAssistantService.get(`/api/v1/chat/conversations?${new URLSearchParams({
      ...(limit && { limit: limit.toString() }),
      ...(offset && { offset: offset.toString() })
    })}`),

  // Supprimer une conversation
  deleteConversation: (conversationId: string) =>
    aiAssistantService.delete(`/api/v1/chat/conversations/${conversationId}`),

  // Recherche juridique via l'assistant
  performLegalResearch: (query: string, context?: any) =>
    aiAssistantService.post('/api/v1/research/search', {
      query,
      context
    }),

  // Analyse de document via l'assistant
  analyzeDocumentWithAI: (documentId: string, questions?: string[]) =>
    aiAssistantService.post('/api/v1/analysis/document', {
      documentId,
      questions
    }),

  // Génération de résumé
  generateSummary: (content: string, type: 'document' | 'conversation' | 'research' = 'document') =>
    aiAssistantService.post('/api/v1/generation/summary', {
      content,
      type
    }),
}
