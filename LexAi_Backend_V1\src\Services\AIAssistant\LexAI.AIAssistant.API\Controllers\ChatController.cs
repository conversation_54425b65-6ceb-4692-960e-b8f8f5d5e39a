using LexAI.AIAssistant.Application.Commands;
using LexAI.AIAssistant.Application.DTOs;
using LexAI.AIAssistant.Application.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using System.Security.Claims;

namespace LexAI.AIAssistant.API.Controllers;

/// <summary>
/// Controller for AI assistant chat operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
[EnableRateLimiting("ChatPolicy")]
public class ChatController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IAIAssistantService _aiAssistantService;
    private readonly IConversationService _conversationService;
    private readonly ILogger<ChatController> _logger;

    /// <summary>
    /// Initializes a new instance of the ChatController
    /// </summary>
    /// <param name="mediator">MediatR mediator</param>
    /// <param name="aiAssistantService">AI assistant service</param>
    /// <param name="conversationService">Conversation service</param>
    /// <param name="logger">Logger</param>
    public ChatController(
        IMediator mediator,
        IAIAssistantService aiAssistantService,
        IConversationService conversationService,
        ILogger<ChatController> logger)
    {
        _mediator = mediator;
        _aiAssistantService = aiAssistantService;
        _conversationService = conversationService;
        _logger = logger;
    }

    /// <summary>
    /// Sends a message to the AI assistant
    /// </summary>
    /// <param name="request">Chat request</param>
    /// <returns>AI response</returns>
    /// <response code="200">Message processed successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">User not authenticated</response>
    /// <response code="429">Rate limit exceeded</response>
    [HttpPost("message")]
    [ProducesResponseType(typeof(ChatResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status429TooManyRequests)]
    public async Task<ActionResult<ChatResponseDto>> SendMessage([FromBody] ChatRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.Message))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Message cannot be empty" });
        }

        _logger.LogInformation("Chat request from user {UserId}: {Message}", userId.Value, request.Message);

        try
        {
            // Set user context
            request.UserId = userId.Value;
            if (string.IsNullOrEmpty(request.SessionId))
            {
                request.SessionId = HttpContext.TraceIdentifier;
            }

            // Execute chat command
            var command = new SendMessageCommand { Request = request };
            var response = await _mediator.Send(command);

            _logger.LogInformation("Chat request processed successfully for user {UserId}. " +
                "Conversation: {ConversationId}, Tokens: {TokensUsed}, Cost: {Cost}", 
                userId.Value, response.ConversationId, response.TokensUsed, response.EstimatedCost);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid chat request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized chat request from user {UserId}", userId.Value);
            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chat request for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Chat Error", Detail = "An error occurred while processing your message" });
        }
    }

    /// <summary>
    /// Continues an existing conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <param name="request">Message request</param>
    /// <returns>AI response</returns>
    /// <response code="200">Message processed successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="404">Conversation not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("conversations/{conversationId:guid}/messages")]
    [ProducesResponseType(typeof(ChatResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ChatResponseDto>> ContinueConversation(
        Guid conversationId, 
        [FromBody] ContinueConversationRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.Message))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Message cannot be empty" });
        }

        _logger.LogInformation("Continue conversation {ConversationId} from user {UserId}: {Message}", 
            conversationId, userId.Value, request.Message);

        try
        {
            var command = new ContinueConversationCommand
            {
                ConversationId = conversationId,
                Message = request.Message,
                UserId = userId.Value
            };

            var response = await _mediator.Send(command);

            _logger.LogInformation("Conversation continued successfully for user {UserId}. " +
                "Conversation: {ConversationId}, Tokens: {TokensUsed}", 
                userId.Value, conversationId, response.TokensUsed);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid continue conversation request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access to conversation {ConversationId} by user {UserId}", 
                conversationId, userId.Value);
            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error continuing conversation {ConversationId} for user {UserId}", 
                conversationId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Chat Error", Detail = "An error occurred while processing your message" });
        }
    }

    /// <summary>
    /// Analyzes a legal document
    /// </summary>
    /// <param name="request">Document analysis request</param>
    /// <returns>Document analysis response</returns>
    /// <response code="200">Document analyzed successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("analyze-document")]
    [ProducesResponseType(typeof(DocumentAnalysisResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<DocumentAnalysisResponseDto>> AnalyzeDocument([FromBody] DocumentAnalysisRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.DocumentContent))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Document content cannot be empty" });
        }

        _logger.LogInformation("Document analysis request from user {UserId}: {DocumentName}", 
            userId.Value, request.DocumentName);

        try
        {
            request.UserId = userId.Value;
            var response = await _aiAssistantService.AnalyzeDocumentAsync(request);

            _logger.LogInformation("Document analysis completed for user {UserId}. " +
                "Document: {DocumentName}, Tokens: {TokensUsed}", 
                userId.Value, request.DocumentName, response.TokensUsed);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid document analysis request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing document for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Analysis Error", Detail = "An error occurred while analyzing the document" });
        }
    }

    /// <summary>
    /// Performs legal research
    /// </summary>
    /// <param name="request">Legal research request</param>
    /// <returns>Legal research response</returns>
    /// <response code="200">Research completed successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("legal-research")]
    [ProducesResponseType(typeof(LegalResearchResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<LegalResearchResponseDto>> PerformLegalResearch([FromBody] LegalResearchRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.Query))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Research query cannot be empty" });
        }

        _logger.LogInformation("Legal research request from user {UserId}: {Query}", userId.Value, request.Query);

        try
        {
            request.UserId = userId.Value;
            var response = await _aiAssistantService.PerformLegalResearchAsync(request);

            _logger.LogInformation("Legal research completed for user {UserId}. " +
                "Query: {Query}, Tokens: {TokensUsed}", 
                userId.Value, request.Query, response.TokensUsed);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid legal research request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing legal research for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Research Error", Detail = "An error occurred while performing legal research" });
        }
    }

    /// <summary>
    /// Generates a legal document
    /// </summary>
    /// <param name="request">Document generation request</param>
    /// <returns>Generated document</returns>
    /// <response code="200">Document generated successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("generate-document")]
    [ProducesResponseType(typeof(DocumentGenerationResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<DocumentGenerationResponseDto>> GenerateDocument([FromBody] DocumentGenerationRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (string.IsNullOrWhiteSpace(request.DocumentType) || string.IsNullOrWhiteSpace(request.Requirements))
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Document type and requirements cannot be empty" });
        }

        _logger.LogInformation("Document generation request from user {UserId}: {DocumentType}", 
            userId.Value, request.DocumentType);

        try
        {
            request.UserId = userId.Value;
            var response = await _aiAssistantService.GenerateDocumentAsync(request);

            _logger.LogInformation("Document generation completed for user {UserId}. " +
                "Type: {DocumentType}, Tokens: {TokensUsed}", 
                userId.Value, request.DocumentType, response.TokensUsed);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid document generation request from user {UserId}", userId.Value);
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating document for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Generation Error", Detail = "An error occurred while generating the document" });
        }
    }

    /// <summary>
    /// Gets conversations for the current user
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>List of conversations</returns>
    /// <response code="200">Conversations retrieved successfully</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("conversations")]
    [ProducesResponseType(typeof(ConversationListResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ConversationListResponseDto>> GetConversations(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        try
        {
            var conversations = await _conversationService.GetUserConversationsAsync(userId.Value, pageSize, (page - 1) * pageSize);
            var totalCount = await _conversationService.GetUserConversationCountAsync(userId.Value);

            var response = new ConversationListResponseDto
            {
                Items = conversations.ToList(),
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversations for user {UserId}", userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Error", Detail = "An error occurred while retrieving conversations" });
        }
    }

    /// <summary>
    /// Gets a specific conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <returns>Conversation details</returns>
    /// <response code="200">Conversation retrieved successfully</response>
    /// <response code="404">Conversation not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpGet("conversations/{conversationId:guid}")]
    [ProducesResponseType(typeof(ConversationDetailDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ConversationDetailDto>> GetConversation(Guid conversationId)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        try
        {
            var conversation = await _conversationService.GetConversationAsync(conversationId, userId.Value);
            if (conversation == null)
            {
                return NotFound(new ProblemDetails { Title = "Conversation Not Found", Detail = "The specified conversation was not found" });
            }

            var response = new ConversationDetailDto
            {
                Id = conversation.Id,
                Title = conversation.Title,
                CreatedAt = conversation.CreatedAt,
                UpdatedAt = conversation.UpdatedAt,
                Messages = conversation.Messages?.Select(m => new MessageDto
                {
                    Id = m.Id,
                    Content = m.Content,
                    Role = m.Role,
                    Timestamp = m.Timestamp,
                    Rating = m.Rating,
                    Metadata = m.Metadata
                }).ToList() ?? new List<MessageDto>()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation {ConversationId} for user {UserId}", conversationId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Error", Detail = "An error occurred while retrieving the conversation" });
        }
    }

    /// <summary>
    /// Deletes a conversation
    /// </summary>
    /// <param name="conversationId">Conversation ID</param>
    /// <returns>Deletion confirmation</returns>
    /// <response code="200">Conversation deleted successfully</response>
    /// <response code="404">Conversation not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpDelete("conversations/{conversationId:guid}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> DeleteConversation(Guid conversationId)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        try
        {
            await _conversationService.DeleteConversationAsync(conversationId, userId.Value);
            return Ok(new { message = "Conversation deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting conversation {ConversationId} for user {UserId}", conversationId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Error", Detail = "An error occurred while deleting the conversation" });
        }
    }

    /// <summary>
    /// Rates a message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="request">Rating request</param>
    /// <returns>Rating confirmation</returns>
    /// <response code="200">Message rated successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="404">Message not found</response>
    /// <response code="401">User not authenticated</response>
    [HttpPost("messages/{messageId:guid}/rate")]
    [HttpPost("rate/{messageId:guid}")] // Alternative route for frontend compatibility
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> RateMessage(Guid messageId, [FromBody] RateMessageRequestDto request)
    {
        var userId = GetCurrentUserId();
        if (!userId.HasValue)
        {
            return Unauthorized(new ProblemDetails { Title = "Authentication Required", Detail = "User not authenticated" });
        }

        if (request.Rating < 1 || request.Rating > 5)
        {
            return BadRequest(new ProblemDetails { Title = "Invalid Request", Detail = "Rating must be between 1 and 5" });
        }

        _logger.LogInformation("Rating message {MessageId} by user {UserId} with rating {Rating}", 
            messageId, userId.Value, request.Rating);

        try
        {
            var command = new RateMessageCommand
            {
                MessageId = messageId,
                Rating = request.Rating,
                Feedback = request.Feedback,
                UserId = userId.Value
            };

            var result = await _mediator.Send(command);

            if (result)
            {
                _logger.LogInformation("Message {MessageId} rated successfully by user {UserId}", messageId, userId.Value);
                return Ok(new { message = "Message rated successfully" });
            }
            else
            {
                return NotFound(new ProblemDetails { Title = "Message Not Found", Detail = "The specified message was not found" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rating message {MessageId} by user {UserId}", messageId, userId.Value);
            return StatusCode(500, new ProblemDetails { Title = "Rating Error", Detail = "An error occurred while rating the message" });
        }
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
    }
}

/// <summary>
/// Continue conversation request DTO
/// </summary>
public class ContinueConversationRequestDto
{
    /// <summary>
    /// User message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Rate message request DTO
/// </summary>
public class RateMessageRequestDto
{
    /// <summary>
    /// Rating (1-5)
    /// </summary>
    public int Rating { get; set; }

    /// <summary>
    /// Optional feedback
    /// </summary>
    public string? Feedback { get; set; }
}

/// <summary>
/// Conversation list response DTO
/// </summary>
public class ConversationListResponseDto
{
    /// <summary>
    /// List of conversations
    /// </summary>
    public List<ConversationSummaryDto> Items { get; set; } = new();

    /// <summary>
    /// Total count of conversations
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }
}

/// <summary>
/// Conversation detail DTO
/// </summary>
public class ConversationDetailDto
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Conversation title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Creation date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last update date
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Messages in the conversation
    /// </summary>
    public List<MessageDto> Messages { get; set; } = new();
}
