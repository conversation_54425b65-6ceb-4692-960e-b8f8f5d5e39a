using LexAI.AIAssistant.Domain.Enums;

namespace LexAI.AIAssistant.Domain.Entities;

/// <summary>
/// Entité représentant une session de chat
/// </summary>
public class ChatSession
{
    /// <summary>
    /// Identifiant unique de la session
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Token de session unique
    /// </summary>
    public string SessionToken { get; set; } = string.Empty;

    /// <summary>
    /// Statut de la session
    /// </summary>
    public SessionStatus Status { get; set; }

    /// <summary>
    /// Contexte de la session (paramètres, préférences, etc.)
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// Date de création de la session
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date d'expiration de la session
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Date de dernière activité
    /// </summary>
    public DateTime? LastActivityAt { get; set; }

    /// <summary>
    /// Adresse IP du client
    /// </summary>
    public string? ClientIpAddress { get; set; }

    /// <summary>
    /// User Agent du client
    /// </summary>
    public string? ClientUserAgent { get; set; }
}
