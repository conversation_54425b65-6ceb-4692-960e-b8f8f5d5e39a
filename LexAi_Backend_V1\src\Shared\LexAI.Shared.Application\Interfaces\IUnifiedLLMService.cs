using LexAI.Shared.Application.DTOs.LLM;

namespace LexAI.Shared.Application.Interfaces;

/// <summary>
/// Interface pour le service LLM unifié
/// </summary>
public interface IUnifiedLLMService
{
    /// <summary>
    /// Envoie un prompt au LLM et retourne la réponse
    /// </summary>
    Task<LLMResponse> SendPromptAsync(string prompt, LLMOptions? options = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Envoie une conversation au LLM
    /// </summary>
    Task<LLMResponse> SendConversationAsync(IEnumerable<LLMMessage> messages, LLMOptions? options = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyse un texte et extrait des informations structurées
    /// </summary>
    Task<T?> AnalyzeStructuredAsync<T>(string text, string analysisPrompt, LLMOptions? options = null, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Vérifie si le service LLM est disponible
    /// </summary>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les informations sur le modèle actuel
    /// </summary>
    LLMModelInfo GetModelInfo();
}


