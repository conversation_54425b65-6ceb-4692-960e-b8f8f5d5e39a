# Script PowerShell pour démarrer l'infrastructure complète LexAI
# Auteur: LexAI Team
# Version: 1.0

Write-Host "🚀 Démarrage de l'infrastructure LexAI..." -ForegroundColor Green

# Vérifier que Docker est installé et en cours d'exécution
try {
    docker --version | Out-Null
    Write-Host "✅ Docker détecté" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker n'est pas installé ou n'est pas en cours d'exécution" -ForegroundColor Red
    exit 1
}

# Vérifier que Docker Compose est disponible
try {
    docker compose version | Out-Null
    Write-Host "✅ Docker Compose détecté" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose n'est pas disponible" -ForegroundColor Red
    exit 1
}

# Naviguer vers le répertoire du projet
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

Write-Host "📁 Répertoire de travail: $projectRoot" -ForegroundColor Cyan

# Créer le fichier .env s'il n'existe pas
$envFile = Join-Path $projectRoot ".env"
if (-not (Test-Path $envFile)) {
    Write-Host "📝 Création du fichier .env..." -ForegroundColor Yellow
    @"
# Configuration LexAI
OPENAI_API_KEY=your-openai-api-key-here
ASPNETCORE_ENVIRONMENT=Development

# Base de données
POSTGRES_PASSWORD=lexai_password_2024!
MONGO_PASSWORD=lexai_mongo_password_2024!
REDIS_PASSWORD=lexai_redis_password_2024!
RABBITMQ_PASSWORD=lexai_rabbitmq_password_2024!

# JWT
JWT_SECRET_KEY=your-super-secret-jwt-key-that-is-at-least-32-characters-long
JWT_ISSUER=LexAI
JWT_AUDIENCE=LexAI-Users

# Ports
POSTGRES_PORT=5432
MONGODB_PORT=27017
REDIS_PORT=6379
RABBITMQ_PORT=5672
RABBITMQ_MANAGEMENT_PORT=15672
QDRANT_HTTP_PORT=6333
QDRANT_GRPC_PORT=6334
MONGO_EXPRESS_PORT=8081
"@ | Out-File -FilePath $envFile -Encoding UTF8
    Write-Host "✅ Fichier .env créé. Veuillez configurer votre clé OpenAI." -ForegroundColor Green
}

# Arrêter les conteneurs existants
Write-Host "🛑 Arrêt des conteneurs existants..." -ForegroundColor Yellow
docker compose down --remove-orphans

# Nettoyer les volumes orphelins (optionnel)
$cleanVolumes = Read-Host "Voulez-vous nettoyer les volumes de données ? (y/N)"
if ($cleanVolumes -eq "y" -or $cleanVolumes -eq "Y") {
    Write-Host "🧹 Nettoyage des volumes..." -ForegroundColor Yellow
    docker compose down -v
    docker volume prune -f
}

# Construire et démarrer les services
Write-Host "🏗️ Construction et démarrage des services..." -ForegroundColor Cyan
docker compose up -d --build

# Attendre que les services soient prêts
Write-Host "⏳ Attente du démarrage des services..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Vérifier l'état des services
Write-Host "🔍 Vérification de l'état des services..." -ForegroundColor Cyan

$services = @(
    @{Name="PostgreSQL"; Port=5432; Container="lexai-postgres"},
    @{Name="MongoDB"; Port=27017; Container="lexai-mongodb"},
    @{Name="Redis"; Port=6379; Container="lexai-redis"},
    @{Name="RabbitMQ"; Port=5672; Container="lexai-rabbitmq"},
    @{Name="Qdrant"; Port=6333; Container="lexai-qdrant"},
    @{Name="Mongo Express"; Port=8081; Container="lexai-mongo-express-0"}
)

foreach ($service in $services) {
    $status = docker ps --filter "name=$($service.Container)" --format "{{.Status}}"
    if ($status -like "*Up*") {
        Write-Host "✅ $($service.Name) - Port $($service.Port)" -ForegroundColor Green
    } else {
        Write-Host "❌ $($service.Name) - Port $($service.Port)" -ForegroundColor Red
    }
}

# Afficher les URLs d'accès
Write-Host "`n🌐 URLs d'accès aux services:" -ForegroundColor Cyan
Write-Host "   📊 Mongo Express: http://localhost:8081 (admin/mongoexpress_2024!)" -ForegroundColor White
Write-Host "   🐰 RabbitMQ Management: http://localhost:15672 (lexai_user/lexai_rabbitmq_password_2024!)" -ForegroundColor White
Write-Host "   🔍 Qdrant Dashboard: http://localhost:6333/dashboard" -ForegroundColor White

# Afficher les informations de connexion
Write-Host "`n📋 Informations de connexion:" -ForegroundColor Cyan
Write-Host "   🐘 PostgreSQL: localhost:5432 (lexai_user/lexai_password_2024!)" -ForegroundColor White
Write-Host "   🍃 MongoDB: localhost:27017 (lexai_admin/lexai_mongo_password_2024!)" -ForegroundColor White
Write-Host "   🔴 Redis: localhost:6379 (password: lexai_redis_password_2024!)" -ForegroundColor White
Write-Host "   🔍 Qdrant: localhost:6333 (HTTP) / localhost:6334 (gRPC)" -ForegroundColor White

# Vérifier les logs en cas d'erreur
$checkLogs = Read-Host "`nVoulez-vous voir les logs des services ? (y/N)"
if ($checkLogs -eq "y" -or $checkLogs -eq "Y") {
    Write-Host "📋 Affichage des logs..." -ForegroundColor Yellow
    docker compose logs --tail=50
}

Write-Host "`n🎉 Infrastructure LexAI démarrée avec succès !" -ForegroundColor Green
Write-Host "💡 Utilisez 'docker compose logs -f' pour suivre les logs en temps réel" -ForegroundColor Cyan
Write-Host "💡 Utilisez 'docker compose down' pour arrêter l'infrastructure" -ForegroundColor Cyan
