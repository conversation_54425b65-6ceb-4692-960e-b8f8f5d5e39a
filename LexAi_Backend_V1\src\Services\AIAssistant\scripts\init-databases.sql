-- Script d'initialisation PostgreSQL pour le service AI Assistant

-- <PERSON><PERSON><PERSON> l'utilisateur pour l'application (avec gestion des erreurs)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'lexai_ai_assistant_user') THEN
        CREATE USER lexai_ai_assistant_user WITH PASSWORD 'lexai_password_2024!';
        RAISE NOTICE 'Utilisateur lexai_ai_assistant_user créé';
    ELSE
        RAISE NOTICE 'Utilisateur lexai_ai_assistant_user existe déjà';
    END IF;
END
$$;

-- S'assurer que le mot de passe est correct
ALTER USER lexai_ai_assistant_user WITH PASSWORD 'lexai_password_2024!';

-- Créer la base de données principale
SELECT 'CREATE DATABASE lexai_ai_assistant OWNER lexai_ai_assistant_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'lexai_ai_assistant')\gexec

-- Accorder tous les privilèges sur les bases de données
GRANT ALL PRIVILEGES ON DATABASE lexai_ai_assistant TO lexai_ai_assistant_user;

-- Se connecter à la base de données principale pour configurer les extensions
\c lexai_ai_assistant;

-- Créer les extensions nécessaires
-- Activer l'extension UUID pour générer des UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Activer l'extension pour les types JSON avancés
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Activer l'extension pour les recherches textuelles
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Accorder les privilèges sur le schéma public
GRANT ALL ON SCHEMA public TO lexai_ai_assistant_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO lexai_ai_assistant_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO lexai_ai_assistant_user;

-- Configurer les privilèges par défaut pour les futurs objets
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO lexai_ai_assistant_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO lexai_ai_assistant_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO lexai_ai_assistant_user;

-- Configurer les paramètres de performance
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Configurer pour les recherches textuelles
ALTER SYSTEM SET default_text_search_config = 'pg_catalog.french';

-- Retourner à la base de données postgres pour les logs
\c postgres;

-- Message de confirmation
SELECT 'Base de données AI Assistant initialisée avec succès!' as message;
