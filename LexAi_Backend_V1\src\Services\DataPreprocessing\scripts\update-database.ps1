﻿# Script pour mettre à jour la base de données
param(
    [string]$ConnectionString = $null,
    [switch]$Production = $false
)

Write-Host "Mise à jour de la base de données Data Preprocessing..." -ForegroundColor Green

# Naviguer vers le répertoire du projet API
$apiProject = "src/Services/DataPreprocessing/LexAI.DataPreprocessing.API"
$infrastructureProject = "src/Services/DataPreprocessing/LexAI.DataPreprocessing.Infrastructure"

if (!(Test-Path $apiProject)) {
    Write-Error "Le projet API n'existe pas : $apiProject"
    exit 1
}

if (!(Test-Path $infrastructureProject)) {
    Write-Error "Le projet Infrastructure n'existe pas : $infrastructureProject"
    exit 1
}

try {
    # Définir l'environnement
    $environment = if ($Production) { "Production" } else { "Development" }
    $env:ASPNETCORE_ENVIRONMENT = $environment
    
    Write-Host "Environnement : $environment" -ForegroundColor Yellow
    
    # Construire la commande
    $command = "dotnet ef database update --project $infrastructureProject --startup-project $apiProject "
    
    if ($ConnectionString) {
        $command += " --connection `"$ConnectionString`""
        Write-Host "Utilisation de la chaîne de connexion personnalisée" -ForegroundColor Yellow
    }
    
    # Afficher les migrations en attente
    Write-Host "Vérification des migrations en attente..." -ForegroundColor Yellow
    dotnet ef migrations list --project $infrastructureProject --startup-project $apiProject 
    
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "Impossible de lister les migrations. Continuons quand même..."
    }
    
    # Demander confirmation en production
    if ($Production) {
        Write-Host ""
        Write-Warning "ATTENTION : Vous êtes sur le point de mettre à jour la base de données de PRODUCTION !"
        $confirmation = Read-Host "Êtes-vous sûr de vouloir continuer ? (oui/non)"
        if ($confirmation -ne "oui") {
            Write-Host "Opération annulée." -ForegroundColor Yellow
            exit 0
        }
    }
    
    # Exécuter la mise à jour
    Write-Host "Mise à jour de la base de données..." -ForegroundColor Yellow
    Invoke-Expression $command
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Base de données mise à jour avec succès !" -ForegroundColor Green
    } else {
        Write-Error "Erreur lors de la mise à jour de la base de données"
        exit 1
    }
}
catch {
    Write-Error "Erreur lors de la mise à jour : $_"
    exit 1
}
finally {
    # Nettoyer les variables d'environnement
    Remove-Item Env:ASPNETCORE_ENVIRONMENT -ErrorAction SilentlyContinue
}
