import React, { useState, useEffect } from 'react'
import { Plus, MessageCircle, Trash2, MoreVertical } from 'lucide-react'
import { chatApi } from '../../services/aiAssistant/chat.api'
import { Button } from '../ui/Button'

interface Conversation {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  messageCount?: number
}

interface ChatSidebarProps {
  currentConversationId?: string
  onConversationSelect: (conversationId: string) => void
  onNewConversation: () => void
}

export const ChatSidebar: React.FC<ChatSidebarProps> = ({
  currentConversationId,
  onConversationSelect,
  onNewConversation
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Charger les conversations
  const loadConversations = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await chatApi.getConversations({ page: 1, pageSize: 50 })
      setConversations(response.items)
    } catch (err: any) {
      setError('Erreur lors du chargement des conversations')
      console.error('Error loading conversations:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Charger les conversations au montage
  useEffect(() => {
    loadConversations()
  }, [])

  // Supprimer une conversation
  const handleDeleteConversation = async (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette conversation ?')) {
      return
    }

    try {
      await chatApi.deleteConversation(conversationId)
      setConversations(prev => prev.filter(c => c.id !== conversationId))
      
      // Si c'est la conversation actuelle, créer une nouvelle
      if (currentConversationId === conversationId) {
        onNewConversation()
      }
    } catch (err) {
      console.error('Error deleting conversation:', err)
    }
  }

  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString('fr-FR', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' })
    }
  }

  return (
    <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <Button
          onClick={onNewConversation}
          className="w-full flex items-center gap-2 justify-center"
          variant="default"
        >
          <Plus className="h-4 w-4" />
          Nouvelle conversation
        </Button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          {/* Pinned Chats Section */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 px-2">
              Pinned chats
            </h3>
            <div className="text-xs text-gray-400 dark:text-gray-500 px-2 py-4 text-center italic">
              No pinned chats yet !
            </div>
          </div>

          {/* Recent Conversations */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 px-2">
              History
            </h3>
          </div>

          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
          )}

          {error && (
            <div className="p-2 text-sm text-red-600 dark:text-red-400">
              {error}
            </div>
          )}

          {!isLoading && !error && conversations.length === 0 && (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400 text-sm italic">
              History is empty
            </div>
          )}

          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`group relative p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                currentConversationId === conversation.id
                  ? 'bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-800'
              }`}
              onClick={() => onConversationSelect(conversation.id)}
            >
              <div className="flex items-start gap-2">
                <MessageCircle className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {conversation.title || 'Nouvelle conversation'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {formatDate(conversation.updatedAt)}
                  </div>
                </div>
                <button
                  onClick={(e) => handleDeleteConversation(conversation.id, e)}
                  className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-all"
                  title="Supprimer"
                >
                  <Trash2 className="h-3 w-3 text-red-500" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={loadConversations}
          className="w-full text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
        >
          Actualiser
        </button>
      </div>
    </div>
  )
}
