2025-06-17 21:12:44.897 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-17 21:12:45.150 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 21:12:45.588 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-17 21:12:45.696 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-17 21:12:45.843 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 21:12:45.848 +04:00 [INF] Hosting environment: Development
2025-06-17 21:12:45.851 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-17 21:12:46.293 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-17 21:12:46.540 +04:00 [INF] Request GET / started with correlation ID 47e34198-b64e-4b13-af52-2ae061bed3dd
2025-06-17 21:12:48.387 +04:00 [INF] Request GET / completed in 1831ms with status 404 (Correlation ID: 47e34198-b64e-4b13-af52-2ae061bed3dd)
2025-06-17 21:12:48.397 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 2107.1161ms
2025-06-17 21:12:48.420 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-17 21:13:53.427 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-17 21:13:53.532 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 10d9cde9-e67d-4859-aa8d-2cff4b6f52b6
2025-06-17 21:13:53.548 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:13:53.558 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 18ms with status 204 (Correlation ID: 10d9cde9-e67d-4859-aa8d-2cff4b6f52b6)
2025-06-17 21:13:53.567 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 139.1406ms
2025-06-17 21:13:53.571 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 21:13:53.587 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 75ead4e7-1d7d-4236-90f5-44df7ab699ec
2025-06-17 21:13:53.593 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:13:53.897 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:47:06 PM', Current time (UTC): '6/17/2025 5:13:53 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-17 21:13:54.012 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:47:06 PM', Current time (UTC): '6/17/2025 5:13:53 PM'.
2025-06-17 21:13:54.024 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/14/2025 10:47:06 PM', Current time (UTC): '6/17/2025 5:13:53 PM'.
2025-06-17 21:13:54.042 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-17 21:13:54.056 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-17 21:13:54.061 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 468ms with status 401 (Correlation ID: 75ead4e7-1d7d-4236-90f5-44df7ab699ec)
2025-06-17 21:13:54.069 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 401 0 null 497.9006ms
2025-06-17 21:13:57.836 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 21:13:57.844 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 52c011cd-eb3a-4bf1-8be1-bf1b4a527bca
2025-06-17 21:13:57.848 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:13:57.874 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:13:57.885 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:13:57.976 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:13:58.968 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:14:00.302 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:14:03.094 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:14:03.240 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:03.261 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:04.733 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-17 21:14:04.790 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 21:14:04.809 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 21:14:04.815 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 21:14:04.820 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 21:14:06.325 +04:00 [INF] Executed DbCommand (131ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-17 21:14:06.734 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-17 21:14:06.869 +04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-17 21:14:07.336 +04:00 [INF] Retrieved 3 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:07.353 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-17 21:14:07.403 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 9411.95ms
2025-06-17 21:14:07.417 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:14:07.428 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 9580ms with status 200 (Correlation ID: 52c011cd-eb3a-4bf1-8be1-bf1b4a527bca)
2025-06-17 21:14:07.468 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 9631.6346ms
2025-06-17 21:14:20.811 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - null null
2025-06-17 21:14:20.826 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 started with correlation ID 7cd7302f-8f39-441c-b74c-0f737ca1be29
2025-06-17 21:14:20.838 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:14:20.843 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 completed in 7ms with status 204 (Correlation ID: 7cd7302f-8f39-441c-b74c-0f737ca1be29)
2025-06-17 21:14:20.857 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - 204 null null 45.2666ms
2025-06-17 21:14:20.867 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - application/json null
2025-06-17 21:14:20.892 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 started with correlation ID cf55f4ff-95d0-44a6-9c17-a8a04337d25b
2025-06-17 21:14:20.898 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:14:20.904 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:14:20.915 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 21:14:20.933 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:14:20.949 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:14:20.956 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:14:20.965 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:14:20.973 +04:00 [INF] Retrieving analysis "51764e83-da50-4569-8184-891ce018b949" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:20.992 +04:00 [INF] Retrieving analysis result: "51764e83-da50-4569-8184-891ce018b949" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:21.155 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-17 21:14:21.334 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='51764e83-da50-4569-8184-891ce018b949', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExtractedText", d2."IsDeleted", d2."ModelUsed", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-17 21:14:21.444 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-17 21:14:21.515 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 568.1468ms
2025-06-17 21:14:21.519 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 21:14:21.521 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 completed in 623ms with status 200 (Correlation ID: cf55f4ff-95d0-44a6-9c17-a8a04337d25b)
2025-06-17 21:14:21.526 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/51764e83-da50-4569-8184-891ce018b949 - 200 null application/json; charset=utf-8 657.9765ms
2025-06-17 21:14:56.994 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-17 21:14:57.007 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID a6361145-6ac9-4502-bd04-183289a154cf
2025-06-17 21:14:57.015 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:14:57.020 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 5ms with status 204 (Correlation ID: a6361145-6ac9-4502-bd04-183289a154cf)
2025-06-17 21:14:57.031 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 36.85ms
2025-06-17 21:14:57.035 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 21:14:57.052 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID f4c2d1a0-9414-42c7-b4f9-79894a5bc741
2025-06-17 21:14:57.060 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:14:57.064 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:14:57.067 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:14:57.073 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:14:57.087 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:14:57.095 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:14:57.115 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:14:57.126 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:57.133 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:57.326 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-17 21:14:57.336 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-17 21:14:57.344 +04:00 [INF] Retrieved 3 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:14:57.347 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-17 21:14:57.350 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 269.6331ms
2025-06-17 21:14:57.354 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:14:57.356 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 295ms with status 200 (Correlation ID: f4c2d1a0-9414-42c7-b4f9-79894a5bc741)
2025-06-17 21:14:57.361 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 325.4803ms
2025-06-17 21:25:18.065 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-17 21:25:18.075 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 489128f4-30aa-40c8-bbc2-4b3394e82c5d
2025-06-17 21:25:18.078 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:25:18.081 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 3ms with status 204 (Correlation ID: 489128f4-30aa-40c8-bbc2-4b3394e82c5d)
2025-06-17 21:25:18.088 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 23.0221ms
2025-06-17 21:25:18.091 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundaryN22h1y8U0vfIykit 599138
2025-06-17 21:25:18.174 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID ca37f39b-7b8d-4db2-9835-fc6086ccc26d
2025-06-17 21:25:18.180 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:25:18.185 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:25:18.188 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-17 21:25:18.207 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:25:18.216 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:25:18.219 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:25:18.222 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:25:18.299 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_france.pdf
2025-06-17 21:25:18.322 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test_france.pdf
2025-06-17 21:25:18.375 +04:00 [INF] Starting comprehensive document analysis for: Cdd_test_france.pdf
2025-06-17 21:25:18.408 +04:00 [INF] Document stored successfully: 96f744f8d9f7277098a42d64026c10e291f056073eb8fd62827cd3c031ff8f01 -> ./storage/documents\96f744f8d9f7277098a42d64026c10e291f056073eb8fd62827cd3c031ff8f01.pdf
2025-06-17 21:25:18.420 +04:00 [INF] Document stored with hash: 96f744f8d9f7277098a42d64026c10e291f056073eb8fd62827cd3c031ff8f01 at ./storage/documents\96f744f8d9f7277098a42d64026c10e291f056073eb8fd62827cd3c031ff8f01.pdf
2025-06-17 21:25:18.475 +04:00 [INF] Starting Azure Document Intelligence extraction for document type: pdf
2025-06-17 21:25:24.502 +04:00 [INF] Azure extraction completed successfully. Processing time: 6007ms, Pages: 2
2025-06-17 21:25:24.508 +04:00 [INF] Starting clause analysis for document type: pdf
2025-06-17 21:25:32.851 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "clauses": [\n    {\n      "clauseText": "Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activi
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:35.241 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise que le contrat est à durée déterminée conformément à l'article L1242-2 du Code du travail, visant un accroissement temporaire d'activité. Elle est concise mais manq
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:37.205 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise la date de début du contrat au 1er juillet 2025 et sa durée de 6 mois, se terminant le 31 décembre 2025 inclus. Elle est claire quant aux dates, mais ne mentionne p
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:39.073 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause définit la fonction de M. Jean Luc en tant que Chargé de gestion des risques numériques, sous la supervision du Chef de département sécurité. Elle précise le poste occupé m
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.Serialization.Metadata.JsonTypeInfo`1.Deserialize(Utf8JsonReader& reader, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:41.718 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise que le salarié doit exercer ses fonctions au siège ou dans un autre lieu désigné par l'employeur dans un rayon de 20 km, ce qui offre une certaine flexibilité à l'e
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:43.892 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause précise le montant de la rémunération brute mensuelle de 1200 euros, indique qu'elle sera versée à terme échu et se réfère aux modalités du Code du travail. Elle est concis
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:46.446 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause établit une durée de travail hebdomadaire de 48 heures, réparties du lundi au samedi, avec des horaires déterminés par l'employeur en fonction de l'activité. Elle ne précis
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:48.719 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause informe le salarié des risques professionnels liés à son poste, notamment l'exposition aux écrans, la charge mentale, la manipulation de données confidentielles et les risq
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:50.837 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause impose au salarié une obligation de confidentialité couvrant la période du contrat et postérieurement, sans préciser la nature des informations protégées ni les exceptions 
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:53.126 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "La clause limite la possibilité de rupture anticipée du contrat aux cas prévus par la loi, tels que faute grave, force majeure, accord commun ou embauche en CDI. Elle offre une certa
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:55.428 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "analysis": "Cette clause prévoit la fin automatique du contrat à une date fixe sans nécessité de notification préalable, ce qui simplifie la procédure mais limite la flexibilité pour les parties
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:25:55.603 +04:00 [INF] Clause analysis completed. Found 10 clauses
2025-06-17 21:25:55.608 +04:00 [INF] Starting risk assessment for document type: pdf
2025-06-17 21:25:58.171 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "risks": [\n    {\n      "riskType": "Risques structurels",\n      "description": "Le contrat ne prévoit pas de mécanismes pour gérer la charge de travail élevée (48h/semaine) pouvant entraîner u
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:02.339 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "risques": {\n    "conformite_RGPD": {\n      "risques_identifies": [\n        "Manipulation et traitement de données confidentielles sans mention explicite de mesures de protection spécifiques",
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:02.679 +04:00 [ERR] Failed to parse JSON even after cleaning. Original: {\n  "risques": {\n    "conformite_RGPD": {\n      "risques_identifies": [\n        "Manipulation et, Cleaned: {\n  "risques": {\n    "conformite_RGPD": {\n      "risques_identifies": [\n        \"Manipulation e
2025-06-17 21:26:02.699 +04:00 [INF] Risk assessment completed. Identified 3 risks
2025-06-17 21:26:02.705 +04:00 [INF] Generating recommendations for document type: pdf
2025-06-17 21:26:06.408 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "type": "Risk Mitigation",\n      "title": "Renforcement de la sécurité des données et cybersécurité",\n      "description": "Mettre en place des protocoles str
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:11.188 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "clause": "Nature du contrat",\n      "suggestion": "Préciser la durée exacte du contrat, les modalités de renouvellement éventuel, et les conditions spécifique
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:14.498 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "category": "Structure",\n      "suggestion": "Organiser le contrat avec une table des matières ou une numérotation claire pour chaque section afin de faciliter
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:18.680 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "recommendations": [\n    {\n      "type": "Compliance",\n      "description": "Vérifier que le contrat inclut une mention explicite de la conformité avec le RGPD, notamment en ce qui concerne la
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:18.834 +04:00 [INF] Prioritizing 32 recommendations
2025-06-17 21:26:21.024 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "prioritizedRecommendations": [\n    {\n      "index": 1,\n      "newPriority": "Critical",\n      "justification": "La sécurité des données et la cybersécurité ont un impact juridique élevé en c
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:21.158 +04:00 [INF] Generated 4 recommendations
2025-06-17 21:26:27.370 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "entities": [\n    {\n      "text": "LexBot S.A.R.L.",\n      "type": "ORGANIZATION",\n      "startPosition": 15,\n      "endPosition": 30,\n      "confidence": 0.95,\n      "normalizedValue": "L
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:28.785 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "citations": [\n    {\n      "title": "article L1242-2 du Code du travail",\n      "source": "Code du travail",\n      "url": "https://www.legifrance.gouv.fr/codes/article_lc/LEGIARTI000006902747
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:30.671 +04:00 [WRN] Failed to parse JSON response, attempting fallback parsing. Content: {\n  "executiveSummary": "Ce contrat de travail à durée déterminée de 6 mois entre LexBot S.A.R.L. et M. Jean Luc définit les modalités d'emploi pour un poste de Chargé de gestion des risques numériqu
System.Text.Json.JsonException: '\' is an invalid start of a property name. Expected a '"'. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
 ---> System.Text.Json.JsonReaderException: '\' is an invalid start of a property name. Expected a '"'. LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, T& value, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at LexAI.Shared.Infrastructure.Services.UnifiedLLMService.AnalyzeStructuredAsync[T](String text, String analysisPrompt, LLMOptions options, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Shared\LexAI.Shared.Infrastructure\Services\UnifiedLLMService.cs:line 193
2025-06-17 21:26:38.406 +04:00 [INF] Executed DbCommand (82ms) [Parameters=[@p0='9602e845-8f66-481f-8642-956174d91787', @p1='Analyse juridique complète du contrat de travail à durée déterminée (CDD) présenté

---

**Synthèse des clauses importantes**

1. **Nature du contrat (Article 1)**  
- Contrat à durée déterminée (CDD) pour accroissement temporaire d'activité, conformément à l'article L1242-2 du Code du travail.

2. **Durée et date de début/fin (Article 2)**  
- Du 1er juillet 2025 au 31 décembre 2025 (6 mois).  
- Fin automatique à l’échéance, sans nécessité de notification.

3. **Poste et responsabilités (Article 3)**  
- Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.

4. **Lieu de travail (Article 4)**  
- Siège social ou tout autre lieu dans un rayon de 20 km, selon décision de l’employeur.

5. **Rémunération (Article 5)**  
- 1 200 euros brut mensuels, versés à terme échu, selon modalités légales.

6. **Durée du travail (Article 6)**  
- 48 heures par semaine, du lundi au samedi, horaires définis par l’employeur.

7. **Risques liés au poste (Article 7)**  
- Exposition aux écrans, charge mentale, manipulation de données confidentielles, risques psychosociaux liés à la cybersécurité.  
- Accompagnement par le médecin du travail et un référent en santé mentale.

8. **Confidentialité (Article 8)**  
- Engagement de confidentialité pendant et après le contrat.

9. **Rupture anticipée (Article 9)**  
- Possible uniquement en cas de faute grave, force majeure, accord mutuel ou embauche en CDI.

10. **Fin du contrat (Article 10)**  
- Fin automatique au terme fixé, sans renouvellement prévu.

---

**Risques identifiés**

1. **Risque de requalification ou de non-respect de la forme du CDD**  
- La nature du contrat doit respecter strictement la législation (article L1242-2). Tout usage abusif pourrait entraîner une requalification en CDI.

2. **Risques liés à la durée et à la rupture**  
- La fin automatique évite la nécessité de procédure de rupture, mais il faut s’assurer que le contrat ne constitue pas un emploi déguisé ou un contrat à durée indéfinie dissimulé.

3. **Risques liés à la santé et sécurité**  
- Exposition prolongée aux écrans, charge mentale, risques psychosociaux.  
- La mention d’un accompagnement est positive, mais il faut vérifier la mise en œuvre effective des mesures de prévention.

---

**Recommandations**

1. **Vérifier la conformité du motif du CDD**  
- S’assurer que l’accroissement temporaire d’activité est bien justifié et documenté.

2. **Respecter la procédure de conclusion du CDD**  
- Formaliser par écrit, respecter les délais, et prévoir une clause de renouvellement si nécessaire.

3. **Clarifier les modalités de lieu de travail**  
- Préciser si le lieu de travail peut évoluer ou si une mobilité est envisageable.

4. **Mettre en place un suivi santé renforcé**  
- S’assurer que le suivi médical et la prévention des risques professionnels sont effectifs.

5. **Anticiper la fin du contrat**  
- Prévoir une communication claire avec le salarié pour éviter tout malentendu.

6. **Vérifier la clause de confidentialité**  
- S’assurer qu’elle couvre bien toutes les données sensibles et qu’elle est conforme à la réglementation RGPD.

---

**Évaluation globale**

Ce contrat semble respecter les exigences légales du CDD, notamment en termes de motif, de durée, et de clauses essentielles. La mention des risques professionnels et de la confidentialité est appropriée, mais il conviendrait d’approfondir la mise en œuvre concrète de ces mesures.

Il est important de veiller à la conformité de la procédure de conclusion, notamment la justification du motif d’accroissement temporaire d’activité, et de s’assurer que le contrat ne présente pas de clauses abusives ou ambiguës.

---

**Conseils juridiques**

- **Vérification du motif du CDD** : Assurez-vous que l’accroissement d’activité est bien temporaire et justifié.  
- **Respect des formalités** : Formaliser le contrat par écrit, respecter le délai de remise au salarié, et conserver une copie signée.  
- **Suivi médical** : Mettre en place un protocole clair pour le suivi santé, notamment en lien avec les risques liés aux écrans et à la charge mentale.  
- **Prévention des risques psychosociaux** : Mettre en œuvre des actions concrètes pour limiter ces risques, notamment par la formation et le soutien psychologique.  
- **Clarté sur le lieu de travail** : Définir précisément si le lieu peut changer et dans quelles conditions.  
- **Confidentialité et RGPD** : Vérifier que la clause couvre toutes les données sensibles et respecter la réglementation en matière de protection des données.

---

**Conclusion**

Ce contrat de CDD est globalement conforme à la législation en vigueur, mais nécessite une vigilance particulière sur la justification du motif, la procédure de conclusion, et la mise en œuvre effective des mesures de prévention des risques professionnels. En respectant ces recommandations, l’employeur pourra limiter ses risques juridiques et assurer une relation de travail saine et conforme.

---

N'hésitez pas à consulter un avocat spécialisé en droit du travail pour une analyse approfondie et adaptée à votre contexte spécifique.' (Nullable = false), @p2='2025-06-17T17:26:38.0574262Z' (DbType = DateTime), @p3='0.9499999999999998', @p4='2025-06-17T17:26:38.0575177Z' (DbType = DateTime), @p5=NULL, @p6=NULL (DbType = DateTime), @p7=NULL, @p8='96f744f8d9f7277098a42d64026c10e291f056073eb8fd62827cd3c031ff8f01' (Nullable = false), @p9='Cdd_test_france.pdf' (Nullable = false), @p10='./storage/documents\96f744f8d9f7277098a42d64026c10e291f056073eb8fd62827cd3c031ff8f01.pdf' (Nullable = false), @p11='pdf' (Nullable = false), @p12='0', @p13='CONTRAT DE TRAVAIL À DURÉE DÉTERMINÉE (CDD)
Entre les soussignés :
L'employeur
LexBot S.A.R.L., au capital de 50 000 euros, dont le siège social est situé au 12, Rue de l'Intelligence Artificielle, 75001 Paris, immatriculée au RCS de Paris sous le numéro 882 000 123, Représentée par Mme Clara Dupont, en sa qualité de Directrice Générale, Ci-après dénommée « l'Employeur »
Et
M. Jean Luc, né le 12 mars 1989 à Lyon (France), demeurant au 8, rue des Lilas, 69001 Lyon, de nationalité française, Ci-après dénommé « le Salarié »
Article 1 - Nature du contrat
Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.
Article 2 - Date de début et de fin
Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.
Article 3 - Poste occupé
M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.
Article 4 - Lieu de travail
Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.
Article 5 - Rémunération
La rémunération brute mensuelle sera de 1 200 euros, versée à terme échu, selon les modalités prévues par le Code du travail.
1 | Page KEVIN WILFRIED
Article 6 - Durée du travail
La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.
Article 7 - Risques liés au poste
Le salarié est informé que le poste comporte les risques professionnels suivants :
· Exposition prolongée aux écrans (fatigue visuelle, TMS)
· Charge mentale importante liée à la gestion des risques informatiques
· Manipulation de données confidentielles (obligation de vigilance accrue)
· Risques psychosociaux en cas d'incident majeur de cybersécurité
L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.
Article 8 - Clause de confidentialité
Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.
Article 9 - Rupture anticipée
Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi : faute grave, force majeure, accord commun ou embauche en CDI.
Article 10 - Fin du contrat
Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.
Fait à Paris, le 14 juin 2025
En deux exemplaires originaux.
Signature de l'Employeur (LexBot)
Signature :
Signature du Salarié (Jean Luc)
Signature :
2 | Page KEVIN WILFRIED' (Nullable = false), @p14='False', @p15='lexai-gpt-4.1-nano' (Nullable = false), @p16='79674', @p17='Completed' (Nullable = false), @p18='1282', @p19='2025-06-17T17:26:38.0576448Z' (Nullable = true) (DbType = DateTime), @p20=NULL, @p21='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_analysis_results ("Id", "AnalysisContent", "AnalyzedAt", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentHash", "DocumentName", "DocumentStoragePath", "DocumentType", "EstimatedCost", "ExtractedText", "IsDeleted", "ModelUsed", "ProcessingTimeMs", "Status", "TokensUsed", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21);
2025-06-17 21:26:38.436 +04:00 [INF] Analysis saved to database with ID: "9602e845-8f66-481f-8642-956174d91787"
2025-06-17 21:26:38.511 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='9828a979-47ba-43b5-8d56-d9b5fd7717e1', @p1='La clause précise que le contrat est à durée déterminée conformément à l'article L1242-2 du Code du travail, visant un accroissement temporaire d'activité. Elle est concise mais manque de détails sur la durée précise, les modalités de renouvellement, ou les conditions spécifiques à l'accroissement d'activité, ce qui peut limiter la compréhension pour les parties et poser des questions sur la conformité à la réglementation applicable.' (Nullable = false), @p2='Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.' (Nullable = false), @p3='Nature du contrat' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.4467156Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='211', @p11='False', @p12='Medium' (Nullable = false), @p13='119', @p14='Préciser la durée exacte du contrat, les conditions de renouvellement, et les modalités précises de l'accroissement d'activité pour renforcer la clarté et la conformité.', @p15='["contrat \u00E0 dur\u00E9e d\u00E9termin\u00E9e","article L1242-2","accroissement temporaire d\u0027activit\u00E9"]' (Nullable = false), @p16='2025-06-17T17:26:38.4467168Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.530 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='d0261a76-fd2d-40c8-9b03-98236e0de467', @p1='La clause précise la date de début du contrat au 1er juillet 2025 et sa durée de 6 mois, se terminant le 31 décembre 2025 inclus. Elle est claire quant aux dates, mais ne mentionne pas les modalités de renouvellement ou de modification, ce qui pourrait poser problème en cas de changement de calendrier. La formulation est concise mais pourrait bénéficier d'une clarification sur la possibilité de prolongation ou de résiliation anticipée.' (Nullable = false), @p2='Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.' (Nullable = false), @p3='Date de début et de fin' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.5216692Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='271', @p11='False', @p12='Low' (Nullable = false), @p13='212', @p14='Ajouter une clause précisant les modalités de renouvellement, de résiliation ou de modification des dates, pour renforcer la clarté et la flexibilité du contrat.', @p15='["date","dur\u00E9e","clart\u00E9","contrat"]' (Nullable = false), @p16='2025-06-17T17:26:38.5216699Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.554 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@p0='3a3aa5a9-dffa-4017-9784-16b8fb323304', @p1='La clause définit la fonction de M. Jean Luc en tant que Chargé de gestion des risques numériques, sous la supervision du Chef de département sécurité. Elle précise le poste occupé mais manque de détails sur la durée, les modalités d'exercice, la rémunération ou les conditions spécifiques. La formulation est claire mais pourrait bénéficier de précisions supplémentaires pour éviter toute ambiguïté.' (Nullable = false), @p2='M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.' (Nullable = false), @p3='Poste occupé' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.5373424Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='347', @p11='False', @p12='Medium' (Nullable = false), @p13='272', @p14='M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques pour une durée de [préciser la durée], sous la responsabilité du Chef de département sécurité, selon les modalités définies dans le contrat de travail. La rémunération sera conforme aux dispositions en vigueur.', @p15='["contrat de travail","clart\u00E9","risque juridique"]' (Nullable = false), @p16='2025-06-17T17:26:38.5373428Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.626 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='9e275a78-cd4d-4e02-ab7a-9bb5fa51ee68', @p1='La clause précise que le salarié doit exercer ses fonctions au siège ou dans un autre lieu désigné par l'employeur dans un rayon de 20 km, ce qui offre une certaine flexibilité à l'employeur tout en limitant la localisation géographique. Cependant, elle manque de détails sur la procédure de désignation du lieu, ce qui pourrait entraîner des ambiguïtés ou des litiges. La clause ne mentionne pas si le salarié doit accepter tout changement dans ce rayon ou si des conditions particulières s'appliquent, ce qui pourrait poser problème en termes d'équité et de respect des droits du salarié.' (Nullable = false), @p2='Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.' (Nullable = false), @p3='Lieu de travail' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6197399Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='423', @p11='False', @p12='Medium' (Nullable = false), @p13='348', @p14='Le salarié exercera ses fonctions principalement au siège de l'entreprise ou dans tout autre lieu désigné par l'employeur dans un rayon de 20 km, sous réserve d'une notification préalable et d'une justification raisonnable. Toute modification du lieu devra respecter un délai de prévenance de 30 jours et faire l'objet d'un accord écrit si elle dépasse le rayon initial.', @p15='["lieu de travail","flexibilit\u00E9","droit du travail","clart\u00E9","\u00E9quit\u00E9"]' (Nullable = false), @p16='2025-06-17T17:26:38.6197403Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.634 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='063c5e3f-c4e5-469c-a776-f6587fe36339', @p1='La clause précise le montant de la rémunération brute mensuelle de 1200 euros, indique qu'elle sera versée à terme échu et se réfère aux modalités du Code du travail. Elle est concise mais manque de détails sur les modalités précises de paiement, la périodicité, les éventuelles retenues ou ajustements, ce qui pourrait entraîner des ambiguïtés ou des malentendus. La référence au Code du travail assure une conformité générale, mais l'absence de précisions spécifiques peut poser des problèmes en cas de litige.' (Nullable = false), @p2='La rémunération brute mensuelle sera de 1 200 euros, versée à terme échu, selon les modalités prévues par le Code du travail.' (Nullable = false), @p3='Rémunération' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6307542Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='491', @p11='False', @p12='Medium' (Nullable = false), @p13='424', @p14='Inclure des détails précis sur la date de paiement, les modalités de calcul, les retenues éventuelles, et les conditions spécifiques applicables pour renforcer la clarté et réduire les risques de litige.', @p15='["r\u00E9mun\u00E9ration","clart\u00E9","conformit\u00E9","droit du travail"]' (Nullable = false), @p16='2025-06-17T17:26:38.6307545Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.645 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='82881d50-afcf-41cf-bf41-339f71e47f17', @p1='La clause établit une durée de travail hebdomadaire de 48 heures, réparties du lundi au samedi, avec des horaires déterminés par l'employeur en fonction de l'activité. Elle ne précise pas si cette durée inclut ou non les heures supplémentaires, ni si elle respecte la réglementation en vigueur concernant la durée maximale légale, le repos quotidien et hebdomadaire, ou la compensation des heures supplémentaires. La flexibilité laissée à l'employeur pour définir les horaires peut entraîner une incertitude pour le salarié quant à ses horaires précis et à ses droits en matière de repos.' (Nullable = false), @p2='La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.' (Nullable = false), @p3='Durée du travail' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6404965Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='585', @p11='False', @p12='Medium' (Nullable = false), @p13='492', @p14='La clause devrait préciser si la durée de 48 heures inclut les heures supplémentaires, mentionner le respect des limites légales (par exemple, 35 heures en France), définir la rémunération ou la compensation des heures supplémentaires, et préciser la répartition des horaires avec plus de clarté pour assurer la transparence.', @p15='["dur\u00E9e du travail","horaires","conformit\u00E9 l\u00E9gale","flexibilit\u00E9"]' (Nullable = false), @p16='2025-06-17T17:26:38.6404967Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.655 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='3dbad7e8-b6e7-4fe0-8b1f-a9ed65075e4a', @p1='La clause informe le salarié des risques professionnels liés à son poste, notamment l'exposition aux écrans, la charge mentale, la manipulation de données confidentielles et les risques psychosociaux en cas d'incident de cybersécurité. Elle mentionne également les mesures d'accompagnement proposées par l'entreprise, ce qui montre une volonté de prévention et de soutien. Cependant, la formulation reste assez sommaire et pourrait manquer de précisions sur les mesures concrètes, la responsabilité de l'employeur, et la nature exacte des risques. La clause semble équilibrée en termes d'information, mais pourrait bénéficier d'une clarification supplémentaire pour renforcer la compréhension et la transparence.' (Nullable = false), @p2='Le salarié est informé que le poste comporte les risques professionnels suivants: Exposition prolongée aux écrans (fatigue visuelle, TMS), Charge mentale importante liée à la gestion des risques informatiques, Manipulation de données confidentielles (obligation de vigilance accrue), Risques psychosociaux en cas d'incident majeur de cybersécurité. L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.' (Nullable = false), @p3='Risques liés au poste' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6507807Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='722', @p11='False', @p12='Medium' (Nullable = false), @p13='586', @p14='Préciser les mesures concrètes de prévention, la responsabilité de l'employeur, et les modalités d'accompagnement pour renforcer la clarté et la protection du salarié.', @p15='["risques professionnels","information du salari\u00E9","pr\u00E9vention","droit du travail"]' (Nullable = false), @p16='2025-06-17T17:26:38.6507810Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.668 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='ee095341-3fbd-4cb9-9ab3-02d1afa13612', @p1='La clause impose au salarié une obligation de confidentialité couvrant la période du contrat et postérieurement, sans préciser la nature des informations protégées ni les exceptions possibles. Elle est concise mais manque de détails sur la définition des 'informations', la durée précise après la contrat, et les modalités d'application. La formulation pourrait entraîner des ambiguïtés quant à l'étendue de l'obligation et sa portée temporelle.' (Nullable = false), @p2='Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.' (Nullable = false), @p3='Clause de confidentialité' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6643178Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='794', @p11='False', @p12='Medium' (Nullable = false), @p13='723', @p14='Le salarié s'engage à ne divulguer aucune information confidentielle relative à l'entreprise ou à ses clients, à l'exclusion des informations déjà publiques, pendant toute la durée du contrat et pendant une période de deux ans après la fin du contrat, sauf obligation légale contraire.', @p15='["confidentialit\u00E9","obligation","droit du travail","clart\u00E9"]' (Nullable = false), @p16='2025-06-17T17:26:38.6643180Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.678 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='8adfab3c-4e2d-48a0-bfc2-178f37fb5736', @p1='La clause limite la possibilité de rupture anticipée du contrat aux cas prévus par la loi, tels que faute grave, force majeure, accord commun ou embauche en CDI. Elle offre une certaine sécurité pour l'employeur en restreignant les motifs de rupture, mais peut également limiter la flexibilité pour le salarié. La formulation est concise mais manque de précisions sur la procédure ou les conditions spécifiques pour chaque cas, ce qui pourrait entraîner des ambiguïtés.' (Nullable = false), @p2='Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi: faute grave, force majeure, accord commun ou embauche en CDI.' (Nullable = false), @p3='Rupture anticipée' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6736879Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='878', @p11='False', @p12='Medium' (Nullable = false), @p13='795', @p14='Clarifier les conditions et procédures pour chaque motif de rupture anticipée, notamment en précisant les démarches à suivre et les éventuelles indemnités ou notifications requises.', @p15='["rupture anticip\u00E9e","clause contractuelle","droit du travail","s\u00E9curit\u00E9 juridique"]' (Nullable = false), @p16='2025-06-17T17:26:38.6736881Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.687 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='7b849dfa-11ac-4efd-bf14-797d44da15c9', @p1='Cette clause prévoit la fin automatique du contrat à une date fixe sans nécessité de notification préalable, ce qui simplifie la procédure mais limite la flexibilité pour les parties. L'absence de renouvellement automatique indique une intention claire de mettre fin au contrat à échéance, mais peut poser problème si aucune partie ne souhaite la fin à cette date. La formulation est concise mais pourrait manquer de précision concernant les modalités en cas de modification ou de prolongation éventuelle.' (Nullable = false), @p2='Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.' (Nullable = false), @p3='Fin du contrat' (Nullable = false), @p4='0.95', @p5='2025-06-17T17:26:38.6829471Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='9602e845-8f66-481f-8642-956174d91787', @p10='958', @p11='False', @p12='Medium' (Nullable = false), @p13='879', @p14='Le contrat prendra automatiquement fin le 31 décembre 2025. Toute prolongation ou renouvellement devra faire l'objet d'un accord écrit préalable entre les parties. En cas de non-renouvellement, les parties devront respecter un délai de préavis de 30 jours avant la date d'échéance.', @p15='["fin de contrat","clause automatique","renouvellement","droit des contrats"]' (Nullable = false), @p16='2025-06-17T17:26:38.6829473Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 21:26:38.730 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='36dad348-81b3-4fca-8183-bf26bbd803d3', @p1='["Article 7 - Risques li\u00E9s au poste"]' (Nullable = false), @p2='2025-06-17T17:26:38.6922042Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Le poste comporte des risques liés à la manipulation de données confidentielles et à la cybersécurité, avec une responsabilité accrue du salarié, pouvant entraîner des incidents majeurs si mal gérés.' (Nullable = false), @p7='9602e845-8f66-481f-8642-956174d91787', @p8='Fuites de données, atteinte à la réputation de l'entreprise, sanctions légales, coûts financiers liés aux incidents de cybersécurité.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.4', @p12='Global: Risques opérationnels' (Nullable = false), @p13='Critical' (Nullable = false), @p14='2025-06-17T17:26:38.6922047Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.745 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='722c8bba-36ad-42f0-9e3c-4d255837c289', @p1='["Article 6 - Dur\u00E9e du travail"]' (Nullable = false), @p2='2025-06-17T17:26:38.7375338Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Le contrat ne prévoit pas de mécanismes pour gérer la charge de travail élevée (48h/semaine) pouvant entraîner un épuisement professionnel et des risques de burnout, ainsi qu'une surcharge pour l'entreprise en cas d'absences ou de défaillances opérationnelles.' (Nullable = false), @p7='9602e845-8f66-481f-8642-956174d91787', @p8='Détérioration de la santé du salarié, baisse de productivité, augmentation des coûts liés à l'absentéisme ou au remplacement.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.6', @p12='Global: Risques structurels' (Nullable = false), @p13='High' (Nullable = false), @p14='2025-06-17T17:26:38.7375343Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.757 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='7e382a3f-3651-4c93-97d5-a50a49c40cb6', @p1='["Article 7 - Risques li\u00E9s au poste"]' (Nullable = false), @p2='2025-06-17T17:26:38.7513427Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Absence de dispositions spécifiques pour la gestion des risques psychosociaux et la prévention des TMS, ce qui pourrait contrevenir aux obligations légales en matière de santé et sécurité au travail.' (Nullable = false), @p7='9602e845-8f66-481f-8642-956174d91787', @p8='Sanctions administratives, amendes, contentieux liés à la non-conformité aux obligations légales.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.5', @p12='Global: Risques de conformité' (Nullable = false), @p13='High' (Nullable = false), @p14='2025-06-17T17:26:38.7513430Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.806 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='5480e5e9-75e4-4e9f-8b74-a6b300ce6c57', @p1='2025-06-17T17:26:38.7675561Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Mettre en place des protocoles stricts de gestion des données confidentielles, incluant des formations régulières, des contrôles d'accès renforcés et une sensibilisation à la cybersécurité pour réduire le risque d'incidents majeurs.' (Nullable = false), @p6='9602e845-8f66-481f-8642-956174d91787', @p7='False', @p8='Règlement Général sur la Protection des Données (RGPD), Code du travail en matière de sécurité informatique', @p9='Critical' (Nullable = false), @p10='["Clause de confidentialit\u00E9","Clause de s\u00E9curit\u00E9 informatique"]' (Nullable = false), @p11='Implémenter un plan de formation en cybersécurité, renforcer les contrôles d'accès et réaliser des audits réguliers de sécurité.', @p12='Renforcement de la sécurité des données et cybersécurité' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-17T17:26:38.7675570Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.826 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='4bab1534-1047-4197-b1c3-8955dd720d61', @p1='2025-06-17T17:26:38.8161347Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Introduire des limites horaires, des périodes de repos obligatoires, et des dispositifs de suivi de la charge de travail pour prévenir l'épuisement professionnel et assurer une gestion équilibrée des ressources.' (Nullable = false), @p6='9602e845-8f66-481f-8642-956174d91787', @p7='False', @p8='Code du travail, notamment l'article L3121-35 sur la durée maximale de travail', @p9='High' (Nullable = false), @p10='["Clause de dur\u00E9e du travail","Clause de repos"]' (Nullable = false), @p11='Réviser le contrat pour inclure des clauses sur la durée du travail, instaurer un suivi régulier de la charge de travail et prévoir des périodes de repos.', @p12='Mise en place de mécanismes pour gérer la charge de travail' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-17T17:26:38.8161351Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.842 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='70d16dc1-2647-41ce-9c03-81a4c5dc87d0', @p1='2025-06-17T17:26:38.8369116Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Mettre en place des actions de prévention, telles que des programmes de sensibilisation, des évaluations régulières du stress, et des aménagements ergonomiques pour réduire les risques liés aux TMS et aux risques psychosociaux.' (Nullable = false), @p6='9602e845-8f66-481f-8642-956174d91787', @p7='False', @p8='Code du travail, notamment l'article L4121-2 sur la prévention des risques professionnels', @p9='High' (Nullable = false), @p10='["Clause de pr\u00E9vention des risques","Clause de sant\u00E9 et s\u00E9curit\u00E9"]' (Nullable = false), @p11='Inclure dans le contrat des clauses sur la prévention des risques psychosociaux, organiser des formations, et réaliser des évaluations ergonomiques régulières.', @p12='Intégration de dispositions pour la prévention des risques psychosociaux et TMS' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-17T17:26:38.8369118Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.852 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='d845ce7f-8865-48ee-abe8-54bea361b5b5', @p1='2025-06-17T17:26:38.8478840Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='' (Nullable = false), @p6='9602e845-8f66-481f-8642-956174d91787', @p7='False', @p8=NULL, @p9='Low' (Nullable = false), @p10='[]' (Nullable = false), @p11=NULL, @p12='' (Nullable = false), @p13='' (Nullable = false), @p14='2025-06-17T17:26:38.8478844Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.941 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='ed90fbb8-0954-4587-9e38-a72a9c263625', @p1='0.95', @p2='2025-06-17T17:26:38.8619889Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='30', @p8='False', @p9='{}' (Nullable = false), @p10='LexBot SARL', @p11='15', @p12='LexBot S.A.R.L.' (Nullable = false), @p13='ORGANIZATION' (Nullable = false), @p14='2025-06-17T17:26:38.8619898Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.957 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='d2d683d2-d971-48db-b3a9-e0aab9e667e2', @p1='0.95', @p2='2025-06-17T17:26:38.9524133Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='48', @p8='False', @p9='{}' (Nullable = false), @p10='50000 EUR', @p11='36', @p12='50 000 euros' (Nullable = false), @p13='AMOUNT' (Nullable = false), @p14='2025-06-17T17:26:38.9524137Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.972 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='f557f643-2693-405d-a921-6cc2f543fc00', @p1='0.95', @p2='2025-06-17T17:26:38.9650827Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='112', @p8='False', @p9='{}' (Nullable = false), @p10='12 Rue de l'Intelligence Artificielle, 75001 Paris', @p11='70', @p12='12, Rue de l'Intelligence Artificielle' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T17:26:38.9650830Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.984 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='fb76cb4a-8795-434f-9ee1-47fa85434c76', @p1='0.95', @p2='2025-06-17T17:26:38.9787582Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='126', @p8='False', @p9='{}' (Nullable = false), @p10='75001 Paris', @p11='114', @p12='75001 Paris' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T17:26:38.9787584Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:38.997 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='b7b080a2-f3b6-4edf-b52d-1fd64185fab2', @p1='0.95', @p2='2025-06-17T17:26:38.9915952Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='164', @p8='False', @p9='{}' (Nullable = false), @p10='RCS 882000123', @p11='154', @p12='882 000 123' (Nullable = false), @p13='OTHER' (Nullable = false), @p14='2025-06-17T17:26:38.9915955Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.013 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='b76ba9d4-43c2-43ad-8b05-06393322f0d4', @p1='0.95', @p2='2025-06-17T17:26:39.0050553Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='189', @p8='False', @p9='{}' (Nullable = false), @p10='Clara Dupont', @p11='174', @p12='Mme Clara Dupont' (Nullable = false), @p13='PERSON' (Nullable = false), @p14='2025-06-17T17:26:39.0050557Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.025 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='58880bc8-dd9f-419c-9631-7068e968719b', @p1='0.95', @p2='2025-06-17T17:26:39.0191081Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='242', @p8='False', @p9='{}' (Nullable = false), @p10='Jean Luc', @p11='232', @p12='M. Jean Luc' (Nullable = false), @p13='PERSON' (Nullable = false), @p14='2025-06-17T17:26:39.0191083Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.045 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='b1370096-1b63-489a-96ff-47f6a4b08aa1', @p1='0.95', @p2='2025-06-17T17:26:39.0357900Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='263', @p8='False', @p9='{}' (Nullable = false), @p10='1989-03-12', @p11='251', @p12='12 mars 1989' (Nullable = false), @p13='DATE' (Nullable = false), @p14='2025-06-17T17:26:39.0357904Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.071 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='8d2a81e2-0b21-4286-ae83-f2d2f9e7810b', @p1='0.95', @p2='2025-06-17T17:26:39.0580188Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='272', @p8='False', @p9='{}' (Nullable = false), @p10='Lyon', @p11='268', @p12='Lyon' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T17:26:39.0580191Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.085 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='3c870114-136c-4053-83e8-6bd0e364871c', @p1='0.95', @p2='2025-06-17T17:26:39.0795407Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='280', @p8='False', @p9='{}' (Nullable = false), @p10='France', @p11='274', @p12='France' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T17:26:39.0795409Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.100 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='7a6ed0e2-fc5d-4106-85fd-92c687914f1b', @p1='0.95', @p2='2025-06-17T17:26:39.0943232Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='305', @p8='False', @p9='{}' (Nullable = false), @p10='8 Rue des Lilas, 69001 Lyon', @p11='290', @p12='8, rue des Lilas' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T17:26:39.0943235Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.115 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='7a7bea6b-9b6e-40f9-af1c-a663b9491936', @p1='0.95', @p2='2025-06-17T17:26:39.1079719Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='317', @p8='False', @p9='{}' (Nullable = false), @p10='69001 Lyon', @p11='307', @p12='69001 Lyon' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T17:26:39.1079722Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.146 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='e5218f04-856e-46a3-8f06-18bad682e54f', @p1='0.95', @p2='2025-06-17T17:26:39.1238205Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='377', @p8='False', @p9='{}' (Nullable = false), @p10='L1242-2', @p11='370', @p12='L1242-2' (Nullable = false), @p13='LEGAL_REFERENCE' (Nullable = false), @p14='2025-06-17T17:26:39.1238208Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.181 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='9dec7dd3-060a-48d3-8fc5-6340ef2b5bc0', @p1='0.95', @p2='2025-06-17T17:26:39.1685345Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='401', @p8='False', @p9='{}' (Nullable = false), @p10='2025-07-01', @p11='385', @p12='1er juillet 2025' (Nullable = false), @p13='DATE' (Nullable = false), @p14='2025-06-17T17:26:39.1685351Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.205 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='e8f38d7d-621f-45be-8b70-4af1ff372133', @p1='0.95', @p2='2025-06-17T17:26:39.1970545Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='429', @p8='False', @p9='{}' (Nullable = false), @p10='2025-12-31', @p11='413', @p12='31 décembre 2025' (Nullable = false), @p13='DATE' (Nullable = false), @p14='2025-06-17T17:26:39.1970552Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.224 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='5d864ddb-e3e8-4155-adb2-c7835b741224', @p1='0.95', @p2='2025-06-17T17:26:39.2176267Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='479', @p8='False', @p9='{}' (Nullable = false), @p10='1200 EUR', @p11='468', @p12='1 200 euros' (Nullable = false), @p13='AMOUNT' (Nullable = false), @p14='2025-06-17T17:26:39.2176274Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.335 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='276740bf-87f9-45b1-9370-a643adaab803', @p1='Référence à la législation applicable pour la conclusion d'un contrat à durée déterminée pour accroissement temporaire d'activité.' (Nullable = false), @p2='2025-06-17T17:26:39.2369079Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='9602e845-8f66-481f-8642-956174d91787', @p7='False', @p8='article L1242-2 du Code du travail', @p9='0.95', @p10='Code du travail' (Nullable = false), @p11='article L1242-2 du Code du travail' (Nullable = false), @p12='Legal Reference' (Nullable = false), @p13='2025-06-17T17:26:39.2369087Z' (Nullable = true) (DbType = DateTime), @p14=NULL, @p15='https://www.legifrance.gouv.fr/codes/article_lc/LEGIARTI000006902747/'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_citations ("Id", "Context", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "IsDeleted", "Reference", "RelevanceScore", "Source", "Title", "Type", "UpdatedAt", "UpdatedBy", "Url")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 21:26:39.341 +04:00 [INF] Related entities saved for analysis: "9602e845-8f66-481f-8642-956174d91787"
2025-06-17 21:26:39.344 +04:00 [INF] Document analysis completed successfully. Processing time: 79674ms, Tokens: 1282
2025-06-17 21:26:39.389 +04:00 [INF] Document analysis completed successfully for document: Cdd_test_france.pdf, Analysis ID: "9602e845-8f66-481f-8642-956174d91787"
2025-06-17 21:26:39.394 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "9602e845-8f66-481f-8642-956174d91787"
2025-06-17 21:26:39.404 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-17 21:26:39.417 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 81202.6096ms
2025-06-17 21:26:39.422 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-17 21:26:39.425 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 81244ms with status 200 (Correlation ID: ca37f39b-7b8d-4db2-9835-fc6086ccc26d)
2025-06-17 21:26:39.434 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 81342.5578ms
2025-06-17 21:26:39.437 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-17 21:26:39.446 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 1804308a-ef0e-4de3-a271-8cebdc052213
2025-06-17 21:26:39.448 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:26:39.449 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: 1804308a-ef0e-4de3-a271-8cebdc052213)
2025-06-17 21:26:39.453 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 16.2932ms
2025-06-17 21:26:39.456 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 21:26:39.464 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 662b075f-2068-4f7b-8d66-cf4435021c18
2025-06-17 21:26:39.466 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:26:39.468 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:26:39.469 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:26:39.470 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:26:39.475 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:26:39.477 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:26:39.481 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:26:39.483 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:26:39.486 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:26:39.493 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-17 21:26:39.504 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-17 21:26:39.513 +04:00 [INF] Retrieved 4 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:26:39.515 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-17 21:26:39.517 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 43.9056ms
2025-06-17 21:26:39.519 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:26:39.520 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 54ms with status 200 (Correlation ID: 662b075f-2068-4f7b-8d66-cf4435021c18)
2025-06-17 21:26:39.523 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 67.3997ms
2025-06-17 21:49:11.878 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-17 21:49:11.893 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID f37d887f-e164-43ab-802d-9647f9f5a9e9
2025-06-17 21:49:11.896 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:49:11.898 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: f37d887f-e164-43ab-802d-9647f9f5a9e9)
2025-06-17 21:49:11.900 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 21.7009ms
2025-06-17 21:49:11.903 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 21:49:11.908 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 31e16ee3-74de-4180-92bb-f3135002dd9c
2025-06-17 21:49:11.910 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:49:11.912 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:49:11.913 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:49:11.914 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:49:11.917 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:49:11.919 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:49:11.921 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:49:11.923 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:49:11.924 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:49:11.949 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-17 21:49:11.964 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExtractedText", d1."IsDeleted", d1."ModelUsed", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-17 21:49:11.970 +04:00 [INF] Retrieved 4 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:49:11.972 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-17 21:49:11.974 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 57.9988ms
2025-06-17 21:49:11.977 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 21:49:11.979 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 69ms with status 200 (Correlation ID: 31e16ee3-74de-4180-92bb-f3135002dd9c)
2025-06-17 21:49:11.982 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 78.6508ms
2025-06-17 21:49:16.153 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 - null null
2025-06-17 21:49:16.160 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 started with correlation ID 06a1c53c-e0ed-44bc-912d-99743bc7a5aa
2025-06-17 21:49:16.162 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:49:16.164 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 completed in 1ms with status 204 (Correlation ID: 06a1c53c-e0ed-44bc-912d-99743bc7a5aa)
2025-06-17 21:49:16.166 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 - 204 null null 13.7116ms
2025-06-17 21:49:16.168 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 - application/json null
2025-06-17 21:49:16.175 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 started with correlation ID 923e7f32-ec86-4993-92a8-02e8454153b0
2025-06-17 21:49:16.177 +04:00 [INF] CORS policy execution successful.
2025-06-17 21:49:16.178 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 21:49:16.179 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 21:49:16.180 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 21:49:16.182 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 21:49:16.183 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 21:49:16.185 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 21:49:16.186 +04:00 [INF] Retrieving analysis "9602e845-8f66-481f-8642-956174d91787" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:49:16.187 +04:00 [INF] Retrieving analysis result: "9602e845-8f66-481f-8642-956174d91787" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 21:49:16.227 +04:00 [INF] Executed DbCommand (37ms) [Parameters=[@__id_0='9602e845-8f66-481f-8642-956174d91787', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExtractedText", d2."IsDeleted", d2."ModelUsed", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExtractedText", d."IsDeleted", d."ModelUsed", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-17 21:49:16.486 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-17 21:49:16.517 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 335.4669ms
2025-06-17 21:49:16.520 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 21:49:16.521 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 completed in 344ms with status 200 (Correlation ID: 923e7f32-ec86-4993-92a8-02e8454153b0)
2025-06-17 21:49:16.525 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/9602e845-8f66-481f-8642-956174d91787 - 200 null application/json; charset=utf-8 356.9728ms
