2025-06-17 23:30:33.159 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 247
2025-06-17 23:32:51.637 +04:00 [FTL] LexAI Document Analysis Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 247
2025-06-17 23:39:05.508 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-17 23:39:05.681 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 23:39:06.294 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-17 23:39:06.298 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-17 23:39:06.454 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 23:39:06.457 +04:00 [INF] Hosting environment: Development
2025-06-17 23:39:06.459 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-17 23:39:07.748 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-17 23:39:07.953 +04:00 [INF] Request GET / started with correlation ID 95cf0fcd-dd91-461c-bb5d-8c938c12bcdc
2025-06-17 23:39:09.624 +04:00 [INF] Request GET / completed in 1667ms with status 404 (Correlation ID: 95cf0fcd-dd91-461c-bb5d-8c938c12bcdc)
2025-06-17 23:39:09.635 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 1892.4109ms
2025-06-17 23:39:09.661 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-17 23:43:30.711 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - null null
2025-06-17 23:43:30.732 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze started with correlation ID 4c6b3ded-9a36-4151-99b8-ea7e2b5514d4
2025-06-17 23:43:30.749 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:43:30.763 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/analyze completed in 22ms with status 204 (Correlation ID: 4c6b3ded-9a36-4151-99b8-ea7e2b5514d4)
2025-06-17 23:43:30.770 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/analyze - 204 null null 59.3383ms
2025-06-17 23:43:30.774 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - multipart/form-data; boundary=----WebKitFormBoundaryAjO5A6f33vqSpAju 599124
2025-06-17 23:43:30.802 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze started with correlation ID 383b930e-f4f6-4a9c-9d79-41cb597c44d4
2025-06-17 23:43:30.810 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:43:31.122 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 23:43:31.141 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-17 23:43:31.212 +04:00 [INF] Route matched with {action = "AnalyzeDocument", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] AnalyzeDocument(LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisUploadRequest, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 23:43:31.451 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 23:43:32.913 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 23:43:34.513 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 23:43:34.624 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-17 23:43:34.649 +04:00 [INF] Starting document analysis for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", document: Cdd_test.pdf
2025-06-17 23:43:34.699 +04:00 [INF] Starting comprehensive document analysis for: Cdd_test.pdf
2025-06-17 23:43:34.718 +04:00 [INF] Document stored successfully: c58e7f6d494cca50c1d5059bd475ba29431720db7480cf33bf00ab87bbd2d9e6 -> ./storage/documents\c58e7f6d494cca50c1d5059bd475ba29431720db7480cf33bf00ab87bbd2d9e6.pdf
2025-06-17 23:43:34.729 +04:00 [INF] Document stored with hash: c58e7f6d494cca50c1d5059bd475ba29431720db7480cf33bf00ab87bbd2d9e6 at ./storage/documents\c58e7f6d494cca50c1d5059bd475ba29431720db7480cf33bf00ab87bbd2d9e6.pdf
2025-06-17 23:43:34.760 +04:00 [INF] Starting Azure Document Intelligence extraction for document type: pdf
2025-06-17 23:43:46.036 +04:00 [INF] Azure extraction completed successfully. Processing time: 11257ms, Pages: 2
2025-06-17 23:43:46.044 +04:00 [INF] Starting clause analysis for document type: pdf
2025-06-17 23:44:17.748 +04:00 [INF] Clause analysis completed. Found 10 clauses
2025-06-17 23:44:17.759 +04:00 [INF] Starting risk assessment for document type: pdf
2025-06-17 23:44:24.997 +04:00 [INF] Risk assessment completed. Identified 3 risks
2025-06-17 23:44:25.002 +04:00 [INF] Generating recommendations for document type: pdf
2025-06-17 23:44:44.269 +04:00 [INF] Prioritizing 29 recommendations
2025-06-17 23:44:46.299 +04:00 [INF] Generated 4 recommendations
2025-06-17 23:45:04.383 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-17 23:45:04.444 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.454 +04:00 [WRN] The property 'DocumentAnalysisResult.FinancialTerms' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.457 +04:00 [WRN] The property 'DocumentAnalysisResult.ImportantDates' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.461 +04:00 [WRN] The property 'DocumentAnalysisResult.KeyPoints' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.464 +04:00 [WRN] The property 'DocumentAnalysisResult.MainParties' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.468 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.474 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:04.477 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-17 23:45:05.843 +04:00 [INF] Executed DbCommand (193ms) [Parameters=[@p0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p1='Analyse juridique complète du contrat de travail à durée déterminée (CDD)

---

**Synthèse des clauses importantes**

1. **Nature du contrat (Article 1)**  
- Contrat à durée déterminée (CDD) en application de l’article L1242-2 du Code du travail, pour un accroissement temporaire d’activité.  
- Important : respecter strictement la nature du CDD, notamment sa finalité temporaire.

2. **Durée et dates (Article 2)**  
- Début : 1er juillet 2025  
- Fin : 31 décembre 2025 (6 mois)  
- Clé : contrat à terme précis, aucune ambiguïté sur la durée.

3. **Poste et responsabilités (Article 3)**  
- Poste : Chargé de gestion des risques numériques  
- Sous la responsabilité du Chef de département sécurité  
- Important : préciser si des missions ou responsabilités spécifiques sont prévues.

4. **Lieu de travail (Article 4)**  
- Siège ou autre lieu dans un rayon de 20 km  
- Recommandation : préciser si télétravail ou déplacements sont possibles.

5. **Rémunération (Article 5)**  
- 1 200 € brut mensuel, payée à terme échu  
- Conformément au Code du travail, mais il faut vérifier si cette rémunération est conforme aux conventions collectives applicables.

6. **Durée du travail (Article 6)**  
- 48 heures par semaine, du lundi au samedi, horaires définis par l’employeur  
- Risque : dépassement du plafond légal (44 heures en moyenne hebdomadaire sur 12 semaines), à vérifier si cela nécessite une contrepartie ou une dérogation.

7. **Risques professionnels (Article 7)**  
- Exposition aux écrans, charge mentale, manipulation de données confidentielles, risques psychosociaux  
- L’employeur propose un accompagnement médical et en santé mentale  
- Recommandation : formaliser ces mesures dans le document unique d’évaluation des risques professionnels (DUERP).

8. **Confidentialité (Article 8)**  
- Engagement à ne pas divulguer d’informations, pendant et après le contrat  
- Clause essentielle pour la protection des données et de la propriété intellectuelle.

9. **Rupture anticipée (Article 9)**  
- Possible en cas de faute grave, force majeure, accord mutuel ou embauche en CDI  
- Conforme au droit, mais il est conseillé de préciser les modalités de rupture.

10. **Fin du contrat (Article 10)**  
- Fin automatique le 31 décembre 2025, sans renouvellement prévu  
- Clé : pas de reconduction automatique, ce qui est conforme.

---

**Risques identifiés**

1. **Non-respect du cadre légal du CDD**  
- Risque de requalification en CDI si la finalité du contrat n’est pas respectée ou si le contrat est utilisé de manière abusive.

2. **Durée excessive ou horaires non conformes**  
- 48 heures par semaine peut dépasser la limite légale ou conventionnelle. La mise en place d’heures supplémentaires ou de dérogations doit être encadrée.

3. **Clausules de confidentialité et de risques professionnels**  
- Risque de litiges si ces clauses ne sont pas suffisamment précises ou si la gestion des risques n’est pas formalisée.

---

**Recommandations**

1. **Vérifier la conformité de la rémunération**  
- S’assurer qu’elle respecte la convention collective applicable et le SMIC si applicable.

2. **Préciser les modalités de travail**  
- Clarifier si du télétravail est prévu, modalités de déplacement, horaires précis, et respecter la réglementation sur la durée du travail.

3. **Formaliser la gestion des risques**  
- Mettre à jour le Document Unique d’Évaluation des Risques Professionnels (DUERP) pour couvrir les risques liés à l’exposition aux écrans, à la charge mentale, etc.

4. **Inclure une clause de renouvellement ou d’échéance claire**  
- Même si le contrat se termine automatiquement, prévoir une clause précisant la procédure en cas de besoin de prolongation ou de renouvellement.

---

**Évaluation globale**

Le contrat est globalement conforme aux exigences légales pour un CDD, mais plusieurs points méritent une attention particulière pour éviter tout contentieux ou requalification. La précision des horaires, la conformité de la rémunération, et la formalisation des mesures de prévention des risques sont essentielles.

---

**Conseils juridiques**

- **Respect strict de la finalité du CDD** : ne pas l’utiliser pour pourvoir un poste permanent ou pour des missions qui relèveraient d’un CDI.
- **Vérification des horaires** : respecter la limite légale de 44 heures en moyenne hebdomadaire, ou obtenir une dérogation.
- **Formaliser la gestion des risques** : intégrer dans le DUERP toutes les mesures de prévention liées aux risques professionnels mentionnés.
- **Veiller à la confidentialité** : rédiger une clause précise, notamment concernant la durée de l’obligation après la fin du contrat.
- **Anticiper la fin du contrat** : prévoir une procédure claire pour la rupture ou le renouvellement, même si la fin est automatique.

---

**Conclusion**

Ce contrat de CDD semble bien rédigé, mais il doit être complété par des précisions sur certains points réglementaires et pratiques pour assurer sa conformité et sa sécurité juridique. Il est conseillé de faire relire ce contrat par un avocat spécialisé en droit du travail pour validation finale.

---

N'hésitez pas à demander une analyse plus approfondie ou une adaptation spécifique selon votre contexte.' (Nullable = false), @p2='2025-06-17T19:45:03.1510082Z' (DbType = DateTime), @p3='0.95', @p4='2025-06-17T19:45:03.1510877Z' (DbType = DateTime), @p5=NULL, @p6=NULL (DbType = DateTime), @p7=NULL, @p8='c58e7f6d494cca50c1d5059bd475ba29431720db7480cf33bf00ab87bbd2d9e6' (Nullable = false), @p9='Cdd_test.pdf' (Nullable = false), @p10='Formaliser les modalités d'emploi pour un poste temporaire en lien avec une augmentation d'activité, en précisant les obligations et risques liés au poste.' (Nullable = false), @p11='./storage/documents\c58e7f6d494cca50c1d5059bd475ba29431720db7480cf33bf00ab87bbd2d9e6.pdf' (Nullable = false), @p12='pdf' (Nullable = false), @p13='0', @p14='Ce contrat de travail à durée déterminée de six mois entre LexBot S.A.R.L. et M. Jean Luc définit les modalités d'emploi, y compris la nature du poste, la rémunération, les risques professionnels, et les clauses de confidentialité et de rupture.' (Nullable = false), @p15='CONTRAT DE TRAVAIL À DURÉE DÉTERMINÉE (CDD)
Entre les soussignés :
L'employeur
LexBot S.A.R.L., au capital de 50 000 euros, dont le siège social est situé au 12, Rue de l'Intelligence Artificielle, 75001 Paris, immatriculée au RCS de Paris sous le numéro 882 000 123, Représentée par Mme Clara Dupont, en sa qualité de Directrice Générale, Ci-après dénommée « l'Employeur »
Et
M. Jean Luc, né le 12 mars 1989 à Lyon (France), demeurant au 8, rue des Lilas, 69001 Lyon, de nationalité française, Ci-après dénommé « le Salarié »
Article 1 - Nature du contrat
Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.
Article 2 - Date de début et de fin
Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.
Article 3 - Poste occupé
M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.
Article 4 - Lieu de travail
Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.
Article 5 - Rémunération
La rémunération brute mensuelle sera de 1 200 euros, versée à terme échu, selon les modalités prévues par le Code du travail.
1 | Page KEVIN WILFRIED
Article 6 - Durée du travail
La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.
Article 7 - Risques liés au poste
Le salarié est informé que le poste comporte les risques professionnels suivants :
· Exposition prolongée aux écrans (fatigue visuelle, TMS)
· Charge mentale importante liée à la gestion des risques informatiques
· Manipulation de données confidentielles (obligation de vigilance accrue)
· Risques psychosociaux en cas d'incident majeur de cybersécurité
L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.
Article 8 - Clause de confidentialité
Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.
Article 9 - Rupture anticipée
Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi : faute grave, force majeure, accord commun ou embauche en CDI.
Article 10 - Fin du contrat
Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.
Fait à Paris, le 14 juin 2025
En deux exemplaires originaux.
Signature de l'Employeur (LexBot)
Signature :
Signature du Salarié (Jean Luc)
Signature :
2 | Page KEVIN WILFRIED' (Nullable = false), @p16='["R\u00E9mun\u00E9ration brute mensuelle de 1200 euros","Contrat sans renouvellement pr\u00E9vu"]' (Nullable = false), @p17='["1 juillet 2025","31 d\u00E9cembre 2025"]' (Nullable = false), @p18='False', @p19='["Contrat \u00E0 dur\u00E9e d\u00E9termin\u00E9e de 6 mois du 1er juillet au 31 d\u00E9cembre 2025","Poste de Charg\u00E9 de gestion des risques num\u00E9riques avec responsabilit\u00E9 hi\u00E9rarchique","Risques professionnels li\u00E9s \u00E0 l\u0027exposition aux \u00E9crans, gestion de donn\u00E9es confidentielles et cybers\u00E9curit\u00E9"]' (Nullable = false), @p20='["LexBot S.A.R.L.","M. Jean Luc"]' (Nullable = false), @p21='lexai-gpt-4.1-nano' (Nullable = false), @p22='Medium' (Nullable = false), @p23='88445', @p24='Completed' (Nullable = false), @p25='1305', @p26='2025-06-17T19:45:03.1511669Z' (Nullable = true) (DbType = DateTime), @p27=NULL, @p28='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_analysis_results ("Id", "AnalysisContent", "AnalyzedAt", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentHash", "DocumentName", "DocumentPurpose", "DocumentStoragePath", "DocumentType", "EstimatedCost", "ExecutiveSummary", "ExtractedText", "FinancialTerms", "ImportantDates", "IsDeleted", "KeyPoints", "MainParties", "ModelUsed", "OverallRiskLevel", "ProcessingTimeMs", "Status", "TokensUsed", "UpdatedAt", "UpdatedBy", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28);
2025-06-17 23:45:05.896 +04:00 [INF] Analysis saved to database with ID: "8974a3b1-2c68-4cb0-a081-7954e2010a82"
2025-06-17 23:45:06.040 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@p0='492a9c3a-c1ea-4bc8-a2c9-2ff642e082a1', @p1='Cette clause précise que le contrat est à durée déterminée conformément à l'article L1242-2 du Code du travail, qui concerne les contrats pour accroissement temporaire d'activité. Elle établit la nature du contrat en lien avec une disposition légale spécifique, ce qui renforce sa légitimité. Cependant, la clause pourrait manquer de détails sur la durée précise, les modalités de renouvellement ou de fin du contrat, ainsi que sur les conditions spécifiques liées à l'accroissement temporaire d'activité, ce qui pourrait limiter la compréhension pour les parties et poser des questions sur la conformité complète.' (Nullable = false), @p2='Le présent contrat est un contrat à durée déterminée conclu en application de l'article L1242-2 du Code du travail pour accroissement temporaire d'activité.' (Nullable = false), @p3='nature du contrat' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:05.9082596Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='211', @p11='False', @p12='Medium' (Nullable = false), @p13='122', @p14='Ajouter des détails sur la durée précise du contrat, ses modalités de renouvellement, et les conditions spécifiques liées à l'accroissement temporaire d'activité pour renforcer la clarté et la conformité.', @p15='["contrat \u00E0 dur\u00E9e d\u00E9termin\u00E9e","accroissement temporaire d\u0027activit\u00E9","droit du travail"]' (Nullable = false), @p16='2025-06-17T19:45:05.9082604Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.058 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='45a8b528-26a3-46d3-b8ca-f1d3e1478964', @p1='La clause précise la date de début du contrat au 1er juillet 2025 et sa durée de 6 mois, se terminant le 31 décembre 2025 inclus. Elle est claire en indiquant explicitement la période d'effet, ce qui facilite la compréhension pour les parties. Cependant, elle ne mentionne pas les modalités de renouvellement ou de résiliation anticipée, ce qui pourrait poser des problèmes en cas de changement de circonstances. La formulation est concise mais pourrait bénéficier d'une clarification sur la possibilité ou non de prolongation ou de résiliation anticipée.' (Nullable = false), @p2='Le contrat prendra effet à compter du 1er juillet 2025 pour une durée de 6 mois, soit jusqu'au 31 décembre 2025 inclus.' (Nullable = false), @p3='date de début et de fin' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.0497447Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='271', @p11='False', @p12='Low' (Nullable = false), @p13='212', @p14='Ajouter une clause précisant si le contrat peut être renouvelé, résilié anticipativement ou non, ainsi que les conditions associées.', @p15='["date","dur\u00E9e","clart\u00E9","effet"]' (Nullable = false), @p16='2025-06-17T19:45:06.0497452Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.072 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='cd08bdcd-cfe0-4adb-ba35-803fd5d733c2', @p1='La clause définit clairement le poste occupé par M. Jean Luc et sa hiérarchie, précisant ses responsabilités et son supérieur direct. Cependant, elle manque de détails sur la durée du contrat, les modalités d'exercice, la rémunération, et les conditions spécifiques liées à ses fonctions, ce qui pourrait entraîner des ambiguïtés ou des litiges. La formulation est concise mais pourrait bénéficier de précisions supplémentaires pour renforcer la compréhension et la transparence.' (Nullable = false), @p2='M. Jean Luc exercera les fonctions de Chargé de gestion des risques numériques, sous la responsabilité du Chef de département sécurité.' (Nullable = false), @p3='poste occupé' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.0674221Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='347', @p11='False', @p12='Medium' (Nullable = false), @p13='272', @p14='Inclure des détails sur la durée du contrat, les responsabilités précises, la rémunération, et les modalités d'exercice pour assurer une meilleure clarté et réduire les risques de litiges.', @p15='["contrat de travail","clart\u00E9","responsabilit\u00E9s","hi\u00E9rarchie"]' (Nullable = false), @p16='2025-06-17T19:45:06.0674224Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.080 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='aac58aa8-7ce4-49da-baa4-6605c83945b6', @p1='La clause précise que le salarié doit exercer ses fonctions au siège ou dans un lieu désigné par l'employeur dans un rayon de 20 km, ce qui offre une certaine flexibilité à l'employeur tout en limitant la localisation géographique. Cependant, la formulation peut manquer de clarté concernant la nature exacte des 'lieux désignés' et la procédure pour en changer. La clause semble équilibrée en termes d'obligation géographique, mais pourrait poser des problèmes si elle n'est pas accompagnée de précisions sur la communication et la consentement du salarié.' (Nullable = false), @p2='Le salarié exercera ses fonctions au siège de l'entreprise ou tout autre lieu désigné par l'employeur dans un rayon de 20 km.' (Nullable = false), @p3='lieu de travail' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.0769210Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='429', @p11='False', @p12='Medium' (Nullable = false), @p13='348', @p14='Le salarié exercera ses fonctions principalement au siège de l'entreprise ou dans tout autre lieu désigné par l'employeur dans un rayon de 20 km, après information préalable. Toute modification du lieu de travail devra faire l'objet d'une communication écrite préalable.', @p15='["lieu de travail","mobilit\u00E9","obligation contractuelle"]' (Nullable = false), @p16='2025-06-17T19:45:06.0769212Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.088 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='c39dc28c-d647-480a-b54f-b25d739ef532', @p1='La clause précise le montant de la rémunération brute mensuelle à 1200 euros, indique qu'elle sera versée à terme échu et se réfère aux modalités du Code du travail. Elle est concise mais manque de détails sur les modalités précises de paiement, la périodicité, les éventuelles déductions ou ajustements, ce qui pourrait poser des problèmes en cas de litige. La référence au Code du travail garantit une conformité générale, mais l'absence de précisions peut limiter la compréhension et la transparence pour le salarié.' (Nullable = false), @p2='La rémunération brute mensuelle sera de 1 200 euros, versée à terme échu, selon les modalités prévues par le Code du travail.' (Nullable = false), @p3='rémunération' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.0838853Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='503', @p11='False', @p12='Medium' (Nullable = false), @p13='430', @p14='Inclure des détails spécifiques sur la date de paiement, les modalités de calcul, et les éventuelles déductions ou ajustements pour améliorer la clarté et réduire les risques de malentendus.', @p15='["r\u00E9mun\u00E9ration","clart\u00E9","conformit\u00E9","d\u00E9tails"]' (Nullable = false), @p16='2025-06-17T19:45:06.0838855Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.098 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='91e11a2f-789e-46a3-989e-93266230ef3d', @p1='La clause établit une durée de travail hebdomadaire de 48 heures, réparties du lundi au samedi, avec des horaires déterminés par l'employeur selon l'activité. Elle ne précise pas si cette durée inclut ou non les heures supplémentaires, ni si des dispositions spécifiques s'appliquent en cas de dépassement. La flexibilité laissée à l'employeur pour définir les horaires peut entraîner une incertitude pour le salarié quant à ses horaires exacts et ses droits en matière de repos. La mention du lundi au samedi indique une semaine de travail de six jours, ce qui peut poser des questions sur le respect du repos hebdomadaire obligatoire.' (Nullable = false), @p2='La durée du travail est fixée à 48 heures par semaine, réparties du lundi au samedi, avec des horaires définis par l'employeur en fonction de l'activité.' (Nullable = false), @p3='durée du travail' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.0930517Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='583', @p11='False', @p12='Medium' (Nullable = false), @p13='504', @p14='Préciser si les 48 heures incluent ou non les heures supplémentaires, définir clairement les horaires ou la procédure pour leur fixation, et assurer le respect du repos hebdomadaire conformément à la législation en vigueur.', @p15='["dur\u00E9e du travail","l\u00E9gislation du travail","horaires","risque juridique"]' (Nullable = false), @p16='2025-06-17T19:45:06.0930520Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.106 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='521b5309-d988-4382-b018-2a13b219f9d5', @p1='La clause informe le salarié des risques professionnels liés à son poste, notamment l'exposition prolongée aux écrans, la charge mentale, la manipulation de données confidentielles et les risques psychosociaux en cas d'incident de cybersécurité. Elle mentionne également l'offre d'accompagnement par le médecin du travail et un référent en santé mentale. La formulation est claire et couvre plusieurs risques, mais elle pourrait bénéficier d'une précision accrue sur la nature des risques et les mesures de prévention spécifiques. La mention de l'accompagnement est un point positif pour l'information et le soutien du salarié.' (Nullable = false), @p2='Le salarié est informé que le poste comporte les risques professionnels suivants : Exposition prolongée aux écrans (fatigue visuelle, TMS), Charge mentale importante liée à la gestion des risques informatiques, Manipulation de données confidentielles (obligation de vigilance accrue), Risques psychosociaux en cas d'incident majeur de cybersécurité. L'entreprise propose un accompagnement par le médecin du travail et un référent en santé mentale.' (Nullable = false), @p3='risques liés au poste' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.1014928Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='692', @p11='False', @p12='Medium' (Nullable = false), @p13='584', @p14='Préciser les mesures de prévention mises en place par l'entreprise et renforcer l'information sur les obligations du salarié en matière de sécurité et de vigilance.', @p15='["risques professionnels","information salari\u00E9","sant\u00E9 et s\u00E9curit\u00E9","cybers\u00E9curit\u00E9"]' (Nullable = false), @p16='2025-06-17T19:45:06.1014930Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.115 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='96e0fc34-8650-4716-855b-41e4bfb003a1', @p1='La clause impose au salarié une obligation de confidentialité couvrant la période du contrat et postérieurement, ce qui est courant mais doit être précis pour éviter toute ambiguïté. La formulation est concise mais manque de détails sur la nature des informations protégées, les exceptions possibles, et la durée précise de l'obligation après la fin du contrat. Elle pourrait également bénéficier de précisions sur les sanctions en cas de violation.' (Nullable = false), @p2='Le salarié s'engage à ne divulguer aucune information relative à l'entreprise ou à ses clients, pendant et après la durée du contrat.' (Nullable = false), @p3='clause de confidentialité' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.1104945Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='754', @p11='False', @p12='Medium' (Nullable = false), @p13='693', @p14='Le salarié s'engage à ne divulguer aucune information confidentielle relative à l'entreprise ou à ses clients, sauf obligation légale, pendant toute la durée du contrat et pendant une période de [X] années après sa cessation. La définition des 'informations' doit être précisée pour inclure, par exemple, les données techniques, commerciales, financières, etc.', @p15='["confidentialit\u00E9","obligation post-contractuelle","clart\u00E9","risque juridique"]' (Nullable = false), @p16='2025-06-17T19:45:06.1104947Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.124 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='8d0c4d5e-528f-4067-8059-673acce03b0c', @p1='Cette clause limite la possibilité de rupture anticipée du contrat aux cas prévus par la loi, tels que faute grave, force majeure, accord commun ou embauche en CDI. Elle offre une certaine sécurité à l'employeur en restreignant les motifs de rupture, mais peut également limiter la flexibilité pour le salarié. La formulation est concise mais pourrait manquer de précision quant à la définition de certains termes comme 'accord commun'. Elle ne mentionne pas explicitement la procédure à suivre en cas de rupture ou les éventuelles indemnités associées, ce qui pourrait poser problème en cas de litige.' (Nullable = false), @p2='Le présent contrat ne pourra être rompu avant son terme que dans les cas prévus par la loi : faute grave, force majeure, accord commun ou embauche en CDI.' (Nullable = false), @p3='rupture anticipée' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.1198126Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='832', @p11='False', @p12='Medium' (Nullable = false), @p13='755', @p14='Inclure une liste précise des motifs de rupture anticipée, définir la procédure à suivre, et préciser les droits du salarié en cas de rupture, tout en assurant une meilleure clarté et équité.', @p15='["rupture anticip\u00E9e","clause contractuelle","droit du travail","s\u00E9curit\u00E9 juridique"]' (Nullable = false), @p16='2025-06-17T19:45:06.1198129Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.136 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='55f7c073-76c1-4b6d-8ae3-1f24be06a8a6', @p1='La clause prévoit la fin automatique du contrat à une date fixe sans nécessité de notification, ce qui simplifie la procédure mais limite la flexibilité pour les parties. L'absence de renouvellement automatique ou option de prolongation peut poser problème si les parties souhaitent continuer leur relation. La formulation est claire mais pourrait bénéficier de précisions sur les modalités en cas de modification ou de négociation avant la date de fin.' (Nullable = false), @p2='Le contrat prendra automatiquement fin le 31 décembre 2025 sans qu'il soit nécessaire de notifier une rupture. Aucun renouvellement n'est prévu.' (Nullable = false), @p3='fin du contrat' (Nullable = false), @p4='0.95', @p5='2025-06-17T19:45:06.1305335Z' (DbType = DateTime), @p6=NULL, @p7=NULL (DbType = DateTime), @p8=NULL, @p9='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p10='902', @p11='False', @p12='Medium' (Nullable = false), @p13='833', @p14='Inclure une clause permettant un renouvellement automatique ou une option de prolongation, ou préciser les modalités de négociation en cas de souhait de prolongation par l'une des parties.', @p15='["fin de contrat","r\u00E9siliation automatique","clart\u00E9","renouvellement"]' (Nullable = false), @p16='2025-06-17T19:45:06.1305337Z' (Nullable = true) (DbType = DateTime), @p17=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO clause_analyses ("Id", "Analysis", "ClauseText", "ClauseType", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "RiskLevel", "StartPosition", "SuggestedRevision", "Tags", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17);
2025-06-17 23:45:06.238 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='57558271-42ea-4107-bfa7-f79e373f386b', @p1='["Article 7 - Risques li\u00E9s au poste"]' (Nullable = false), @p2='2025-06-17T19:45:06.1417109Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Le poste comporte des risques liés à l'exposition prolongée aux écrans, à la charge mentale et à la manipulation de données sensibles, ce qui peut entraîner des problèmes de santé et de sécurité si non correctement gérés.' (Nullable = false), @p7='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p8='Accidents de santé (fatigue visuelle, TMS, stress), erreurs ou fuites de données sensibles, risques psychosociaux importants.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.8', @p12='Global: Risques structurels' (Nullable = false), @p13='High' (Nullable = false), @p14='2025-06-17T19:45:06.1417115Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.248 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='3d48f202-3621-4e21-8d35-9e19df16edd3', @p1='["Article 1 - Nature du contrat","Article 2 - Date de d\u00E9but et de fin","Article 5 - R\u00E9mun\u00E9ration","Article 6 - Dur\u00E9e du travail","Article 8 - Clause de confidentialit\u00E9","Article 9 - Rupture anticip\u00E9e","Article 10 - Fin du contrat"]' (Nullable = false), @p2='2025-06-17T19:45:06.2430498Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Le contrat doit respecter les dispositions légales du Code du travail concernant la durée, la rémunération, la confidentialité et la rupture du contrat. Toute non-conformité pourrait entraîner des sanctions légales ou des contentieux.' (Nullable = false), @p7='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p8='Sanctions administratives, contentieux juridiques, requalification du contrat, amendes.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.7', @p12='Global: Risques de conformité' (Nullable = false), @p13='High' (Nullable = false), @p14='2025-06-17T19:45:06.2430502Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.262 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='5c6b781c-47af-4740-8c01-454f264ebacd', @p1='["Article 7 - Risques li\u00E9s au poste"]' (Nullable = false), @p2='2025-06-17T19:45:06.2554151Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='Le poste implique des risques psychosociaux liés à la gestion du stress et à la charge mentale, ainsi que des risques liés à la sécurité informatique en cas d'incident majeur, pouvant perturber le fonctionnement de l'entreprise.' (Nullable = false), @p7='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p8='Diminution de la productivité, épuisement professionnel, incidents de cybersécurité impactant la continuité des activités.' (Nullable = false), @p9='False', @p10='À définir' (Nullable = false), @p11='0.6', @p12='Global: Risques opérationnels' (Nullable = false), @p13='Medium' (Nullable = false), @p14='2025-06-17T19:45:06.2554155Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO risk_assessments ("Id", "AffectedClauses", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "Impact", "IsDeleted", "Mitigation", "Probability", "RiskType", "Severity", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.371 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='431d3510-d402-4f94-b6a1-c317517e8c99', @p1='2025-06-17T19:45:06.2767108Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='S'assurer que le contrat de travail respecte toutes les dispositions légales relatives à la durée du travail, la rémunération, la confidentialité et la rupture du contrat pour éviter tout contentieux ou sanctions.' (Nullable = false), @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='False', @p8='Code du travail, notamment les articles L3121-1 et suivants pour la durée du travail, et les dispositions relatives à la confidentialité et à la rupture.', @p9='Critical' (Nullable = false), @p10='["Articles L3121-1 \u00E0 L3121-35","Articles relatifs \u00E0 la confidentialit\u00E9 et \u00E0 la rupture"]' (Nullable = false), @p11='Faire relire le contrat par un expert en droit du travail et mettre à jour les clauses en conformité avec la législation en vigueur.', @p12='Vérification de la conformité du contrat aux dispositions légales' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-17T19:45:06.2767117Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.384 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='f839ac75-6d52-48b0-8565-51ccc4e983cb', @p1='2025-06-17T19:45:06.3791539Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Instaurer des pauses régulières pour réduire l'exposition prolongée aux écrans et aménager l'espace de travail avec du mobilier ergonomique afin de prévenir les troubles musculo-squelettiques et la fatigue visuelle.' (Nullable = false), @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='False', @p8='Recommandations de l'Agence nationale pour l'amélioration des conditions de travail (ANACT) et obligations en matière de santé et sécurité au travail.', @p9='High' (Nullable = false), @p10='["Article L4121-1 du Code du travail","Recommandations ergonomiques"]' (Nullable = false), @p11='Programmer des pauses de 5 à 10 minutes toutes les heures et fournir du matériel ergonomique adapté (supports d'écran, fauteuils ajustables).', @p12='Mise en place de pauses régulières et d'aménagements ergonomiques' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-17T19:45:06.3791543Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.398 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='e53c0613-fe2d-4545-b027-b58c592b058f', @p1='2025-06-17T19:45:06.3912380Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='Proposer une formation spécifique pour sensibiliser le personnel aux risques liés à la charge mentale et aux bonnes pratiques pour la manipulation et la protection des données sensibles.' (Nullable = false), @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='False', @p8='Obligation de formation en matière de sécurité et de confidentialité selon l'article L4141-2 du Code du travail.', @p9='High' (Nullable = false), @p10='["Article L4141-2","R\u00E8glement g\u00E9n\u00E9ral sur la protection des donn\u00E9es (RGPD)"]' (Nullable = false), @p11='Organiser des sessions de formation régulières et fournir des supports pédagogiques adaptés.', @p12='Formation à la gestion de la charge mentale et à la manipulation sécurisée des données sensibles' (Nullable = false), @p13='Risk Mitigation' (Nullable = false), @p14='2025-06-17T19:45:06.3912382Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.411 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='cfce597c-30c7-491e-bcd0-af0aa5930b0b', @p1='2025-06-17T19:45:06.4060691Z' (DbType = DateTime), @p2=NULL, @p3=NULL (DbType = DateTime), @p4=NULL, @p5='' (Nullable = false), @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='False', @p8=NULL, @p9='Low' (Nullable = false), @p10='[]' (Nullable = false), @p11=NULL, @p12='' (Nullable = false), @p13='' (Nullable = false), @p14='2025-06-17T19:45:06.4060693Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_recommendations ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "Description", "DocumentAnalysisResultId", "IsDeleted", "LegalBasis", "Priority", "RelatedClauses", "SuggestedAction", "Title", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.505 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='814ac2e1-36e5-447f-b16b-d82b48be7b30', @p1='0.95', @p2='2025-06-17T19:45:06.4194880Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='30', @p8='False', @p9='{}' (Nullable = false), @p10='LexBot SARL', @p11='16', @p12='LexBot S.A.R.L.' (Nullable = false), @p13='ORGANIZATION' (Nullable = false), @p14='2025-06-17T19:45:06.4194887Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.518 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='ef144d6e-c6c7-4984-9e73-78365db4b206', @p1='0.95', @p2='2025-06-17T19:45:06.5133047Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='48', @p8='False', @p9='{}' (Nullable = false), @p10='50000 EUR', @p11='36', @p12='50 000 euros' (Nullable = false), @p13='AMOUNT' (Nullable = false), @p14='2025-06-17T19:45:06.5133052Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.530 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='f69fe233-b866-41f1-83df-bc30f674b826', @p1='0.95', @p2='2025-06-17T19:45:06.5251100Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='112', @p8='False', @p9='{}' (Nullable = false), @p10='12 Rue de l'Intelligence Artificielle, 75001 Paris', @p11='70', @p12='12, Rue de l'Intelligence Artificielle' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T19:45:06.5251103Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.539 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='4ed3294f-a62e-46b9-b7ad-82ef75433377', @p1='0.95', @p2='2025-06-17T19:45:06.5354988Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='126', @p8='False', @p9='{}' (Nullable = false), @p10='75001 Paris', @p11='114', @p12='75001 Paris' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T19:45:06.5354991Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.550 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='955e02bd-aa02-4c44-bdb6-37448a26da28', @p1='0.95', @p2='2025-06-17T19:45:06.5443271Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='169', @p8='False', @p9='{}' (Nullable = false), @p10='RCS Paris 882000123', @p11='157', @p12='882 000 123' (Nullable = false), @p13='LEGAL_REFERENCE' (Nullable = false), @p14='2025-06-17T19:45:06.5443277Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.568 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='97ee9184-523f-427c-b06e-c5f03a4b6885', @p1='0.95', @p2='2025-06-17T19:45:06.5593419Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='189', @p8='False', @p9='{}' (Nullable = false), @p10='Clara Dupont', @p11='174', @p12='Mme Clara Dupont' (Nullable = false), @p13='PERSON' (Nullable = false), @p14='2025-06-17T19:45:06.5593424Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.587 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='b3d32364-c046-4603-ae39-663ad83131f7', @p1='0.95', @p2='2025-06-17T19:45:06.5785514Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='242', @p8='False', @p9='{}' (Nullable = false), @p10='Jean Luc', @p11='232', @p12='M. Jean Luc' (Nullable = false), @p13='PERSON' (Nullable = false), @p14='2025-06-17T19:45:06.5785520Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.606 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='f455e52c-14ee-47ea-95f7-f425c9172e36', @p1='0.95', @p2='2025-06-17T19:45:06.5976305Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='259', @p8='False', @p9='{}' (Nullable = false), @p10='1989-03-12', @p11='245', @p12='12 mars 1989' (Nullable = false), @p13='DATE' (Nullable = false), @p14='2025-06-17T19:45:06.5976310Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.625 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p0='ce5a5ff5-250e-4680-a554-45e915ef0f82', @p1='0.95', @p2='2025-06-17T19:45:06.6169716Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='266', @p8='False', @p9='{}' (Nullable = false), @p10='Lyon', @p11='262', @p12='Lyon' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T19:45:06.6169721Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.645 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='72ce192d-dca0-480a-9e1b-22fe903f997e', @p1='0.95', @p2='2025-06-17T19:45:06.6344102Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='274', @p8='False', @p9='{}' (Nullable = false), @p10='France', @p11='268', @p12='France' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T19:45:06.6344106Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.684 +04:00 [INF] Executed DbCommand (27ms) [Parameters=[@p0='26660206-56e2-4374-8095-ff8bc52ed0a9', @p1='0.95', @p2='2025-06-17T19:45:06.6543796Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='312', @p8='False', @p9='{}' (Nullable = false), @p10='8 Rue des Lilas, 69001 Lyon', @p11='278', @p12='8, rue des Lilas, 69001 Lyon' (Nullable = false), @p13='LOCATION' (Nullable = false), @p14='2025-06-17T19:45:06.6543805Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.697 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p0='d5a101b9-31d0-459a-b7cf-ed8de3592519', @p1='0.95', @p2='2025-06-17T19:45:06.6909104Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='386', @p8='False', @p9='{}' (Nullable = false), @p10='2025-07-01', @p11='370', @p12='1er juillet 2025' (Nullable = false), @p13='DATE' (Nullable = false), @p14='2025-06-17T19:45:06.6909109Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.713 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p0='8d5aa575-2d3b-421b-a3b1-3c42cab28032', @p1='0.95', @p2='2025-06-17T19:45:06.7074146Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='410', @p8='False', @p9='{}' (Nullable = false), @p10='2025-12-31', @p11='393', @p12='31 décembre 2025' (Nullable = false), @p13='DATE' (Nullable = false), @p14='2025-06-17T19:45:06.7074150Z' (Nullable = true) (DbType = DateTime), @p15=NULL], CommandType='"Text"', CommandTimeout='30']
INSERT INTO extracted_entities ("Id", "ConfidenceScore", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "EndPosition", "IsDeleted", "Metadata", "NormalizedValue", "StartPosition", "Text", "Type", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.798 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='1421cf8b-c7d3-49d6-9951-c3ac2222f212', @p1='Référence à la législation applicable pour la conclusion d'un contrat à durée déterminée pour accroissement temporaire d'activité.' (Nullable = false), @p2='2025-06-17T19:45:06.7221028Z' (DbType = DateTime), @p3=NULL, @p4=NULL (DbType = DateTime), @p5=NULL, @p6='8974a3b1-2c68-4cb0-a081-7954e2010a82', @p7='False', @p8='article L1242-2 du Code du travail', @p9='0.95', @p10='Code du travail' (Nullable = false), @p11='article L1242-2 du Code du travail' (Nullable = false), @p12='Legal Reference' (Nullable = false), @p13='2025-06-17T19:45:06.7221051Z' (Nullable = true) (DbType = DateTime), @p14=NULL, @p15='https://www.legifrance.gouv.fr/codes/article_lc/LEGIARTI000006902747/'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO document_citations ("Id", "Context", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DocumentAnalysisResultId", "IsDeleted", "Reference", "RelevanceScore", "Source", "Title", "Type", "UpdatedAt", "UpdatedBy", "Url")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
2025-06-17 23:45:06.809 +04:00 [INF] Related entities saved for analysis: "8974a3b1-2c68-4cb0-a081-7954e2010a82"
2025-06-17 23:45:06.813 +04:00 [INF] Document analysis completed successfully. Processing time: 88445ms, Tokens: 1305
2025-06-17 23:45:06.864 +04:00 [INF] Document analysis completed successfully for document: Cdd_test.pdf, Analysis ID: "8974a3b1-2c68-4cb0-a081-7954e2010a82"
2025-06-17 23:45:06.869 +04:00 [INF] Document analysis completed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6", analysis ID: "8974a3b1-2c68-4cb0-a081-7954e2010a82"
2025-06-17 23:45:06.883 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-17 23:45:06.939 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API) in 95706.603ms
2025-06-17 23:45:06.942 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.AnalyzeDocument (LexAI.DocumentAnalysis.API)'
2025-06-17 23:45:06.947 +04:00 [INF] Request POST /api/v1/DocumentAnalysis/analyze completed in 96137ms with status 200 (Correlation ID: 383b930e-f4f6-4a9c-9d79-41cb597c44d4)
2025-06-17 23:45:06.977 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5004/api/v1/DocumentAnalysis/analyze - 200 null application/json; charset=utf-8 96202.3239ms
2025-06-17 23:46:32.702 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-17 23:46:32.724 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID c5adde91-e267-4ff0-a91f-cef4527c2dfc
2025-06-17 23:46:32.727 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:46:32.730 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 2ms with status 204 (Correlation ID: c5adde91-e267-4ff0-a91f-cef4527c2dfc)
2025-06-17 23:46:32.736 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 34.091ms
2025-06-17 23:46:32.742 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 23:46:32.747 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 3c750487-9779-4c9c-a1da-2ef113fe3588
2025-06-17 23:46:32.751 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:46:32.759 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 23:46:32.764 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 23:46:32.778 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 23:46:32.788 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 23:46:32.790 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 23:46:32.795 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 23:46:32.828 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:46:32.840 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:46:33.443 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-17 23:46:33.726 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-17 23:46:33.854 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-17 23:46:33.936 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:46:33.944 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-17 23:46:33.961 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 1175.3323ms
2025-06-17 23:46:33.967 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 23:46:33.969 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 1218ms with status 200 (Correlation ID: 3c750487-9779-4c9c-a1da-2ef113fe3588)
2025-06-17 23:46:33.973 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 1231.265ms
2025-06-17 23:46:46.099 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - null null
2025-06-17 23:46:46.107 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID da9ebd1c-5870-4089-a021-26e3777ffa0d
2025-06-17 23:46:46.111 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:46:46.112 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 1ms with status 204 (Correlation ID: da9ebd1c-5870-4089-a021-26e3777ffa0d)
2025-06-17 23:46:46.117 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 204 null null 18.4252ms
2025-06-17 23:46:46.119 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - application/json null
2025-06-17 23:46:46.128 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID 19dd4b85-18aa-4e52-abc7-df7c505d5bb7
2025-06-17 23:46:46.131 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:46:46.134 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 23:46:46.139 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 23:46:46.146 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 23:46:46.154 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 23:46:46.157 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 23:46:46.168 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 23:46:46.176 +04:00 [INF] Retrieving analysis "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:46:46.187 +04:00 [INF] Retrieving analysis result: "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:46:46.260 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-17 23:46:46.457 +04:00 [INF] Executed DbCommand (33ms) [Parameters=[@__id_0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentPurpose", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExecutiveSummary", d2."ExtractedText", d2."FinancialTerms", d2."ImportantDates", d2."IsDeleted", d2."KeyPoints", d2."MainParties", d2."ModelUsed", d2."OverallRiskLevel", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-17 23:46:46.806 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-17 23:46:46.811 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 661.1389ms
2025-06-17 23:46:46.814 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 23:46:46.816 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 685ms with status 200 (Correlation ID: 19dd4b85-18aa-4e52-abc7-df7c505d5bb7)
2025-06-17 23:46:46.826 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 200 null application/json; charset=utf-8 706.4731ms
2025-06-17 23:48:59.051 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-17 23:48:59.077 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 5d87e996-86cf-41e5-9804-a6d6a7024947
2025-06-17 23:48:59.085 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:48:59.088 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 4ms with status 204 (Correlation ID: 5d87e996-86cf-41e5-9804-a6d6a7024947)
2025-06-17 23:48:59.100 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 48.7658ms
2025-06-17 23:48:59.107 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-17 23:48:59.129 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID fb93f724-d787-4b99-80c1-0c0346d54bce
2025-06-17 23:48:59.135 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:48:59.141 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 23:48:59.147 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 23:48:59.152 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 23:48:59.164 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 23:48:59.169 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 23:48:59.176 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 23:48:59.180 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:48:59.184 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:48:59.204 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-17 23:48:59.219 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-17 23:48:59.231 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:48:59.234 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-17 23:48:59.239 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 77.9215ms
2025-06-17 23:48:59.241 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-17 23:48:59.245 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 110ms with status 200 (Correlation ID: fb93f724-d787-4b99-80c1-0c0346d54bce)
2025-06-17 23:48:59.252 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 144.9908ms
2025-06-17 23:49:12.458 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - null null
2025-06-17 23:49:12.469 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID 9e75e4b4-f19f-4476-895c-8088ad36346b
2025-06-17 23:49:12.472 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:49:12.475 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 2ms with status 204 (Correlation ID: 9e75e4b4-f19f-4476-895c-8088ad36346b)
2025-06-17 23:49:12.484 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 204 null null 25.0319ms
2025-06-17 23:49:12.488 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - application/json null
2025-06-17 23:49:12.503 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID bd28646f-820c-4ebc-b18f-6df3228321cc
2025-06-17 23:49:12.512 +04:00 [INF] CORS policy execution successful.
2025-06-17 23:49:12.519 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-17 23:49:12.528 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 23:49:12.537 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-17 23:49:12.554 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-17 23:49:12.562 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-17 23:49:12.573 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-17 23:49:12.579 +04:00 [INF] Retrieving analysis "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:49:12.583 +04:00 [INF] Retrieving analysis result: "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-17 23:49:12.630 +04:00 [INF] Executed DbCommand (41ms) [Parameters=[@__id_0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentPurpose", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExecutiveSummary", d2."ExtractedText", d2."FinancialTerms", d2."ImportantDates", d2."IsDeleted", d2."KeyPoints", d2."MainParties", d2."ModelUsed", d2."OverallRiskLevel", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-17 23:49:12.872 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-17 23:49:12.919 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 365.5868ms
2025-06-17 23:49:12.924 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-17 23:49:12.927 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 415ms with status 200 (Correlation ID: bd28646f-820c-4ebc-b18f-6df3228321cc)
2025-06-17 23:49:12.932 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 200 null application/json; charset=utf-8 444.0119ms
