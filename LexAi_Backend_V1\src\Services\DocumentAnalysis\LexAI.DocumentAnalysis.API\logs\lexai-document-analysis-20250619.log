2025-06-19 00:30:55.184 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-19 00:30:57.025 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:30:57.676 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-19 00:30:57.683 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-19 00:30:57.955 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:30:58.194 +04:00 [INF] Hosting environment: Development
2025-06-19 00:30:58.209 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-19 00:30:58.746 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-19 00:30:58.984 +04:00 [INF] Request GET / started with correlation ID 45aec68f-bc2d-4b50-a888-97a41be6217b
2025-06-19 00:30:59.177 +04:00 [INF] Request GET / completed in 184ms with status 404 (Correlation ID: 45aec68f-bc2d-4b50-a888-97a41be6217b)
2025-06-19 00:30:59.195 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 453.6487ms
2025-06-19 00:30:59.234 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
