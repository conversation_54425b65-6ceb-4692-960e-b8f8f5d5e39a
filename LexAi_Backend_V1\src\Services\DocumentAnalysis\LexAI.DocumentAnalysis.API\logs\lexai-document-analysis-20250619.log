2025-06-19 00:30:55.184 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-19 00:30:57.025 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:30:57.676 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-19 00:30:57.683 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-19 00:30:57.955 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:30:58.194 +04:00 [INF] Hosting environment: Development
2025-06-19 00:30:58.209 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-19 00:30:58.746 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-19 00:30:58.984 +04:00 [INF] Request GET / started with correlation ID 45aec68f-bc2d-4b50-a888-97a41be6217b
2025-06-19 00:30:59.177 +04:00 [INF] Request GET / completed in 184ms with status 404 (Correlation ID: 45aec68f-bc2d-4b50-a888-97a41be6217b)
2025-06-19 00:30:59.195 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 453.6487ms
2025-06-19 00:30:59.234 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-19 00:52:06.935 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-19 00:52:07.092 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 00:52:07.539 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-19 00:52:07.541 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-19 00:52:08.015 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 00:52:08.017 +04:00 [INF] Hosting environment: Development
2025-06-19 00:52:08.018 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-19 00:52:08.643 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-19 00:52:08.881 +04:00 [INF] Request GET / started with correlation ID ac808669-98e0-4602-979e-51a2ddaa523a
2025-06-19 00:52:10.503 +04:00 [INF] Request GET / completed in 1613ms with status 404 (Correlation ID: ac808669-98e0-4602-979e-51a2ddaa523a)
2025-06-19 00:52:10.515 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 1874.6215ms
2025-06-19 00:52:10.538 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-19 01:05:40.083 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-19 01:05:40.257 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 01:05:40.972 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-19 01:05:40.975 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-19 01:05:41.310 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 01:05:41.315 +04:00 [INF] Hosting environment: Development
2025-06-19 01:05:41.318 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-19 01:05:41.703 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-19 01:05:42.013 +04:00 [INF] Request GET / started with correlation ID 1b2b26fe-4f73-4afc-b375-8d3f3205f3ad
2025-06-19 01:05:42.136 +04:00 [INF] Request GET / completed in 113ms with status 404 (Correlation ID: 1b2b26fe-4f73-4afc-b375-8d3f3205f3ad)
2025-06-19 01:05:42.147 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 450.7216ms
2025-06-19 01:05:42.171 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-19 19:58:40.823 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-19 19:58:40.967 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 19:58:41.203 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-19 19:58:41.206 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-19 19:58:42.459 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-19 19:58:42.584 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 19:58:42.600 +04:00 [INF] Hosting environment: Development
2025-06-19 19:58:42.605 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-19 19:58:42.845 +04:00 [INF] Request GET / started with correlation ID 1e040231-9332-4dbe-a32a-95e5d778636e
2025-06-19 19:58:43.107 +04:00 [INF] Request GET / completed in 245ms with status 404 (Correlation ID: 1e040231-9332-4dbe-a32a-95e5d778636e)
2025-06-19 19:58:43.126 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 669.3615ms
2025-06-19 19:58:43.149 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-19 20:41:06.821 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-19 20:41:06.971 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-19 20:41:07.721 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-19 20:41:07.724 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-19 20:41:07.863 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-19 20:41:07.865 +04:00 [INF] Hosting environment: Development
2025-06-19 20:41:07.868 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-19 20:41:08.288 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-19 20:41:08.519 +04:00 [INF] Request GET / started with correlation ID a026cc49-71d5-48bd-b878-d48d54e4f570
2025-06-19 20:41:10.207 +04:00 [INF] Request GET / completed in 1679ms with status 404 (Correlation ID: a026cc49-71d5-48bd-b878-d48d54e4f570)
2025-06-19 20:41:10.222 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 1936.2475ms
2025-06-19 20:41:10.250 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
