namespace LexAI.DocumentAnalysis.Application.DTOs;

/// <summary>
/// Requête d'analyse de document
/// </summary>
public class DocumentAnalysisRequestDto
{
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public byte[] DocumentContent { get; set; } = Array.Empty<byte>();
    public string? DocumentUrl { get; set; }
    public Guid UserId { get; set; }
    public AnalysisOptions Options { get; set; } = new();
}

/// <summary>
/// Options d'analyse
/// </summary>
public class AnalysisOptions
{
    public bool ExtractClauses { get; set; } = true;
    public bool PerformRiskAssessment { get; set; } = true;
    public bool GenerateRecommendations { get; set; } = true;
    public bool ExtractEntities { get; set; } = true;
    public bool FindCitations { get; set; } = true;
    public bool UseAzureDocumentIntelligence { get; set; } = true;
    public string? SpecificAnalysisType { get; set; }
    public List<string> FocusAreas { get; set; } = new();
    public string? Language { get; set; } = "fr";
}

/// <summary>
/// Réponse d'analyse de document
/// </summary>
public class DocumentAnalysisResponseDto
{
    public Guid Id { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string AnalysisContent { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public int ProcessingTimeMs { get; set; }
    public int TokensUsed { get; set; }
    public decimal EstimatedCost { get; set; }
    public string ModelUsed { get; set; } = string.Empty;
    public DateTime AnalyzedAt { get; set; }

    // Résultats détaillés
    public List<ClauseAnalysisDto> Clauses { get; set; } = new();
    public List<RiskAssessmentDto> Risks { get; set; } = new();
    public List<DocumentRecommendationDto> Recommendations { get; set; } = new();
    public List<ExtractedEntityDto> Entities { get; set; } = new();
    public List<DocumentCitationDto> Citations { get; set; } = new();
    public DocumentSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// Analyse d'une clause
/// </summary>
public class ClauseAnalysisDto
{
    public Guid Id { get; set; }
    public string ClauseText { get; set; } = string.Empty;
    public string ClauseType { get; set; } = string.Empty;
    public string Analysis { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public int StartPosition { get; set; }
    public int EndPosition { get; set; }
    public string? SuggestedRevision { get; set; }
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Évaluation des risques
/// </summary>
public class RiskAssessmentDto
{
    public Guid Id { get; set; }
    public string RiskType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public double Probability { get; set; }
    public string Impact { get; set; } = string.Empty;
    public string Mitigation { get; set; } = string.Empty;
    public List<string> AffectedClauses { get; set; } = new();
}

/// <summary>
/// Recommandation pour le document
/// </summary>
public class DocumentRecommendationDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string? SuggestedAction { get; set; }
    public string? LegalBasis { get; set; }
    public List<string> RelatedClauses { get; set; } = new();
}

/// <summary>
/// Entité extraite
/// </summary>
public class ExtractedEntityDto
{
    public Guid Id { get; set; }
    public string Text { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public int StartPosition { get; set; }
    public int EndPosition { get; set; }
    public string? NormalizedValue { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Citation juridique
/// </summary>
public class DocumentCitationDto
{
    public Guid Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string? Url { get; set; }
    public string? Reference { get; set; }
    public double RelevanceScore { get; set; }
    public string Context { get; set; } = string.Empty;
}

/// <summary>
/// Résumé du document
/// </summary>
public class DocumentSummaryDto
{
    public string ExecutiveSummary { get; set; } = string.Empty;
    public List<string> KeyPoints { get; set; } = new();
    public List<string> MainParties { get; set; } = new();
    public List<string> ImportantDates { get; set; } = new();
    public List<string> FinancialTerms { get; set; } = new();
    public string DocumentPurpose { get; set; } = string.Empty;
    public string OverallRiskLevel { get; set; } = string.Empty;
}

/// <summary>
/// Requête de liste d'analyses
/// </summary>
public class DocumentAnalysisListRequestDto
{
    public Guid? UserId { get; set; }
    public string? DocumentType { get; set; }
    public string? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// Réponse de liste d'analyses
/// </summary>
public class DocumentAnalysisListResponseDto
{
    public List<DocumentAnalysisSummaryDto> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}

/// <summary>
/// Résumé d'analyse pour les listes
/// </summary>
public class DocumentAnalysisSummaryDto
{
    public Guid Id { get; set; }
    public string DocumentName { get; set; } = string.Empty;
    public string DocumentType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public double ConfidenceScore { get; set; }
    public int RiskCount { get; set; }
    public int RecommendationCount { get; set; }
    public string OverallRiskLevel { get; set; } = string.Empty;
    public DateTime AnalyzedAt { get; set; }
    public int ProcessingTimeMs { get; set; }
}
