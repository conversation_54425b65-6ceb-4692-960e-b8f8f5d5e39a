{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "LexAI.DocumentAnalysis": "Debug"}}, "AllowedHosts": "*", "DocumentStorage": {"BasePath": "./storage/documents"}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:3001"]}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "Azure": {"DocumentIntelligence": {"Endpoint": "https://lexai-document-intelligence.cognitiveservices.azure.com/", "ApiKey": "93jMJwXHlfYZFVdorxiTRzM3UYUa3BG7KFT5gEyNp0pqpKlbiM3gJQQJ99BFACrIdLPXJ3w3AAALACOGJhwU", "ApiVersion": "2023-07-31", "DefaultModel": "prebuilt-read", "DocumentAnalysisModel": "prebuilt-document", "ContractAnalysisModel": "prebuilt-contract", "TimeoutSeconds": 120, "MaxFileSizeMB": 50, "SupportedFileTypes": ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword", "text/plain", "image/jpeg", "image/png", "image/tiff"], "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelayMs": 1000, "EnableCaching": true, "CacheDurationMinutes": 60, "EnableDetailedLogging": true}, "OpenAI": {"Endpoint": "https://lexai-az-openai.openai.azure.com/", "ApiKey": "4vl6Y7N8YV5D5oMUtEJYoYAlPc1tv1s0IPpbxsHrpe6zk2CPAGZQJQQJ99BFAC5T7U2XJ3w3AAABACOGv9Ha", "DeploymentName": "lexai-gpt-4.1-nano", "MaxTokens": 8000, "Temperature": 0.1, "TopP": 0.95, "FrequencyPenalty": 0, "PresencePenalty": 0, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelayMs": 1000}}, "LLM": {"DefaultProvider": "AzureOpenAI", "Providers": {"AzureOpenAI": {"Endpoint": "https://lexai-az-openai.openai.azure.com/", "ApiKey": "4vl6Y7N8YV5D5oMUtEJYoYAlPc1tv1s0IPpbxsHrpe6zk2CPAGZQJQQJ99BFAC5T7U2XJ3w3AAABACOGv9Ha", "ApiVersion": "nano-2025-04-14", "Models": {"Chat": "gpt-4.1", "Embedding": "text-embedding-ada-002"}}, "Local": {"Endpoint": "http://localhost:11434", "Models": {"Chat": "llama3.2:3b", "Embedding": "nomic-embed-text"}}}, "DefaultSettings": {"MaxTokens": 8000, "Temperature": 0.1, "TopP": 0.95, "FrequencyPenalty": 0, "PresencePenalty": 0}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5435;Database=lexai_document_analysis;Username=lexai_doc_analyse_user;Password=lexai_password_2024!", "Redis": "localhost:6379"}, "HealthChecks": {"UI": {"Enabled": true, "Path": "/health-ui"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "LexAI.DocumentAnalysis": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/lexai-document-analysis-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}]}}