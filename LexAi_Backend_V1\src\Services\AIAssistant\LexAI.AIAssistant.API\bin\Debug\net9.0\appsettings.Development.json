{"ConnectionStrings": {"PostgreSql": "Host=localhost;Port=5433;Database=identity_db;Username=lexai_user;Password=lexai_password_2024!"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001"]}}