{"ConnectionStrings": {"PostgreSql": "Host=localhost;Port=5436;Database=lexai_ai_assistant;Username=lexai_ai_assistant_user;Password=lexai_password_2024!"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:3001"]}, "Azure": {"OpenAI": {"Endpoint": "https://lexai-az-openai.openai.azure.com/", "ApiKey": "4vl6Y7N8YV5D5oMUtEJYoYAlPc1tv1s0IPpbxsHrpe6zk2CPAGZQJQQJ99BFAC5T7U2XJ3w3AAABACOGv9Ha", "DeploymentName": "lexai-gpt-4.1-nano", "MaxTokens": 8000, "Temperature": 0.1, "TopP": 0.95, "FrequencyPenalty": 0, "PresencePenalty": 0, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelayMs": 1000}}, "LLMService": {"DefaultProvider": "AzureOpenAI", "FallbackProvider": "Local", "MaxRetries": 3, "TimeoutSeconds": 120}, "OpenAI": {"ApiKey": "your-openai-api-key-fallback", "ChatModel": "gpt-4", "EmbeddingModel": "text-embedding-3-small", "MaxTokens": 4000, "Temperature": 0.7}}