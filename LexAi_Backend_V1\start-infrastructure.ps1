# Script PowerShell pour démarrer l'infrastructure LexAI
Write-Host "🚀 Démarrage de l'infrastructure LexAI..." -ForegroundColor Green

# Vérifier si Docker est installé et en cours d'exécution
try {
    docker --version | Out-Null
    Write-Host "✅ Docker détecté" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker n'est pas installé ou n'est pas en cours d'exécution" -ForegroundColor Red
    Write-Host "Veuillez installer Docker Desktop et le démarrer avant de continuer." -ForegroundColor Yellow
    exit 1
}

# Démarrer l'infrastructure avec Docker Compose
Write-Host "📦 Démarrage des services d'infrastructure..." -ForegroundColor Blue

try {
    # Arrêter les services existants s'ils sont en cours d'exécution
    Write-Host "🛑 Arrêt des services existants..." -ForegroundColor Yellow
    docker-compose -f docker-compose.infrastructure.yml down

    # Démarrer les nouveaux services
    Write-Host "🔄 Démarrage des nouveaux services..." -ForegroundColor Blue
    docker-compose -f docker-compose.infrastructure.yml up -d

    # Attendre que les services soient prêts
    Write-Host "⏳ Attente que les services soient prêts..." -ForegroundColor Yellow
    Start-Sleep -Seconds 30

    # Vérifier l'état des services
    Write-Host "🔍 Vérification de l'état des services..." -ForegroundColor Blue
    docker-compose -f docker-compose.infrastructure.yml ps

    Write-Host ""
    Write-Host "🎉 Infrastructure démarrée avec succès!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Services disponibles:" -ForegroundColor Cyan
    Write-Host "  • PostgreSQL: localhost:5432" -ForegroundColor White
    Write-Host "  • MongoDB: localhost:27017" -ForegroundColor White
    Write-Host "  • Qdrant: localhost:6333" -ForegroundColor White
    Write-Host "  • Redis: localhost:6379" -ForegroundColor White
    Write-Host "  • Elasticsearch: localhost:9200" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Prochaines étapes:" -ForegroundColor Cyan
    Write-Host "  1. Exécuter les migrations de base de données" -ForegroundColor White
    Write-Host "  2. Démarrer les services backend" -ForegroundColor White
    Write-Host "  3. Configurer les variables d'environnement" -ForegroundColor White

} catch {
    Write-Host "❌ Erreur lors du démarrage de l'infrastructure: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✨ Infrastructure prête pour le développement!" -ForegroundColor Green
