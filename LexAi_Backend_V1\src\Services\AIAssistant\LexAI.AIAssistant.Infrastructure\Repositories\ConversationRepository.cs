using LexAI.AIAssistant.Application.Commands;
using LexAI.AIAssistant.Domain.Entities;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Data;
using Dapper;

namespace LexAI.AIAssistant.Infrastructure.Repositories;

/// <summary>
/// Repository pour la gestion des conversations
/// </summary>
public class ConversationRepository : IConversationRepository
{
    private readonly string _connectionString;
    private readonly ILogger<ConversationRepository> _logger;

    public ConversationRepository(
        string connectionString,
        ILogger<ConversationRepository> logger)
    {
        _connectionString = connectionString;
        _logger = logger;
    }

    /// <summary>
    /// Obtient une conversation par son ID
    /// </summary>
    public async Task<Conversation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT c.*, m.*
                FROM conversations c
                LEFT JOIN messages m ON c.id = m.conversation_id
                WHERE c.id = @Id
                ORDER BY m.created_at";

            var conversationDict = new Dictionary<Guid, Conversation>();

            await connection.QueryAsync<Conversation, Message, Conversation>(
                sql,
                (conversation, message) =>
                {
                    if (!conversationDict.TryGetValue(conversation.Id, out var existingConversation))
                    {
                        existingConversation = conversation;
                        conversationDict.Add(conversation.Id, existingConversation);
                    }

                    if (message != null)
                    {
                        existingConversation.AddMessage(message);
                    }

                    return existingConversation;
                },
                new { Id = id },
                splitOn: "id");

            return conversationDict.Values.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation by ID: {ConversationId}", id);
            return null;
        }
    }

    /// <summary>
    /// Obtient une conversation par l'ID d'un message
    /// </summary>
    public async Task<Conversation?> GetByMessageIdAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT c.*, m.*
                FROM conversations c
                INNER JOIN messages m ON c.id = m.conversation_id
                WHERE m.id = @MessageId";

            var result = await connection.QueryAsync<Conversation, Message, Conversation>(
                sql,
                (conversation, message) =>
                {
                    if (message != null)
                    {
                        conversation.AddMessage(message);
                    }
                    return conversation;
                },
                new { MessageId = messageId },
                splitOn: "id");

            return result.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation by message ID: {MessageId}", messageId);
            return null;
        }
    }

    /// <summary>
    /// Ajoute une nouvelle conversation
    /// </summary>
    public async Task AddAsync(Conversation conversation, CancellationToken cancellationToken = default)
    {
        await SaveAsync(conversation, cancellationToken);
    }

    /// <summary>
    /// Met à jour une conversation
    /// </summary>
    public async Task UpdateAsync(Conversation conversation, CancellationToken cancellationToken = default)
    {
        await SaveAsync(conversation, cancellationToken);
    }

    /// <summary>
    /// Sauvegarde une conversation
    /// </summary>
    public async Task<Conversation> SaveAsync(Conversation conversation, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            using var transaction = await connection.BeginTransactionAsync(cancellationToken);

            try
            {
                // Insérer ou mettre à jour la conversation
                const string conversationSql = @"
                    INSERT INTO conversations (id, user_id, session_id, title, created_at, updated_at, is_active)
                    VALUES (@Id, @UserId, @SessionId, @Title, @CreatedAt, @UpdatedAt, @IsActive)
                    ON CONFLICT (id) DO UPDATE SET
                        title = EXCLUDED.title,
                        updated_at = EXCLUDED.updated_at,
                        is_active = EXCLUDED.is_active";

                await connection.ExecuteAsync(conversationSql, conversation, transaction);

                // Insérer les nouveaux messages
                if (conversation.Messages?.Any() == true)
                {
                    const string messageSql = @"
                        INSERT INTO messages (id, conversation_id, content, role, created_at, token_count, estimated_cost)
                        VALUES (@Id, @ConversationId, @Content, @Role, @CreatedAt, @TokenCount, @EstimatedCost)
                        ON CONFLICT (id) DO NOTHING";

                    foreach (var message in conversation.Messages)
                    {
                        await connection.ExecuteAsync(messageSql, new
                        {
                            Id = message.Id,
                            ConversationId = conversation.Id,
                            Content = message.Content,
                            Role = message.Role.ToString(),
                            CreatedAt = message.CreatedAt,
                            TokenCount = message.TokensUsed,
                            EstimatedCost = message.EstimatedCost
                        }, transaction);
                    }
                }

                await transaction.CommitAsync(cancellationToken);
                return conversation;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving conversation: {ConversationId}", conversation.Id);
            throw;
        }
    }

    /// <summary>
    /// Met à jour le rating d'un message
    /// </summary>
    public async Task<bool> UpdateMessageRatingAsync(Guid messageId, int rating, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                UPDATE messages
                SET rating = @Rating, updated_at = @UpdatedAt
                WHERE id = @MessageId";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Rating = rating,
                UpdatedAt = DateTime.UtcNow,
                MessageId = messageId
            });

            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating message rating: {MessageId}", messageId);
            return false;
        }
    }

    /// <summary>
    /// Obtient les conversations d'un utilisateur
    /// </summary>
    public async Task<IEnumerable<Conversation>> GetByUserIdAsync(Guid userId, int limit = 20, int offset = 0, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT * FROM conversations
                WHERE user_id = @UserId
                ORDER BY updated_at DESC
                LIMIT @Limit OFFSET @Offset";

            var conversations = await connection.QueryAsync<Conversation>(sql, new { UserId = userId, Limit = limit, Offset = offset });
            return conversations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversations for user: {UserId}", userId);
            return Enumerable.Empty<Conversation>();
        }
    }

    /// <summary>
    /// Supprime une conversation
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            using var transaction = await connection.BeginTransactionAsync(cancellationToken);

            try
            {
                // Supprimer les messages
                await connection.ExecuteAsync("DELETE FROM messages WHERE conversation_id = @Id", new { Id = id }, transaction);

                // Supprimer la conversation
                var rowsAffected = await connection.ExecuteAsync("DELETE FROM conversations WHERE id = @Id", new { Id = id }, transaction);

                await transaction.CommitAsync(cancellationToken);
                return rowsAffected > 0;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting conversation: {ConversationId}", id);
            return false;
        }
    }
}
