using LexAI.AIAssistant.Application.Commands;
using LexAI.AIAssistant.Domain.Entities;
using LexAI.AIAssistant.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LexAI.AIAssistant.Infrastructure.Repositories;

/// <summary>
/// Repository pour la gestion des conversations avec EF Core
/// </summary>
public class ConversationRepository : IConversationRepository
{
    private readonly AIAssistantDbContext _context;
    private readonly ILogger<ConversationRepository> _logger;

    public ConversationRepository(
        AIAssistantDbContext context,
        ILogger<ConversationRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Obtient une conversation par son ID
    /// </summary>
    public async Task<Conversation?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting conversation by ID: {ConversationId}", id);

            return await _context.Conversations
                .Include(c => c.Messages)
                .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation by ID: {ConversationId}", id);
            return null;
        }
    }

    /// <summary>
    /// Obtient une conversation par l'ID d'un message
    /// </summary>
    public async Task<Conversation?> GetByMessageIdAsync(Guid messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting conversation by message ID: {MessageId}", messageId);

            return await _context.Conversations
                .Include(c => c.Messages)
                .FirstOrDefaultAsync(c => c.Messages.Any(m => m.Id == messageId), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversation by message ID: {MessageId}", messageId);
            return null;
        }
    }

    /// <summary>
    /// Ajoute une nouvelle conversation
    /// </summary>
    public async Task AddAsync(Conversation conversation, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Adding new conversation: {ConversationId}", conversation.Id);

            _context.Conversations.Add(conversation);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding conversation: {ConversationId}", conversation.Id);
            throw;
        }
    }

    /// <summary>
    /// Met à jour une conversation
    /// </summary>
    public async Task UpdateAsync(Conversation conversation, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating conversation: {ConversationId}", conversation.Id);

            // Recharger l'entité depuis la base de données pour éviter les conflits de concurrence
            var existingConversation = await _context.Conversations
                .Include(c => c.Messages)
                .FirstOrDefaultAsync(c => c.Id == conversation.Id, cancellationToken);

            if (existingConversation == null)
            {
                throw new InvalidOperationException($"Conversation {conversation.Id} not found");
            }

            // Mettre à jour les propriétés en utilisant les méthodes de l'entité
            if (!string.IsNullOrEmpty(conversation.Title) && existingConversation.Title != conversation.Title)
            {
                existingConversation.UpdateTitle(conversation.Title, conversation.UpdatedBy ?? "System");
            }

            if (!string.IsNullOrEmpty(conversation.Summary) && existingConversation.Summary != conversation.Summary)
            {
                existingConversation.SetSummary(conversation.Summary);
            }

            if (conversation.UserRating.HasValue && existingConversation.UserRating != conversation.UserRating)
            {
                existingConversation.SetUserRating(conversation.UserRating.Value, conversation.UserFeedback, conversation.UpdatedBy ?? "System");
            }

            if (conversation.IsArchived && !existingConversation.IsArchived)
            {
                existingConversation.Archive(conversation.UpdatedBy ?? "System");
            }
            else if (!conversation.IsArchived && existingConversation.IsArchived)
            {
                existingConversation.Unarchive(conversation.UpdatedBy ?? "System");
            }

            // L'activité sera mise à jour automatiquement par les méthodes de l'entité

            // Gérer les nouveaux messages
            foreach (var message in conversation.Messages)
            {
                var existingMessage = existingConversation.Messages.FirstOrDefault(m => m.Id == message.Id);
                if (existingMessage == null)
                {
                    existingConversation.Messages.Add(message);
                }
            }

            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateConcurrencyException ex)
        {
            _logger.LogWarning(ex, "Concurrency conflict updating conversation: {ConversationId}. Retrying...", conversation.Id);

            // Recharger et réessayer une fois
            _context.Entry(conversation).Reload();
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating conversation: {ConversationId}", conversation.Id);
            throw;
        }
    }

    /// <summary>
    /// Sauvegarde une conversation avec gestion de la concurrence
    /// </summary>
    public async Task<Conversation> SaveAsync(Conversation conversation, CancellationToken cancellationToken = default)
    {
        int retryCount = 0;
        const int maxRetries = 3;

        while (retryCount < maxRetries)
        {
            try
            {
                _logger.LogDebug("Saving conversation: {ConversationId}, Attempt: {Attempt}", conversation.Id, retryCount + 1);

                // Utiliser le contexte existant
                var context = _context;

                var existingConversation = await context.Conversations
                    .Include(c => c.Messages)
                    .FirstOrDefaultAsync(c => c.Id == conversation.Id, cancellationToken);

                if (existingConversation == null)
                {
                    // Nouvelle conversation - ajouter avec tous ses messages
                    _logger.LogDebug("Adding new conversation {ConversationId} with {MessageCount} messages",
                        conversation.Id, conversation.Messages.Count);
                    context.Conversations.Add(conversation);
                }
                else
                {
                    // Conversation existante - mettre à jour les propriétés
                    _logger.LogDebug("Updating existing conversation {ConversationId}. Current messages: {CurrentCount}, New messages: {NewCount}",
                        existingConversation.Id, existingConversation.Messages.Count, conversation.Messages.Count);

                    // Mettre à jour les propriétés de la conversation via Entity Framework
                    if (existingConversation.Title != conversation.Title)
                    {
                        existingConversation.UpdateTitle(conversation.Title, "system");
                    }

                    // NE PAS utiliser SetValues car cela écrase les collections de navigation !
                    // Mettre à jour manuellement les propriétés nécessaires
                    // Les autres propriétés sont mises à jour automatiquement par AddMessage

                    // Ajouter les nouveaux messages
                    foreach (var message in conversation.Messages)
                    {
                        var existingMessage = existingConversation.Messages.FirstOrDefault(m => m.Id == message.Id);
                        if (existingMessage == null)
                        {
                            _logger.LogDebug("Adding new message {MessageId} to conversation {ConversationId}",
                                message.Id, conversation.Id);

                            // Vérifier l'état du message dans le contexte
                            var messageEntry = context.Entry(message);
                            _logger.LogDebug("Message {MessageId} state in context: {State}", message.Id, messageEntry.State);

                            // Si le message est déjà tracké, le détacher d'abord
                            if (messageEntry.State != EntityState.Detached)
                            {
                                _logger.LogWarning("Message {MessageId} is already tracked with state {State}. Detaching...",
                                    message.Id, messageEntry.State);
                                messageEntry.State = EntityState.Detached;
                            }

                            existingConversation.Messages.Add(message);

                            // Vérifier l'état après l'ajout
                            var newMessageEntry = context.Entry(message);
                            _logger.LogDebug("Message {MessageId} state after adding: {State}", message.Id, newMessageEntry.State);
                        }
                        else
                        {
                            // Mettre à jour le message existant si nécessaire
                            // Les messages sont généralement immutables une fois créés
                            // Seules certaines propriétés peuvent être mises à jour
                            if (existingMessage.Rating != message.Rating && message.Rating.HasValue)
                            {
                                existingMessage.SetUserRating(message.Rating.Value, message.UserFeedback, "user");
                            }
                        }
                    }
                }

                // Vérifier l'état des entités avant la sauvegarde
                var conversationEntry = context.Entry(existingConversation ?? conversation);
                _logger.LogDebug("Conversation state before save: {State}", conversationEntry.State);

                var messageEntries = context.ChangeTracker.Entries<Message>().ToList();
                _logger.LogDebug("Message entries before save: {Count}", messageEntries.Count);
                foreach (var messageEntry in messageEntries)
                {
                    _logger.LogDebug("Message {MessageId} state: {State}", messageEntry.Entity.Id, messageEntry.State);
                }

                await SaveChangesAsync(cancellationToken);

                _logger.LogDebug("Conversation saved successfully: {ConversationId}", conversation.Id);

                // Vérifier après la sauvegarde
                var savedConversation = await context.Conversations
                    .Include(c => c.Messages)
                    .FirstOrDefaultAsync(c => c.Id == conversation.Id, cancellationToken);

                _logger.LogDebug("Conversation after save - Messages count: {Count}", savedConversation?.Messages.Count ?? 0);

                return conversation;
            }
            catch (DbUpdateConcurrencyException ex)
            {
                retryCount++;
                _logger.LogWarning(ex, "Concurrency conflict saving conversation {ConversationId}. Retry attempt {RetryCount}/{MaxRetries}",
                    conversation.Id, retryCount, maxRetries);

                if (retryCount >= maxRetries)
                {
                    _logger.LogError(ex, "Max retry attempts reached for conversation {ConversationId}", conversation.Id);
                    throw;
                }

                // Attendre un peu avant de réessayer
                await Task.Delay(100 * retryCount, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving conversation: {ConversationId}", conversation.Id);
                throw;
            }
        }

        throw new InvalidOperationException($"Failed to save conversation {conversation.Id} after {maxRetries} attempts");
    }

    /// <summary>
    /// Sauvegarde les changements dans le contexte avec gestion de la concurrence
    /// </summary>
    public async Task SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        int retryCount = 0;
        const int maxRetries = 3;

        while (retryCount < maxRetries)
        {
            try
            {
                await _context.SaveChangesAsync(cancellationToken);
                return; // Succès, sortir de la boucle
            }
            catch (DbUpdateConcurrencyException ex)
            {
                retryCount++;
                _logger.LogWarning(ex, "Concurrency conflict detected. Retry attempt {RetryCount}/{MaxRetries}", retryCount, maxRetries);

                if (retryCount >= maxRetries)
                {
                    _logger.LogError(ex, "Max retry attempts reached for concurrency conflict");
                    throw;
                }

                // Recharger les entités en conflit
                foreach (var entry in ex.Entries)
                {
                    await entry.ReloadAsync(cancellationToken);
                }

                // Attendre un peu avant de réessayer
                await Task.Delay(100 * retryCount, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving changes to database");
                throw;
            }
        }
    }

    /// <summary>
    /// Met à jour le rating d'un message
    /// </summary>
    public async Task<bool> UpdateMessageRatingAsync(Guid messageId, int rating, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Updating message rating: {MessageId}, Rating: {Rating}", messageId, rating);

            var message = await _context.Messages.FirstOrDefaultAsync(m => m.Id == messageId, cancellationToken);
            if (message == null)
            {
                return false;
            }

            message.Rating = rating;
            message.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating message rating: {MessageId}", messageId);
            return false;
        }
    }

    /// <summary>
    /// Obtient les conversations d'un utilisateur
    /// </summary>
    public async Task<IEnumerable<Conversation>> GetByUserIdAsync(Guid userId, int limit = 20, int offset = 0, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting conversations for user: {UserId}, Limit: {Limit}, Offset: {Offset}", userId, limit, offset);

            return await _context.Conversations
                .Include(c => c.Messages)
                .Where(c => c.UserId == userId)
                .OrderByDescending(c => c.UpdatedAt)
                .Skip(offset)
                .Take(limit)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conversations for user: {UserId}", userId);
            return Enumerable.Empty<Conversation>();
        }
    }

    /// <summary>
    /// Supprime une conversation
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Deleting conversation: {ConversationId}", id);

            var conversation = await _context.Conversations
                .Include(c => c.Messages)
                .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);

            if (conversation == null)
            {
                return false;
            }

            _context.Conversations.Remove(conversation);
            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting conversation: {ConversationId}", id);
            return false;
        }
    }
}
