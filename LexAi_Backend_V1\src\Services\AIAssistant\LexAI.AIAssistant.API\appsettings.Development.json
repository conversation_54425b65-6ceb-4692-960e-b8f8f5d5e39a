{"ConnectionStrings": {"PostgreSql": "Host=localhost;Port=5436;Database=lexai_ai_assistant;Username=lexai_ai_assistant_user;Password=lexai_password_2024!"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:3001"]}, "AzureOpenAI": {"Endpoint": "https://your-azure-openai-endpoint.openai.azure.com/", "ApiKey": "your-azure-openai-api-key", "ChatDeploymentName": "gpt-4", "EmbeddingDeploymentName": "text-embedding-3-small", "ApiVersion": "2024-02-01", "MaxTokens": 4000, "Temperature": 0.7, "TopP": 1.0, "FrequencyPenalty": 0.0, "PresencePenalty": 0.0}, "LLMService": {"DefaultProvider": "AzureOpenAI", "FallbackProvider": "Local", "MaxRetries": 3, "TimeoutSeconds": 120}, "OpenAI": {"ApiKey": "your-openai-api-key-fallback", "ChatModel": "gpt-4", "EmbeddingModel": "text-embedding-3-small", "MaxTokens": 4000, "Temperature": 0.7}}