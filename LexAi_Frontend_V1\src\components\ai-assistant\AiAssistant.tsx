import React, { useState } from 'react'
import { Send, <PERSON>clip, <PERSON>ting<PERSON>, MoreHorizontal, RotateCcw, Co<PERSON> } from 'lucide-react'
import { useThemeStore } from '../../store/themeStore'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface Chat {
  id: string
  title: string
  messages: Message[]
  isPinned?: boolean
}

const AiAssistant: React.FC = () => {
  const { theme } = useThemeStore()
  const [chats, setChats] = useState<Chat[]>([])
  const [activeChat, setActiveChat] = useState<string | null>(null)
  const [message, setMessage] = useState('')
  const [isInternetEnabled, setIsInternetEnabled] = useState(true)
  const [isPromptsEnabled, setIsPromptsEnabled] = useState(true)

  const createNewChat = () => {
    const newChat: Chat = {
      id: Date.now().toString(),
      title: 'New Chat',
      messages: []
    }
    setChats(prev => [newChat, ...prev])
    setActiveChat(newChat.id)
  }

  const sendMessage = () => {
    if (!message.trim() || !activeChat) return

    const newMessage: Message = {
      id: Date.now().toString(),
      content: message,
      isUser: true,
      timestamp: new Date()
    }

    setChats(prev => prev.map(chat => 
      chat.id === activeChat 
        ? { ...chat, messages: [...chat.messages, newMessage] }
        : chat
    ))

    setMessage('')
  }

  const currentChat = chats.find(chat => chat.id === activeChat)
  const pinnedChats = chats.filter(chat => chat.isPinned)
  const historyChats = chats.filter(chat => !chat.isPinned)

  return (
    <div className={`flex h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Sidebar */}
      <div className={`w-80 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} border-r ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">ai</span>
              </div>
              <span className="font-semibold">lawyer</span>
            </div>
            <button className="px-4 py-2 bg-orange-400 text-black rounded-lg text-sm font-medium hover:bg-orange-500 transition-colors">
              Activate PRO
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-4">Chats</h3>
            
            <button 
              onClick={createNewChat}
              className="w-full px-4 py-3 bg-orange-400 text-black rounded-lg font-medium hover:bg-orange-500 transition-colors mb-6"
            >
              New Chat
            </button>

            {/* Pinned Chats */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-400">Pinned chats</h4>
                <MoreHorizontal className="w-4 h-4 text-gray-400" />
              </div>
              {pinnedChats.length === 0 ? (
                <p className="text-sm text-gray-500">No pinned chats yet :(</p>
              ) : (
                <div className="space-y-2">
                  {pinnedChats.map(chat => (
                    <div 
                      key={chat.id}
                      onClick={() => setActiveChat(chat.id)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        activeChat === chat.id 
                          ? 'bg-gray-700' 
                          : 'hover:bg-gray-700'
                      }`}
                    >
                      <span className="text-sm">{chat.title}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* History */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-400">History</h4>
                <MoreHorizontal className="w-4 h-4 text-gray-400" />
              </div>
              {historyChats.length === 0 ? (
                <p className="text-sm text-gray-500">
                  History is empty. Let's start your first chat!
                </p>
              ) : (
                <div className="space-y-2">
                  {historyChats.map(chat => (
                    <div 
                      key={chat.id}
                      onClick={() => setActiveChat(chat.id)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        activeChat === chat.id 
                          ? 'bg-gray-700' 
                          : 'hover:bg-gray-700'
                      }`}
                    >
                      <span className="text-sm">{chat.title}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="p-4 border-t border-gray-700 text-center">
          <p className="text-xs text-gray-400 mb-2">
            Earn with us as an elite distributor! Click{' '}
            <span className="text-blue-400 cursor-pointer">here</span> for the survey 🔥
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {!activeChat ? (
          /* Welcome Screen */
          <div className="flex-1 flex items-center justify-center">
            <div className={`max-w-md mx-auto text-center p-8 rounded-2xl ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
              <div className="mb-6">
                <Settings className="w-6 h-6 text-gray-400 ml-auto mb-4" />
                <h2 className="text-xl font-semibold mb-2">New Chat</h2>
                <p className="text-gray-400 mb-4">
                  Hello, User 👋
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Please select a question from the "Prompts" library below or write your own question.
                </p>
                <p className="text-xs text-gray-500 mb-6">and</p>
                <p className="text-sm text-gray-500 mb-6">
                  You can also add context from any of the already existing chat from history
                </p>
                <button className="px-6 py-2 bg-orange-400 text-black rounded-lg font-medium hover:bg-orange-500 transition-colors">
                  Add
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* Chat Interface */
          <>
            {/* Chat Header */}
            <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex items-center justify-between`}>
              <h2 className="text-lg font-semibold">{currentChat?.title}</h2>
              <Settings className="w-5 h-5 text-gray-400 cursor-pointer" />
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4">
              {currentChat?.messages.map(msg => (
                <div key={msg.id} className={`mb-4 ${msg.isUser ? 'text-right' : 'text-left'}`}>
                  <div className={`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    msg.isUser 
                      ? 'bg-blue-600 text-white' 
                      : theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
                  }`}>
                    {msg.content}
                  </div>
                </div>
              ))}
            </div>

            {/* Input Area */}
            <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
              {/* Toggle Buttons */}
              <div className="flex items-center justify-end space-x-2 mb-4">
                <button 
                  onClick={() => setIsInternetEnabled(!isInternetEnabled)}
                  className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
                    isInternetEnabled 
                      ? 'bg-green-600 text-white' 
                      : 'bg-gray-600 text-gray-300'
                  }`}
                >
                  <div className="w-2 h-2 bg-current rounded-full"></div>
                  <span>Internet</span>
                </button>
                <button 
                  onClick={() => setIsPromptsEnabled(!isPromptsEnabled)}
                  className={`px-3 py-1 rounded-full text-xs ${
                    isPromptsEnabled 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-600 text-gray-300'
                  }`}
                >
                  Prompts
                </button>
              </div>

              {/* Input */}
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <Paperclip className="w-5 h-5 text-gray-400 cursor-pointer" />
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  placeholder="Type your message here"
                  className="flex-1 bg-transparent outline-none placeholder-gray-400"
                />
                <button 
                  onClick={sendMessage}
                  className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default AiAssistant
