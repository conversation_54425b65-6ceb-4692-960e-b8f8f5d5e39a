2025-06-14 17:56:26.299 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-14 17:56:26.490 +04:00 [INF] Hangfire SQL objects installed.
2025-06-14 17:56:26.512 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-14 17:56:27.437 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-14 17:56:27.473 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 17:56:27.728 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-14 17:56:27.731 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-14 17:56:27.798 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-14 17:56:27.801 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-14 17:56:27.809 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-14 17:56:27.811 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-14 17:56:27.812 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-14 17:56:27.818 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-14 17:56:27.858 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 17:56:27.860 +04:00 [INF] Hosting environment: Development
2025-06-14 17:56:27.862 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-14 17:56:28.510 +04:00 [INF] Server datapreprocessing-kevin11:35032:280b5de8 successfully announced in 41.8912 ms
2025-06-14 17:56:28.540 +04:00 [INF] Server datapreprocessing-kevin11:35032:280b5de8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-14 17:56:28.597 +04:00 [INF] 1 servers were removed due to timeout
2025-06-14 17:56:28.727 +04:00 [INF] Removed 1 outdated record(s) from 'aggregatedcounter' table.
2025-06-14 17:56:28.941 +04:00 [INF] Server datapreprocessing-kevin11:35032:280b5de8 all the dispatchers started
2025-06-14 17:56:29.225 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-14 17:56:29.472 +04:00 [INF] Request GET / started with correlation ID c82be628-ab59-4385-a1c8-cdfcf840e25e
2025-06-14 17:56:29.915 +04:00 [INF] Request GET / completed in 437ms with status 404 (Correlation ID: c82be628-ab59-4385-a1c8-cdfcf840e25e)
2025-06-14 17:56:29.932 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 707.1809ms
2025-06-14 17:56:29.935 +04:00 [INF] Removed 1 outdated record(s) from 'aggregatedcounter' table.
2025-06-14 17:56:29.937 +04:00 [INF] Generating processing statistics
2025-06-14 17:56:29.946 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-14 17:56:29.947 +04:00 [INF] Starting cleanup of failed documents
2025-06-14 17:56:30.989 +04:00 [INF] Removed 3 outdated record(s) from 'job' table.
2025-06-14 18:00:00.307 +04:00 [INF] Generating processing statistics
