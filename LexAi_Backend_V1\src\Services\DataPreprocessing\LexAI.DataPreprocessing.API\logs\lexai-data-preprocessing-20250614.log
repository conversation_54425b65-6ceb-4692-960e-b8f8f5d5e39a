2025-06-14 17:56:26.299 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-14 17:56:26.490 +04:00 [INF] Hangfire SQL objects installed.
2025-06-14 17:56:26.512 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-14 17:56:27.437 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-14 17:56:27.473 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 17:56:27.728 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-14 17:56:27.731 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-14 17:56:27.798 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-14 17:56:27.801 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-14 17:56:27.809 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-14 17:56:27.811 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-14 17:56:27.812 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-14 17:56:27.818 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-14 17:56:27.858 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 17:56:27.860 +04:00 [INF] Hosting environment: Development
2025-06-14 17:56:27.862 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-14 17:56:28.510 +04:00 [INF] Server datapreprocessing-kevin11:35032:280b5de8 successfully announced in 41.8912 ms
2025-06-14 17:56:28.540 +04:00 [INF] Server datapreprocessing-kevin11:35032:280b5de8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-14 17:56:28.597 +04:00 [INF] 1 servers were removed due to timeout
2025-06-14 17:56:28.727 +04:00 [INF] Removed 1 outdated record(s) from 'aggregatedcounter' table.
2025-06-14 17:56:28.941 +04:00 [INF] Server datapreprocessing-kevin11:35032:280b5de8 all the dispatchers started
2025-06-14 17:56:29.225 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-14 17:56:29.472 +04:00 [INF] Request GET / started with correlation ID c82be628-ab59-4385-a1c8-cdfcf840e25e
2025-06-14 17:56:29.915 +04:00 [INF] Request GET / completed in 437ms with status 404 (Correlation ID: c82be628-ab59-4385-a1c8-cdfcf840e25e)
2025-06-14 17:56:29.932 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 707.1809ms
2025-06-14 17:56:29.935 +04:00 [INF] Removed 1 outdated record(s) from 'aggregatedcounter' table.
2025-06-14 17:56:29.937 +04:00 [INF] Generating processing statistics
2025-06-14 17:56:29.946 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-14 17:56:29.947 +04:00 [INF] Starting cleanup of failed documents
2025-06-14 17:56:30.989 +04:00 [INF] Removed 3 outdated record(s) from 'job' table.
2025-06-14 18:00:00.307 +04:00 [INF] Generating processing statistics
2025-06-14 19:30:46.254 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-14 19:30:46.867 +04:00 [INF] Hangfire SQL objects installed.
2025-06-14 19:30:46.880 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-14 19:30:47.362 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-14 19:30:47.389 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 19:30:47.598 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-14 19:30:47.600 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-14 19:30:47.787 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-14 19:30:47.789 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-14 19:30:47.793 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-14 19:30:47.794 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-14 19:30:47.795 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-14 19:30:47.796 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-14 19:30:47.827 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 19:30:47.830 +04:00 [INF] Hosting environment: Development
2025-06-14 19:30:47.832 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-14 19:30:48.324 +04:00 [INF] Server datapreprocessing-kevin11:20580:bcf1a2c1 successfully announced in 464.2374 ms
2025-06-14 19:30:48.336 +04:00 [INF] Server datapreprocessing-kevin11:20580:bcf1a2c1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-14 19:30:48.448 +04:00 [INF] 1 servers were removed due to timeout
2025-06-14 19:30:48.943 +04:00 [INF] Server datapreprocessing-kevin11:20580:bcf1a2c1 all the dispatchers started
2025-06-14 19:30:49.404 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-14 19:30:49.814 +04:00 [INF] Request GET / started with correlation ID 85d96691-d5a7-4b47-884c-442eda59a324
2025-06-14 19:30:50.081 +04:00 [INF] Generating processing statistics
2025-06-14 19:30:50.164 +04:00 [INF] Request GET / completed in 335ms with status 404 (Correlation ID: 85d96691-d5a7-4b47-884c-442eda59a324)
2025-06-14 19:30:50.174 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 770.512ms
2025-06-14 19:30:50.193 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-14 20:00:00.910 +04:00 [INF] Generating processing statistics
2025-06-14 21:21:26.885 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-14 21:21:27.067 +04:00 [INF] Hangfire SQL objects installed.
2025-06-14 21:21:27.083 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-14 21:21:27.700 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-14 21:21:27.729 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 21:21:28.045 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-14 21:21:28.047 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-14 21:21:28.103 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-14 21:21:28.117 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-14 21:21:28.119 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-14 21:21:28.121 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-14 21:21:28.123 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-14 21:21:28.125 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-14 21:21:28.177 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 21:21:28.182 +04:00 [INF] Hosting environment: Development
2025-06-14 21:21:28.186 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-14 21:21:28.295 +04:00 [INF] Server datapreprocessing-kevin11:34940:0d9c6505 successfully announced in 34.4792 ms
2025-06-14 21:21:28.337 +04:00 [INF] Server datapreprocessing-kevin11:34940:0d9c6505 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-14 21:21:28.455 +04:00 [INF] 1 servers were removed due to timeout
2025-06-14 21:21:28.695 +04:00 [INF] Server datapreprocessing-kevin11:34940:0d9c6505 all the dispatchers started
2025-06-14 21:21:30.029 +04:00 [INF] Generating processing statistics
2025-06-14 21:21:30.144 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-14 21:21:30.614 +04:00 [INF] Request GET / started with correlation ID f21f1c8d-12bb-4859-9d14-6e6c3be61151
2025-06-14 21:21:30.844 +04:00 [INF] Request GET / completed in 222ms with status 404 (Correlation ID: f21f1c8d-12bb-4859-9d14-6e6c3be61151)
2025-06-14 21:21:30.874 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 727.7313ms
2025-06-14 21:21:30.897 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-14 22:00:08.519 +04:00 [INF] Generating processing statistics
2025-06-14 22:22:41.563 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-14 22:22:41.714 +04:00 [INF] Hangfire SQL objects installed.
2025-06-14 22:22:41.723 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-14 22:22:42.236 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-14 22:22:42.264 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 22:22:42.590 +04:00 [INF] Now listening on: https://localhost:5001
2025-06-14 22:22:42.594 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-14 22:22:42.687 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-14 22:22:42.748 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-14 22:22:42.751 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-14 22:22:42.752 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-14 22:22:42.753 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-14 22:22:42.755 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-14 22:22:42.954 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 22:22:42.978 +04:00 [INF] Hosting environment: Development
2025-06-14 22:22:42.994 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-14 22:22:43.410 +04:00 [INF] Server datapreprocessing-kevin11:23396:d3399cae successfully announced in 434.7633 ms
2025-06-14 22:22:43.444 +04:00 [INF] Server datapreprocessing-kevin11:23396:d3399cae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-14 22:22:43.919 +04:00 [INF] Server datapreprocessing-kevin11:23396:d3399cae all the dispatchers started
2025-06-14 22:22:44.334 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5001/ - null null
2025-06-14 22:22:44.966 +04:00 [INF] Request GET / started with correlation ID ec2e4fad-2659-4061-a9a9-23597ac83017
2025-06-14 22:22:45.171 +04:00 [INF] Request GET / completed in 176ms with status 404 (Correlation ID: ec2e4fad-2659-4061-a9a9-23597ac83017)
2025-06-14 22:22:45.187 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5001/ - 404 0 null 854.2827ms
2025-06-14 22:22:45.198 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5001/, Response status code: 404
2025-06-14 22:28:57.734 +04:00 [INF] 1 servers were removed due to timeout
2025-06-14 23:00:08.660 +04:00 [INF] Generating processing statistics
