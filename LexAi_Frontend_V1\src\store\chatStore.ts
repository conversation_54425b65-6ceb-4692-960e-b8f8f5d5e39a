import { create } from 'zustand'
import type { ChatSession, ChatMessage } from '../types'

interface ChatStore {
  sessions: ChatSession[]
  currentSession: ChatSession | null
  isLoading: boolean
  
  // Actions
  createSession: (title?: string) => ChatSession
  selectSession: (sessionId: string) => void
  deleteSession: (sessionId: string) => void
  sendMessage: (content: string) => Promise<void>
  clearCurrentSession: () => void
  setLoading: (loading: boolean) => void
}

export const useChatStore = create<ChatStore>((set, get) => ({
  sessions: [],
  currentSession: null,
  isLoading: false,

  createSession: (title = 'Nouvelle conversation') => {
    const newSession: ChatSession = {
      id: Math.random().toString(36).substr(2, 9),
      title,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    set(state => ({
      sessions: [newSession, ...state.sessions],
      currentSession: newSession
    }))

    return newSession
  },

  selectSession: (sessionId: string) => {
    const { sessions } = get()
    const session = sessions.find(s => s.id === sessionId)
    if (session) {
      set({ currentSession: session })
    }
  },

  deleteSession: (sessionId: string) => {
    set(state => {
      const newSessions = state.sessions.filter(s => s.id !== sessionId)
      const newCurrentSession = state.currentSession?.id === sessionId 
        ? null 
        : state.currentSession

      return {
        sessions: newSessions,
        currentSession: newCurrentSession
      }
    })
  },

  sendMessage: async (content: string) => {
    const { currentSession } = get()
    if (!currentSession) return

    // Ajouter le message de l'utilisateur
    const userMessage: ChatMessage = {
      id: Math.random().toString(36).substr(2, 9),
      content,
      role: 'user',
      timestamp: new Date()
    }

    set(state => ({
      currentSession: state.currentSession ? {
        ...state.currentSession,
        messages: [...state.currentSession.messages, userMessage],
        updatedAt: new Date()
      } : null,
      isLoading: true
    }))

    try {
      // Simulation d'un appel API à l'IA
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Réponse simulée de l'IA
      const aiResponse = generateAIResponse(content)
      
      const assistantMessage: ChatMessage = {
        id: Math.random().toString(36).substr(2, 9),
        content: aiResponse,
        role: 'assistant',
        timestamp: new Date()
      }

      set(state => {
        const updatedSession = state.currentSession ? {
          ...state.currentSession,
          messages: [...state.currentSession.messages, assistantMessage],
          updatedAt: new Date()
        } : null

        return {
          currentSession: updatedSession,
          sessions: state.sessions.map(s => 
            s.id === state.currentSession?.id ? updatedSession! : s
          ),
          isLoading: false
        }
      })

    } catch (error) {
      set({ isLoading: false })
      console.error('Erreur lors de l\'envoi du message:', error)
    }
  },

  clearCurrentSession: () => {
    set({ currentSession: null })
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading })
  }
}))

// Fonction pour générer une réponse IA simulée
function generateAIResponse(userMessage: string): string {
  const responses = [
    "Je comprends votre question juridique. Voici quelques éléments de réponse basés sur la législation française...",
    "D'après les articles du Code civil, voici ce que je peux vous dire...",
    "Cette situation nécessite une analyse approfondie. Permettez-moi de vous expliquer les aspects juridiques pertinents...",
    "Selon la jurisprudence récente, voici les éléments à considérer...",
    "Je vais analyser votre demande et vous fournir une réponse détaillée basée sur le droit français..."
  ]
  
  return responses[Math.floor(Math.random() * responses.length)]
}
