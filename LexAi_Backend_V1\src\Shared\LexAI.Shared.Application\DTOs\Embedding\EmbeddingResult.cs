﻿
namespace LexAI.Shared.Application.DTOs.Embedding
{
    /// <summary>
    /// Résultat de génération d'embedding
    /// </summary>
    public class EmbeddingResult
    {
        public bool Success { get; set; }
        public float[]? Embedding { get; set; }
        public string? ErrorMessage { get; set; }
        public string Model { get; set; } = string.Empty;
        public int TokenCount { get; set; }
        public decimal EstimatedCost { get; set; }
        public int ProcessingTimeMs { get; set; }
        public bool UsedFallback { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }
}
