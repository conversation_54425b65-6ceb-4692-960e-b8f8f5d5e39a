using LexAI.DocumentAnalysis.Domain.Entities;

namespace LexAI.DocumentAnalysis.Domain.Repositories;

/// <summary>
/// Interface du repository pour les analyses de documents
/// </summary>
public interface IDocumentAnalysisRepository
{
    /// <summary>
    /// Obtient une analyse par ID et utilisateur
    /// </summary>
    Task<DocumentAnalysisResult?> GetByIdAsync(Guid id, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les analyses d'un utilisateur avec pagination et filtres
    /// </summary>
    Task<(IEnumerable<DocumentAnalysisResult> Items, int TotalCount)> GetUserAnalysesAsync(
        Guid userId,
        string? documentType = null,
        DocumentAnalysisStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int page = 1,
        int pageSize = 10,
        string? sortBy = null,
        bool sortDescending = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Ajoute une nouvelle analyse
    /// </summary>
    Task<DocumentAnalysisResult> AddAsync(DocumentAnalysisResult analysis, CancellationToken cancellationToken = default);

    /// <summary>
    /// Met à jour une analyse existante
    /// </summary>
    Task UpdateAsync(DocumentAnalysisResult analysis, CancellationToken cancellationToken = default);

    /// <summary>
    /// Supprime une analyse
    /// </summary>
    Task<bool> DeleteAsync(Guid id, Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si une analyse existe pour un utilisateur
    /// </summary>
    Task<bool> ExistsAsync(Guid id, Guid userId, CancellationToken cancellationToken = default);
}
