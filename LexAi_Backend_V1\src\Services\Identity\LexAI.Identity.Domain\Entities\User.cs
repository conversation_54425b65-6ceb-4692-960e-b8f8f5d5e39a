using LexAI.Shared.Domain.Common;
using LexAI.Shared.Domain.Enums;
using LexAI.Identity.Domain.ValueObjects;

namespace LexAI.Identity.Domain.Entities;

/// <summary>
/// Represents a user in the LexAI system
/// </summary>
public class User : AuditableEntity
{
    /// <summary>
    /// User's email address (unique identifier)
    /// </summary>
    public Email Email { get; private set; } = null!;

    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; private set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; private set; } = string.Empty;

    /// <summary>
    /// User's phone number
    /// </summary>
    public PhoneNumber? PhoneNumber { get; private set; }

    /// <summary>
    /// Hashed password
    /// </summary>
    public string PasswordHash { get; private set; } = string.Empty;

    /// <summary>
    /// User's role in the system
    /// </summary>
    public UserRole Role { get; private set; }

    /// <summary>
    /// Indicates if the user's email is verified
    /// </summary>
    public bool IsEmailVerified { get; private set; } = false;

    /// <summary>
    /// Indicates if the user account is active
    /// </summary>
    public bool IsActive { get; private set; } = true;

    /// <summary>
    /// Indicates if the user account is locked
    /// </summary>
    public bool IsLocked { get; private set; } = false;

    /// <summary>
    /// Date and time when the account was locked
    /// </summary>
    public DateTime? LockedAt { get; private set; }

    /// <summary>
    /// Number of failed login attempts
    /// </summary>
    public int FailedLoginAttempts { get; private set; } = 0;

    /// <summary>
    /// Date and time of the last login
    /// </summary>
    public DateTime? LastLoginAt { get; private set; }

    /// <summary>
    /// IP address of the last login
    /// </summary>
    public string? LastLoginIpAddress { get; private set; }

    /// <summary>
    /// User's preferred language
    /// </summary>
    public string PreferredLanguage { get; private set; } = "fr-FR";

    /// <summary>
    /// User's timezone
    /// </summary>
    public string TimeZone { get; private set; } = "Europe/Paris";

    /// <summary>
    /// User's profile picture URL
    /// </summary>
    public string? ProfilePictureUrl { get; private set; }

    /// <summary>
    /// Collection of refresh tokens for this user
    /// </summary>
    public virtual ICollection<RefreshToken> RefreshTokens { get; private set; } = new List<RefreshToken>();

    /// <summary>
    /// Collection of user permissions
    /// </summary>
    public virtual ICollection<UserPermission> Permissions { get; private set; } = new List<UserPermission>();

    /// <summary>
    /// Full name of the user
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Private constructor for EF Core
    /// </summary>
    private User() { }

    /// <summary>
    /// Creates a new user
    /// </summary>
    /// <param name="email">User's email address</param>
    /// <param name="firstName">User's first name</param>
    /// <param name="lastName">User's last name</param>
    /// <param name="role">User's role</param>
    /// <param name="createdBy">ID of the user creating this user</param>
    /// <returns>New user instance</returns>
    public static User Create(
        string email,
        string firstName,
        string lastName,
        UserRole role,
        string createdBy)
    {
        var user = new User
        {
            Email = Email.Create(email),
            FirstName = firstName?.Trim() ?? throw new ArgumentNullException(nameof(firstName)),
            LastName = lastName?.Trim() ?? throw new ArgumentNullException(nameof(lastName)),
            Role = role,
            CreatedBy = createdBy
        };

        user.AddAuditEntry("Created", createdBy, $"User created with role {role}");
        return user;
    }

    /// <summary>
    /// Sets the user's password
    /// </summary>
    /// <param name="password">Plain text password</param>
    /// <param name="updatedBy">ID of the user updating the password</param>
    public void SetPassword(string password, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be empty", nameof(password));

        if (password.Length < 8)
            throw new ArgumentException("Password must be at least 8 characters long", nameof(password));

        PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("PasswordChanged", updatedBy);
    }

    /// <summary>
    /// Verifies the provided password against the stored hash
    /// </summary>
    /// <param name="password">Password to verify</param>
    /// <returns>True if password is correct</returns>
    public bool VerifyPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(PasswordHash))
            return false;

        return BCrypt.Net.BCrypt.Verify(password, PasswordHash);
    }

    /// <summary>
    /// Records a successful login
    /// </summary>
    /// <param name="ipAddress">IP address of the login</param>
    public void RecordSuccessfulLogin(string? ipAddress = null)
    {
        LastLoginAt = DateTime.UtcNow;
        LastLoginIpAddress = ipAddress;
        FailedLoginAttempts = 0;

        if (IsLocked)
        {
            IsLocked = false;
            LockedAt = null;
        }

        AddAuditEntry("Login", Id.ToString(), $"Successful login from {ipAddress}");
    }

    /// <summary>
    /// Records a failed login attempt
    /// </summary>
    /// <param name="ipAddress">IP address of the failed attempt</param>
    public void RecordFailedLogin(string? ipAddress = null)
    {
        FailedLoginAttempts++;

        // Lock account after 5 failed attempts
        if (FailedLoginAttempts >= 5)
        {
            IsLocked = true;
            LockedAt = DateTime.UtcNow;
        }

        AddAuditEntry("FailedLogin", Id.ToString(), $"Failed login attempt from {ipAddress}. Attempts: {FailedLoginAttempts}");
    }

    /// <summary>
    /// Verifies the user's email
    /// </summary>
    /// <param name="verifiedBy">ID of the user performing the verification</param>
    public void VerifyEmail(string verifiedBy)
    {
        IsEmailVerified = true;
        UpdatedBy = verifiedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("EmailVerified", verifiedBy);
    }

    /// <summary>
    /// Activates the user account
    /// </summary>
    /// <param name="activatedBy">ID of the user performing the activation</param>
    public void Activate(string activatedBy)
    {
        IsActive = true;
        IsLocked = false;
        LockedAt = null;
        FailedLoginAttempts = 0;
        UpdatedBy = activatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("Activated", activatedBy);
    }

    /// <summary>
    /// Deactivates the user account
    /// </summary>
    /// <param name="deactivatedBy">ID of the user performing the deactivation</param>
    /// <param name="reason">Reason for deactivation</param>
    public void Deactivate(string deactivatedBy, string? reason = null)
    {
        IsActive = false;
        UpdatedBy = deactivatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("Deactivated", deactivatedBy, reason);
    }

    /// <summary>
    /// Updates user profile information
    /// </summary>
    /// <param name="firstName">New first name</param>
    /// <param name="lastName">New last name</param>
    /// <param name="phoneNumber">New phone number</param>
    /// <param name="updatedBy">ID of the user performing the update</param>
    public void UpdateProfile(
        string firstName,
        string lastName,
        string? phoneNumber,
        string updatedBy)
    {
        FirstName = firstName?.Trim() ?? throw new ArgumentNullException(nameof(firstName));
        LastName = lastName?.Trim() ?? throw new ArgumentNullException(nameof(lastName));

        if (!string.IsNullOrWhiteSpace(phoneNumber))
        {
            PhoneNumber = PhoneNumber.Create(phoneNumber);
        }

        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("ProfileUpdated", updatedBy);
    }

    /// <summary>
    /// Changes the user's role
    /// </summary>
    /// <param name="newRole">New role to assign</param>
    /// <param name="changedBy">ID of the user changing the role</param>
    public void ChangeRole(UserRole newRole, string changedBy)
    {
        var oldRole = Role;
        Role = newRole;
        UpdatedBy = changedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("RoleChanged", changedBy, $"Role changed from {oldRole} to {newRole}");
    }

    /// <summary>
    /// Updates user preferences (language and timezone)
    /// </summary>
    /// <param name="preferredLanguage">Preferred language</param>
    /// <param name="timeZone">Time zone</param>
    /// <param name="updatedBy">ID of the user performing the update</param>
    public void UpdatePreferences(string preferredLanguage, string timeZone, string updatedBy)
    {
        if (string.IsNullOrWhiteSpace(preferredLanguage))
            throw new ArgumentException("Preferred language cannot be empty", nameof(preferredLanguage));

        if (string.IsNullOrWhiteSpace(timeZone))
            throw new ArgumentException("Time zone cannot be empty", nameof(timeZone));

        PreferredLanguage = preferredLanguage.Trim();
        TimeZone = timeZone.Trim();
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;

        AddAuditEntry("PreferencesUpdated", updatedBy, $"Language: {preferredLanguage}, TimeZone: {timeZone}");
    }
}
