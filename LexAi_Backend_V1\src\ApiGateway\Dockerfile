# LexAI API Gateway Dockerfile

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy solution file
COPY ["LexAI.sln", "."]

# Copy project files
COPY ["src/ApiGateway/LexAI.ApiGateway.csproj", "src/ApiGateway/"]
COPY ["src/Shared/LexAI.Shared.Domain/LexAI.Shared.Domain.csproj", "src/Shared/LexAI.Shared.Domain/"]
COPY ["src/Shared/LexAI.Shared.Infrastructure/LexAI.Shared.Infrastructure.csproj", "src/Shared/LexAI.Shared.Infrastructure/"]

# Restore dependencies
RUN dotnet restore "src/ApiGateway/LexAI.ApiGateway.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/src/ApiGateway"
RUN dotnet build "LexAI.ApiGateway.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "LexAI.ApiGateway.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# Create non-root user for security
RUN groupadd -r lexai && useradd -r -g lexai lexai

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs && chown -R lexai:lexai /app/logs

# Set ownership
RUN chown -R lexai:lexai /app

# Switch to non-root user
USER lexai

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/gateway/ping || exit 1

# Set environment variables
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# Entry point
ENTRYPOINT ["dotnet", "LexAI.ApiGateway.dll"]
