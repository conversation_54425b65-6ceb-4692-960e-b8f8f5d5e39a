using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Queries;
using LexAI.DocumentAnalysis.Domain.Enums;
using LexAI.DocumentAnalysis.Infrastructure.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Handlers;

/// <summary>
/// Handler pour la query GetUserAnalysesQuery
/// </summary>
public class GetUserAnalysesQueryHandler : IRequestHandler<GetUserAnalysesQuery, DocumentAnalysisListResponseDto>
{
    private readonly DocumentAnalysisDbContext _context;
    private readonly ILogger<GetUserAnalysesQueryHandler> _logger;

    public GetUserAnalysesQueryHandler(
        DocumentAnalysisDbContext context,
        ILogger<GetUserAnalysesQueryHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<DocumentAnalysisListResponseDto> Handle(
        GetUserAnalysesQuery request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving analyses for user: {UserId}", request.UserId);

        try
        {
            var query = _context.DocumentAnalysisResults
                .Where(a => a.UserId == request.UserId);

            // Filtres
            if (!string.IsNullOrEmpty(request.DocumentType))
            {
                query = query.Where(a => a.DocumentType == request.DocumentType);
            }

            if (!string.IsNullOrEmpty(request.Status))
            {
                if (Enum.TryParse<DocumentAnalysisStatus>(request.Status, out var status))
                {
                    query = query.Where(a => a.Status == status);
                }
            }

            if (!string.IsNullOrEmpty(request.FromDate) && DateTime.TryParse(request.FromDate, out var fromDate))
            {
                query = query.Where(a => a.AnalyzedAt >= fromDate);
            }

            if (!string.IsNullOrEmpty(request.ToDate) && DateTime.TryParse(request.ToDate, out var toDate))
            {
                query = query.Where(a => a.AnalyzedAt <= toDate.AddDays(1));
            }

            // Tri
            query = !string.IsNullOrEmpty(request.SortBy) && request.SortBy.ToLower() == "analyzedat" && request.SortDescending
                ? query.OrderByDescending(a => a.AnalyzedAt)
                : query.OrderByDescending(a => a.AnalyzedAt);

            var totalCount = await query.CountAsync(cancellationToken);

            var skip = (request.Page - 1) * request.PageSize;

            var analyses = await query
                .Skip(skip)
                .Take(request.PageSize)
                .Include(a => a.Risks)
                .Include(a => a.Recommendations)
                .Select(a => new DocumentAnalysisSummaryDto
                {
                    Id = a.Id,
                    DocumentName = a.DocumentName,
                    DocumentType = a.DocumentType,
                    Status = a.Status.ToString(),
                    ConfidenceScore = a.ConfidenceScore,
                    RiskCount = a.Risks.Count,
                    RecommendationCount = a.Recommendations.Count,
                    OverallRiskLevel = a.OverallRiskLevel ?? "Unknown",
                    AnalyzedAt = a.AnalyzedAt,
                    ProcessingTimeMs = a.ProcessingTimeMs
                })
                .ToListAsync(cancellationToken);

            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);

            return new DocumentAnalysisListResponseDto
            {
                Items = analyses,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user analyses: {UserId}", request.UserId);
            throw;
        }
    }
}
