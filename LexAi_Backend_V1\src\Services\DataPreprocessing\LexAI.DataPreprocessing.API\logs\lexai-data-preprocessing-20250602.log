2025-06-02 21:55:47.193 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 21:55:47.343 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 21:55:47.356 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 21:55:47.841 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 21:55:47.869 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 21:55:48.211 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 21:55:48.213 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 21:55:48.278 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 21:55:48.282 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 21:55:48.283 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 21:55:48.285 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 21:55:48.286 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 21:55:48.332 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 21:55:48.360 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 21:55:48.362 +04:00 [INF] Hosting environment: Development
2025-06-02 21:55:48.364 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 21:55:48.434 +04:00 [INF] Server datapreprocessing-kevin11:50076:1d220394 successfully announced in 16.8899 ms
2025-06-02 21:55:48.478 +04:00 [INF] Server datapreprocessing-kevin11:50076:1d220394 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 21:55:48.604 +04:00 [INF] 1 servers were removed due to timeout
2025-06-02 21:55:48.882 +04:00 [INF] Server datapreprocessing-kevin11:50076:1d220394 all the dispatchers started
2025-06-02 21:55:49.859 +04:00 [INF] Generating processing statistics
2025-06-02 21:55:50.136 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-02 21:55:50.286 +04:00 [INF] Request GET / started with correlation ID 9343312b-d460-4dc8-87e3-876d8249c414
2025-06-02 21:55:50.453 +04:00 [INF] Request GET / completed in 158ms with status 404 (Correlation ID: 9343312b-d460-4dc8-87e3-876d8249c414)
2025-06-02 21:55:50.465 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 329.9525ms
2025-06-02 21:55:50.476 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-02 21:59:22.048 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - null null
2025-06-02 21:59:22.048 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - null null
2025-06-02 21:59:22.086 +04:00 [INF] Request OPTIONS /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 started with correlation ID 69861e89-eea7-4aac-b0b6-4bd4e698f1fe
2025-06-02 21:59:22.088 +04:00 [INF] Request OPTIONS /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 started with correlation ID 475bee43-7a2d-48b9-997e-56e74931c4de
2025-06-02 21:59:22.095 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:22.095 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:22.102 +04:00 [INF] Request OPTIONS /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 completed in 11ms with status 204 (Correlation ID: 69861e89-eea7-4aac-b0b6-4bd4e698f1fe)
2025-06-02 21:59:22.102 +04:00 [INF] Request OPTIONS /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 completed in 8ms with status 204 (Correlation ID: 475bee43-7a2d-48b9-997e-56e74931c4de)
2025-06-02 21:59:22.109 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - 204 null null 61.019ms
2025-06-02 21:59:22.114 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - 204 null null 66.6187ms
2025-06-02 21:59:22.114 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - application/json null
2025-06-02 21:59:22.142 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 started with correlation ID baf09776-0883-4a64-8d6f-1adc90ffde72
2025-06-02 21:59:22.145 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:22.223 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 3:53:32 PM', Current time (UTC): '6/2/2025 5:59:22 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 21:59:22.231 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 3:53:32 PM', Current time (UTC): '6/2/2025 5:59:22 PM'.
2025-06-02 21:59:22.236 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 3:53:32 PM', Current time (UTC): '6/2/2025 5:59:22 PM'.
2025-06-02 21:59:22.244 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 21:59:22.260 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 21:59:22.263 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 completed in 117ms with status 401 (Correlation ID: baf09776-0883-4a64-8d6f-1adc90ffde72)
2025-06-02 21:59:22.268 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - 401 0 null 154.5815ms
2025-06-02 21:59:22.276 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - application/json null
2025-06-02 21:59:22.286 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 started with correlation ID 026594c9-8f79-47ff-9cca-f155ff588e94
2025-06-02 21:59:22.289 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:22.293 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 3:53:32 PM', Current time (UTC): '6/2/2025 5:59:22 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 21:59:22.296 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 3:53:32 PM', Current time (UTC): '6/2/2025 5:59:22 PM'.
2025-06-02 21:59:22.300 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 3:53:32 PM', Current time (UTC): '6/2/2025 5:59:22 PM'.
2025-06-02 21:59:22.304 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 21:59:22.306 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 21:59:22.308 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 completed in 18ms with status 401 (Correlation ID: 026594c9-8f79-47ff-9cca-f155ff588e94)
2025-06-02 21:59:22.311 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - 401 0 null 34.6718ms
2025-06-02 21:59:23.913 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - application/json null
2025-06-02 21:59:23.916 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 started with correlation ID 433d2817-de8f-4385-b657-a69ccb582c32
2025-06-02 21:59:23.918 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:23.926 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 21:59:23.930 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:23.946 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 21:59:24.680 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 21:59:24.682 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 21:59:24.685 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 21:59:24.688 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 21:59:24.690 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 21:59:24.692 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 21:59:25.581 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 21:59:25.759 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 21:59:25.775 +04:00 [WRN] Document "997fadd5-ab5e-4755-af77-28ce2f8694d9" not found for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 21:59:25.790 +04:00 [INF] Executing NotFoundObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 21:59:25.868 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - application/json null
2025-06-02 21:59:25.875 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 1916.9956ms
2025-06-02 21:59:25.881 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 started with correlation ID 8bbc3f9d-8016-4af3-926d-ca25787cab67
2025-06-02 21:59:25.887 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:25.891 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:25.893 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 completed in 1974ms with status 404 (Correlation ID: 433d2817-de8f-4385-b657-a69ccb582c32)
2025-06-02 21:59:25.895 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 21:59:25.901 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:25.906 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 21:59:25.913 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - 404 null application/json; charset=utf-8 2000.0068ms
2025-06-02 21:59:25.934 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 21:59:25.940 +04:00 [WRN] Document "997fadd5-ab5e-4755-af77-28ce2f8694d9" not found for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 21:59:25.945 +04:00 [INF] Executing NotFoundObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-02 21:59:25.950 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 36.1906ms
2025-06-02 21:59:25.955 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:25.958 +04:00 [INF] Request GET /api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 completed in 67ms with status 404 (Correlation ID: 8bbc3f9d-8016-4af3-926d-ca25787cab67)
2025-06-02 21:59:25.966 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/997fadd5-ab5e-4755-af77-28ce2f8694d9 - 404 null application/json; charset=utf-8 97.9496ms
2025-06-02 21:59:27.572 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 21:59:27.572 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 21:59:27.586 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 49c89c31-bc85-4971-a453-dc23c26e3577
2025-06-02 21:59:27.593 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 7694227e-3906-4b87-a497-b8a90db29eae
2025-06-02 21:59:27.596 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:27.601 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:27.603 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 49c89c31-bc85-4971-a453-dc23c26e3577)
2025-06-02 21:59:27.604 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 7694227e-3906-4b87-a497-b8a90db29eae)
2025-06-02 21:59:27.606 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 34.0367ms
2025-06-02 21:59:27.609 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 37.1415ms
2025-06-02 21:59:27.624 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 21:59:27.633 +04:00 [INF] Request GET /api/documents started with correlation ID 1d419f72-d3f9-4c0a-ab0f-3d3f7557f6a4
2025-06-02 21:59:27.644 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:27.650 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 21:59:27.660 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:27.684 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 21:59:27.824 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 21:59:27.841 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 21:59:27.846 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 21:59:27.961 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 263.1343ms
2025-06-02 21:59:27.963 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 21:59:27.964 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:27.970 +04:00 [INF] Request GET /api/documents started with correlation ID f2b2e139-b0c5-4120-ab95-6f87eb5c05d9
2025-06-02 21:59:27.972 +04:00 [INF] Request GET /api/documents completed in 328ms with status 200 (Correlation ID: 1d419f72-d3f9-4c0a-ab0f-3d3f7557f6a4)
2025-06-02 21:59:27.974 +04:00 [INF] CORS policy execution successful.
2025-06-02 21:59:27.978 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 353.5853ms
2025-06-02 21:59:27.980 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 21:59:27.990 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:27.992 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 21:59:28.011 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 21:59:28.029 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 21:59:28.032 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 37.511ms
2025-06-02 21:59:28.039 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 21:59:28.043 +04:00 [INF] Request GET /api/documents completed in 68ms with status 200 (Correlation ID: f2b2e139-b0c5-4120-ab95-6f87eb5c05d9)
2025-06-02 21:59:28.047 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 83.9724ms
2025-06-02 22:00:05.464 +04:00 [INF] Generating processing statistics
2025-06-02 22:00:21.058 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 22:00:21.071 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 7ef24592-2e83-4f35-b57c-ec625454f51e
2025-06-02 22:00:21.079 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:00:21.083 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 4ms with status 204 (Correlation ID: 7ef24592-2e83-4f35-b57c-ec625454f51e)
2025-06-02 22:00:21.093 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 35.1091ms
2025-06-02 22:00:21.104 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryYxAe4mHtzD7zLZXA 474196
2025-06-02 22:00:21.139 +04:00 [INF] Request POST /api/documents/upload started with correlation ID a49d5fa0-8d81-4af4-ae26-88a349018f39
2025-06-02 22:00:21.146 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:00:21.150 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:00:21.157 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:00:21.182 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:00:21.342 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 22:00:21.478 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 22:00:21.507 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 22:00:21.542 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 22:00:32.167 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 6dc2cd77-f3d5-49be-a327-4f823b53b69e.pdf
2025-06-02 22:00:32.475 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 22:00:32.514 +04:00 [INF] Document added successfully: "5c681ca7-515e-4689-9638-c1b943954886"
2025-06-02 22:00:32.515 +04:00 [INF] Document uploaded successfully: "5c681ca7-515e-4689-9638-c1b943954886" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 22:00:44.519 +04:00 [INF] Background processing started for document "5c681ca7-515e-4689-9638-c1b943954886"
2025-06-02 22:00:47.245 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "5c681ca7-515e-4689-9638-c1b943954886", Processing started: true
2025-06-02 22:02:31.450 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 22:02:31.508 +04:00 [INF] Starting orchestrated processing for document "5c681ca7-515e-4689-9638-c1b943954886": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 22:02:33.587 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 132397.6838ms
2025-06-02 22:02:42.025 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:02:42.029 +04:00 [INF] Request POST /api/documents/upload completed in 140882ms with status 200 (Correlation ID: a49d5fa0-8d81-4af4-ae26-88a349018f39)
2025-06-02 22:02:42.042 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 140936.9591ms
2025-06-02 22:02:42.283 +04:00 [INF] Starting text extraction for document "5c681ca7-515e-4689-9638-c1b943954886": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 22:02:42.293 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-02 22:02:42.308 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-02 22:02:42.314 +04:00 [INF] Text extraction completed for document "5c681ca7-515e-4689-9638-c1b943954886". Length: 179, Confidence: 0.70, Time: 29ms
2025-06-02 22:02:51.584 +04:00 [INF] Starting classification for document "5c681ca7-515e-4689-9638-c1b943954886": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 22:02:51.588 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-02 22:02:51.607 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 22:02:51.610 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 22:02:54.376 +04:00 [INF] Received HTTP response headers after 2760.8365ms - 429
2025-06-02 22:02:54.381 +04:00 [INF] End processing HTTP request after 2774.5295ms - 429
2025-06-02 22:02:54.385 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-02 22:02:54.418 +04:00 [INF] Classification completed for document "5c681ca7-515e-4689-9638-c1b943954886". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 2831ms
2025-06-02 22:03:23.865 +04:00 [INF] Starting chunking for document "5c681ca7-515e-4689-9638-c1b943954886" with strategy "Semantic"
2025-06-02 22:03:23.869 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-02 22:03:23.874 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-02 22:03:23.882 +04:00 [INF] Chunking completed for document "5c681ca7-515e-4689-9638-c1b943954886". Chunks: 1, Average size: 179, Time: 15ms
2025-06-02 22:04:21.579 +04:00 [INF] Starting vectorization with model "OpenAISmall" for 1 chunks
2025-06-02 22:20:35.418 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 22:20:35.418 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 22:20:35.445 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 33e4ce99-8451-4a99-9666-ce1d74fc3270
2025-06-02 22:20:35.454 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 08dff681-6c09-4ab1-a260-5def9487fd00
2025-06-02 22:20:35.456 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:35.460 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:35.462 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 6ms with status 204 (Correlation ID: 33e4ce99-8451-4a99-9666-ce1d74fc3270)
2025-06-02 22:20:35.466 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 5ms with status 204 (Correlation ID: 08dff681-6c09-4ab1-a260-5def9487fd00)
2025-06-02 22:20:35.471 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 54.0003ms
2025-06-02 22:20:35.490 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 71.8467ms
2025-06-02 22:20:35.487 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 22:20:35.549 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID a62147df-6f11-45a0-8417-bbb9c26fbbc3
2025-06-02 22:20:35.552 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:35.554 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:20:35.556 +04:00 [INF] Request GET /api/documents/undefined completed in 3ms with status 404 (Correlation ID: a62147df-6f11-45a0-8417-bbb9c26fbbc3)
2025-06-02 22:20:35.559 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 71.7128ms
2025-06-02 22:20:35.566 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 22:20:35.567 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 22:20:35.573 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 36621631-7555-4c73-bd3e-5b7452f0bfc3
2025-06-02 22:20:35.578 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:35.580 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:20:35.581 +04:00 [INF] Request GET /api/documents/undefined completed in 3ms with status 404 (Correlation ID: 36621631-7555-4c73-bd3e-5b7452f0bfc3)
2025-06-02 22:20:35.586 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 19.1726ms
2025-06-02 22:20:35.591 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 22:20:37.778 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:20:37.779 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:20:37.789 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 7cea42b7-352f-4b03-b78f-d1825118d2be
2025-06-02 22:20:37.797 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID c4ec11de-6804-4133-95cf-60dc9549d7d9
2025-06-02 22:20:37.803 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:37.807 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:37.809 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 7cea42b7-352f-4b03-b78f-d1825118d2be)
2025-06-02 22:20:37.812 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: c4ec11de-6804-4133-95cf-60dc9549d7d9)
2025-06-02 22:20:37.823 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 45.037ms
2025-06-02 22:20:37.827 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:20:37.831 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 52.2088ms
2025-06-02 22:20:37.863 +04:00 [INF] Request GET /api/documents started with correlation ID c8df0a72-2f49-46ec-9dd6-38f44c610b41
2025-06-02 22:20:37.878 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:37.883 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:20:37.889 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:20:37.908 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:20:38.001 +04:00 [INF] Executed DbCommand (32ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:20:38.119 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:20:38.162 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 249.1219ms
2025-06-02 22:20:38.165 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:20:38.167 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:20:38.174 +04:00 [INF] Request GET /api/documents started with correlation ID aa7d2d60-f70b-4693-8bea-1fada0c617d7
2025-06-02 22:20:38.176 +04:00 [INF] Request GET /api/documents completed in 298ms with status 200 (Correlation ID: c8df0a72-2f49-46ec-9dd6-38f44c610b41)
2025-06-02 22:20:38.179 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:20:38.188 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 360.5478ms
2025-06-02 22:20:38.193 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:20:38.221 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:20:38.229 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:20:38.258 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:20:38.276 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:20:38.279 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 37.267ms
2025-06-02 22:20:38.281 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:20:38.285 +04:00 [INF] Request GET /api/documents completed in 105ms with status 200 (Correlation ID: aa7d2d60-f70b-4693-8bea-1fada0c617d7)
2025-06-02 22:20:38.292 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 127.2075ms
2025-06-02 22:21:41.367 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 22:21:41.390 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID c9b4ca35-2a59-4fc9-b0f5-4896ad7e5ca1
2025-06-02 22:21:41.399 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:21:41.407 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 7ms with status 204 (Correlation ID: c9b4ca35-2a59-4fc9-b0f5-4896ad7e5ca1)
2025-06-02 22:21:41.425 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 58.3171ms
2025-06-02 22:21:41.434 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryG84uDdMdSusvO8NO 34401
2025-06-02 22:21:41.501 +04:00 [INF] Request POST /api/documents/upload started with correlation ID 5ab41549-863c-47c2-85ca-dff188e57e8d
2025-06-02 22:21:41.505 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:21:41.508 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:21:41.526 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:21:41.544 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:21:41.566 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": Instructions.docx
2025-06-02 22:21:41.572 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 22:21:41.574 +04:00 [INF] Processing document upload: Instructions.docx by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 22:21:41.581 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 22:21:46.927 +04:00 [INF] File stored successfully: Instructions.docx -> 42ae8e53-b616-4e29-a0aa-57db6630dfc6.docx
2025-06-02 22:21:46.947 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 22:21:46.962 +04:00 [INF] Document added successfully: "922f5462-4ba5-415e-89e5-************"
2025-06-02 22:21:46.964 +04:00 [INF] Document uploaded successfully: "922f5462-4ba5-415e-89e5-************" - Instructions.docx
2025-06-02 22:21:53.644 +04:00 [INF] Background processing started for document "922f5462-4ba5-415e-89e5-************"
2025-06-02 22:22:16.071 +04:00 [INF] Starting orchestrated processing for document "922f5462-4ba5-415e-89e5-************": Instructions.docx
2025-06-02 22:22:16.094 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "922f5462-4ba5-415e-89e5-************", Processing started: true
2025-06-02 22:22:16.107 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 22:22:16.113 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 34561.5287ms
2025-06-02 22:22:16.118 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:22:16.119 +04:00 [INF] Starting text extraction for document "922f5462-4ba5-415e-89e5-************": Instructions.docx
2025-06-02 22:22:16.120 +04:00 [INF] Request POST /api/documents/upload completed in 34615ms with status 200 (Correlation ID: 5ab41549-863c-47c2-85ca-dff188e57e8d)
2025-06-02 22:22:16.124 +04:00 [INF] Extracting text from Instructions.docx (MIME: application/vnd.openxmlformats-officedocument.wordprocessingml.document)
2025-06-02 22:22:16.128 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 34694.6904ms
2025-06-02 22:22:16.133 +04:00 [INF] Text extraction successful for Instructions.docx. Length: 167
2025-06-02 22:22:16.142 +04:00 [INF] Text extraction completed for document "922f5462-4ba5-415e-89e5-************". Length: 167, Confidence: 0.70, Time: 18ms
2025-06-02 22:22:16.177 +04:00 [INF] Starting classification for document "922f5462-4ba5-415e-89e5-************": Instructions.docx
2025-06-02 22:22:16.178 +04:00 [INF] Classifying text into legal domains. Text length: 167
2025-06-02 22:22:16.188 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 22:22:16.192 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 22:22:18.823 +04:00 [INF] Received HTTP response headers after 2628.2584ms - 429
2025-06-02 22:22:18.825 +04:00 [INF] End processing HTTP request after 2637.1698ms - 429
2025-06-02 22:22:18.828 +04:00 [WRN] AI classification failed, using rule-based result: "Tax" (0.25)
2025-06-02 22:22:18.830 +04:00 [INF] Classification completed for document "922f5462-4ba5-415e-89e5-************". Domain: "Tax", Confidence: 0.25, Keywords: 11, Entities: 0, Time: 2652ms
2025-06-02 22:22:18.854 +04:00 [INF] Starting chunking for document "922f5462-4ba5-415e-89e5-************" with strategy "Semantic"
2025-06-02 22:22:18.856 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 167
2025-06-02 22:22:18.870 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-02 22:22:18.872 +04:00 [INF] Chunking completed for document "922f5462-4ba5-415e-89e5-************". Chunks: 1, Average size: 167, Time: 16ms
2025-06-02 22:23:09.299 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-02 22:23:09.311 +04:00 [ERR] Execution Worker is in the Failed state now due to an exception, execution will be retried no more than in 00:00:36
Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Util.NpgsqlTimeout.CheckAndApply(NpgsqlConnector connector)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlStorage.CreateAndOpenConnection()
   at Hangfire.PostgreSql.PostgreSqlJobQueue.<>c__DisplayClass13_0.<Dequeue_Transaction>b__0()
   at Hangfire.PostgreSql.Utils.Utils.TryExecute[T](Func`1 func, T& result, Func`2 swallowException, Nullable`1 tryCount)
   at Hangfire.PostgreSql.PostgreSqlJobQueue.Dequeue_Transaction(String[] queues, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlJobQueue.Dequeue(String[] queues, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlConnection.FetchNextJob(String[] queues, CancellationToken cancellationToken)
   at Hangfire.Server.Worker.Execute(BackgroundProcessContext context) in C:\projects\hangfire-525\src\Hangfire.Core\Server\Worker.cs:line 102
   at Hangfire.Server.BackgroundProcessDispatcherBuilder.ExecuteProcess(Guid executionId, Object state) in C:\projects\hangfire-525\src\Hangfire.Core\Server\BackgroundProcessDispatcherBuilder.cs:line 82
   at Hangfire.Processing.BackgroundExecution.Run(Action`2 callback, Object state) in C:\projects\hangfire-525\src\Hangfire.Core\Processing\BackgroundExecution.cs:line 118
2025-06-02 22:23:43.392 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-02 22:23:52.609 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-02 22:23:52.615 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-02 22:23:55.262 +04:00 [INF] Received HTTP response headers after 2639.5131ms - 200
2025-06-02 22:23:55.269 +04:00 [INF] End processing HTTP request after 2659.7575ms - 200
2025-06-02 22:24:09.451 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-02 22:24:09.459 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-02 22:24:12.606 +04:00 [INF] Received HTTP response headers after 3143.9467ms - 200
2025-06-02 22:24:12.611 +04:00 [INF] End processing HTTP request after 3159.748ms - 200
2025-06-02 22:25:53.402 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-02 22:30:10.887 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-02 22:30:37.760 +04:00 [ERR] Execution RecurringJobScheduler is in the Failed state now due to an exception, execution will be retried no more than in 00:00:04
Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlStorage.CreateAndOpenConnection()
   at Hangfire.PostgreSql.PostgreSqlConnection.AcquireLock(String resource, TimeSpan timeout)
   at Hangfire.Server.RecurringJobScheduler.UseConnectionDistributedLock[T](JobStorage storage, Func`2 action)
   at Hangfire.Server.RecurringJobScheduler.Execute(BackgroundProcessContext context) in C:\projects\hangfire-525\src\Hangfire.Core\Server\RecurringJobScheduler.cs:line 176
   at Hangfire.Server.BackgroundProcessDispatcherBuilder.ExecuteProcess(Guid executionId, Object state) in C:\projects\hangfire-525\src\Hangfire.Core\Server\BackgroundProcessDispatcherBuilder.cs:line 82
   at Hangfire.Processing.BackgroundExecution.Run(Action`2 callback, Object state) in C:\projects\hangfire-525\src\Hangfire.Core\Processing\BackgroundExecution.cs:line 118
2025-06-02 22:31:33.828 +04:00 [WRN] Server datapreprocessing-kevin11:50076:1d220394 encountered an exception while sending heartbeat
Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlStorage.CreateAndOpenConnection()
   at Hangfire.PostgreSql.PostgreSqlStorage.UseConnection[T](DbConnection dedicatedConnection, Func`2 func)
   at Hangfire.PostgreSql.PostgreSqlConnection.Heartbeat(String serverId)
   at Hangfire.Server.ServerHeartbeatProcess.Execute(BackgroundProcessContext context) in C:\projects\hangfire-525\src\Hangfire.Core\Server\ServerHeartbeatProcess.cs:line 50
2025-06-02 22:31:25.954 +04:00 [INF] Execution DelayedJobScheduler recovered from the Faulted state after 00:00:48.1988233 and is in the Running state now
2025-06-02 22:32:38.483 +04:00 [INF] Execution RecurringJobScheduler recovered from the Failed state after 00:00:26.4644436 and is in the Running state now
2025-06-02 22:32:48.017 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 41, Cost: $0.0000, Time: 571197ms
2025-06-02 22:32:53.922 +04:00 [INF] Server datapreprocessing-kevin11:50076:1d220394 is now able to continue sending heartbeats
2025-06-02 22:34:17.587 +04:00 [INF] Execution RecurringJobScheduler recovered from the Faulted state after 00:00:36.9670244 and is in the Running state now
2025-06-02 22:34:48.958 +04:00 [INF] Starting routing for 1 chunks
2025-06-02 22:34:48.966 +04:00 [INF] Routing completed for 1 chunks in 4ms
2025-06-02 22:34:49.286 +04:00 [INF] Created MongoDB collection: legal_other_chunks
2025-06-02 22:34:49.289 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_other_chunks
2025-06-02 22:34:49.394 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 102ms
2025-06-02 22:36:13.246 +04:00 [INF] Starting quality assessment for document "922f5462-4ba5-415e-89e5-************"
2025-06-02 22:36:13.277 +04:00 [INF] Quality assessment completed for document "922f5462-4ba5-415e-89e5-************". Score: 0.67, Passed: false, Issues: 2, Time: 24ms
2025-06-02 22:37:18.662 +04:00 [INF] Execution DelayedJobScheduler recovered from the Faulted state after 00:01:05.4166618 and is in the Running state now
2025-06-02 22:37:24.995 +04:00 [INF] Document processing completed successfully for "922f5462-4ba5-415e-89e5-************". Total time: 889312ms, Chunks: 1, Cost: $0.0000
2025-06-02 22:38:23.794 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:38:23.794 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:38:23.821 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 5e61d666-8487-4448-8c38-60e58c78d1c8
2025-06-02 22:38:24.096 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 34cbff9f-8285-447f-8b3b-96efd8b9fdd0
2025-06-02 22:38:24.099 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:38:24.106 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:38:24.149 +04:00 [INF] Request OPTIONS /api/documents completed in 50ms with status 204 (Correlation ID: 5e61d666-8487-4448-8c38-60e58c78d1c8)
2025-06-02 22:38:24.157 +04:00 [INF] Request OPTIONS /api/documents completed in 51ms with status 204 (Correlation ID: 34cbff9f-8285-447f-8b3b-96efd8b9fdd0)
2025-06-02 22:38:24.189 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 394.9765ms
2025-06-02 22:38:24.193 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:38:24.211 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 417.4036ms
2025-06-02 22:38:24.265 +04:00 [INF] Request GET /api/documents started with correlation ID 37be9fa0-716d-4c65-a632-995acd610ea8
2025-06-02 22:38:24.287 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:38:24.290 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:38:24.295 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:38:24.310 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:38:24.325 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:38:24.369 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:38:24.416 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 101.4215ms
2025-06-02 22:38:24.419 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:38:24.420 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:38:24.428 +04:00 [INF] Request GET /api/documents started with correlation ID 22111ab6-d022-4cc8-b134-1a0fdd8d51c0
2025-06-02 22:38:24.434 +04:00 [INF] Request GET /api/documents completed in 147ms with status 200 (Correlation ID: 37be9fa0-716d-4c65-a632-995acd610ea8)
2025-06-02 22:38:24.439 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:38:24.444 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 251.7526ms
2025-06-02 22:38:24.447 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:38:24.453 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:38:24.454 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:38:24.463 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:38:24.474 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:38:24.477 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 20.5031ms
2025-06-02 22:38:24.482 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:38:24.485 +04:00 [INF] Request GET /api/documents completed in 45ms with status 200 (Correlation ID: 22111ab6-d022-4cc8-b134-1a0fdd8d51c0)
2025-06-02 22:38:24.489 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 70.7426ms
2025-06-02 22:38:30.528 +04:00 [INF] Execution DelayedJobScheduler recovered from the Faulted state after 00:00:16.0826263 and is in the Running state now
2025-06-02 22:38:30.528 +04:00 [INF] Execution RecurringJobScheduler recovered from the Faulted state after 00:00:16.0829497 and is in the Running state now
2025-06-02 22:38:44.229 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - null null
2025-06-02 22:38:44.231 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - null null
2025-06-02 22:38:44.237 +04:00 [INF] Request OPTIONS /api/documents/922f5462-4ba5-415e-89e5-************ started with correlation ID a341ec97-c14c-444e-b55b-b4e2c52cbe4a
2025-06-02 22:38:44.242 +04:00 [INF] Request OPTIONS /api/documents/922f5462-4ba5-415e-89e5-************ started with correlation ID 26f0df32-89dc-4193-9ea1-8e291a4abf8f
2025-06-02 22:38:44.245 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:38:44.248 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:38:44.251 +04:00 [INF] Request OPTIONS /api/documents/922f5462-4ba5-415e-89e5-************ completed in 5ms with status 204 (Correlation ID: a341ec97-c14c-444e-b55b-b4e2c52cbe4a)
2025-06-02 22:38:44.253 +04:00 [INF] Request OPTIONS /api/documents/922f5462-4ba5-415e-89e5-************ completed in 4ms with status 204 (Correlation ID: 26f0df32-89dc-4193-9ea1-8e291a4abf8f)
2025-06-02 22:38:44.258 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - 204 null null 28.1729ms
2025-06-02 22:38:44.259 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - application/json null
2025-06-02 22:38:44.261 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - 204 null null 30.2573ms
2025-06-02 22:39:06.509 +04:00 [INF] Request GET /api/documents/922f5462-4ba5-415e-89e5-************ started with correlation ID 257d7962-395b-49a7-9658-d49559302d1c
2025-06-02 22:39:06.581 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - application/json null
2025-06-02 22:39:06.686 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:39:06.698 +04:00 [INF] Request GET /api/documents/922f5462-4ba5-415e-89e5-************ started with correlation ID eaf20de6-4aa3-4e3c-b839-a984e175bce0
2025-06-02 22:39:06.701 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:39:06.703 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:39:06.704 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:06.705 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:39:06.713 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:06.719 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:39:06.719 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:39:06.736 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 22:39:06.755 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 22:39:06.757 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 35.1931ms
2025-06-02 22:39:06.759 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:06.761 +04:00 [INF] Request GET /api/documents/922f5462-4ba5-415e-89e5-************ completed in 74ms with status 200 (Correlation ID: 257d7962-395b-49a7-9658-d49559302d1c)
2025-06-02 22:39:06.764 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - 200 null application/json; charset=utf-8 22504.1085ms
2025-06-02 22:39:06.812 +04:00 [INF] Executed DbCommand (60ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 22:39:06.825 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 22:39:06.832 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 102.869ms
2025-06-02 22:39:06.836 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:06.840 +04:00 [INF] Request GET /api/documents/922f5462-4ba5-415e-89e5-************ completed in 136ms with status 200 (Correlation ID: eaf20de6-4aa3-4e3c-b839-a984e175bce0)
2025-06-02 22:39:06.848 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/922f5462-4ba5-415e-89e5-************ - 200 null application/json; charset=utf-8 267.8171ms
2025-06-02 22:39:19.686 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:39:19.686 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:39:19.699 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 9327f6d7-55f2-4e11-9265-9ceb8f3e6a48
2025-06-02 22:39:19.703 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID eafec522-d7cd-4119-bfd4-4e2dcedd1011
2025-06-02 22:39:19.707 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:39:19.709 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:39:19.710 +04:00 [INF] Request OPTIONS /api/documents completed in 3ms with status 204 (Correlation ID: 9327f6d7-55f2-4e11-9265-9ceb8f3e6a48)
2025-06-02 22:39:19.711 +04:00 [INF] Request OPTIONS /api/documents completed in 2ms with status 204 (Correlation ID: eafec522-d7cd-4119-bfd4-4e2dcedd1011)
2025-06-02 22:39:19.715 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 29.8119ms
2025-06-02 22:39:19.718 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:39:19.721 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 35.0427ms
2025-06-02 22:39:19.762 +04:00 [INF] Request GET /api/documents started with correlation ID f42aeb3d-e6dc-43ab-a088-9a10678fb86c
2025-06-02 22:39:19.786 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:39:19.789 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:39:19.793 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:19.797 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:39:19.818 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:39:19.830 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:39:19.838 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 34.1725ms
2025-06-02 22:39:19.844 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:39:19.847 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:19.866 +04:00 [INF] Request GET /api/documents started with correlation ID 9f8e8265-5755-4df5-9a0c-0b72475bbaea
2025-06-02 22:39:19.870 +04:00 [INF] Request GET /api/documents completed in 84ms with status 200 (Correlation ID: f42aeb3d-e6dc-43ab-a088-9a10678fb86c)
2025-06-02 22:39:19.874 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:39:19.887 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 168.3695ms
2025-06-02 22:39:19.890 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:39:19.968 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:19.972 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:39:19.995 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:39:20.011 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:39:20.015 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 36.4726ms
2025-06-02 22:39:20.019 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:39:20.020 +04:00 [INF] Request GET /api/documents completed in 146ms with status 200 (Correlation ID: 9f8e8265-5755-4df5-9a0c-0b72475bbaea)
2025-06-02 22:39:20.023 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 179.5152ms
2025-06-02 22:47:29.409 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:47:29.428 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 7f533b58-c0ce-4cf1-8ab2-f6281980df63
2025-06-02 22:47:29.435 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:47:29.439 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 7f533b58-c0ce-4cf1-8ab2-f6281980df63)
2025-06-02 22:47:29.448 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 38.8177ms
2025-06-02 22:47:29.454 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:47:29.470 +04:00 [INF] Request GET /api/documents started with correlation ID 4c32c18b-19a2-4795-8c31-58d139f3dc18
2025-06-02 22:47:29.473 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:47:29.477 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:47:29.479 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:47:29.482 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:47:29.530 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:47:29.539 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:47:29.541 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 53.6814ms
2025-06-02 22:47:29.544 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:47:29.546 +04:00 [INF] Request GET /api/documents completed in 72ms with status 200 (Correlation ID: 4c32c18b-19a2-4795-8c31-58d139f3dc18)
2025-06-02 22:47:29.550 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 95.9982ms
2025-06-02 22:50:24.396 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 22:50:24.412 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 74f0454a-a4a6-4fe2-a4cc-628bed719933
2025-06-02 22:50:24.415 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:50:24.419 +04:00 [INF] Request OPTIONS /api/documents completed in 4ms with status 204 (Correlation ID: 74f0454a-a4a6-4fe2-a4cc-628bed719933)
2025-06-02 22:50:24.425 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 28.7295ms
2025-06-02 22:50:24.428 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 22:50:24.440 +04:00 [INF] Request GET /api/documents started with correlation ID b887a2f3-7ec0-4c5f-bb6b-d2cfeb196f82
2025-06-02 22:50:24.443 +04:00 [INF] CORS policy execution successful.
2025-06-02 22:50:24.445 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 22:50:24.447 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:50:24.449 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 22:50:24.470 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 22:50:24.482 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 22:50:24.489 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 35.2641ms
2025-06-02 22:50:24.494 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 22:50:24.496 +04:00 [INF] Request GET /api/documents completed in 53ms with status 200 (Correlation ID: b887a2f3-7ec0-4c5f-bb6b-d2cfeb196f82)
2025-06-02 22:50:24.502 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 73.8387ms
2025-06-02 23:00:10.202 +04:00 [INF] Generating processing statistics
2025-06-02 23:00:10.593 +04:00 [INF] Execution Worker recovered from the Failed state after 00:36:27.1922080 and is in the Running state now
2025-06-02 23:22:49.292 +04:00 [INF] Start installing Hangfire SQL objects...
2025-06-02 23:22:49.425 +04:00 [INF] Hangfire SQL objects installed.
2025-06-02 23:22:49.440 +04:00 [INF] Hangfire Dashboard configured successfully
2025-06-02 23:22:49.827 +04:00 [INF] LexAI Data Preprocessing Service started successfully
2025-06-02 23:22:49.858 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 23:22:50.144 +04:00 [INF] Now listening on: https://localhost:61113
2025-06-02 23:22:50.147 +04:00 [INF] Now listening on: http://localhost:61114
2025-06-02 23:22:50.347 +04:00 [INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: data_preprocessing_hangfire, Schema: hangfire'
2025-06-02 23:22:50.351 +04:00 [INF] Using the following options for PostgreSQL job storage:
2025-06-02 23:22:50.355 +04:00 [INF]     Queue poll interval: 00:00:10.
2025-06-02 23:22:50.358 +04:00 [INF]     Invisibility timeout: 00:30:00.
2025-06-02 23:22:50.359 +04:00 [INF]     Use sliding invisibility timeout: False.
2025-06-02 23:22:50.363 +04:00 [INF] Using the following options for Hangfire Server:
    Worker count: 12
    Listening queues: 'default', 'processing', 'vectorization'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-06-02 23:22:50.393 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 23:22:50.396 +04:00 [INF] Hosting environment: Development
2025-06-02 23:22:50.399 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DataPreprocessing\LexAI.DataPreprocessing.API
2025-06-02 23:22:50.428 +04:00 [INF] Server datapreprocessing-kevin11:47980:01ad1ec3 successfully announced in 14.5406 ms
2025-06-02 23:22:50.439 +04:00 [INF] Server datapreprocessing-kevin11:47980:01ad1ec3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-06-02 23:22:50.648 +04:00 [INF] Server datapreprocessing-kevin11:47980:01ad1ec3 all the dispatchers started
2025-06-02 23:22:51.660 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/ - null null
2025-06-02 23:22:52.033 +04:00 [INF] Request GET / started with correlation ID 6c90172e-218a-4406-a950-1ee6b6331109
2025-06-02 23:22:52.336 +04:00 [INF] Request GET / completed in 298ms with status 404 (Correlation ID: 6c90172e-218a-4406-a950-1ee6b6331109)
2025-06-02 23:22:52.375 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/ - 404 0 null 714.7096ms
2025-06-02 23:22:52.402 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/, Response status code: 404
2025-06-02 23:23:13.617 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 23:23:13.617 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 23:23:13.627 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID a4ec64c6-4435-485f-9886-6579822607c9
2025-06-02 23:23:13.627 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 98095524-3e37-41e3-8dda-5e72ca99864a
2025-06-02 23:23:13.648 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:13.649 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:13.654 +04:00 [INF] Request OPTIONS /api/documents completed in 9ms with status 204 (Correlation ID: 98095524-3e37-41e3-8dda-5e72ca99864a)
2025-06-02 23:23:13.654 +04:00 [INF] Request OPTIONS /api/documents completed in 11ms with status 204 (Correlation ID: a4ec64c6-4435-485f-9886-6579822607c9)
2025-06-02 23:23:13.660 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 43.009ms
2025-06-02 23:23:13.740 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:23:13.740 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 123.2619ms
2025-06-02 23:23:13.761 +04:00 [INF] Request GET /api/documents started with correlation ID 01faca19-068a-467b-8b8e-974a13b2ca84
2025-06-02 23:23:13.771 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:13.935 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 6:59:23 PM', Current time (UTC): '6/2/2025 7:23:13 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 23:23:13.954 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 6:59:23 PM', Current time (UTC): '6/2/2025 7:23:13 PM'.
2025-06-02 23:23:13.961 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 6:59:23 PM', Current time (UTC): '6/2/2025 7:23:13 PM'.
2025-06-02 23:23:13.972 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 23:23:13.981 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 23:23:13.985 +04:00 [INF] Request GET /api/documents completed in 214ms with status 401 (Correlation ID: 01faca19-068a-467b-8b8e-974a13b2ca84)
2025-06-02 23:23:13.991 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 257.7107ms
2025-06-02 23:23:13.996 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:23:14.018 +04:00 [INF] Request GET /api/documents started with correlation ID aadeafee-2216-4bd5-9339-5ff05c594a7f
2025-06-02 23:23:14.020 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:14.026 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 6:59:23 PM', Current time (UTC): '6/2/2025 7:23:14 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-02 23:23:14.031 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 6:59:23 PM', Current time (UTC): '6/2/2025 7:23:14 PM'.
2025-06-02 23:23:14.035 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/2/2025 6:59:23 PM', Current time (UTC): '6/2/2025 7:23:14 PM'.
2025-06-02 23:23:14.039 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-02 23:23:14.042 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-02 23:23:14.046 +04:00 [INF] Request GET /api/documents completed in 25ms with status 401 (Correlation ID: aadeafee-2216-4bd5-9339-5ff05c594a7f)
2025-06-02 23:23:14.055 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 401 0 null 58.85ms
2025-06-02 23:23:15.696 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:23:15.702 +04:00 [INF] Request GET /api/documents started with correlation ID b34db865-3d39-474f-9005-a32f53afbecd
2025-06-02 23:23:15.704 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:15.713 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:23:15.718 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:23:15.740 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:23:17.059 +04:00 [WRN] The property 'DocumentChunk.DomainRelevance' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 23:23:17.062 +04:00 [WRN] The property 'DocumentChunk.EmbeddingVector' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 23:23:17.067 +04:00 [WRN] The property 'DocumentChunk.Keywords' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 23:23:17.070 +04:00 [WRN] The property 'NamedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 23:23:17.072 +04:00 [WRN] The property 'ProcessingError.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 23:23:17.073 +04:00 [WRN] The property 'ProcessingStep.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-02 23:23:18.218 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 23:23:18.466 +04:00 [INF] Executed DbCommand (17ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 23:23:18.503 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 23:23:18.543 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:23:18.544 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 2797.5628ms
2025-06-02 23:23:18.549 +04:00 [INF] Request GET /api/documents started with correlation ID feeb1ed9-ac2d-4b8c-a794-fb6fc9ca14d7
2025-06-02 23:23:18.552 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:23:18.553 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:18.557 +04:00 [INF] Request GET /api/documents completed in 2852ms with status 200 (Correlation ID: b34db865-3d39-474f-9005-a32f53afbecd)
2025-06-02 23:23:18.558 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:23:18.566 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:23:18.569 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:23:18.573 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 2876.9832ms
2025-06-02 23:23:18.591 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 23:23:18.598 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 23:23:18.601 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 27.105ms
2025-06-02 23:23:18.605 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:23:18.607 +04:00 [INF] Request GET /api/documents completed in 53ms with status 200 (Correlation ID: feeb1ed9-ac2d-4b8c-a794-fb6fc9ca14d7)
2025-06-02 23:23:18.610 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 66.4999ms
2025-06-02 23:23:46.842 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - null null
2025-06-02 23:23:46.862 +04:00 [INF] Request OPTIONS /api/documents/upload started with correlation ID 0acce0e7-175e-4dd4-9075-29ed1c0e4a5b
2025-06-02 23:23:46.869 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:46.872 +04:00 [INF] Request OPTIONS /api/documents/upload completed in 3ms with status 204 (Correlation ID: 0acce0e7-175e-4dd4-9075-29ed1c0e4a5b)
2025-06-02 23:23:46.877 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/upload - 204 null null 34.3615ms
2025-06-02 23:23:46.885 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/api/documents/upload - multipart/form-data; boundary=----WebKitFormBoundaryuCZfNsSW07UPnq3U 474196
2025-06-02 23:23:46.892 +04:00 [INF] Request POST /api/documents/upload started with correlation ID cb993696-fccb-4e61-9d28-02d2a1ebd344
2025-06-02 23:23:46.976 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:23:46.978 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:23:46.981 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:23:46.993 +04:00 [INF] Route matched with {action = "UploadDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto]] UploadDocument(Microsoft.AspNetCore.Http.IFormFile, System.String) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:23:47.095 +04:00 [INF] Document upload request from user "62e0a975-363b-47ac-abf8-68fcf260cbb5": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 23:23:47.112 +04:00 [INF] Using default processing configuration for user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 23:23:47.124 +04:00 [INF] Processing document upload: INTRODUCTION AU DROIT DU TRAVAIL.pdf by user "62e0a975-363b-47ac-abf8-68fcf260cbb5"
2025-06-02 23:23:47.142 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 23:23:47.172 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__fileHash_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."FileHash" = @__fileHash_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 23:23:51.244 +04:00 [INF] File stored successfully: INTRODUCTION AU DROIT DU TRAVAIL.pdf -> 45097508-b988-4a55-bb0e-dc50970a5a53.pdf
2025-06-02 23:23:51.518 +04:00 [INF] Executed DbCommand (19ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = Double), @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = DateTime), @p6='?', @p7='?', @p8='?' (DbType = Decimal), @p9='?', @p10='?', @p11='?', @p12='?' (DbType = Int64), @p13='?' (DbType = Boolean), @p14='?' (DbType = Boolean), @p15='?' (DbType = Object), @p16='?', @p17='?' (DbType = Object), @p18='?', @p19='?', @p20='?' (DbType = Int32), @p21='?' (DbType = DateTime), @p22='?', @p23='?', @p24='?', @p25='?' (DbType = Guid), @p26='?', @p27='?' (DbType = DateTime), @p28='?' (DbType = Guid), @p29='?', @p30='?' (DbType = Boolean), @p31='?' (DbType = Object), @p32='?' (DbType = DateTime), @p33='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Documents" ("Id", "ChunkCount", "ClassificationConfidence", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "DetectedDomain", "EstimatedCost", "ExtractedText", "FileHash", "FileName", "FileSize", "IsDeleted", "IsVectorized", "Metadata", "MimeType", "ProcessingTime", "Status", "StoragePath", "TotalTokens", "UpdatedAt", "UpdatedBy", "VectorCollection", "VectorDatabase")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24)
RETURNING xmin;
INSERT INTO "ProcessingSteps" ("Id", "AgentName", "CompletedAt", "DocumentId", "ErrorMessage", "IsSuccessful", "Metadata", "StartedAt", "StepName")
VALUES (@p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33);
2025-06-02 23:23:51.559 +04:00 [INF] Document added successfully: "6c144ab8-28ee-4a6b-a1a5-014868c00449"
2025-06-02 23:23:51.562 +04:00 [INF] Document uploaded successfully: "6c144ab8-28ee-4a6b-a1a5-014868c00449" - INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 23:23:51.566 +04:00 [INF] Background processing started for document "6c144ab8-28ee-4a6b-a1a5-014868c00449"
2025-06-02 23:24:00.426 +04:00 [INF] Starting orchestrated processing for document "6c144ab8-28ee-4a6b-a1a5-014868c00449": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 23:24:00.431 +04:00 [INF] Document uploaded successfully for user "62e0a975-363b-47ac-abf8-68fcf260cbb5". Document: "6c144ab8-28ee-4a6b-a1a5-014868c00449", Processing started: true
2025-06-02 23:24:00.437 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto'.
2025-06-02 23:24:00.442 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API) in 13445.9659ms
2025-06-02 23:24:00.444 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.UploadDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:00.447 +04:00 [INF] Request POST /api/documents/upload completed in 13549ms with status 200 (Correlation ID: cb993696-fccb-4e61-9d28-02d2a1ebd344)
2025-06-02 23:24:00.451 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/api/documents/upload - 200 null application/json; charset=utf-8 13566.0938ms
2025-06-02 23:24:00.626 +04:00 [INF] Starting text extraction for document "6c144ab8-28ee-4a6b-a1a5-014868c00449": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 23:24:00.640 +04:00 [INF] Extracting text from INTRODUCTION AU DROIT DU TRAVAIL.pdf (MIME: application/pdf)
2025-06-02 23:24:00.669 +04:00 [INF] Text extraction successful for INTRODUCTION AU DROIT DU TRAVAIL.pdf. Length: 179
2025-06-02 23:24:00.677 +04:00 [INF] Text extraction completed for document "6c144ab8-28ee-4a6b-a1a5-014868c00449". Length: 179, Confidence: 0.70, Time: 48ms
2025-06-02 23:24:00.775 +04:00 [INF] Starting classification for document "6c144ab8-28ee-4a6b-a1a5-014868c00449": INTRODUCTION AU DROIT DU TRAVAIL.pdf
2025-06-02 23:24:00.779 +04:00 [INF] Classifying text into legal domains. Text length: 179
2025-06-02 23:24:00.795 +04:00 [INF] Start processing HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 23:24:00.799 +04:00 [INF] Sending HTTP request POST https://api.openai.com/v1/chat/completions
2025-06-02 23:24:03.354 +04:00 [INF] Received HTTP response headers after 2550.1974ms - 429
2025-06-02 23:24:03.359 +04:00 [INF] End processing HTTP request after 2564.9706ms - 429
2025-06-02 23:24:03.369 +04:00 [WRN] AI classification failed, using rule-based result: "Labor" (0.33)
2025-06-02 23:24:03.397 +04:00 [INF] Classification completed for document "6c144ab8-28ee-4a6b-a1a5-014868c00449". Domain: "Labor", Confidence: 0.33, Keywords: 10, Entities: 0, Time: 2619ms
2025-06-02 23:24:03.477 +04:00 [INF] Starting chunking for document "6c144ab8-28ee-4a6b-a1a5-014868c00449" with strategy "Semantic"
2025-06-02 23:24:03.482 +04:00 [INF] Chunking text with strategy "Semantic". Text length: 179
2025-06-02 23:24:03.486 +04:00 [INF] Created 1 chunks using "Semantic" strategy
2025-06-02 23:24:03.497 +04:00 [INF] Chunking completed for document "6c144ab8-28ee-4a6b-a1a5-014868c00449". Chunks: 1, Average size: 179, Time: 17ms
2025-06-02 23:24:03.531 +04:00 [INF] Starting vectorization with model "HuggingFace" for 1 chunks
2025-06-02 23:24:03.538 +04:00 [INF] Generating embeddings for 1 texts using local model
2025-06-02 23:24:03.540 +04:00 [INF] Start processing HTTP request GET http://localhost:8000/health
2025-06-02 23:24:03.542 +04:00 [INF] Sending HTTP request GET http://localhost:8000/health
2025-06-02 23:24:05.738 +04:00 [INF] Received HTTP response headers after 2194.7378ms - 200
2025-06-02 23:24:05.743 +04:00 [INF] End processing HTTP request after 2202.4437ms - 200
2025-06-02 23:24:05.751 +04:00 [INF] Start processing HTTP request POST http://localhost:8000/embeddings
2025-06-02 23:24:05.753 +04:00 [INF] Sending HTTP request POST http://localhost:8000/embeddings
2025-06-02 23:24:08.724 +04:00 [INF] Received HTTP response headers after 2966.1147ms - 200
2025-06-02 23:24:08.728 +04:00 [INF] End processing HTTP request after 2976.9694ms - 200
2025-06-02 23:24:08.767 +04:00 [INF] Generated 1/1 embeddings successfully
2025-06-02 23:24:08.774 +04:00 [INF] Vectorization completed. Successful: 1/1, Tokens: 44, Cost: $0.0000, Time: 5240ms
2025-06-02 23:24:08.814 +04:00 [INF] Starting routing for 1 chunks
2025-06-02 23:24:08.823 +04:00 [INF] Routing completed for 1 chunks in 6ms
2025-06-02 23:24:09.219 +04:00 [INF] Storing 1 vectors in "MongoDB"/legal_labor_chunks
2025-06-02 23:24:09.521 +04:00 [INF] Vector storage completed. Stored: 1/1, Time: 295ms
2025-06-02 23:24:09.540 +04:00 [INF] Starting quality assessment for document "6c144ab8-28ee-4a6b-a1a5-014868c00449"
2025-06-02 23:24:09.580 +04:00 [INF] Quality assessment completed for document "6c144ab8-28ee-4a6b-a1a5-014868c00449". Score: 0.67, Passed: false, Issues: 2, Time: 33ms
2025-06-02 23:24:09.620 +04:00 [INF] Document processing completed successfully for "6c144ab8-28ee-4a6b-a1a5-014868c00449". Total time: 9156ms, Chunks: 1, Cost: $0.0000
2025-06-02 23:24:21.066 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - null null
2025-06-02 23:24:21.068 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - null null
2025-06-02 23:24:21.091 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID 177ce9ce-ef87-47c5-babe-5920656f0974
2025-06-02 23:24:21.149 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID c4dd6345-1e07-4221-a8b7-431075573f79
2025-06-02 23:24:21.158 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:21.166 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:21.171 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 14ms with status 204 (Correlation ID: 177ce9ce-ef87-47c5-babe-5920656f0974)
2025-06-02 23:24:21.174 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 8ms with status 204 (Correlation ID: c4dd6345-1e07-4221-a8b7-431075573f79)
2025-06-02 23:24:21.190 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 204 null null 123.7729ms
2025-06-02 23:24:21.196 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - application/json null
2025-06-02 23:24:21.198 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 204 null null 130.243ms
2025-06-02 23:24:21.220 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID 0f693d0a-5c87-4c55-8210-1ab5d4c67899
2025-06-02 23:24:21.229 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:21.232 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:24:21.234 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:21.248 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:24:21.317 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 23:24:21.359 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 23:24:21.423 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 23:24:21.440 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 186.8897ms
2025-06-02 23:24:21.443 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - application/json null
2025-06-02 23:24:21.444 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:21.449 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID a2799315-c264-4b86-872f-52057814f13a
2025-06-02 23:24:21.450 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 221ms with status 200 (Correlation ID: 0f693d0a-5c87-4c55-8210-1ab5d4c67899)
2025-06-02 23:24:21.452 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:21.457 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 200 null application/json; charset=utf-8 260.9785ms
2025-06-02 23:24:21.460 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:24:21.473 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:21.478 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:24:21.488 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 23:24:21.498 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 23:24:21.501 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 17.3168ms
2025-06-02 23:24:21.503 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:21.505 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 52ms with status 200 (Correlation ID: a2799315-c264-4b86-872f-52057814f13a)
2025-06-02 23:24:21.509 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 200 null application/json; charset=utf-8 66.5748ms
2025-06-02 23:24:47.280 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - null null
2025-06-02 23:24:47.320 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - null null
2025-06-02 23:24:47.324 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID c6cee783-4a74-4821-8349-cd4447a2b0ae
2025-06-02 23:24:47.330 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID b9a117d5-bb96-45aa-b860-f8671deb360d
2025-06-02 23:24:47.333 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:47.337 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:47.339 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 5ms with status 204 (Correlation ID: c6cee783-4a74-4821-8349-cd4447a2b0ae)
2025-06-02 23:24:47.340 +04:00 [INF] Request OPTIONS /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 3ms with status 204 (Correlation ID: b9a117d5-bb96-45aa-b860-f8671deb360d)
2025-06-02 23:24:47.345 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 204 null null 65.467ms
2025-06-02 23:24:47.398 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 204 null null 77.0389ms
2025-06-02 23:24:47.395 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - application/json null
2025-06-02 23:24:47.427 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID 44c95abd-fbad-42a3-896a-3e15d076f6b8
2025-06-02 23:24:47.430 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:47.432 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:24:47.434 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:47.437 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:24:47.447 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 23:24:47.453 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 23:24:47.456 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 15.7576ms
2025-06-02 23:24:47.459 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - application/json null
2025-06-02 23:24:47.461 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:47.467 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 started with correlation ID 42b7c9c1-a124-4ce9-9631-f121fd96acdf
2025-06-02 23:24:47.469 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 39ms with status 200 (Correlation ID: 44c95abd-fbad-42a3-896a-3e15d076f6b8)
2025-06-02 23:24:47.472 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:24:47.477 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 200 null application/json; charset=utf-8 82.5959ms
2025-06-02 23:24:47.480 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:24:47.488 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:47.491 +04:00 [INF] Route matched with {action = "GetDocument", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]] GetDocument(System.Guid) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:24:47.502 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."Id" = @__id_0
    LIMIT 1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."Id", d0."Id", p."Id"
2025-06-02 23:24:47.510 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto'.
2025-06-02 23:24:47.512 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API) in 15.0262ms
2025-06-02 23:24:47.515 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetDocument (LexAI.DataPreprocessing.API)'
2025-06-02 23:24:47.516 +04:00 [INF] Request GET /api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 completed in 44ms with status 200 (Correlation ID: 42b7c9c1-a124-4ce9-9631-f121fd96acdf)
2025-06-02 23:24:47.520 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/6c144ab8-28ee-4a6b-a1a5-014868c00449 - 200 null application/json; charset=utf-8 60.7579ms
2025-06-02 23:27:01.738 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 23:27:01.780 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 23:27:01.788 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID 21860047-1371-4f74-bb17-8595132a7f1c
2025-06-02 23:27:01.795 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID d1fbd882-41a7-49b5-b8ed-657ed0621b25
2025-06-02 23:27:01.799 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:27:01.802 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:27:01.805 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: 21860047-1371-4f74-bb17-8595132a7f1c)
2025-06-02 23:27:01.807 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: d1fbd882-41a7-49b5-b8ed-657ed0621b25)
2025-06-02 23:27:01.812 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 73.2822ms
2025-06-02 23:27:01.824 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 44.552ms
2025-06-02 23:27:01.823 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:27:01.850 +04:00 [INF] Request GET /api/documents started with correlation ID 8908d0f1-0b26-4337-85c9-04bca462166e
2025-06-02 23:27:01.853 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:27:01.855 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:27:01.857 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:27:01.859 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:27:01.871 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 23:27:01.902 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 23:27:01.906 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 44.7664ms
2025-06-02 23:27:01.908 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:27:01.909 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:27:01.912 +04:00 [INF] Request GET /api/documents started with correlation ID 30bf2146-1fed-46dd-8527-dffd7bffb0b5
2025-06-02 23:27:01.913 +04:00 [INF] Request GET /api/documents completed in 60ms with status 200 (Correlation ID: 8908d0f1-0b26-4337-85c9-04bca462166e)
2025-06-02 23:27:01.915 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:27:01.919 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 96.8632ms
2025-06-02 23:27:01.923 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:27:01.929 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:27:01.931 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:27:01.940 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 23:27:01.947 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 23:27:01.951 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 17.5844ms
2025-06-02 23:27:01.954 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:27:01.956 +04:00 [INF] Request GET /api/documents completed in 40ms with status 200 (Correlation ID: 30bf2146-1fed-46dd-8527-dffd7bffb0b5)
2025-06-02 23:27:01.961 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 53.0089ms
2025-06-02 23:27:50.586 +04:00 [INF] 1 servers were removed due to timeout
2025-06-02 23:29:02.164 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire - null null
2025-06-02 23:29:02.443 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire - 200 null text/html 278.9543ms
2025-06-02 23:29:02.463 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css-dark18140494117768 - null null
2025-06-02 23:29:02.504 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/js181401705399491 - null null
2025-06-02 23:29:02.461 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/css18140713211274 - null null
2025-06-02 23:29:02.512 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:02.511 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:02.689 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css-dark18140494117768 - 200 null text/css 226.368ms
2025-06-02 23:29:02.696 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/css18140713211274 - 200 null text/css 235.5696ms
2025-06-02 23:29:02.721 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/js181401705399491 - 200 null application/javascript 251.1105ms
2025-06-02 23:29:02.724 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 212.544ms
2025-06-02 23:29:02.985 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 472.9271ms
2025-06-02 23:29:04.821 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:04.870 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 48.6532ms
2025-06-02 23:29:06.884 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:06.902 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.8041ms
2025-06-02 23:29:08.916 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:08.929 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.159ms
2025-06-02 23:29:10.938 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:10.960 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.0444ms
2025-06-02 23:29:11.585 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/jobs/enqueued - null null
2025-06-02 23:29:11.641 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/jobs/enqueued - 200 null text/html 55.4927ms
2025-06-02 23:29:11.713 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:11.713 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:11.725 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 11.3662ms
2025-06-02 23:29:11.744 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 30.7702ms
2025-06-02 23:29:13.743 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-02 23:29:13.766 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.0619ms
2025-06-02 23:29:13.832 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/retries - null null
2025-06-02 23:29:13.869 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/retries - 200 null text/html 37.1524ms
2025-06-02 23:29:13.908 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:13.908 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:13.920 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 11.62ms
2025-06-02 23:29:13.923 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 14.3146ms
2025-06-02 23:29:15.932 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:29:15.950 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4833ms
2025-06-02 23:29:17.003 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/servers - null null
2025-06-02 23:29:17.106 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/servers - 200 null text/html 102.8523ms
2025-06-02 23:29:17.169 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:17.169 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:17.179 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 9.3837ms
2025-06-02 23:29:17.181 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 12.0307ms
2025-06-02 23:29:19.203 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:29:19.220 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5562ms
2025-06-02 23:29:21.235 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:29:21.248 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.222ms
2025-06-02 23:29:21.817 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/jobs/enqueued - null null
2025-06-02 23:29:21.838 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/jobs/enqueued - 200 null text/html 20.7986ms
2025-06-02 23:29:21.879 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:21.879 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:21.892 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 13.4756ms
2025-06-02 23:29:21.894 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 15.5186ms
2025-06-02 23:29:23.900 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-06-02 23:29:23.914 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9049ms
2025-06-02 23:29:25.846 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/ - null null
2025-06-02 23:29:25.869 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/ - 200 null text/html 22.864ms
2025-06-02 23:29:25.939 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:25.939 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:25.965 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 25.7743ms
2025-06-02 23:29:25.968 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 28.2719ms
2025-06-02 23:29:28.018 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:28.029 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4079ms
2025-06-02 23:29:30.047 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:30.062 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0749ms
2025-06-02 23:29:30.617 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/?period=week - null null
2025-06-02 23:29:30.643 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/?period=week - 200 null text/html 26.0386ms
2025-06-02 23:29:30.681 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:30.681 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:30.693 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 12.1473ms
2025-06-02 23:29:30.715 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 33.573ms
2025-06-02 23:29:32.753 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:32.773 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.8076ms
2025-06-02 23:29:34.785 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:34.798 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6457ms
2025-06-02 23:29:36.820 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:36.840 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.2447ms
2025-06-02 23:29:38.856 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:38.873 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.515ms
2025-06-02 23:29:40.886 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:40.904 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4587ms
2025-06-02 23:29:42.917 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:42.935 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.1093ms
2025-06-02 23:29:44.953 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:44.973 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.9764ms
2025-06-02 23:29:46.985 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:47.008 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.7089ms
2025-06-02 23:29:49.022 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:49.035 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0578ms
2025-06-02 23:29:51.051 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:51.065 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8782ms
2025-06-02 23:29:53.084 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:53.096 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.9509ms
2025-06-02 23:29:55.121 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-06-02 23:29:55.139 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.7561ms
2025-06-02 23:29:55.192 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/hangfire/recurring - null null
2025-06-02 23:29:55.295 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/hangfire/recurring - 200 null text/html 102.6333ms
2025-06-02 23:29:55.345 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 23:29:55.345 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/_vs/browserLink - null null
2025-06-02 23:29:55.352 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_framework/aspnetcore-browser-refresh.js - 200 16539 application/javascript; charset=utf-8 6.9604ms
2025-06-02 23:29:55.370 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.337ms
2025-06-02 23:29:57.385 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:29:57.405 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.3944ms
2025-06-02 23:29:59.411 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:29:59.425 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.0842ms
2025-06-02 23:30:01.436 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:01.455 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.7154ms
2025-06-02 23:30:03.466 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:03.490 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.7939ms
2025-06-02 23:30:05.503 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:05.521 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4708ms
2025-06-02 23:30:07.534 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:07.549 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.8127ms
2025-06-02 23:30:09.564 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:09.580 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.2449ms
2025-06-02 23:30:11.594 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:11.607 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.9178ms
2025-06-02 23:30:13.615 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:13.626 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0184ms
2025-06-02 23:30:15.633 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:15.649 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.218ms
2025-06-02 23:30:18.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:18.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.2988ms
2025-06-02 23:30:21.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:21.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9109ms
2025-06-02 23:30:24.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:24.350 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 28.9554ms
2025-06-02 23:30:27.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:27.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.7837ms
2025-06-02 23:30:30.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:30.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.3202ms
2025-06-02 23:30:33.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:33.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5451ms
2025-06-02 23:30:36.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:36.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2628ms
2025-06-02 23:30:39.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:39.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7546ms
2025-06-02 23:30:42.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:42.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.4501ms
2025-06-02 23:30:45.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:45.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.8055ms
2025-06-02 23:30:48.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:48.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.9281ms
2025-06-02 23:30:51.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:51.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.3195ms
2025-06-02 23:30:54.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:54.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9034ms
2025-06-02 23:30:57.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:30:57.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5407ms
2025-06-02 23:31:00.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:00.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.0705ms
2025-06-02 23:31:03.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:03.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.0652ms
2025-06-02 23:31:06.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:06.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9073ms
2025-06-02 23:31:09.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:09.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.9567ms
2025-06-02 23:31:12.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:12.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6827ms
2025-06-02 23:31:15.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:15.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.3995ms
2025-06-02 23:31:18.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:18.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2663ms
2025-06-02 23:31:21.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:21.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5697ms
2025-06-02 23:31:24.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:24.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.6078ms
2025-06-02 23:31:27.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:27.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.6615ms
2025-06-02 23:31:30.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:30.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.4566ms
2025-06-02 23:31:33.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:33.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.6678ms
2025-06-02 23:31:36.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:36.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.931ms
2025-06-02 23:31:39.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:39.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.9334ms
2025-06-02 23:31:42.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:42.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.4042ms
2025-06-02 23:31:45.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:45.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7391ms
2025-06-02 23:31:48.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:48.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.4733ms
2025-06-02 23:31:51.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:51.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.3991ms
2025-06-02 23:31:54.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:54.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.43ms
2025-06-02 23:31:57.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:31:57.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.0244ms
2025-06-02 23:32:00.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:00.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8874ms
2025-06-02 23:32:03.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:03.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4131ms
2025-06-02 23:32:06.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:06.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.115ms
2025-06-02 23:32:09.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:09.348 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.4789ms
2025-06-02 23:32:12.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:12.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.444ms
2025-06-02 23:32:15.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:15.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1584ms
2025-06-02 23:32:18.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:18.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.6302ms
2025-06-02 23:32:21.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:21.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.7935ms
2025-06-02 23:32:24.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:24.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.2923ms
2025-06-02 23:32:27.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:27.344 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.6771ms
2025-06-02 23:32:30.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:30.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.586ms
2025-06-02 23:32:33.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:33.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.2189ms
2025-06-02 23:32:36.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:36.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.7116ms
2025-06-02 23:32:39.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:39.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7676ms
2025-06-02 23:32:42.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:42.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2909ms
2025-06-02 23:32:45.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:45.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4922ms
2025-06-02 23:32:48.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:48.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.447ms
2025-06-02 23:32:51.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:51.343 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 28.8516ms
2025-06-02 23:32:54.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:54.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.229ms
2025-06-02 23:32:57.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:32:57.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.241ms
2025-06-02 23:33:00.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:00.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.8271ms
2025-06-02 23:33:03.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:03.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3155ms
2025-06-02 23:33:06.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:06.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.7707ms
2025-06-02 23:33:09.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:09.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.6512ms
2025-06-02 23:33:12.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:12.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.1856ms
2025-06-02 23:33:15.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:15.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.9755ms
2025-06-02 23:33:18.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:18.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.1981ms
2025-06-02 23:33:21.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:21.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.394ms
2025-06-02 23:33:24.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:24.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.4319ms
2025-06-02 23:33:27.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:27.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9332ms
2025-06-02 23:33:30.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:30.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8988ms
2025-06-02 23:33:33.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:33.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3132ms
2025-06-02 23:33:36.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:36.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6375ms
2025-06-02 23:33:39.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:39.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3844ms
2025-06-02 23:33:42.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:42.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.1837ms
2025-06-02 23:33:45.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:45.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.6051ms
2025-06-02 23:33:48.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:48.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.9254ms
2025-06-02 23:33:51.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:51.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 30.8965ms
2025-06-02 23:33:54.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:54.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.9596ms
2025-06-02 23:33:57.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:33:57.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5305ms
2025-06-02 23:34:00.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:00.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.858ms
2025-06-02 23:34:03.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:03.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1261ms
2025-06-02 23:34:06.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:06.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.7836ms
2025-06-02 23:34:09.328 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:09.348 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.0882ms
2025-06-02 23:34:12.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:12.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.5022ms
2025-06-02 23:34:15.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:15.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5112ms
2025-06-02 23:34:18.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:18.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5474ms
2025-06-02 23:34:21.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:21.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.2727ms
2025-06-02 23:34:24.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:24.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6632ms
2025-06-02 23:34:27.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:27.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.3665ms
2025-06-02 23:34:30.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:30.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3069ms
2025-06-02 23:34:33.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:33.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.4406ms
2025-06-02 23:34:36.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:36.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5364ms
2025-06-02 23:34:39.322 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:39.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.2058ms
2025-06-02 23:34:42.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:42.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5208ms
2025-06-02 23:34:45.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:45.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.5586ms
2025-06-02 23:34:48.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:48.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.8609ms
2025-06-02 23:34:51.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:51.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.641ms
2025-06-02 23:34:54.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:54.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0243ms
2025-06-02 23:34:57.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:34:57.345 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.4393ms
2025-06-02 23:35:00.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:00.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.8054ms
2025-06-02 23:35:03.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:03.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7252ms
2025-06-02 23:35:06.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:06.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.5063ms
2025-06-02 23:35:09.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:09.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.3515ms
2025-06-02 23:35:12.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:12.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.2908ms
2025-06-02 23:35:15.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:15.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1184ms
2025-06-02 23:35:18.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:18.312 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.6961ms
2025-06-02 23:35:21.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:21.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.4692ms
2025-06-02 23:35:24.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:24.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.4591ms
2025-06-02 23:35:27.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:27.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4011ms
2025-06-02 23:35:30.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:30.346 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.5225ms
2025-06-02 23:35:33.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:33.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.0176ms
2025-06-02 23:35:36.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:36.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9866ms
2025-06-02 23:35:39.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:39.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.8678ms
2025-06-02 23:35:42.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:42.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.6234ms
2025-06-02 23:35:45.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:45.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.1627ms
2025-06-02 23:35:48.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:48.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.42ms
2025-06-02 23:35:51.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:51.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.383ms
2025-06-02 23:35:54.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:54.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.758ms
2025-06-02 23:35:57.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:35:57.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5532ms
2025-06-02 23:36:00.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:00.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3906ms
2025-06-02 23:36:03.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:03.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0477ms
2025-06-02 23:36:06.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:06.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7009ms
2025-06-02 23:36:09.325 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:09.345 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.2551ms
2025-06-02 23:36:12.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:12.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5528ms
2025-06-02 23:36:15.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:15.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.8031ms
2025-06-02 23:36:18.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:18.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9748ms
2025-06-02 23:36:21.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:21.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.1ms
2025-06-02 23:36:24.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:24.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.6588ms
2025-06-02 23:36:27.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:27.352 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.4534ms
2025-06-02 23:36:30.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:30.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.0972ms
2025-06-02 23:36:33.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:33.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.3196ms
2025-06-02 23:36:36.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:36.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.8741ms
2025-06-02 23:36:39.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:39.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.5455ms
2025-06-02 23:36:42.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:42.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6415ms
2025-06-02 23:36:45.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:45.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.912ms
2025-06-02 23:36:48.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:48.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.1624ms
2025-06-02 23:36:51.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:51.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.3405ms
2025-06-02 23:36:54.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:54.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3133ms
2025-06-02 23:36:57.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:36:57.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.0155ms
2025-06-02 23:37:00.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:00.309 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.8209ms
2025-06-02 23:37:03.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:03.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7527ms
2025-06-02 23:37:06.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:06.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.0319ms
2025-06-02 23:37:09.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:09.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1756ms
2025-06-02 23:37:12.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:12.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6273ms
2025-06-02 23:37:15.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:15.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.4161ms
2025-06-02 23:37:18.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:18.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.5065ms
2025-06-02 23:37:21.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:21.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9357ms
2025-06-02 23:37:24.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:24.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.0948ms
2025-06-02 23:37:27.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:27.343 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.5485ms
2025-06-02 23:37:30.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:30.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.7352ms
2025-06-02 23:37:33.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:33.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.454ms
2025-06-02 23:37:36.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:36.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.6122ms
2025-06-02 23:37:39.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:39.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.3693ms
2025-06-02 23:37:42.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:42.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6514ms
2025-06-02 23:37:45.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:45.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5663ms
2025-06-02 23:37:48.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:48.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6649ms
2025-06-02 23:37:51.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:51.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7015ms
2025-06-02 23:37:54.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:54.353 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 32.9961ms
2025-06-02 23:37:57.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:37:57.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3354ms
2025-06-02 23:38:00.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:00.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.4073ms
2025-06-02 23:38:03.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:03.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.8354ms
2025-06-02 23:38:06.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:06.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.4134ms
2025-06-02 23:38:09.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:09.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1609ms
2025-06-02 23:38:12.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:12.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0266ms
2025-06-02 23:38:15.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:15.339 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.8435ms
2025-06-02 23:38:18.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:18.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4319ms
2025-06-02 23:38:21.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:21.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.8497ms
2025-06-02 23:38:24.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:24.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.7084ms
2025-06-02 23:38:27.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:27.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.4731ms
2025-06-02 23:38:30.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:30.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.4774ms
2025-06-02 23:38:33.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:33.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4415ms
2025-06-02 23:38:36.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:36.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.8999ms
2025-06-02 23:38:39.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:39.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6369ms
2025-06-02 23:38:42.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:42.369 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 50.3915ms
2025-06-02 23:38:45.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:45.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.974ms
2025-06-02 23:38:48.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:48.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3839ms
2025-06-02 23:38:51.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:51.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.3023ms
2025-06-02 23:38:54.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:54.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.643ms
2025-06-02 23:38:57.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:38:57.352 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 36.5153ms
2025-06-02 23:39:00.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:00.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.3807ms
2025-06-02 23:39:03.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:03.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7772ms
2025-06-02 23:39:06.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:06.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.8354ms
2025-06-02 23:39:09.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:09.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8678ms
2025-06-02 23:39:12.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:12.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.2543ms
2025-06-02 23:39:15.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:15.339 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.1686ms
2025-06-02 23:39:18.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:18.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.0992ms
2025-06-02 23:39:21.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:21.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9588ms
2025-06-02 23:39:24.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:24.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.079ms
2025-06-02 23:39:27.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:27.345 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.9577ms
2025-06-02 23:39:30.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:30.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.0147ms
2025-06-02 23:39:33.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:33.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5254ms
2025-06-02 23:39:36.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:36.343 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.1873ms
2025-06-02 23:39:39.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:39.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1412ms
2025-06-02 23:39:42.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:42.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.739ms
2025-06-02 23:39:45.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:45.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.3066ms
2025-06-02 23:39:48.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:48.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1568ms
2025-06-02 23:39:51.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:51.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6822ms
2025-06-02 23:39:54.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:54.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7474ms
2025-06-02 23:39:57.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:39:57.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.5372ms
2025-06-02 23:40:00.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:00.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.9193ms
2025-06-02 23:40:03.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:03.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4226ms
2025-06-02 23:40:06.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:06.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7686ms
2025-06-02 23:40:09.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:09.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0326ms
2025-06-02 23:40:12.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:12.312 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.1653ms
2025-06-02 23:40:15.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:15.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.1443ms
2025-06-02 23:40:18.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:18.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.181ms
2025-06-02 23:40:21.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:21.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5466ms
2025-06-02 23:40:24.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:24.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.3217ms
2025-06-02 23:40:27.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:27.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.9345ms
2025-06-02 23:40:30.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:30.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6715ms
2025-06-02 23:40:33.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:33.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6959ms
2025-06-02 23:40:36.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:36.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.141ms
2025-06-02 23:40:39.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:39.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3442ms
2025-06-02 23:40:42.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:42.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6059ms
2025-06-02 23:40:45.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:45.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.2651ms
2025-06-02 23:40:48.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:48.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3679ms
2025-06-02 23:40:51.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:51.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.2575ms
2025-06-02 23:40:54.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:54.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.2681ms
2025-06-02 23:40:57.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:40:57.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.2308ms
2025-06-02 23:41:00.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:00.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.0587ms
2025-06-02 23:41:03.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:03.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.7956ms
2025-06-02 23:41:06.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:06.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5658ms
2025-06-02 23:41:09.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:09.357 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 43.444ms
2025-06-02 23:41:12.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:12.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.8315ms
2025-06-02 23:41:15.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:15.349 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.8953ms
2025-06-02 23:41:18.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:18.344 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.9953ms
2025-06-02 23:41:21.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:21.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.1203ms
2025-06-02 23:41:24.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:24.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4208ms
2025-06-02 23:41:27.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:27.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.6564ms
2025-06-02 23:41:30.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:30.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0684ms
2025-06-02 23:41:33.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:33.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5991ms
2025-06-02 23:41:36.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:36.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.3297ms
2025-06-02 23:41:39.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:39.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5383ms
2025-06-02 23:41:42.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:42.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0284ms
2025-06-02 23:41:45.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:45.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.1407ms
2025-06-02 23:41:48.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:48.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.6678ms
2025-06-02 23:41:51.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:51.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.6671ms
2025-06-02 23:41:54.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:54.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7307ms
2025-06-02 23:41:57.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:41:57.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.5035ms
2025-06-02 23:42:00.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:00.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.0778ms
2025-06-02 23:42:03.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:03.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.5893ms
2025-06-02 23:42:06.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:06.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0966ms
2025-06-02 23:42:09.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:09.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4413ms
2025-06-02 23:42:12.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:12.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.5918ms
2025-06-02 23:42:15.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:15.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.4188ms
2025-06-02 23:42:18.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:18.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.7382ms
2025-06-02 23:42:21.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:21.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.9447ms
2025-06-02 23:42:24.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:24.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.3818ms
2025-06-02 23:42:27.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:27.311 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.0414ms
2025-06-02 23:42:30.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:30.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.0956ms
2025-06-02 23:42:33.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:33.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.6541ms
2025-06-02 23:42:36.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:36.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1338ms
2025-06-02 23:42:39.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:39.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.5323ms
2025-06-02 23:42:42.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:42.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3802ms
2025-06-02 23:42:45.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:45.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.9439ms
2025-06-02 23:42:48.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:48.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.8143ms
2025-06-02 23:42:51.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:51.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6877ms
2025-06-02 23:42:54.482 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:54.499 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1235ms
2025-06-02 23:42:57.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:42:57.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.409ms
2025-06-02 23:43:00.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:00.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.8602ms
2025-06-02 23:43:03.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:03.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.2528ms
2025-06-02 23:43:06.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:06.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.914ms
2025-06-02 23:43:09.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:09.344 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.7161ms
2025-06-02 23:43:12.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:12.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.6527ms
2025-06-02 23:43:15.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:15.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1284ms
2025-06-02 23:43:18.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:18.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.5509ms
2025-06-02 23:43:21.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:21.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.9612ms
2025-06-02 23:43:24.301 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:24.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.2658ms
2025-06-02 23:43:27.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:27.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.3557ms
2025-06-02 23:43:30.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:30.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.1723ms
2025-06-02 23:43:33.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:33.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6621ms
2025-06-02 23:43:36.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:36.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5423ms
2025-06-02 23:43:39.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:39.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.2459ms
2025-06-02 23:43:42.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:42.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5941ms
2025-06-02 23:43:45.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:45.311 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.818ms
2025-06-02 23:43:48.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:48.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4898ms
2025-06-02 23:43:51.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:51.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.0967ms
2025-06-02 23:43:54.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:54.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.3499ms
2025-06-02 23:43:57.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:43:57.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9117ms
2025-06-02 23:44:00.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:00.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1789ms
2025-06-02 23:44:03.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:03.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9867ms
2025-06-02 23:44:06.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:06.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.8661ms
2025-06-02 23:44:09.322 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:09.357 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 34.5929ms
2025-06-02 23:44:12.348 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:12.378 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 30.1858ms
2025-06-02 23:44:12.464 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 23:44:12.464 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - null null
2025-06-02 23:44:12.518 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID ea4451ff-aeef-4429-b8ae-2f307c16d579
2025-06-02 23:44:12.518 +04:00 [INF] Request OPTIONS /api/documents/undefined started with correlation ID 4882dd16-c3ab-4d1a-abb4-c1e7d6438c76
2025-06-02 23:44:12.522 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:12.524 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:12.535 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 13ms with status 204 (Correlation ID: ea4451ff-aeef-4429-b8ae-2f307c16d579)
2025-06-02 23:44:12.535 +04:00 [INF] Request OPTIONS /api/documents/undefined completed in 11ms with status 204 (Correlation ID: 4882dd16-c3ab-4d1a-abb4-c1e7d6438c76)
2025-06-02 23:44:12.563 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 99.5582ms
2025-06-02 23:44:12.560 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents/undefined - 204 null null 96.0984ms
2025-06-02 23:44:12.586 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 23:44:12.644 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID bb6113de-3f09-46dc-af41-afec23ef2f19
2025-06-02 23:44:12.647 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:12.657 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:44:12.660 +04:00 [INF] Request GET /api/documents/undefined completed in 13ms with status 404 (Correlation ID: bb6113de-3f09-46dc-af41-afec23ef2f19)
2025-06-02 23:44:12.665 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 78.3809ms
2025-06-02 23:44:12.668 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents/undefined - application/json null
2025-06-02 23:44:12.673 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 23:44:12.678 +04:00 [INF] Request GET /api/documents/undefined started with correlation ID 39a963dd-3d3d-4820-8099-cb1b09d6e729
2025-06-02 23:44:12.685 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:12.687 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:44:12.690 +04:00 [INF] Request GET /api/documents/undefined completed in 4ms with status 404 (Correlation ID: 39a963dd-3d3d-4820-8099-cb1b09d6e729)
2025-06-02 23:44:12.694 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents/undefined - 404 0 null 26.7994ms
2025-06-02 23:44:12.701 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:61113/api/documents/undefined, Response status code: 404
2025-06-02 23:44:15.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:15.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.4566ms
2025-06-02 23:44:18.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:18.344 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.3825ms
2025-06-02 23:44:21.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:21.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.6672ms
2025-06-02 23:44:24.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:24.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.9916ms
2025-06-02 23:44:27.323 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:27.355 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 31.4722ms
2025-06-02 23:44:29.482 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 23:44:29.482 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:61113/api/documents? - null null
2025-06-02 23:44:29.493 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID a50563f5-8eb2-4e4f-a9d6-f38f8c95bba5
2025-06-02 23:44:29.505 +04:00 [INF] Request OPTIONS /api/documents started with correlation ID ac95303d-6f67-44af-92c7-f20e2e312f16
2025-06-02 23:44:29.509 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:29.512 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:29.516 +04:00 [INF] Request OPTIONS /api/documents completed in 6ms with status 204 (Correlation ID: a50563f5-8eb2-4e4f-a9d6-f38f8c95bba5)
2025-06-02 23:44:29.518 +04:00 [INF] Request OPTIONS /api/documents completed in 5ms with status 204 (Correlation ID: ac95303d-6f67-44af-92c7-f20e2e312f16)
2025-06-02 23:44:29.525 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 43.3123ms
2025-06-02 23:44:29.529 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:44:29.530 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:61113/api/documents? - 204 null null 47.6611ms
2025-06-02 23:44:29.574 +04:00 [INF] Request GET /api/documents started with correlation ID 086362bc-944e-4833-b70a-86d078dbe895
2025-06-02 23:44:29.591 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:29.596 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:44:29.605 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:44:29.611 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:44:29.773 +04:00 [INF] Executed DbCommand (56ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 23:44:29.804 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 23:44:29.819 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 201.4572ms
2025-06-02 23:44:29.822 +04:00 [INF] Request starting HTTP/2 GET https://localhost:61113/api/documents? - application/json null
2025-06-02 23:44:29.827 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:44:29.840 +04:00 [INF] Request GET /api/documents started with correlation ID 6d3ee520-1cde-469e-8436-8e915e12f9f9
2025-06-02 23:44:29.847 +04:00 [INF] Request GET /api/documents completed in 256ms with status 200 (Correlation ID: 086362bc-944e-4833-b70a-86d078dbe895)
2025-06-02 23:44:29.856 +04:00 [INF] CORS policy execution successful.
2025-06-02 23:44:29.869 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 339.4703ms
2025-06-02 23:44:29.873 +04:00 [INF] JWT Token validated for user: Kevin Jules
2025-06-02 23:44:29.895 +04:00 [INF] Executing endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:44:29.900 +04:00 [INF] Route matched with {action = "GetUserDocuments", controller = "Documents"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto]]] GetUserDocuments(Int32, Int32) on controller LexAI.DataPreprocessing.API.Controllers.DocumentsController (LexAI.DataPreprocessing.API).
2025-06-02 23:44:29.930 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__ToString_0='?', @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."ChunkCount", d1."ClassificationConfidence", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DetectedDomain", d1."EstimatedCost", d1."ExtractedText", d1."FileHash", d1."FileName", d1."FileSize", d1."IsDeleted", d1."IsVectorized", d1."Metadata", d1."MimeType", d1."ProcessingTime", d1."Status", d1."StoragePath", d1."TotalTokens", d1."UpdatedAt", d1."UpdatedBy", d1."VectorCollection", d1."VectorDatabase", d1.xmin, d0."Id", d0."CharacterCount", d0."Content", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."DocumentId", d0."DomainRelevance", d0."EmbeddingModel", d0."EmbeddingVector", d0."EndPosition", d0."ImportanceScore", d0."IsDeleted", d0."IsVectorized", d0."Keywords", d0."Metadata", d0."QualityScore", d0."SequenceNumber", d0."StartPosition", d0."TokenCount", d0."Type", d0."UpdatedAt", d0."UpdatedBy", d0."VectorDimension", d0."VectorId", d0.xmin, p."Id", p."AgentName", p."CompletedAt", p."DocumentId", p."ErrorMessage", p."IsSuccessful", p."Metadata", p."StartedAt", p."StepName", p0."Id", p0."AgentName", p0."DocumentId", p0."ErrorCode", p0."ErrorMessage", p0."IsResolved", p0."Metadata", p0."OccurredAt", p0."ResolutionNotes", p0."ResolvedAt", p0."Severity", p0."StepName"
FROM (
    SELECT d."Id", d."ChunkCount", d."ClassificationConfidence", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DetectedDomain", d."EstimatedCost", d."ExtractedText", d."FileHash", d."FileName", d."FileSize", d."IsDeleted", d."IsVectorized", d."Metadata", d."MimeType", d."ProcessingTime", d."Status", d."StoragePath", d."TotalTokens", d."UpdatedAt", d."UpdatedBy", d."VectorCollection", d."VectorDatabase", d.xmin
    FROM "Documents" AS d
    WHERE d."CreatedBy" = @__ToString_0
    ORDER BY d."CreatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN "DocumentChunks" AS d0 ON d1."Id" = d0."DocumentId"
LEFT JOIN "ProcessingSteps" AS p ON d1."Id" = p."DocumentId"
LEFT JOIN "ProcessingErrors" AS p0 ON d1."Id" = p0."DocumentId"
ORDER BY d1."CreatedAt" DESC, d1."Id", d0."Id", p."Id"
2025-06-02 23:44:29.947 +04:00 [INF] Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[LexAI.DataPreprocessing.Domain.Entities.Document, LexAI.DataPreprocessing.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[LexAI.DataPreprocessing.API.Controllers.DocumentSummaryDto, LexAI.DataPreprocessing.API, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 23:44:29.973 +04:00 [INF] Executed action LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API) in 61.4402ms
2025-06-02 23:44:29.986 +04:00 [INF] Executed endpoint 'LexAI.DataPreprocessing.API.Controllers.DocumentsController.GetUserDocuments (LexAI.DataPreprocessing.API)'
2025-06-02 23:44:29.989 +04:00 [INF] Request GET /api/documents completed in 132ms with status 200 (Correlation ID: 6d3ee520-1cde-469e-8436-8e915e12f9f9)
2025-06-02 23:44:29.993 +04:00 [INF] Request finished HTTP/2 GET https://localhost:61113/api/documents? - 200 null application/json; charset=utf-8 171.3455ms
2025-06-02 23:44:30.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:30.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.5785ms
2025-06-02 23:44:33.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:33.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9362ms
2025-06-02 23:44:36.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:36.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.8834ms
2025-06-02 23:44:39.301 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:39.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6219ms
2025-06-02 23:44:42.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:42.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3072ms
2025-06-02 23:44:45.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:45.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.3081ms
2025-06-02 23:44:48.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:48.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.9486ms
2025-06-02 23:44:51.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:51.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.91ms
2025-06-02 23:44:54.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:54.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3905ms
2025-06-02 23:44:57.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:44:57.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.7524ms
2025-06-02 23:45:00.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:00.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4182ms
2025-06-02 23:45:03.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:03.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.1427ms
2025-06-02 23:45:06.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:06.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.8168ms
2025-06-02 23:45:09.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:09.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.6642ms
2025-06-02 23:45:12.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:12.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.6988ms
2025-06-02 23:45:15.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:15.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0742ms
2025-06-02 23:45:18.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:18.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.4027ms
2025-06-02 23:45:21.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:21.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7716ms
2025-06-02 23:45:24.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:24.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.5305ms
2025-06-02 23:45:27.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:27.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.5741ms
2025-06-02 23:45:30.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:30.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.6829ms
2025-06-02 23:45:33.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:33.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.9311ms
2025-06-02 23:45:36.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:36.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.7317ms
2025-06-02 23:45:39.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:39.349 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 35.914ms
2025-06-02 23:45:42.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:42.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.7588ms
2025-06-02 23:45:45.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:45.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4554ms
2025-06-02 23:45:48.323 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:48.356 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 33.5696ms
2025-06-02 23:45:51.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:51.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.3494ms
2025-06-02 23:45:54.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:54.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.1249ms
2025-06-02 23:45:57.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:45:57.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.3483ms
2025-06-02 23:46:00.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:00.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.0038ms
2025-06-02 23:46:03.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:03.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.0104ms
2025-06-02 23:46:06.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:06.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.5137ms
2025-06-02 23:46:09.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:09.339 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.3576ms
2025-06-02 23:46:12.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:12.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.7067ms
2025-06-02 23:46:15.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:15.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.1435ms
2025-06-02 23:46:18.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:18.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5478ms
2025-06-02 23:46:21.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:21.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.077ms
2025-06-02 23:46:24.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:24.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0596ms
2025-06-02 23:46:27.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:27.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.541ms
2025-06-02 23:46:30.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:30.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9874ms
2025-06-02 23:46:33.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:33.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.5982ms
2025-06-02 23:46:36.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:36.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.3721ms
2025-06-02 23:46:39.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:39.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.7054ms
2025-06-02 23:46:42.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:42.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1251ms
2025-06-02 23:46:45.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:45.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6996ms
2025-06-02 23:46:48.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:48.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.1135ms
2025-06-02 23:46:51.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:51.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0789ms
2025-06-02 23:46:54.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:54.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.3817ms
2025-06-02 23:46:57.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:46:57.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1671ms
2025-06-02 23:47:00.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:00.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.8586ms
2025-06-02 23:47:03.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:03.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.8642ms
2025-06-02 23:47:06.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:06.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.3589ms
2025-06-02 23:47:09.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:09.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6267ms
2025-06-02 23:47:12.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:12.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.7565ms
2025-06-02 23:47:15.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:15.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.5225ms
2025-06-02 23:47:18.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:18.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.1421ms
2025-06-02 23:47:21.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:21.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.9667ms
2025-06-02 23:47:24.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:24.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.6674ms
2025-06-02 23:47:27.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:27.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8181ms
2025-06-02 23:47:30.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:30.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.5034ms
2025-06-02 23:47:33.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:33.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.9397ms
2025-06-02 23:47:36.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:36.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0982ms
2025-06-02 23:47:39.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:39.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.1036ms
2025-06-02 23:47:42.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:42.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9315ms
2025-06-02 23:47:45.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:45.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5529ms
2025-06-02 23:47:48.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:48.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1205ms
2025-06-02 23:47:51.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:51.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.8875ms
2025-06-02 23:47:54.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:54.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.2621ms
2025-06-02 23:47:57.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:47:57.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.7445ms
2025-06-02 23:48:00.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:00.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.6228ms
2025-06-02 23:48:03.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:03.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7151ms
2025-06-02 23:48:06.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:06.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9334ms
2025-06-02 23:48:09.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:09.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.7933ms
2025-06-02 23:48:12.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:12.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.1216ms
2025-06-02 23:48:15.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:15.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.0574ms
2025-06-02 23:48:18.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:18.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7828ms
2025-06-02 23:48:21.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:21.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.9506ms
2025-06-02 23:48:24.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:24.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5771ms
2025-06-02 23:48:27.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:27.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1578ms
2025-06-02 23:48:30.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:30.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1232ms
2025-06-02 23:48:33.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:33.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.0951ms
2025-06-02 23:48:36.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:36.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6476ms
2025-06-02 23:48:39.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:39.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.3822ms
2025-06-02 23:48:42.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:42.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.1793ms
2025-06-02 23:48:45.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:45.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.6201ms
2025-06-02 23:48:48.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:48.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1295ms
2025-06-02 23:48:51.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:51.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.4352ms
2025-06-02 23:48:54.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:54.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1397ms
2025-06-02 23:48:57.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:48:57.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.7704ms
2025-06-02 23:49:00.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:00.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7976ms
2025-06-02 23:49:03.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:03.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.1189ms
2025-06-02 23:49:06.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:06.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7234ms
2025-06-02 23:49:09.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:09.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.2376ms
2025-06-02 23:49:12.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:12.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.4951ms
2025-06-02 23:49:15.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:15.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.3174ms
2025-06-02 23:49:18.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:18.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.8869ms
2025-06-02 23:49:21.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:21.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.9584ms
2025-06-02 23:49:24.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:24.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7989ms
2025-06-02 23:49:27.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:27.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.7333ms
2025-06-02 23:49:30.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:30.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.2118ms
2025-06-02 23:49:33.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:33.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.3233ms
2025-06-02 23:49:36.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:36.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5715ms
2025-06-02 23:49:39.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:39.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.8948ms
2025-06-02 23:49:42.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:42.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.2891ms
2025-06-02 23:49:45.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:45.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.1896ms
2025-06-02 23:49:48.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:48.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.9208ms
2025-06-02 23:49:51.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:51.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3474ms
2025-06-02 23:49:54.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:54.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.1168ms
2025-06-02 23:49:57.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:49:57.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.1997ms
2025-06-02 23:50:00.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:00.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.88ms
2025-06-02 23:50:03.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:03.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9163ms
2025-06-02 23:50:06.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:06.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.1185ms
2025-06-02 23:50:09.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:09.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.9818ms
2025-06-02 23:50:12.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:12.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.781ms
2025-06-02 23:50:15.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:15.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.4013ms
2025-06-02 23:50:18.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:18.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.9189ms
2025-06-02 23:50:21.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:21.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4409ms
2025-06-02 23:50:24.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:24.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0961ms
2025-06-02 23:50:27.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:27.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.1263ms
2025-06-02 23:50:30.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:30.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.4357ms
2025-06-02 23:50:33.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:33.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1855ms
2025-06-02 23:50:36.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:36.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.5276ms
2025-06-02 23:50:39.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:39.317 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.5508ms
2025-06-02 23:50:42.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:42.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.0055ms
2025-06-02 23:50:45.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:45.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.2259ms
2025-06-02 23:50:48.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:48.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6499ms
2025-06-02 23:50:51.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:51.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.3404ms
2025-06-02 23:50:54.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:54.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.1145ms
2025-06-02 23:50:57.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:50:57.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7986ms
2025-06-02 23:51:00.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:00.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6016ms
2025-06-02 23:51:03.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:03.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.274ms
2025-06-02 23:51:06.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:06.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.8256ms
2025-06-02 23:51:09.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:09.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.8939ms
2025-06-02 23:51:12.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:12.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.8428ms
2025-06-02 23:51:15.324 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:15.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.7514ms
2025-06-02 23:51:18.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:18.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.7564ms
2025-06-02 23:51:21.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:21.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 30.7659ms
2025-06-02 23:51:24.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:24.343 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.6458ms
2025-06-02 23:51:27.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:27.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.762ms
2025-06-02 23:51:30.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:30.347 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.8639ms
2025-06-02 23:51:33.323 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:33.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.816ms
2025-06-02 23:51:36.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:36.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6588ms
2025-06-02 23:51:39.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:39.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0767ms
2025-06-02 23:51:42.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:42.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.9019ms
2025-06-02 23:51:45.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:45.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.0539ms
2025-06-02 23:51:48.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:48.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.1744ms
2025-06-02 23:51:51.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:51.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.6767ms
2025-06-02 23:51:54.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:54.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.9565ms
2025-06-02 23:51:57.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:51:57.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5241ms
2025-06-02 23:52:00.324 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:00.351 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.1013ms
2025-06-02 23:52:03.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:03.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0598ms
2025-06-02 23:52:06.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:06.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0943ms
2025-06-02 23:52:09.323 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:09.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.5359ms
2025-06-02 23:52:12.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:12.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5736ms
2025-06-02 23:52:15.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:15.374 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 68.6393ms
2025-06-02 23:52:18.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:18.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.2048ms
2025-06-02 23:52:21.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:21.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.2439ms
2025-06-02 23:52:24.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:24.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.4034ms
2025-06-02 23:52:27.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:27.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.2755ms
2025-06-02 23:52:30.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:30.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.0108ms
2025-06-02 23:52:33.301 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:33.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.5352ms
2025-06-02 23:52:36.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:36.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.7618ms
2025-06-02 23:52:39.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:39.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1055ms
2025-06-02 23:52:42.322 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:42.354 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 32.1937ms
2025-06-02 23:52:45.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:45.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1502ms
2025-06-02 23:52:48.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:48.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.6976ms
2025-06-02 23:52:51.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:51.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.0871ms
2025-06-02 23:52:54.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:54.345 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.8989ms
2025-06-02 23:52:57.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:52:57.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.3735ms
2025-06-02 23:53:00.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:00.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6488ms
2025-06-02 23:53:03.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:03.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6628ms
2025-06-02 23:53:06.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:06.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.5728ms
2025-06-02 23:53:09.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:09.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8761ms
2025-06-02 23:53:12.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:12.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.6315ms
2025-06-02 23:53:15.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:15.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1054ms
2025-06-02 23:53:18.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:18.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.9825ms
2025-06-02 23:53:21.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:21.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 28.305ms
2025-06-02 23:53:24.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:24.347 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 32.6648ms
2025-06-02 23:53:27.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:27.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.5965ms
2025-06-02 23:53:30.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:30.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.7369ms
2025-06-02 23:53:33.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:33.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.4298ms
2025-06-02 23:53:36.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:36.342 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.8059ms
2025-06-02 23:53:39.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:39.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.5583ms
2025-06-02 23:53:42.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:42.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.42ms
2025-06-02 23:53:45.323 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:45.353 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.5212ms
2025-06-02 23:53:48.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:48.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.6311ms
2025-06-02 23:53:51.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:51.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.8151ms
2025-06-02 23:53:54.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:54.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.4078ms
2025-06-02 23:53:57.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:53:57.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.6696ms
2025-06-02 23:54:00.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:00.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.6648ms
2025-06-02 23:54:03.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:03.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.2751ms
2025-06-02 23:54:06.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:06.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.0684ms
2025-06-02 23:54:09.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:09.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.7759ms
2025-06-02 23:54:12.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:12.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.4422ms
2025-06-02 23:54:15.321 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:15.338 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.6819ms
2025-06-02 23:54:18.327 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:18.373 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 45.5451ms
2025-06-02 23:54:21.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:21.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.7758ms
2025-06-02 23:54:24.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:24.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.6546ms
2025-06-02 23:54:27.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:27.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.6333ms
2025-06-02 23:54:30.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:30.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7699ms
2025-06-02 23:54:33.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:33.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.4019ms
2025-06-02 23:54:36.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:36.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.9631ms
2025-06-02 23:54:39.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:39.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.6693ms
2025-06-02 23:54:42.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:42.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.3466ms
2025-06-02 23:54:45.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:45.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.6481ms
2025-06-02 23:54:48.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:48.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 24.0497ms
2025-06-02 23:54:51.340 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:51.367 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.9684ms
2025-06-02 23:54:54.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:54.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.2508ms
2025-06-02 23:54:57.362 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:54:57.403 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 40.4345ms
2025-06-02 23:55:00.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:00.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.7642ms
2025-06-02 23:55:03.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:03.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.535ms
2025-06-02 23:55:06.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:06.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.9126ms
2025-06-02 23:55:09.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:09.341 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.1651ms
2025-06-02 23:55:12.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:12.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.7238ms
2025-06-02 23:55:15.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:15.315 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.3611ms
2025-06-02 23:55:18.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:18.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.6384ms
2025-06-02 23:55:21.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:21.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5219ms
2025-06-02 23:55:24.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:24.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.2511ms
2025-06-02 23:55:27.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:27.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5813ms
2025-06-02 23:55:30.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:30.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0531ms
2025-06-02 23:55:33.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:33.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.7452ms
2025-06-02 23:55:36.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:36.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.182ms
2025-06-02 23:55:39.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:39.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5955ms
2025-06-02 23:55:42.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:42.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5843ms
2025-06-02 23:55:45.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:45.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3508ms
2025-06-02 23:55:48.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:48.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6341ms
2025-06-02 23:55:51.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:51.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0057ms
2025-06-02 23:55:54.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:54.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.2944ms
2025-06-02 23:55:57.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:55:57.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.276ms
2025-06-02 23:56:00.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:00.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.6007ms
2025-06-02 23:56:03.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:03.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.0956ms
2025-06-02 23:56:06.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:06.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.6425ms
2025-06-02 23:56:09.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:09.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.1927ms
2025-06-02 23:56:12.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:12.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.275ms
2025-06-02 23:56:15.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:15.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.4664ms
2025-06-02 23:56:18.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:18.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.797ms
2025-06-02 23:56:21.317 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:21.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.5013ms
2025-06-02 23:56:24.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:24.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.7816ms
2025-06-02 23:56:27.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:27.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.5493ms
2025-06-02 23:56:30.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:30.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.3823ms
2025-06-02 23:56:33.316 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:33.345 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 29.1506ms
2025-06-02 23:56:36.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:36.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5745ms
2025-06-02 23:56:39.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:39.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.0399ms
2025-06-02 23:56:42.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:42.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 20.1612ms
2025-06-02 23:56:45.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:45.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.8857ms
2025-06-02 23:56:48.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:48.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4069ms
2025-06-02 23:56:51.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:51.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.0695ms
2025-06-02 23:56:54.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:54.336 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.4712ms
2025-06-02 23:56:57.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:56:57.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.0343ms
2025-06-02 23:57:00.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:00.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7685ms
2025-06-02 23:57:03.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:03.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.6004ms
2025-06-02 23:57:06.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:06.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.4652ms
2025-06-02 23:57:09.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:09.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4873ms
2025-06-02 23:57:12.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:12.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.6747ms
2025-06-02 23:57:15.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:15.324 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4872ms
2025-06-02 23:57:18.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:18.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.4662ms
2025-06-02 23:57:21.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:21.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1715ms
2025-06-02 23:57:24.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:24.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.4982ms
2025-06-02 23:57:27.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:27.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.0663ms
2025-06-02 23:57:30.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:30.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.9565ms
2025-06-02 23:57:33.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:33.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.5974ms
2025-06-02 23:57:36.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:36.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.2558ms
2025-06-02 23:57:39.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:39.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.8663ms
2025-06-02 23:57:42.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:42.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.7526ms
2025-06-02 23:57:45.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:45.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.5034ms
2025-06-02 23:57:48.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:48.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.9411ms
2025-06-02 23:57:51.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:51.326 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.3646ms
2025-06-02 23:57:54.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:54.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.8138ms
2025-06-02 23:57:57.309 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:57:57.335 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 25.5814ms
2025-06-02 23:58:00.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:00.310 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 8.0974ms
2025-06-02 23:58:03.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:03.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.5214ms
2025-06-02 23:58:06.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:06.333 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.4732ms
2025-06-02 23:58:09.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:09.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.0805ms
2025-06-02 23:58:12.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:12.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.1686ms
2025-06-02 23:58:15.310 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:15.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.469ms
2025-06-02 23:58:18.311 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:18.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.0263ms
2025-06-02 23:58:21.301 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:21.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.6545ms
2025-06-02 23:58:24.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:24.313 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 9.9702ms
2025-06-02 23:58:27.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:27.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.9217ms
2025-06-02 23:58:30.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:30.318 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.5382ms
2025-06-02 23:58:33.328 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:33.351 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 23.1781ms
2025-06-02 23:58:36.307 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:36.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.9059ms
2025-06-02 23:58:39.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:39.339 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 27.9371ms
2025-06-02 23:58:42.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:42.320 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 11.7013ms
2025-06-02 23:58:45.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:45.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 16.4653ms
2025-06-02 23:58:48.305 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:48.323 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.4471ms
2025-06-02 23:58:51.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:51.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.2311ms
2025-06-02 23:58:54.318 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:54.330 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.0078ms
2025-06-02 23:58:57.304 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:58:57.322 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.1646ms
2025-06-02 23:59:00.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:00.325 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.2923ms
2025-06-02 23:59:03.302 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:03.319 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.3438ms
2025-06-02 23:59:06.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:06.328 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.2847ms
2025-06-02 23:59:09.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:09.329 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 22.866ms
2025-06-02 23:59:12.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:12.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.2364ms
2025-06-02 23:59:15.324 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:15.343 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 19.1237ms
2025-06-02 23:59:18.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:18.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 7.8175ms
2025-06-02 23:59:21.299 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:21.310 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 10.7235ms
2025-06-02 23:59:24.313 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:24.327 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 14.1905ms
2025-06-02 23:59:27.312 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:27.331 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.962ms
2025-06-02 23:59:30.306 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:30.321 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 15.3077ms
2025-06-02 23:59:33.319 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:33.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.1566ms
2025-06-02 23:59:36.314 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:36.332 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 18.5009ms
2025-06-02 23:59:39.301 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:39.314 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 12.6195ms
2025-06-02 23:59:42.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:42.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.409ms
2025-06-02 23:59:45.303 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:45.316 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.1463ms
2025-06-02 23:59:48.308 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:48.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 26.6883ms
2025-06-02 23:59:51.315 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:51.337 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 21.696ms
2025-06-02 23:59:54.320 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:54.334 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 13.672ms
2025-06-02 23:59:57.322 +04:00 [INF] Request starting HTTP/2 POST https://localhost:61113/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-06-02 23:59:57.340 +04:00 [INF] Request finished HTTP/2 POST https://localhost:61113/hangfire/stats - 200 null application/json 17.8714ms
