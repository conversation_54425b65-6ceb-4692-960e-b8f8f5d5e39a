<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.LegalResearch.API</name>
    </assembly>
    <members>
        <member name="T:LexAI.LegalResearch.API.Controllers.SearchController">
            <summary>
            Controller for legal research search operations
            </summary>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.#ctor(MediatR.IMediator,LexAI.LegalResearch.Application.Interfaces.ILegalSearchService,Microsoft.Extensions.Logging.ILogger{LexAI.LegalResearch.API.Controllers.SearchController})">
            <summary>
            Initializes a new instance of the SearchController
            </summary>
            <param name="mediator">MediatR mediator</param>
            <param name="searchService">Legal search service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.Search(LexAI.LegalResearch.Application.DTOs.SearchRequestDto)">
            <summary>
            Performs a legal research search
            </summary>
            <param name="request">Search request</param>
            <returns>Search results</returns>
            <response code="200">Search completed successfully</response>
            <response code="400">Invalid search request</response>
            <response code="401">User not authenticated</response>
            <response code="429">Rate limit exceeded</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.HybridSearch(LexAI.LegalResearch.Application.DTOs.SearchRequestDto)">
            <summary>
            Performs a hybrid search combining semantic and keyword search
            </summary>
            <param name="request">Search request</param>
            <returns>Hybrid search results</returns>
            <response code="200">Hybrid search completed successfully</response>
            <response code="400">Invalid search request</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.FindSimilarDocuments(System.Guid,System.Int32)">
            <summary>
            Finds documents similar to a given document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="limit">Maximum number of similar documents (default: 10)</param>
            <returns>Similar documents</returns>
            <response code="200">Similar documents found</response>
            <response code="404">Document not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.GetSearchSuggestions(System.String,System.Int32)">
            <summary>
            Gets search suggestions based on partial query
            </summary>
            <param name="q">Partial query text</param>
            <param name="limit">Maximum number of suggestions (default: 10)</param>
            <returns>Search suggestions</returns>
            <response code="200">Suggestions retrieved successfully</response>
            <response code="400">Invalid request</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.AnalyzeQuery(System.String)">
            <summary>
            Analyzes a search query to extract intent and entities
            </summary>
            <param name="query">Query text to analyze</param>
            <returns>Query analysis result</returns>
            <response code="200">Query analyzed successfully</response>
            <response code="400">Invalid query</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.ProvideFeedback(System.Guid,LexAI.LegalResearch.Application.DTOs.UserFeedbackDto)">
            <summary>
            Provides feedback on search results
            </summary>
            <param name="queryId">Search query ID</param>
            <param name="feedback">User feedback</param>
            <returns>Feedback confirmation</returns>
            <response code="200">Feedback recorded successfully</response>
            <response code="400">Invalid feedback</response>
            <response code="404">Query not found</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.GetSearchAnalytics(System.String)">
            <summary>
            Gets search analytics for the current user
            </summary>
            <param name="sessionId">Optional session ID filter</param>
            <returns>Search analytics</returns>
            <response code="200">Analytics retrieved successfully</response>
            <response code="401">User not authenticated</response>
        </member>
        <member name="M:LexAI.LegalResearch.API.Controllers.SearchController.TestSearch(LexAI.LegalResearch.Application.DTOs.SearchRequestDto)">
            <summary>
            Test endpoint for development - performs a search without authentication
            </summary>
            <param name="request">Search request</param>
            <returns>Search results</returns>
            <response code="200">Search completed successfully</response>
            <response code="400">Invalid search request</response>
        </member>
    </members>
</doc>
