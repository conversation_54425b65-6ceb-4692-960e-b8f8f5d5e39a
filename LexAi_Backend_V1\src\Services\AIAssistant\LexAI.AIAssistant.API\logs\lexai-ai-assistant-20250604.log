2025-06-04 11:26:29.090 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-04 11:26:29.282 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 11:26:29.854 +04:00 [INF] Now listening on: https://localhost:58323
2025-06-04 11:26:29.857 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-04 11:26:30.300 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 11:26:30.303 +04:00 [INF] Hosting environment: Development
2025-06-04 11:26:30.304 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-04 11:26:31.986 +04:00 [INF] Request starting HTTP/2 GET https://localhost:58323/ - null null
2025-06-04 11:26:32.288 +04:00 [INF] Request GET / started with correlation ID 18e5e79e-0b59-4138-8f84-c2be664d42e3
2025-06-04 11:26:32.417 +04:00 [INF] Request GET / completed in 123ms with status 404 (Correlation ID: 18e5e79e-0b59-4138-8f84-c2be664d42e3)
2025-06-04 11:26:32.434 +04:00 [INF] Request finished HTTP/2 GET https://localhost:58323/ - 404 0 null 460.8696ms
2025-06-04 11:26:32.444 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:58323/, Response status code: 404
