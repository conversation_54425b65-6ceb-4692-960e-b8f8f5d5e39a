using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.Entities;
using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LexAI.LegalResearch.Application.Commands;

/// <summary>
/// Command to perform a legal search
/// </summary>
public class PerformSearchCommand : IRequest<SearchResponseDto>
{
    /// <summary>
    /// Search request
    /// </summary>
    public SearchRequestDto Request { get; set; } = null!;
}

/// <summary>
/// Handler for PerformSearchCommand
/// </summary>
public class PerformSearchCommandHandler : IRequestHandler<PerformSearchCommand, SearchResponseDto>
{
    private readonly ILegalSearchService _searchService;
    private readonly ISearchQueryRepository _queryRepository;
    private readonly ILogger<PerformSearchCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the PerformSearchCommandHandler
    /// </summary>
    /// <param name="searchService">Legal search service</param>
    /// <param name="queryRepository">Search query repository</param>
    /// <param name="logger">Logger</param>
    public PerformSearchCommandHandler(
        ILegalSearchService searchService,
        ISearchQueryRepository queryRepository,
        ILogger<PerformSearchCommandHandler> logger)
    {
        _searchService = searchService;
        _queryRepository = queryRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the PerformSearchCommand
    /// </summary>
    /// <param name="request">Search command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Search response</returns>
    public async Task<SearchResponseDto> Handle(PerformSearchCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Performing legal search for query: {Query}", request.Request.Query);

        try
        {
            // Create search query entity
            var searchQuery = SearchQuery.Create(
                request.Request.Query,
                request.Request.UserId,
                request.Request.SessionId);

            // Set filters if provided
            if (request.Request.DomainFilter.HasValue ||
                request.Request.TypeFilter.HasValue ||
                request.Request.DateFilter != null ||
                !string.IsNullOrEmpty(request.Request.LanguageFilter))
            {
                var dateFilter = request.Request.DateFilter != null
                    ? DateRange.Create(request.Request.DateFilter.StartDate, request.Request.DateFilter.EndDate)
                    : null;

                searchQuery.SetFilters(
                    request.Request.DomainFilter,
                    request.Request.TypeFilter,
                    dateFilter,
                    request.Request.LanguageFilter);
            }

            // Set search parameters
            var parameters = SearchParameters.Create(
                request.Request.Method,
                request.Request.Limit,
                request.Request.MinRelevanceScore,
                request.Request.IncludeHighlights);

            searchQuery.SetParameters(parameters);

            // Save query to repository
            await _queryRepository.AddAsync(searchQuery, cancellationToken);

            // Mark search as started
            searchQuery.MarkAsStarted();
            await _queryRepository.UpdateAsync(searchQuery, cancellationToken);

            // Perform the search
            SearchResponseDto response;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                response = request.Request.Method switch
                {
                    SearchMethod.Hybrid => await _searchService.HybridSearchAsync(request.Request, cancellationToken),
                    _ => await _searchService.SearchAsync(request.Request, cancellationToken)
                };

                stopwatch.Stop();

                // Convert response results to search result entities
                var searchResults = response.Results.Select(r => SearchResult.Create(
                    r.DocumentId,
                    r.Title,
                    r.Summary,
                    r.RelevanceScore,
                    r.DocumentType,
                    r.LegalDomain,
                    DocumentSource.Create(
                        r.Source.Name,
                        r.Source.Url,
                        r.Source.Type,
                        r.Source.Authority,
                        r.Source.Jurisdiction,
                        r.Source.ReliabilityScore)
                )).ToList();

                // Mark search as completed
                searchQuery.MarkAsCompleted(
                    searchResults,
                    stopwatch.ElapsedMilliseconds,
                    response.QualityScore,
                    response.IsCached);

                // Set the query ID in response
                response.QueryId = searchQuery.Id;

                _logger.LogInformation("Search completed successfully. Query ID: {QueryId}, Results: {ResultCount}, Time: {ExecutionTime}ms",
                    searchQuery.Id, response.TotalResults, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Mark search as failed
                searchQuery.MarkAsFailed(ex.Message, stopwatch.ElapsedMilliseconds);

                _logger.LogError(ex, "Search failed for query: {Query}", request.Request.Query);
                throw;
            }
            finally
            {
                // Update query in repository
                await _queryRepository.UpdateAsync(searchQuery, cancellationToken);
            }

            return response;
        }
        catch (Exception ex) when (!(ex is DomainException))
        {
            _logger.LogError(ex, "Error performing search for query: {Query}", request.Request.Query);
            throw;
        }
    }
}

/// <summary>
/// Command to index a legal document
/// </summary>
public class IndexDocumentCommand : IRequest<bool>
{
    /// <summary>
    /// Document ID to index
    /// </summary>
    public Guid DocumentId { get; set; }

    /// <summary>
    /// Force re-indexing even if already indexed
    /// </summary>
    public bool ForceReindex { get; set; } = false;
}

/// <summary>
/// Handler for IndexDocumentCommand
/// </summary>
public class IndexDocumentCommandHandler : IRequestHandler<IndexDocumentCommand, bool>
{
    private readonly ILegalDocumentRepository _documentRepository;
    private readonly IDocumentChunkingService _chunkingService;
    private readonly IEmbeddingService _embeddingService;
    private readonly IVectorDatabaseService _vectorService;
    private readonly ILogger<IndexDocumentCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the IndexDocumentCommandHandler
    /// </summary>
    /// <param name="documentRepository">Document repository</param>
    /// <param name="chunkingService">Document chunking service</param>
    /// <param name="embeddingService">Embedding service</param>
    /// <param name="vectorService">Vector database service</param>
    /// <param name="logger">Logger</param>
    public IndexDocumentCommandHandler(
        ILegalDocumentRepository documentRepository,
        IDocumentChunkingService chunkingService,
        IEmbeddingService embeddingService,
        IVectorDatabaseService vectorService,
        ILogger<IndexDocumentCommandHandler> logger)
    {
        _documentRepository = documentRepository;
        _chunkingService = chunkingService;
        _embeddingService = embeddingService;
        _vectorService = vectorService;
        _logger = logger;
    }

    /// <summary>
    /// Handles the IndexDocumentCommand
    /// </summary>
    /// <param name="request">Index command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if indexing was successful</returns>
    public async Task<bool> Handle(IndexDocumentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting document indexing for document: {DocumentId}", request.DocumentId);

        try
        {
            // Get the document
            var document = await _documentRepository.GetByIdAsync(request.DocumentId, cancellationToken);
            if (document == null)
            {
                _logger.LogWarning("Document not found: {DocumentId}", request.DocumentId);
                throw new EntityNotFoundException("Document", request.DocumentId);
            }

            // Check if already indexed and not forcing re-index
            if (document.IsIndexed && !request.ForceReindex)
            {
                _logger.LogInformation("Document {DocumentId} is already indexed", request.DocumentId);
                return true;
            }

            // Chunk the document
            _logger.LogDebug("Chunking document: {DocumentId}", request.DocumentId);
            var chunks = await _chunkingService.ChunkDocumentAsync(
                document.Id,
                document.Content,
                cancellationToken: cancellationToken);

            var chunkList = chunks.ToList();
            _logger.LogDebug("Created {ChunkCount} chunks for document: {DocumentId}", chunkList.Count, request.DocumentId);

            // Generate embeddings for chunks
            _logger.LogDebug("Generating embeddings for {ChunkCount} chunks", chunkList.Count);
            var texts = chunkList.Select(c => c.Content).ToArray();
            var embeddings = await _embeddingService.GenerateEmbeddingsAsync(texts, cancellationToken);

            // Add embeddings to chunks
            for (int i = 0; i < chunkList.Count; i++)
            {
                chunkList[i].EmbeddingVector = embeddings[i];
            }

            // Store chunks in vector database
            _logger.LogDebug("Storing {ChunkCount} chunks in vector database", chunkList.Count);
            await _vectorService.StoreChunksAsync(chunkList, cancellationToken);

            // Update document chunks and mark as indexed
            var domainChunks = chunkList.Select(c =>
            {
                var chunk = DocumentChunk.Create(
                    c.DocumentId,
                    c.Content,
                    c.Type,
                    c.StartPosition,
                    c.EndPosition,
                    c.SequenceNumber);

                if (c.EmbeddingVector != null && c.EmbeddingVector.Length > 0)
                {
                    chunk.SetEmbedding(c.EmbeddingVector, "default");
                }

                if (c.Keywords != null && c.Keywords.Count > 0)
                {
                    chunk.AddKeywords(c.Keywords);
                }

                return chunk;
            }).ToList();

            document.AddChunks(domainChunks);
            document.MarkAsIndexed();

            // Save document
            await _documentRepository.UpdateAsync(document, cancellationToken);

            _logger.LogInformation("Document indexing completed successfully for document: {DocumentId}", request.DocumentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing document: {DocumentId}", request.DocumentId);
            throw;
        }
    }
}

/// <summary>
/// Command to provide feedback on search results
/// </summary>
public class ProvideSearchFeedbackCommand : IRequest<bool>
{
    /// <summary>
    /// Search query ID
    /// </summary>
    public Guid QueryId { get; set; }

    /// <summary>
    /// User feedback
    /// </summary>
    public UserFeedbackDto Feedback { get; set; } = null!;
}

/// <summary>
/// Handler for ProvideSearchFeedbackCommand
/// </summary>
public class ProvideSearchFeedbackCommandHandler : IRequestHandler<ProvideSearchFeedbackCommand, bool>
{
    private readonly ISearchQueryRepository _queryRepository;
    private readonly ILogger<ProvideSearchFeedbackCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the ProvideSearchFeedbackCommandHandler
    /// </summary>
    /// <param name="queryRepository">Search query repository</param>
    /// <param name="logger">Logger</param>
    public ProvideSearchFeedbackCommandHandler(
        ISearchQueryRepository queryRepository,
        ILogger<ProvideSearchFeedbackCommandHandler> logger)
    {
        _queryRepository = queryRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the ProvideSearchFeedbackCommand
    /// </summary>
    /// <param name="request">Feedback command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if feedback was recorded successfully</returns>
    public async Task<bool> Handle(ProvideSearchFeedbackCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Recording search feedback for query: {QueryId}", request.QueryId);

        try
        {
            var query = await _queryRepository.GetByIdAsync(request.QueryId, cancellationToken);
            if (query == null)
            {
                _logger.LogWarning("Search query not found: {QueryId}", request.QueryId);
                throw new EntityNotFoundException("SearchQuery", request.QueryId);
            }

            // Convert DTO to domain object
            var feedback = UserFeedback.Create(
                request.Feedback.OverallRating,
                request.Feedback.RelevanceRating,
                request.Feedback.CompletenessRating,
                request.Feedback.UsefulnessRating,
                request.Feedback.Comments);

            // Add feedback to query
            query.AddFeedback(feedback);

            // Update query
            await _queryRepository.UpdateAsync(query, cancellationToken);

            _logger.LogInformation("Search feedback recorded successfully for query: {QueryId}", request.QueryId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording search feedback for query: {QueryId}", request.QueryId);
            throw;
        }
    }
}
