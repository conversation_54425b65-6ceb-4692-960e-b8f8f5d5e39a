2025-06-26 22:07:08.571 +04:00 [FTL] LexAI AI Assistant Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 236
2025-06-26 22:07:24.158 +04:00 [FTL] LexAI AI Assistant Service failed to start
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Program.cs:line 236
2025-06-26 22:08:34.703 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 22:08:34.961 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:08:36.049 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 22:08:36.054 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 22:08:36.282 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:08:36.288 +04:00 [INF] Hosting environment: Development
2025-06-26 22:08:36.295 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 22:08:38.397 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-26 22:08:38.826 +04:00 [INF] Request GET / started with correlation ID fb963367-0cde-4770-8aed-a0fb1bef2652
2025-06-26 22:08:40.899 +04:00 [INF] Request GET / completed in 2064ms with status 404 (Correlation ID: fb963367-0cde-4770-8aed-a0fb1bef2652)
2025-06-26 22:08:40.949 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 2556.0331ms
2025-06-26 22:08:40.969 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-26 22:10:18.747 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 22:10:18.747 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 22:10:18.795 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 49a619fb-b47a-4796-ac2c-1d72ffacaf3f
2025-06-26 22:10:18.796 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 6d04f7a0-6572-4ef5-8c43-eddd5e6c4a24
2025-06-26 22:10:18.813 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:18.813 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:18.818 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 6ms with status 204 (Correlation ID: 6d04f7a0-6572-4ef5-8c43-eddd5e6c4a24)
2025-06-26 22:10:18.818 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 9ms with status 204 (Correlation ID: 49a619fb-b47a-4796-ac2c-1d72ffacaf3f)
2025-06-26 22:10:18.826 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 78.8429ms
2025-06-26 22:10:18.833 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 86.2981ms
2025-06-26 22:10:18.833 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:10:18.859 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 1bf891a7-c897-4b47-b3f6-c95a01593463
2025-06-26 22:10:18.862 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:19.019 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/23/2025 6:12:22 PM', Current time (UTC): '6/26/2025 6:10:19 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-26 22:10:19.088 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/23/2025 6:12:22 PM', Current time (UTC): '6/26/2025 6:10:19 PM'.
2025-06-26 22:10:19.091 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/23/2025 6:12:22 PM', Current time (UTC): '6/26/2025 6:10:19 PM'.
2025-06-26 22:10:19.101 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-26 22:10:19.106 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-26 22:10:19.109 +04:00 [INF] Request GET /api/chat/conversations completed in 247ms with status 401 (Correlation ID: 1bf891a7-c897-4b47-b3f6-c95a01593463)
2025-06-26 22:10:19.112 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 401 0 null 278.9065ms
2025-06-26 22:10:19.118 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:10:19.130 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 4e693abf-bc73-44fe-8892-f071fc7d4b29
2025-06-26 22:10:19.134 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:19.136 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/23/2025 6:12:22 PM', Current time (UTC): '6/26/2025 6:10:19 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-26 22:10:19.139 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/23/2025 6:12:22 PM', Current time (UTC): '6/26/2025 6:10:19 PM'.
2025-06-26 22:10:19.140 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/23/2025 6:12:22 PM', Current time (UTC): '6/26/2025 6:10:19 PM'.
2025-06-26 22:10:19.141 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-26 22:10:19.142 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-26 22:10:19.144 +04:00 [INF] Request GET /api/chat/conversations completed in 10ms with status 401 (Correlation ID: 4e693abf-bc73-44fe-8892-f071fc7d4b29)
2025-06-26 22:10:19.147 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 401 0 null 29.3516ms
2025-06-26 22:10:20.587 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:10:20.591 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID f2c224a5-9018-4bf7-a9bc-989538650c69
2025-06-26 22:10:20.593 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:20.599 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:10:20.603 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:10:20.617 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:10:21.806 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:10:23.745 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 22:10:23.757 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 22:10:24.412 +04:00 [INF] Executed DbCommand (78ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:10:24.436 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:10:24.446 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 22:10:24.461 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:10:24.462 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 3838.7835ms
2025-06-26 22:10:24.467 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 4e29c270-59cc-4dd0-8d19-01f62cdcb064
2025-06-26 22:10:24.468 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:10:24.469 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:10:24.471 +04:00 [INF] Request GET /api/chat/conversations completed in 3878ms with status 200 (Correlation ID: f2c224a5-9018-4bf7-a9bc-989538650c69)
2025-06-26 22:10:24.472 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:10:24.476 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:10:24.480 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:10:24.483 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 3895.8115ms
2025-06-26 22:10:24.483 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:10:24.541 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:10:24.551 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:10:24.556 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 22:10:24.559 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 75.915ms
2025-06-26 22:10:24.562 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:10:24.564 +04:00 [INF] Request GET /api/chat/conversations completed in 94ms with status 200 (Correlation ID: 4e29c270-59cc-4dd0-8d19-01f62cdcb064)
2025-06-26 22:10:24.568 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 106.3836ms
2025-06-26 22:11:08.370 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 22:11:08.378 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID e4ff59a1-1740-4fe1-999e-8c3d3747ea43
2025-06-26 22:11:08.381 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:11:08.383 +04:00 [INF] Request OPTIONS /api/chat/message completed in 2ms with status 204 (Correlation ID: e4ff59a1-1740-4fe1-999e-8c3d3747ea43)
2025-06-26 22:11:08.387 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 17.04ms
2025-06-26 22:11:08.389 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 45
2025-06-26 22:11:08.394 +04:00 [INF] Request POST /api/chat/message started with correlation ID 5057cb48-a950-4a75-bcd4-28ad5d0984d0
2025-06-26 22:11:08.396 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:11:08.398 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:11:08.399 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 22:11:08.403 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:11:08.407 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:11:08.472 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail au cameroun
2025-06-26 22:11:08.478 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail au cameroun
2025-06-26 22:11:08.645 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 22:11:08.665 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail au cameroun
2025-06-26 22:11:17.508 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1244, Cost: 0, Time: 7002ms
2025-06-26 22:11:17.559 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:11:17.675 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 22:11:18.484 +04:00 [WRN] Concurrency conflict saving conversation "29294898-b859-41ce-9519-aa38931bccd5". Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 239
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-26 22:11:18.666 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:11:18.676 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 22:11:18.970 +04:00 [WRN] Concurrency conflict saving conversation "29294898-b859-41ce-9519-aa38931bccd5". Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 239
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-26 22:11:19.189 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:11:19.209 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 22:11:19.478 +04:00 [WRN] Concurrency conflict saving conversation "29294898-b859-41ce-9519-aa38931bccd5". Retry attempt 3/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 239
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-26 22:11:19.504 +04:00 [ERR] Max retry attempts reached for conversation "29294898-b859-41ce-9519-aa38931bccd5"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 239
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
2025-06-26 22:11:19.807 +04:00 [ERR] Error processing message for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 239
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 212
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
2025-06-26 22:11:20.087 +04:00 [ERR] Error processing chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Data\AIAssistantDbContext.cs:line 239
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 199
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 212
   at LexAI.AIAssistant.Application.Commands.SendMessageCommandHandler.Handle(SendMessageCommand request, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Application\Commands\ChatCommands.cs:line 154
   at LexAI.AIAssistant.API.Controllers.ChatController.SendMessage(ChatRequestDto request) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API\Controllers\ChatController.cs:line 86
2025-06-26 22:11:20.098 +04:00 [INF] Executing ObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ProblemDetails'.
2025-06-26 22:11:20.109 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 11702.9999ms
2025-06-26 22:11:20.111 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 22:11:20.114 +04:00 [INF] Request POST /api/chat/message completed in 11717ms with status 500 (Correlation ID: 5057cb48-a950-4a75-bcd4-28ad5d0984d0)
2025-06-26 22:11:20.117 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 500 null application/json; charset=utf-8 11726.9316ms
2025-06-26 22:16:54.790 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 22:16:54.813 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID fc1163d6-cf58-45fb-b16b-3df3861a62c8
2025-06-26 22:16:54.821 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:16:54.824 +04:00 [INF] Request OPTIONS /api/chat/message completed in 3ms with status 204 (Correlation ID: fc1163d6-cf58-45fb-b16b-3df3861a62c8)
2025-06-26 22:16:54.830 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 40.0535ms
2025-06-26 22:16:54.833 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 43
2025-06-26 22:16:54.871 +04:00 [INF] Request POST /api/chat/message started with correlation ID 4ea39aa0-3391-4d78-b983-b2c917e67ffd
2025-06-26 22:16:54.882 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:16:54.889 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:16:54.896 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 22:16:54.915 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:16:54.922 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:16:54.943 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail en France
2025-06-26 22:16:54.946 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail en France
2025-06-26 22:16:54.971 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 22:16:54.975 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Le droit de travail en France
2025-06-26 22:17:02.605 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1042, Cost: 0, Time: 6079ms
2025-06-26 22:17:02.632 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:17:02.669 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 22:17:03.158 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken)
2025-06-26 22:17:03.247 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 22:17:03.387 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
2025-06-26 22:17:03.874 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Data.AIAssistantDbContext.SaveChangesAsync(CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken)
2025-06-26 22:17:03.901 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 22:17:04.121 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
2025-06-26 22:17:04.129 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "f562949f-5b13-4193-9449-5b162149b9d8", Tokens: 1042, Cost: 0
2025-06-26 22:17:04.136 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "f562949f-5b13-4193-9449-5b162149b9d8", Tokens: 1042, Cost: 0
2025-06-26 22:17:04.140 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 22:17:04.212 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 9290.2484ms
2025-06-26 22:17:04.217 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 22:17:04.218 +04:00 [INF] Request POST /api/chat/message completed in 9336ms with status 200 (Correlation ID: 4ea39aa0-3391-4d78-b983-b2c917e67ffd)
2025-06-26 22:17:04.222 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 9388.8954ms
2025-06-26 22:22:10.141 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 22:22:10.241 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:22:10.509 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 22:22:10.514 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 22:22:10.558 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:22:10.561 +04:00 [INF] Hosting environment: Development
2025-06-26 22:22:10.569 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 22:22:12.511 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-26 22:22:12.800 +04:00 [INF] Request GET / started with correlation ID ca9d94dd-50b6-4ed8-829c-96b5537cdc2b
2025-06-26 22:22:13.034 +04:00 [INF] Request GET / completed in 228ms with status 404 (Correlation ID: ca9d94dd-50b6-4ed8-829c-96b5537cdc2b)
2025-06-26 22:22:13.062 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 553.9214ms
2025-06-26 22:22:13.093 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-26 22:26:05.254 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 22:26:05.456 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 22:26:05.896 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 22:26:05.898 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 22:26:05.961 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 22:26:05.964 +04:00 [INF] Hosting environment: Development
2025-06-26 22:26:05.966 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 22:26:06.842 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-26 22:26:07.258 +04:00 [INF] Request GET / started with correlation ID d42f742e-1689-40cd-87ff-7a5206ddf4e1
2025-06-26 22:26:07.396 +04:00 [INF] Request GET / completed in 129ms with status 404 (Correlation ID: d42f742e-1689-40cd-87ff-7a5206ddf4e1)
2025-06-26 22:26:07.405 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 565.3862ms
2025-06-26 22:26:07.450 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-26 22:26:16.001 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 22:26:16.001 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 22:26:16.018 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 99a0eebb-1853-4d16-9fcd-5522c2d3646c
2025-06-26 22:26:16.020 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 670f54d5-85de-4a75-8004-e5e4178c42e9
2025-06-26 22:26:16.036 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:16.047 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:16.056 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 16ms with status 204 (Correlation ID: 670f54d5-85de-4a75-8004-e5e4178c42e9)
2025-06-26 22:26:16.056 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 29ms with status 204 (Correlation ID: 99a0eebb-1853-4d16-9fcd-5522c2d3646c)
2025-06-26 22:26:16.064 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 62.3201ms
2025-06-26 22:26:16.071 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 69.145ms
2025-06-26 22:26:16.072 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:26:16.104 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 6e9174b9-3755-429a-a51a-80d48c61f8a0
2025-06-26 22:26:16.113 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:16.293 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:26:16.309 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:26:16.352 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:26:16.441 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:26:19.858 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 22:26:19.895 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 22:26:21.752 +04:00 [INF] Executed DbCommand (139ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:26:22.051 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:26:22.073 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 22:26:22.115 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:26:22.117 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 5754.498ms
2025-06-26 22:26:22.121 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID ecfb2086-02a0-4820-8d76-39401d1b71c4
2025-06-26 22:26:22.125 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:26:22.129 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:22.133 +04:00 [INF] Request GET /api/chat/conversations completed in 6020ms with status 200 (Correlation ID: 6e9174b9-3755-429a-a51a-80d48c61f8a0)
2025-06-26 22:26:22.136 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:26:22.139 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:26:22.144 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:26:22.161 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 6089.0204ms
2025-06-26 22:26:22.164 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:26:22.185 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:26:22.194 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:26:22.200 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 22:26:22.204 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 39.8731ms
2025-06-26 22:26:22.206 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:26:22.208 +04:00 [INF] Request GET /api/chat/conversations completed in 79ms with status 200 (Correlation ID: ecfb2086-02a0-4820-8d76-39401d1b71c4)
2025-06-26 22:26:22.212 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 97.4468ms
2025-06-26 22:26:24.844 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - null null
2025-06-26 22:26:24.855 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 started with correlation ID 4c78eb25-2512-4006-aa6d-11920d628316
2025-06-26 22:26:24.858 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:24.861 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 completed in 3ms with status 204 (Correlation ID: 4c78eb25-2512-4006-aa6d-11920d628316)
2025-06-26 22:26:24.870 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - 204 null null 25.8408ms
2025-06-26 22:26:24.875 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - application/json null
2025-06-26 22:26:24.893 +04:00 [INF] Request GET /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 started with correlation ID b9b70d6b-74c1-4d55-907c-fbb92dd244b4
2025-06-26 22:26:24.898 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:24.901 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:26:24.910 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:26:24.926 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:26:24.934 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:26:25.194 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:26:25.230 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 22:26:25.367 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 435.7745ms
2025-06-26 22:26:25.372 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:26:25.377 +04:00 [INF] Request GET /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 completed in 479ms with status 200 (Correlation ID: b9b70d6b-74c1-4d55-907c-fbb92dd244b4)
2025-06-26 22:26:25.386 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - 200 null application/json; charset=utf-8 511.3351ms
2025-06-26 22:26:33.649 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 - null null
2025-06-26 22:26:33.658 +04:00 [INF] Request OPTIONS /api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 started with correlation ID 73d26eb1-1ca4-4d77-ba25-7150e1231148
2025-06-26 22:26:33.661 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:33.664 +04:00 [INF] Request OPTIONS /api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 completed in 2ms with status 204 (Correlation ID: 73d26eb1-1ca4-4d77-ba25-7150e1231148)
2025-06-26 22:26:33.667 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 - 204 null null 17.8527ms
2025-06-26 22:26:33.669 +04:00 [INF] Request starting HTTP/2 DELETE https://localhost:5003/api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 - application/json null
2025-06-26 22:26:33.676 +04:00 [INF] Request DELETE /api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 started with correlation ID 2ecaaa9c-e827-4924-9604-79001a44ac78
2025-06-26 22:26:33.679 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:33.683 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:26:33.685 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-26 22:26:33.695 +04:00 [INF] Route matched with {action = "DeleteConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] DeleteConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:26:33.699 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:26:33.707 +04:00 [INF] Deleting conversation "29294898-b859-41ce-9519-aa38931bccd5" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-26 22:26:33.716 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:26:33.728 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:26:33.975 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "Conversations"
WHERE "Id" = @p0 AND xmin = @p1;
2025-06-26 22:26:34.001 +04:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-26 22:26:34.016 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API) in 317.1944ms
2025-06-26 22:26:34.021 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.DeleteConversation (LexAI.AIAssistant.API)'
2025-06-26 22:26:34.023 +04:00 [INF] Request DELETE /api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 completed in 343ms with status 200 (Correlation ID: 2ecaaa9c-e827-4924-9604-79001a44ac78)
2025-06-26 22:26:34.026 +04:00 [INF] Request finished HTTP/2 DELETE https://localhost:5003/api/chat/conversations/29294898-b859-41ce-9519-aa38931bccd5 - 200 null application/json; charset=utf-8 356.7937ms
2025-06-26 22:26:44.396 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - null null
2025-06-26 22:26:44.405 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 started with correlation ID 287a1568-1d4b-4fd0-bc66-66601a895912
2025-06-26 22:26:44.408 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:44.410 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 completed in 1ms with status 204 (Correlation ID: 287a1568-1d4b-4fd0-bc66-66601a895912)
2025-06-26 22:26:44.415 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - 204 null null 18.0789ms
2025-06-26 22:26:44.416 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - application/json null
2025-06-26 22:26:44.426 +04:00 [INF] Request GET /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 started with correlation ID d51082a2-6532-4fc4-b0c0-1fe6f323d000
2025-06-26 22:26:44.429 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:26:44.430 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:26:44.432 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:26:44.433 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:26:44.437 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:26:44.445 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:26:44.450 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 22:26:44.453 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 16.2931ms
2025-06-26 22:26:44.455 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:26:44.457 +04:00 [INF] Request GET /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 completed in 28ms with status 200 (Correlation ID: d51082a2-6532-4fc4-b0c0-1fe6f323d000)
2025-06-26 22:26:44.461 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - 200 null application/json; charset=utf-8 44.475ms
2025-06-26 22:29:09.473 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages - null null
2025-06-26 22:29:09.482 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages started with correlation ID 1a218de8-9c0e-4bef-a98a-311b705ed3af
2025-06-26 22:29:09.485 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:29:09.487 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages completed in 2ms with status 204 (Correlation ID: 1a218de8-9c0e-4bef-a98a-311b705ed3af)
2025-06-26 22:29:09.495 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages - 204 null null 21.6877ms
2025-06-26 22:29:09.498 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages - application/json 42
2025-06-26 22:29:09.509 +04:00 [INF] Request POST /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages started with correlation ID 50c88832-48dd-49b3-9d4c-3932979a3b46
2025-06-26 22:29:09.513 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:29:09.516 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:29:09.518 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation (LexAI.AIAssistant.API)'
2025-06-26 22:29:09.528 +04:00 [INF] Route matched with {action = "ContinueConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] ContinueConversation(System.Guid, LexAI.AIAssistant.API.Controllers.ContinueConversationRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:29:09.532 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:29:09.580 +04:00 [INF] Continue conversation "f562949f-5b13-4193-9449-5b162149b9d8" from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La jurice prudence au Canada
2025-06-26 22:29:09.599 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La jurice prudence au Canada
2025-06-26 22:29:09.610 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:29:09.628 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La jurice prudence au Canada
2025-06-26 22:29:16.089 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 746, Cost: 0, Time: 4343ms
2025-06-26 22:30:56.576 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:32:50.681 +04:00 [INF] Executed DbCommand (23ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p4='?' (DbType = Object), @p28='?' (DbType = Guid), @p5='?' (DbType = Double), @p6='?', @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime), @p9='?', @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?', @p15='?' (DbType = Decimal), @p16='?' (DbType = Boolean), @p17='?' (DbType = Boolean), @p18='?', @p19='?', @p20='?' (DbType = Object), @p21='?' (DbType = Int32), @p22='?', @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = DateTime), @p26='?', @p27='?', @p29='?' (DbType = Object), @p53='?' (DbType = Guid), @p30='?' (DbType = Double), @p31='?', @p32='?' (DbType = Guid), @p33='?' (DbType = DateTime), @p34='?', @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = Int32), @p38='?' (DbType = Int32), @p39='?', @p40='?' (DbType = Decimal), @p41='?' (DbType = Boolean), @p42='?' (DbType = Boolean), @p43='?', @p44='?', @p45='?' (DbType = Object), @p46='?' (DbType = Int32), @p47='?', @p48='?' (DbType = Int32), @p49='?' (DbType = Int32), @p50='?' (DbType = DateTime), @p51='?', @p52='?', @p54='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "TotalTokensUsed" = @p1, "UpdatedAt" = @p2
WHERE "Id" = @p3 AND xmin = @p4
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p5, "Content" = @p6, "ConversationId" = @p7, "CreatedAt" = @p8, "CreatedBy" = @p9, "DeletedAt" = @p10, "DeletedBy" = @p11, "DetectedDomain" = @p12, "DetectedIntent" = @p13, "ErrorMessage" = @p14, "EstimatedCost" = @p15, "IsDeleted" = @p16, "IsEdited" = @p17, "MessageType" = @p18, "OriginalContent" = @p19, "ProcessingTime" = @p20, "Rating" = @p21, "Role" = @p22, "Status" = @p23, "TokensUsed" = @p24, "UpdatedAt" = @p25, "UpdatedBy" = @p26, "UserFeedback" = @p27
WHERE "Id" = @p28 AND xmin = @p29
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p30, "Content" = @p31, "ConversationId" = @p32, "CreatedAt" = @p33, "CreatedBy" = @p34, "DeletedAt" = @p35, "DeletedBy" = @p36, "DetectedDomain" = @p37, "DetectedIntent" = @p38, "ErrorMessage" = @p39, "EstimatedCost" = @p40, "IsDeleted" = @p41, "IsEdited" = @p42, "MessageType" = @p43, "OriginalContent" = @p44, "ProcessingTime" = @p45, "Rating" = @p46, "Role" = @p47, "Status" = @p48, "TokensUsed" = @p49, "UpdatedAt" = @p50, "UpdatedBy" = @p51, "UserFeedback" = @p52
WHERE "Id" = @p53 AND xmin = @p54
RETURNING xmin;
2025-06-26 22:32:50.812 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 240
2025-06-26 22:32:50.935 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 22:32:51.044 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p4='?' (DbType = Object), @p28='?' (DbType = Guid), @p5='?' (DbType = Double), @p6='?', @p7='?' (DbType = Guid), @p8='?' (DbType = DateTime), @p9='?', @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Int32), @p13='?' (DbType = Int32), @p14='?', @p15='?' (DbType = Decimal), @p16='?' (DbType = Boolean), @p17='?' (DbType = Boolean), @p18='?', @p19='?', @p20='?' (DbType = Object), @p21='?' (DbType = Int32), @p22='?', @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = DateTime), @p26='?', @p27='?', @p29='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "TotalTokensUsed" = @p1, "UpdatedAt" = @p2
WHERE "Id" = @p3 AND xmin = @p4
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p5, "Content" = @p6, "ConversationId" = @p7, "CreatedAt" = @p8, "CreatedBy" = @p9, "DeletedAt" = @p10, "DeletedBy" = @p11, "DetectedDomain" = @p12, "DetectedIntent" = @p13, "ErrorMessage" = @p14, "EstimatedCost" = @p15, "IsDeleted" = @p16, "IsEdited" = @p17, "MessageType" = @p18, "OriginalContent" = @p19, "ProcessingTime" = @p20, "Rating" = @p21, "Role" = @p22, "Status" = @p23, "TokensUsed" = @p24, "UpdatedAt" = @p25, "UpdatedBy" = @p26, "UserFeedback" = @p27
WHERE "Id" = @p28 AND xmin = @p29
RETURNING xmin;
2025-06-26 22:32:51.094 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 240
2025-06-26 22:32:51.102 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 22:32:51.407 +04:00 [INF] Executed DbCommand (90ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p4='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "TotalTokensUsed" = @p1, "UpdatedAt" = @p2
WHERE "Id" = @p3 AND xmin = @p4
RETURNING xmin;
2025-06-26 22:33:00.428 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "f562949f-5b13-4193-9449-5b162149b9d8", Tokens: 746, Cost: 0
2025-06-26 22:33:06.711 +04:00 [INF] Conversation continued successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "f562949f-5b13-4193-9449-5b162149b9d8", Tokens: 746
2025-06-26 22:33:06.719 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 22:33:06.738 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation (LexAI.AIAssistant.API) in 237205.7849ms
2025-06-26 22:33:06.741 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.ContinueConversation (LexAI.AIAssistant.API)'
2025-06-26 22:33:06.743 +04:00 [INF] Request POST /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages completed in 237229ms with status 200 (Correlation ID: 50c88832-48dd-49b3-9d4c-3932979a3b46)
2025-06-26 22:33:06.747 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8/messages - 200 null application/json; charset=utf-8 237249.5192ms
2025-06-26 22:33:52.583 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 22:33:52.611 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 8d58285b-6349-48e2-9c86-724cebdff559
2025-06-26 22:33:52.615 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:33:52.617 +04:00 [INF] Request OPTIONS /api/chat/message completed in 2ms with status 204 (Correlation ID: 8d58285b-6349-48e2-9c86-724cebdff559)
2025-06-26 22:33:52.621 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 38.4731ms
2025-06-26 22:33:52.624 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 34
2025-06-26 22:33:52.632 +04:00 [INF] Request POST /api/chat/message started with correlation ID 1fc31218-e486-4156-8838-6c52f18bb1c7
2025-06-26 22:33:52.635 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:33:52.636 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:33:52.638 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 22:33:52.645 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:33:52.649 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:33:52.695 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La justice en France
2025-06-26 22:33:52.699 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La justice en France
2025-06-26 22:33:52.789 +04:00 [INF] Executed DbCommand (46ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 22:33:52.799 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La justice en France
2025-06-26 22:34:06.072 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 744, Cost: 0, Time: 5921ms
2025-06-26 22:35:02.148 +04:00 [INF] Executed DbCommand (23ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:36:17.472 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 22:36:17.535 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 240
2025-06-26 22:36:17.547 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 22:36:17.658 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
2025-06-26 22:36:17.712 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 240
2025-06-26 22:36:17.719 +04:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 22:36:17.937 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
2025-06-26 22:36:21.876 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "3d36b4c1-764d-404c-b55d-a841d58c4c0a", Tokens: 744, Cost: 0
2025-06-26 22:36:37.369 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "3d36b4c1-764d-404c-b55d-a841d58c4c0a", Tokens: 744, Cost: 0
2025-06-26 22:36:39.248 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 22:36:42.924 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 170275.6524ms
2025-06-26 22:36:42.926 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 22:36:42.928 +04:00 [INF] Request POST /api/chat/message completed in 170293ms with status 200 (Correlation ID: 1fc31218-e486-4156-8838-6c52f18bb1c7)
2025-06-26 22:36:42.936 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 170312.2301ms
2025-06-26 22:36:56.461 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 22:36:56.466 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 22:36:56.466 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 46dcd866-f454-4912-af88-6502c92d155a
2025-06-26 22:36:56.470 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID ef8cbb8f-22d3-4497-b45e-0694c6369a48
2025-06-26 22:36:56.473 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:36:56.478 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:36:56.480 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 7ms with status 204 (Correlation ID: 46dcd866-f454-4912-af88-6502c92d155a)
2025-06-26 22:36:56.483 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 5ms with status 204 (Correlation ID: ef8cbb8f-22d3-4497-b45e-0694c6369a48)
2025-06-26 22:36:56.487 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 25.7264ms
2025-06-26 22:36:56.489 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:36:56.490 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 23.7825ms
2025-06-26 22:36:56.499 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 43f81a24-815a-488e-8b2a-c8ccf12023a4
2025-06-26 22:36:56.507 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:36:56.509 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:36:56.511 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:36:56.515 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:36:56.518 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:36:56.531 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:36:56.540 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:36:56.545 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 22:36:56.550 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 32.0733ms
2025-06-26 22:36:56.554 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:36:56.554 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 22:36:56.557 +04:00 [INF] Request GET /api/chat/conversations completed in 49ms with status 200 (Correlation ID: 43f81a24-815a-488e-8b2a-c8ccf12023a4)
2025-06-26 22:36:56.564 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID e3119e06-3238-45eb-b0fc-cf623b02639b
2025-06-26 22:36:56.567 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 77.298ms
2025-06-26 22:36:56.568 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:36:56.575 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:36:56.578 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:36:56.581 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:36:56.584 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:36:56.595 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:36:56.603 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 22:36:56.610 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 22:36:56.614 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 29.4987ms
2025-06-26 22:36:56.616 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 22:36:56.618 +04:00 [INF] Request GET /api/chat/conversations completed in 49ms with status 200 (Correlation ID: e3119e06-3238-45eb-b0fc-cf623b02639b)
2025-06-26 22:36:56.621 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 66.899ms
2025-06-26 22:37:01.131 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - null null
2025-06-26 22:37:01.138 +04:00 [INF] Request OPTIONS /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a started with correlation ID 59a14dd5-6e3e-484b-896e-96cb39c66970
2025-06-26 22:37:01.143 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:37:01.146 +04:00 [INF] Request OPTIONS /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a completed in 3ms with status 204 (Correlation ID: 59a14dd5-6e3e-484b-896e-96cb39c66970)
2025-06-26 22:37:01.152 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - 204 null null 21.4432ms
2025-06-26 22:37:01.157 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - application/json null
2025-06-26 22:37:01.175 +04:00 [INF] Request GET /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a started with correlation ID acb571c1-9584-4939-8e65-1ce381ebba0d
2025-06-26 22:37:01.181 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:37:01.186 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:37:01.189 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:37:01.193 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:37:01.201 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:37:01.219 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:37:01.234 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 22:37:01.243 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 42.351ms
2025-06-26 22:37:01.249 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:37:01.254 +04:00 [INF] Request GET /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a completed in 72ms with status 200 (Correlation ID: acb571c1-9584-4939-8e65-1ce381ebba0d)
2025-06-26 22:37:01.264 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - 200 null application/json; charset=utf-8 107.3003ms
2025-06-26 22:37:05.139 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - null null
2025-06-26 22:37:05.163 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 started with correlation ID abc30bc2-a07f-416c-9918-72976b4ec756
2025-06-26 22:37:05.170 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:37:05.175 +04:00 [INF] Request OPTIONS /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 completed in 5ms with status 204 (Correlation ID: abc30bc2-a07f-416c-9918-72976b4ec756)
2025-06-26 22:37:05.188 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - 204 null null 47.9289ms
2025-06-26 22:37:05.193 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - application/json null
2025-06-26 22:37:05.221 +04:00 [INF] Request GET /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 started with correlation ID 962ae744-bd69-404d-8b9b-66b153ebdb12
2025-06-26 22:37:05.226 +04:00 [INF] CORS policy execution successful.
2025-06-26 22:37:05.230 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 22:37:05.233 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:37:05.239 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 22:37:05.244 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 22:37:05.255 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 22:37:05.263 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 22:37:05.266 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 22.5565ms
2025-06-26 22:37:05.269 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 22:37:05.271 +04:00 [INF] Request GET /api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 completed in 45ms with status 200 (Correlation ID: 962ae744-bd69-404d-8b9b-66b153ebdb12)
2025-06-26 22:37:05.275 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/f562949f-5b13-4193-9449-5b162149b9d8 - 200 null application/json; charset=utf-8 81.9157ms
2025-06-26 23:08:26.824 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 23:08:26.881 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 23:08:26.978 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 23:08:26.981 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 23:08:26.982 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 23:08:26.983 +04:00 [INF] Hosting environment: Development
2025-06-26 23:08:26.984 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 23:09:19.028 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:09:19.028 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:09:19.153 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 3cb3cfd8-6b3e-4d8d-900a-d11988825d93
2025-06-26 23:09:19.153 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 3055858a-9527-483f-a34e-2fd11970aea1
2025-06-26 23:09:19.159 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:09:19.159 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:09:19.163 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 4ms with status 204 (Correlation ID: 3055858a-9527-483f-a34e-2fd11970aea1)
2025-06-26 23:09:19.163 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 6ms with status 204 (Correlation ID: 3cb3cfd8-6b3e-4d8d-900a-d11988825d93)
2025-06-26 23:09:19.174 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 148.5091ms
2025-06-26 23:09:19.174 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 148.5491ms
2025-06-26 23:09:19.186 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:09:19.207 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID d57ff57f-f696-4818-91c6-da472665c26e
2025-06-26 23:09:19.210 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:09:19.316 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:09:19.325 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:09:19.371 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:09:19.380 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:09:21.069 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 23:09:21.080 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 23:09:21.663 +04:00 [INF] Executed DbCommand (36ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:09:21.802 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:09:21.810 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:09:21.834 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 2455.9761ms
2025-06-26 23:09:21.836 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:09:21.839 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:09:21.843 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 047152d1-28d8-478b-b07b-cbf933e84a21
2025-06-26 23:09:21.844 +04:00 [INF] Request GET /api/chat/conversations completed in 2634ms with status 200 (Correlation ID: d57ff57f-f696-4818-91c6-da472665c26e)
2025-06-26 23:09:21.845 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:09:21.850 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:09:21.856 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:09:21.860 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:09:21.862 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 2676.1741ms
2025-06-26 23:09:21.864 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:09:21.893 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:09:21.906 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:09:21.912 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:09:21.914 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 51.3511ms
2025-06-26 23:09:21.916 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:09:21.918 +04:00 [INF] Request GET /api/chat/conversations completed in 73ms with status 200 (Correlation ID: 047152d1-28d8-478b-b07b-cbf933e84a21)
2025-06-26 23:09:21.922 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 85.9256ms
2025-06-26 23:09:58.234 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 23:09:58.241 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID bf23ff48-56f1-4877-8859-46335d790425
2025-06-26 23:09:58.245 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:09:58.250 +04:00 [INF] Request OPTIONS /api/chat/message completed in 5ms with status 204 (Correlation ID: bf23ff48-56f1-4877-8859-46335d790425)
2025-06-26 23:09:58.260 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 26.1964ms
2025-06-26 23:09:58.265 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 36
2025-06-26 23:09:58.275 +04:00 [INF] Request POST /api/chat/message started with correlation ID 836b26c9-e212-46dd-8e6d-056a57c8ec87
2025-06-26 23:09:58.282 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:09:58.294 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:09:58.305 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:09:58.326 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:09:58.345 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:09:58.542 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La verdicte populaire 
2025-06-26 23:09:58.550 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La verdicte populaire 
2025-06-26 23:09:58.786 +04:00 [INF] Executed DbCommand (20ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 23:09:58.846 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": La verdicte populaire 
2025-06-26 23:10:04.240 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 543, Cost: 0, Time: 3369ms
2025-06-26 23:10:04.311 +04:00 [INF] Executed DbCommand (12ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:10:04.553 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 23:10:04.588 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 269
2025-06-26 23:10:04.684 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:10:04.822 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
2025-06-26 23:10:04.835 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 269
2025-06-26 23:10:04.856 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:10:05.082 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
2025-06-26 23:10:05.088 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "89f78494-d0e9-4e7e-870b-06767af13d2a", Tokens: 543, Cost: 0
2025-06-26 23:10:05.094 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "89f78494-d0e9-4e7e-870b-06767af13d2a", Tokens: 543, Cost: 0
2025-06-26 23:10:05.103 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 23:10:05.198 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 6858.2706ms
2025-06-26 23:10:05.202 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:10:05.203 +04:00 [INF] Request POST /api/chat/message completed in 6921ms with status 200 (Correlation ID: 836b26c9-e212-46dd-8e6d-056a57c8ec87)
2025-06-26 23:10:05.208 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 6943.2464ms
2025-06-26 23:10:35.732 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:10:35.735 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:10:35.744 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 35575790-fca6-4e54-8f16-ec262ab99ff2
2025-06-26 23:10:35.754 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 38d8d703-5a5c-4ac1-93ef-5fb60a928b61
2025-06-26 23:10:35.761 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:35.768 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:35.770 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 9ms with status 204 (Correlation ID: 35575790-fca6-4e54-8f16-ec262ab99ff2)
2025-06-26 23:10:35.774 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 6ms with status 204 (Correlation ID: 38d8d703-5a5c-4ac1-93ef-5fb60a928b61)
2025-06-26 23:10:35.779 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 46.8725ms
2025-06-26 23:10:35.786 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:10:35.787 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 51.5856ms
2025-06-26 23:10:35.812 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID eb41f652-199f-4a4d-91bf-5a912f7649eb
2025-06-26 23:10:35.833 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:35.837 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:10:35.843 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:10:35.846 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:10:35.854 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:10:35.875 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:10:35.895 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:10:35.900 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:10:35.905 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 51.4461ms
2025-06-26 23:10:35.908 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:10:35.909 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:10:35.912 +04:00 [INF] Request GET /api/chat/conversations completed in 79ms with status 200 (Correlation ID: eb41f652-199f-4a4d-91bf-5a912f7649eb)
2025-06-26 23:10:35.921 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 685abc24-5111-415d-a79e-7fd4ba7d09b3
2025-06-26 23:10:35.926 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 140.0868ms
2025-06-26 23:10:35.928 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:35.946 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:10:35.948 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:10:35.950 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:10:35.957 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:10:35.975 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:10:36.021 +04:00 [INF] Executed DbCommand (30ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:10:36.026 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:10:36.027 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 71.1684ms
2025-06-26 23:10:36.030 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:10:36.033 +04:00 [INF] Request GET /api/chat/conversations completed in 104ms with status 200 (Correlation ID: 685abc24-5111-415d-a79e-7fd4ba7d09b3)
2025-06-26 23:10:36.036 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 127.8149ms
2025-06-26 23:10:37.900 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - null null
2025-06-26 23:10:37.909 +04:00 [INF] Request OPTIONS /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a started with correlation ID b47d82b3-a456-4bbc-ba32-dac10e7ff18e
2025-06-26 23:10:37.916 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:37.921 +04:00 [INF] Request OPTIONS /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a completed in 5ms with status 204 (Correlation ID: b47d82b3-a456-4bbc-ba32-dac10e7ff18e)
2025-06-26 23:10:37.931 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - 204 null null 30.1891ms
2025-06-26 23:10:37.935 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - application/json null
2025-06-26 23:10:37.949 +04:00 [INF] Request GET /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a started with correlation ID 70023669-a71e-4772-b620-3323bed6c68f
2025-06-26 23:10:37.956 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:37.958 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:10:37.962 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:10:37.979 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:10:37.989 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:10:38.046 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:10:38.073 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 23:10:38.120 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 130.8909ms
2025-06-26 23:10:38.124 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:10:38.127 +04:00 [INF] Request GET /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a completed in 171ms with status 200 (Correlation ID: 70023669-a71e-4772-b620-3323bed6c68f)
2025-06-26 23:10:38.132 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - 200 null application/json; charset=utf-8 196.592ms
2025-06-26 23:10:46.913 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - null null
2025-06-26 23:10:46.921 +04:00 [INF] Request OPTIONS /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a started with correlation ID 5e65f2a1-27d7-4884-9794-470b002ae004
2025-06-26 23:10:46.927 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:46.931 +04:00 [INF] Request OPTIONS /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a completed in 4ms with status 204 (Correlation ID: 5e65f2a1-27d7-4884-9794-470b002ae004)
2025-06-26 23:10:46.942 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - 204 null null 28.2432ms
2025-06-26 23:10:46.947 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - application/json null
2025-06-26 23:10:46.984 +04:00 [INF] Request GET /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a started with correlation ID 5406a07d-6b8c-47dd-a8b4-813ba9f8994b
2025-06-26 23:10:46.996 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:47.008 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:10:47.021 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:10:47.028 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:10:47.045 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:10:47.078 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:10:47.087 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 23:10:47.091 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 45.4491ms
2025-06-26 23:10:47.094 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:10:47.096 +04:00 [INF] Request GET /api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a completed in 100ms with status 200 (Correlation ID: 5406a07d-6b8c-47dd-a8b4-813ba9f8994b)
2025-06-26 23:10:47.101 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/3d36b4c1-764d-404c-b55d-a841d58c4c0a - 200 null application/json; charset=utf-8 153.6926ms
2025-06-26 23:10:57.741 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - null null
2025-06-26 23:10:57.751 +04:00 [INF] Request OPTIONS /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a started with correlation ID 7739ed05-2515-43b8-8755-0e390432703d
2025-06-26 23:10:57.758 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:57.761 +04:00 [INF] Request OPTIONS /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a completed in 3ms with status 204 (Correlation ID: 7739ed05-2515-43b8-8755-0e390432703d)
2025-06-26 23:10:57.771 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - 204 null null 29.4565ms
2025-06-26 23:10:57.777 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - application/json null
2025-06-26 23:10:57.815 +04:00 [INF] Request GET /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a started with correlation ID bd69ac00-fc1c-46f4-a5dd-dc3f20eced04
2025-06-26 23:10:57.821 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:10:57.827 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:10:57.837 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:10:57.845 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:10:57.857 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:10:57.880 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:10:57.892 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 23:10:57.898 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 41.2978ms
2025-06-26 23:10:57.903 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:10:57.907 +04:00 [INF] Request GET /api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a completed in 85ms with status 200 (Correlation ID: bd69ac00-fc1c-46f4-a5dd-dc3f20eced04)
2025-06-26 23:10:57.915 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/89f78494-d0e9-4e7e-870b-06767af13d2a - 200 null application/json; charset=utf-8 137.8411ms
2025-06-26 23:13:37.738 +04:00 [INF] Application is shutting down...
2025-06-26 23:23:17.569 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 23:23:17.608 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 23:23:17.684 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 23:23:17.686 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 23:23:17.690 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 23:23:17.692 +04:00 [INF] Hosting environment: Development
2025-06-26 23:23:17.693 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 23:26:28.420 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 23:26:28.462 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 23:26:28.556 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 23:26:28.558 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 23:26:28.559 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 23:26:28.560 +04:00 [INF] Hosting environment: Development
2025-06-26 23:26:28.561 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 23:28:03.269 +04:00 [INF] Application is shutting down...
2025-06-26 23:28:40.585 +04:00 [INF] LexAI AI Assistant Service started successfully
2025-06-26 23:28:40.782 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-26 23:28:44.176 +04:00 [INF] Now listening on: https://localhost:5003
2025-06-26 23:28:44.179 +04:00 [INF] Now listening on: http://localhost:58325
2025-06-26 23:28:44.610 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-26 23:28:44.612 +04:00 [INF] Hosting environment: Development
2025-06-26 23:28:44.614 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.API
2025-06-26 23:28:45.374 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/ - null null
2025-06-26 23:28:45.613 +04:00 [INF] Request GET / started with correlation ID 9b9b76c2-eb19-4b8f-af2a-d5254088aeb0
2025-06-26 23:28:45.914 +04:00 [INF] Request GET / completed in 293ms with status 404 (Correlation ID: 9b9b76c2-eb19-4b8f-af2a-d5254088aeb0)
2025-06-26 23:28:45.943 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/ - 404 0 null 564.3371ms
2025-06-26 23:28:45.997 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5003/, Response status code: 404
2025-06-26 23:29:09.103 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:29:09.103 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:29:11.088 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID a2970218-861e-4898-8209-746521784808
2025-06-26 23:29:11.088 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 313a9796-3523-4f2f-95ad-6064c3638bec
2025-06-26 23:29:11.120 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:11.120 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:11.132 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 25ms with status 204 (Correlation ID: a2970218-861e-4898-8209-746521784808)
2025-06-26 23:29:11.132 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 15ms with status 204 (Correlation ID: 313a9796-3523-4f2f-95ad-6064c3638bec)
2025-06-26 23:29:11.143 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 2040.1674ms
2025-06-26 23:29:11.149 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 2046.9589ms
2025-06-26 23:29:11.149 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:29:11.171 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 165a6720-6387-4480-b5ad-6cfec09d21b7
2025-06-26 23:29:11.174 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:11.374 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/26/2025 7:10:20 PM', Current time (UTC): '6/26/2025 7:29:11 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-26 23:29:11.471 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/26/2025 7:10:20 PM', Current time (UTC): '6/26/2025 7:29:11 PM'.
2025-06-26 23:29:11.476 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/26/2025 7:10:20 PM', Current time (UTC): '6/26/2025 7:29:11 PM'.
2025-06-26 23:29:11.501 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-26 23:29:11.517 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-26 23:29:11.520 +04:00 [INF] Request GET /api/chat/conversations completed in 346ms with status 401 (Correlation ID: 165a6720-6387-4480-b5ad-6cfec09d21b7)
2025-06-26 23:29:11.528 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 401 0 null 378.0339ms
2025-06-26 23:29:11.535 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:29:11.542 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 6ac2aec4-65ae-4b75-a116-48a253f34b52
2025-06-26 23:29:11.549 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:11.555 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/26/2025 7:10:20 PM', Current time (UTC): '6/26/2025 7:29:11 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-26 23:29:11.561 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/26/2025 7:10:20 PM', Current time (UTC): '6/26/2025 7:29:11 PM'.
2025-06-26 23:29:11.563 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/26/2025 7:10:20 PM', Current time (UTC): '6/26/2025 7:29:11 PM'.
2025-06-26 23:29:11.567 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-26 23:29:11.569 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-26 23:29:11.570 +04:00 [INF] Request GET /api/chat/conversations completed in 21ms with status 401 (Correlation ID: 6ac2aec4-65ae-4b75-a116-48a253f34b52)
2025-06-26 23:29:11.574 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 401 0 null 39.4401ms
2025-06-26 23:29:14.468 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:29:14.479 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 2c56cd10-6b09-45cd-acc9-7fbc9587af0d
2025-06-26 23:29:14.484 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:14.516 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:29:14.534 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:29:14.604 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:29:14.749 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:29:17.093 +04:00 [WRN] The property 'ChatSession.Context' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 23:29:17.134 +04:00 [WRN] The property 'Conversation.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-26 23:29:19.290 +04:00 [INF] Executed DbCommand (163ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:29:19.628 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:29:19.663 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:29:19.714 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:29:19.722 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 5096.9285ms
2025-06-26 23:29:19.729 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID f726fc84-fc97-4e7c-8efa-872d978a38d5
2025-06-26 23:29:19.737 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:29:19.743 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:19.748 +04:00 [INF] Request GET /api/chat/conversations completed in 5264ms with status 200 (Correlation ID: 2c56cd10-6b09-45cd-acc9-7fbc9587af0d)
2025-06-26 23:29:19.749 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:29:19.755 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:29:19.766 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:29:19.779 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:29:19.784 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 5316.5547ms
2025-06-26 23:29:19.866 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:29:19.890 +04:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:29:19.907 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:29:19.916 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 136.7559ms
2025-06-26 23:29:19.921 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:29:19.927 +04:00 [INF] Request GET /api/chat/conversations completed in 183ms with status 200 (Correlation ID: f726fc84-fc97-4e7c-8efa-872d978a38d5)
2025-06-26 23:29:19.937 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 222.4901ms
2025-06-26 23:29:48.578 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 23:29:48.599 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID a41ee527-94a2-40d1-9adc-d67639e46d15
2025-06-26 23:29:48.628 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:48.632 +04:00 [INF] Request OPTIONS /api/chat/message completed in 11ms with status 204 (Correlation ID: a41ee527-94a2-40d1-9adc-d67639e46d15)
2025-06-26 23:29:48.636 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 57.8509ms
2025-06-26 23:29:48.643 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 31
2025-06-26 23:29:48.656 +04:00 [INF] Request POST /api/chat/message started with correlation ID a2660bd1-6849-420e-b916-0108e689f80e
2025-06-26 23:29:48.663 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:29:48.667 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:29:48.673 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:29:48.699 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:29:48.712 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:29:48.927 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": L'audit financier
2025-06-26 23:29:48.941 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": L'audit financier
2025-06-26 23:30:02.525 +04:00 [INF] Executed DbCommand (10ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 23:30:02.546 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": L'audit financier
2025-06-26 23:30:08.199 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 769, Cost: 0, Time: 4133ms
2025-06-26 23:30:40.385 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:32:22.717 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 23:32:22.788 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:32:22.825 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:32:22.950 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
2025-06-26 23:32:23.003 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:32:23.011 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:32:23.223 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
2025-06-26 23:32:23.229 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:32:23.231 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "9a5e1955-ccdb-46b8-be00-d3709b2aa87b", Tokens: 769, Cost: 0
2025-06-26 23:32:23.234 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "9a5e1955-ccdb-46b8-be00-d3709b2aa87b", Tokens: 769, Cost: 0
2025-06-26 23:32:23.236 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 23:32:23.255 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 154548.7846ms
2025-06-26 23:32:23.257 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:32:23.259 +04:00 [INF] Request POST /api/chat/message completed in 154596ms with status 200 (Correlation ID: a2660bd1-6849-420e-b916-0108e689f80e)
2025-06-26 23:32:23.262 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 154618.2362ms
2025-06-26 23:36:08.010 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:36:08.052 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - null null
2025-06-26 23:36:08.075 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID ef0eee4d-362a-485e-bed1-cd40a74eb648
2025-06-26 23:36:08.080 +04:00 [INF] Request OPTIONS /api/chat/conversations started with correlation ID 5593d3aa-ff6d-4b87-9570-6f4238d12c15
2025-06-26 23:36:08.082 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:08.084 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:08.085 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 3ms with status 204 (Correlation ID: ef0eee4d-362a-485e-bed1-cd40a74eb648)
2025-06-26 23:36:08.086 +04:00 [INF] Request OPTIONS /api/chat/conversations completed in 2ms with status 204 (Correlation ID: 5593d3aa-ff6d-4b87-9570-6f4238d12c15)
2025-06-26 23:36:08.090 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 79.4089ms
2025-06-26 23:36:08.095 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 204 null null 43.1283ms
2025-06-26 23:36:08.097 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:36:08.129 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 25f92173-84af-49c3-8f3c-b415ea85ab91
2025-06-26 23:36:08.132 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:08.136 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:36:08.140 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:36:08.142 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:36:08.146 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:36:08.211 +04:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:36:08.234 +04:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:36:08.255 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:36:08.263 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 117.4208ms
2025-06-26 23:36:08.272 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - application/json null
2025-06-26 23:36:08.273 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:36:08.281 +04:00 [INF] Request GET /api/chat/conversations started with correlation ID 32041b32-710a-445e-83a8-481654b3f7c9
2025-06-26 23:36:08.285 +04:00 [INF] Request GET /api/chat/conversations completed in 153ms with status 200 (Correlation ID: 25f92173-84af-49c3-8f3c-b415ea85ab91)
2025-06-26 23:36:08.291 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:08.300 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 203.0266ms
2025-06-26 23:36:08.304 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:36:08.335 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:36:08.341 +04:00 [INF] Route matched with {action = "GetConversations", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationListResponseDto]] GetConversations(Int32, Int32) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:36:08.355 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:36:08.412 +04:00 [INF] Executed DbCommand (44ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:36:08.431 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."UserId" = @__userId_0
    ORDER BY c."UpdatedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."UpdatedAt" DESC, c0."Id"
2025-06-26 23:36:08.438 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationListResponseDto'.
2025-06-26 23:36:08.444 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API) in 90.0065ms
2025-06-26 23:36:08.449 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversations (LexAI.AIAssistant.API)'
2025-06-26 23:36:08.451 +04:00 [INF] Request GET /api/chat/conversations completed in 160ms with status 200 (Correlation ID: 32041b32-710a-445e-83a8-481654b3f7c9)
2025-06-26 23:36:08.461 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations?page=1&pageSize=50 - 200 null application/json; charset=utf-8 189.203ms
2025-06-26 23:36:18.621 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b - null null
2025-06-26 23:36:18.630 +04:00 [INF] Request OPTIONS /api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b started with correlation ID fcb4d1ee-7084-4180-9662-5d46179f9aee
2025-06-26 23:36:18.633 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:18.635 +04:00 [INF] Request OPTIONS /api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b completed in 2ms with status 204 (Correlation ID: fcb4d1ee-7084-4180-9662-5d46179f9aee)
2025-06-26 23:36:18.641 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b - 204 null null 20.4704ms
2025-06-26 23:36:18.645 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5003/api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b - application/json null
2025-06-26 23:36:18.653 +04:00 [INF] Request GET /api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b started with correlation ID ec7f4ccd-2419-44e0-a4cc-06741b3a3c51
2025-06-26 23:36:18.659 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:18.663 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:36:18.666 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:36:18.680 +04:00 [INF] Route matched with {action = "GetConversation", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.API.Controllers.ConversationDetailDto]] GetConversation(System.Guid) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:36:18.688 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:36:18.733 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:36:18.768 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.API.Controllers.ConversationDetailDto'.
2025-06-26 23:36:18.813 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API) in 124.8571ms
2025-06-26 23:36:18.817 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.GetConversation (LexAI.AIAssistant.API)'
2025-06-26 23:36:18.818 +04:00 [INF] Request GET /api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b completed in 158ms with status 200 (Correlation ID: ec7f4ccd-2419-44e0-a4cc-06741b3a3c51)
2025-06-26 23:36:18.821 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5003/api/chat/conversations/9a5e1955-ccdb-46b8-be00-d3709b2aa87b - 200 null application/json; charset=utf-8 175.9162ms
2025-06-26 23:36:45.419 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 23:36:45.429 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID c0f81e75-9aec-43e8-b4a9-a1d175593bb9
2025-06-26 23:36:45.434 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:45.437 +04:00 [INF] Request OPTIONS /api/chat/message completed in 3ms with status 204 (Correlation ID: c0f81e75-9aec-43e8-b4a9-a1d175593bb9)
2025-06-26 23:36:45.455 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 35.5163ms
2025-06-26 23:36:45.461 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 29
2025-06-26 23:36:45.497 +04:00 [INF] Request POST /api/chat/message started with correlation ID d71491cf-6890-4e0e-9938-728c90560631
2025-06-26 23:36:45.504 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:36:45.508 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:36:45.515 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:36:45.520 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:36:45.524 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:36:45.536 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Test de requete
2025-06-26 23:38:16.087 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Test de requete
2025-06-26 23:39:48.433 +04:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 23:45:11.494 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Test de requete
2025-06-26 23:45:15.161 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 266, Cost: 0, Time: 1576ms
2025-06-26 23:45:18.302 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:45:19.298 +04:00 [INF] Executed DbCommand (11ms) [Parameters=[@p5='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?', @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime), @p6='?' (DbType = Object), @p30='?' (DbType = Guid), @p7='?' (DbType = Double), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?', @p17='?' (DbType = Decimal), @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Int32), @p24='?', @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p31='?' (DbType = Object), @p55='?' (DbType = Guid), @p32='?' (DbType = Double), @p33='?', @p34='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p38='?', @p39='?' (DbType = Int32), @p40='?' (DbType = Int32), @p41='?', @p42='?' (DbType = Decimal), @p43='?' (DbType = Boolean), @p44='?' (DbType = Boolean), @p45='?', @p46='?', @p47='?' (DbType = Object), @p48='?' (DbType = Int32), @p49='?', @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (DbType = DateTime), @p53='?', @p54='?', @p56='?' (DbType = Object), @p80='?' (DbType = Guid), @p57='?' (DbType = Double), @p58='?', @p59='?' (DbType = Guid), @p60='?' (DbType = DateTime), @p61='?', @p62='?' (DbType = DateTime), @p63='?', @p64='?' (DbType = Int32), @p65='?' (DbType = Int32), @p66='?', @p67='?' (DbType = Decimal), @p68='?' (DbType = Boolean), @p69='?' (DbType = Boolean), @p70='?', @p71='?', @p72='?' (DbType = Object), @p73='?' (DbType = Int32), @p74='?', @p75='?' (DbType = Int32), @p76='?' (DbType = Int32), @p77='?' (DbType = DateTime), @p78='?', @p79='?', @p81='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "Title" = @p2, "TotalTokensUsed" = @p3, "UpdatedAt" = @p4
WHERE "Id" = @p5 AND xmin = @p6
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p7, "Content" = @p8, "ConversationId" = @p9, "CreatedAt" = @p10, "CreatedBy" = @p11, "DeletedAt" = @p12, "DeletedBy" = @p13, "DetectedDomain" = @p14, "DetectedIntent" = @p15, "ErrorMessage" = @p16, "EstimatedCost" = @p17, "IsDeleted" = @p18, "IsEdited" = @p19, "MessageType" = @p20, "OriginalContent" = @p21, "ProcessingTime" = @p22, "Rating" = @p23, "Role" = @p24, "Status" = @p25, "TokensUsed" = @p26, "UpdatedAt" = @p27, "UpdatedBy" = @p28, "UserFeedback" = @p29
WHERE "Id" = @p30 AND xmin = @p31
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p32, "Content" = @p33, "ConversationId" = @p34, "CreatedAt" = @p35, "CreatedBy" = @p36, "DeletedAt" = @p37, "DeletedBy" = @p38, "DetectedDomain" = @p39, "DetectedIntent" = @p40, "ErrorMessage" = @p41, "EstimatedCost" = @p42, "IsDeleted" = @p43, "IsEdited" = @p44, "MessageType" = @p45, "OriginalContent" = @p46, "ProcessingTime" = @p47, "Rating" = @p48, "Role" = @p49, "Status" = @p50, "TokensUsed" = @p51, "UpdatedAt" = @p52, "UpdatedBy" = @p53, "UserFeedback" = @p54
WHERE "Id" = @p55 AND xmin = @p56
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p57, "Content" = @p58, "ConversationId" = @p59, "CreatedAt" = @p60, "CreatedBy" = @p61, "DeletedAt" = @p62, "DeletedBy" = @p63, "DetectedDomain" = @p64, "DetectedIntent" = @p65, "ErrorMessage" = @p66, "EstimatedCost" = @p67, "IsDeleted" = @p68, "IsEdited" = @p69, "MessageType" = @p70, "OriginalContent" = @p71, "ProcessingTime" = @p72, "Rating" = @p73, "Role" = @p74, "Status" = @p75, "TokensUsed" = @p76, "UpdatedAt" = @p77, "UpdatedBy" = @p78, "UserFeedback" = @p79
WHERE "Id" = @p80 AND xmin = @p81
RETURNING xmin;
2025-06-26 23:45:19.395 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:45:19.405 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:45:19.524 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@p5='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?', @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime), @p6='?' (DbType = Object), @p30='?' (DbType = Guid), @p7='?' (DbType = Double), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?', @p17='?' (DbType = Decimal), @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Int32), @p24='?', @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p31='?' (DbType = Object), @p55='?' (DbType = Guid), @p32='?' (DbType = Double), @p33='?', @p34='?' (DbType = Guid), @p35='?' (DbType = DateTime), @p36='?', @p37='?' (DbType = DateTime), @p38='?', @p39='?' (DbType = Int32), @p40='?' (DbType = Int32), @p41='?', @p42='?' (DbType = Decimal), @p43='?' (DbType = Boolean), @p44='?' (DbType = Boolean), @p45='?', @p46='?', @p47='?' (DbType = Object), @p48='?' (DbType = Int32), @p49='?', @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (DbType = DateTime), @p53='?', @p54='?', @p56='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "Title" = @p2, "TotalTokensUsed" = @p3, "UpdatedAt" = @p4
WHERE "Id" = @p5 AND xmin = @p6
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p7, "Content" = @p8, "ConversationId" = @p9, "CreatedAt" = @p10, "CreatedBy" = @p11, "DeletedAt" = @p12, "DeletedBy" = @p13, "DetectedDomain" = @p14, "DetectedIntent" = @p15, "ErrorMessage" = @p16, "EstimatedCost" = @p17, "IsDeleted" = @p18, "IsEdited" = @p19, "MessageType" = @p20, "OriginalContent" = @p21, "ProcessingTime" = @p22, "Rating" = @p23, "Role" = @p24, "Status" = @p25, "TokensUsed" = @p26, "UpdatedAt" = @p27, "UpdatedBy" = @p28, "UserFeedback" = @p29
WHERE "Id" = @p30 AND xmin = @p31
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p32, "Content" = @p33, "ConversationId" = @p34, "CreatedAt" = @p35, "CreatedBy" = @p36, "DeletedAt" = @p37, "DeletedBy" = @p38, "DetectedDomain" = @p39, "DetectedIntent" = @p40, "ErrorMessage" = @p41, "EstimatedCost" = @p42, "IsDeleted" = @p43, "IsEdited" = @p44, "MessageType" = @p45, "OriginalContent" = @p46, "ProcessingTime" = @p47, "Rating" = @p48, "Role" = @p49, "Status" = @p50, "TokensUsed" = @p51, "UpdatedAt" = @p52, "UpdatedBy" = @p53, "UserFeedback" = @p54
WHERE "Id" = @p55 AND xmin = @p56
RETURNING xmin;
2025-06-26 23:45:19.567 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:45:19.575 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:45:19.793 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p5='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?', @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime), @p6='?' (DbType = Object), @p30='?' (DbType = Guid), @p7='?' (DbType = Double), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?', @p17='?' (DbType = Decimal), @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Int32), @p24='?', @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p31='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "Title" = @p2, "TotalTokensUsed" = @p3, "UpdatedAt" = @p4
WHERE "Id" = @p5 AND xmin = @p6
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p7, "Content" = @p8, "ConversationId" = @p9, "CreatedAt" = @p10, "CreatedBy" = @p11, "DeletedAt" = @p12, "DeletedBy" = @p13, "DetectedDomain" = @p14, "DetectedIntent" = @p15, "ErrorMessage" = @p16, "EstimatedCost" = @p17, "IsDeleted" = @p18, "IsEdited" = @p19, "MessageType" = @p20, "OriginalContent" = @p21, "ProcessingTime" = @p22, "Rating" = @p23, "Role" = @p24, "Status" = @p25, "TokensUsed" = @p26, "UpdatedAt" = @p27, "UpdatedBy" = @p28, "UserFeedback" = @p29
WHERE "Id" = @p30 AND xmin = @p31
RETURNING xmin;
2025-06-26 23:45:19.940 +04:00 [WRN] Concurrency conflict detected. Retry attempt 3/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:45:19.949 +04:00 [ERR] Max retry attempts reached for concurrency conflict
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:45:32.952 +04:00 [WRN] Concurrency conflict saving conversation "f289cff3-ce19-40fa-a260-98918a7589f8". Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 313
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveAsync(Conversation conversation, CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 252
2025-06-26 23:45:35.339 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:45:37.977 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@p5='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?', @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime), @p6='?' (DbType = Object), @p30='?' (DbType = Guid), @p7='?' (DbType = Double), @p8='?', @p9='?' (DbType = Guid), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?', @p17='?' (DbType = Decimal), @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Int32), @p24='?', @p25='?' (DbType = Int32), @p26='?' (DbType = Int32), @p27='?' (DbType = DateTime), @p28='?', @p29='?', @p31='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "Title" = @p2, "TotalTokensUsed" = @p3, "UpdatedAt" = @p4
WHERE "Id" = @p5 AND xmin = @p6
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p7, "Content" = @p8, "ConversationId" = @p9, "CreatedAt" = @p10, "CreatedBy" = @p11, "DeletedAt" = @p12, "DeletedBy" = @p13, "DetectedDomain" = @p14, "DetectedIntent" = @p15, "ErrorMessage" = @p16, "EstimatedCost" = @p17, "IsDeleted" = @p18, "IsEdited" = @p19, "MessageType" = @p20, "OriginalContent" = @p21, "ProcessingTime" = @p22, "Rating" = @p23, "Role" = @p24, "Status" = @p25, "TokensUsed" = @p26, "UpdatedAt" = @p27, "UpdatedBy" = @p28, "UserFeedback" = @p29
WHERE "Id" = @p30 AND xmin = @p31
RETURNING xmin;
2025-06-26 23:45:38.044 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:45:38.054 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:45:38.177 +04:00 [INF] Executed DbCommand (6ms) [Parameters=[@p5='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?', @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime), @p6='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "Title" = @p2, "TotalTokensUsed" = @p3, "UpdatedAt" = @p4
WHERE "Id" = @p5 AND xmin = @p6
RETURNING xmin;
2025-06-26 23:45:38.195 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:45:38.210 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "f289cff3-ce19-40fa-a260-98918a7589f8", Tokens: 266, Cost: 0
2025-06-26 23:45:38.221 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "f289cff3-ce19-40fa-a260-98918a7589f8", Tokens: 266, Cost: 0
2025-06-26 23:45:38.226 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 23:45:38.231 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 532707.7818ms
2025-06-26 23:45:38.234 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:45:38.236 +04:00 [INF] Request POST /api/chat/message completed in 532732ms with status 200 (Correlation ID: d71491cf-6890-4e0e-9938-728c90560631)
2025-06-26 23:45:38.238 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 532777.3723ms
2025-06-26 23:46:24.907 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5003/api/chat/message - null null
2025-06-26 23:46:24.916 +04:00 [INF] Request OPTIONS /api/chat/message started with correlation ID 3e0188f1-01fa-4dcf-aec8-eb973326e5c9
2025-06-26 23:46:24.923 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:46:24.927 +04:00 [INF] Request OPTIONS /api/chat/message completed in 3ms with status 204 (Correlation ID: 3e0188f1-01fa-4dcf-aec8-eb973326e5c9)
2025-06-26 23:46:24.939 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5003/api/chat/message - 204 null null 31.3554ms
2025-06-26 23:46:24.942 +04:00 [INF] Request starting HTTP/2 POST https://localhost:5003/api/chat/message - application/json 44
2025-06-26 23:46:24.962 +04:00 [INF] Request POST /api/chat/message started with correlation ID 9c31097c-bd9f-494d-bc93-1b4a5f947061
2025-06-26 23:46:24.964 +04:00 [INF] CORS policy execution successful.
2025-06-26 23:46:24.968 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-26 23:46:24.973 +04:00 [INF] Executing endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:46:24.984 +04:00 [INF] Route matched with {action = "SendMessage", controller = "Chat"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.AIAssistant.Application.DTOs.ChatResponseDto]] SendMessage(LexAI.AIAssistant.Application.DTOs.ChatRequestDto) on controller LexAI.AIAssistant.API.Controllers.ChatController (LexAI.AIAssistant.API).
2025-06-26 23:46:24.992 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-26 23:46:25.009 +04:00 [INF] Chat request from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Conseil en droit international
2025-06-26 23:46:31.873 +04:00 [INF] Processing message from user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Conseil en droit international
2025-06-26 23:47:06.026 +04:00 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?' (DbType = Decimal), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?', @p12='?', @p13='?', @p14='?', @p15='?', @p16='?' (DbType = Int32), @p17='?' (DbType = DateTime), @p18='?', @p19='?', @p20='?' (DbType = Guid), @p21='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Conversations" ("Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "EstimatedCost", "IsArchived", "IsDeleted", "LastActivityAt", "MessageCount", "PrimaryDomain", "SessionId", "Status", "Summary", "Tags", "Title", "TotalTokensUsed", "UpdatedAt", "UpdatedBy", "UserFeedback", "UserId", "UserRating")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21)
RETURNING xmin;
2025-06-26 23:49:18.212 +04:00 [INF] Processing enhanced chat request for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6": Conseil en droit international
2025-06-26 23:49:23.993 +04:00 [INF] Enhanced chat request processed successfully. Model: lexai-gpt-4.1-nano, Tokens: 1009, Cost: 0, Time: 4214ms
2025-06-26 23:50:26.102 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:51:22.694 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object), @p56='?' (DbType = Guid), @p33='?' (DbType = Double), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = DateTime), @p37='?', @p38='?' (DbType = DateTime), @p39='?', @p40='?' (DbType = Int32), @p41='?' (DbType = Int32), @p42='?', @p43='?' (DbType = Decimal), @p44='?' (DbType = Boolean), @p45='?' (DbType = Boolean), @p46='?', @p47='?', @p48='?' (DbType = Object), @p49='?' (DbType = Int32), @p50='?', @p51='?' (DbType = Int32), @p52='?' (DbType = Int32), @p53='?' (DbType = DateTime), @p54='?', @p55='?', @p57='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p33, "Content" = @p34, "ConversationId" = @p35, "CreatedAt" = @p36, "CreatedBy" = @p37, "DeletedAt" = @p38, "DeletedBy" = @p39, "DetectedDomain" = @p40, "DetectedIntent" = @p41, "ErrorMessage" = @p42, "EstimatedCost" = @p43, "IsDeleted" = @p44, "IsEdited" = @p45, "MessageType" = @p46, "OriginalContent" = @p47, "ProcessingTime" = @p48, "Rating" = @p49, "Role" = @p50, "Status" = @p51, "TokensUsed" = @p52, "UpdatedAt" = @p53, "UpdatedBy" = @p54, "UserFeedback" = @p55
WHERE "Id" = @p56 AND xmin = @p57
RETURNING xmin;
2025-06-26 23:51:22.750 +04:00 [WRN] Concurrency conflict detected. Retry attempt 1/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:51:22.758 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:51:22.866 +04:00 [INF] Executed DbCommand (4ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object), @p31='?' (DbType = Guid), @p8='?' (DbType = Double), @p9='?', @p10='?' (DbType = Guid), @p11='?' (DbType = DateTime), @p12='?', @p13='?' (DbType = DateTime), @p14='?', @p15='?' (DbType = Int32), @p16='?' (DbType = Int32), @p17='?', @p18='?' (DbType = Decimal), @p19='?' (DbType = Boolean), @p20='?' (DbType = Boolean), @p21='?', @p22='?', @p23='?' (DbType = Object), @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?' (DbType = Int32), @p28='?' (DbType = DateTime), @p29='?', @p30='?', @p32='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
UPDATE "Messages" SET "ConfidenceScore" = @p8, "Content" = @p9, "ConversationId" = @p10, "CreatedAt" = @p11, "CreatedBy" = @p12, "DeletedAt" = @p13, "DeletedBy" = @p14, "DetectedDomain" = @p15, "DetectedIntent" = @p16, "ErrorMessage" = @p17, "EstimatedCost" = @p18, "IsDeleted" = @p19, "IsEdited" = @p20, "MessageType" = @p21, "OriginalContent" = @p22, "ProcessingTime" = @p23, "Rating" = @p24, "Role" = @p25, "Status" = @p26, "TokensUsed" = @p27, "UpdatedAt" = @p28, "UpdatedBy" = @p29, "UserFeedback" = @p30
WHERE "Id" = @p31 AND xmin = @p32
RETURNING xmin;
2025-06-26 23:51:22.910 +04:00 [WRN] Concurrency conflict detected. Retry attempt 2/3
Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException: The database operation was expected to affect 1 row(s), but actually affected 0 row(s); data may have been modified or deleted since entities were loaded. See https://go.microsoft.com/fwlink/?LinkId=527962 for information on understanding and handling optimistic concurrency exceptions.
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.ThrowAggregateUpdateConcurrencyExceptionAsync(RelationalDataReader reader, Int32 commandIndex, Int32 expectedRowsAffected, Int32 rowsAffected, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at LexAI.AIAssistant.Infrastructure.Repositories.ConversationRepository.SaveChangesAsync(CancellationToken cancellationToken) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\AIAssistant\LexAI.AIAssistant.Infrastructure\Repositories\ConversationRepository.cs:line 302
2025-06-26 23:51:22.920 +04:00 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM "Messages" AS m
WHERE m."Id" = @__p_0
LIMIT 1
2025-06-26 23:51:23.135 +04:00 [INF] Executed DbCommand (7ms) [Parameters=[@p6='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32), @p3='?', @p4='?' (DbType = Int32), @p5='?' (DbType = DateTime), @p7='?' (DbType = Object)], CommandType='"Text"', CommandTimeout='30']
UPDATE "Conversations" SET "LastActivityAt" = @p0, "MessageCount" = @p1, "PrimaryDomain" = @p2, "Title" = @p3, "TotalTokensUsed" = @p4, "UpdatedAt" = @p5
WHERE "Id" = @p6 AND xmin = @p7
RETURNING xmin;
2025-06-26 23:51:23.146 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__conversation_Id_0='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT c0."Id", c0."CreatedAt", c0."CreatedBy", c0."DeletedAt", c0."DeletedBy", c0."EstimatedCost", c0."IsArchived", c0."IsDeleted", c0."LastActivityAt", c0."MessageCount", c0."PrimaryDomain", c0."SessionId", c0."Status", c0."Summary", c0."Tags", c0."Title", c0."TotalTokensUsed", c0."UpdatedAt", c0."UpdatedBy", c0."UserFeedback", c0."UserId", c0."UserRating", c0.xmin, m."Id", m."ConfidenceScore", m."Content", m."ConversationId", m."CreatedAt", m."CreatedBy", m."DeletedAt", m."DeletedBy", m."DetectedDomain", m."DetectedIntent", m."ErrorMessage", m."EstimatedCost", m."IsDeleted", m."IsEdited", m."MessageType", m."OriginalContent", m."ProcessingTime", m."Rating", m."Role", m."Status", m."TokensUsed", m."UpdatedAt", m."UpdatedBy", m."UserFeedback", m.xmin
FROM (
    SELECT c."Id", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."EstimatedCost", c."IsArchived", c."IsDeleted", c."LastActivityAt", c."MessageCount", c."PrimaryDomain", c."SessionId", c."Status", c."Summary", c."Tags", c."Title", c."TotalTokensUsed", c."UpdatedAt", c."UpdatedBy", c."UserFeedback", c."UserId", c."UserRating", c.xmin
    FROM "Conversations" AS c
    WHERE c."Id" = @__conversation_Id_0
    LIMIT 1
) AS c0
LEFT JOIN "Messages" AS m ON c0."Id" = m."ConversationId"
ORDER BY c0."Id"
2025-06-26 23:51:23.150 +04:00 [INF] Message processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "155885c8-b862-4c4c-a1ab-0f4e9bd12636", Tokens: 1009, Cost: 0
2025-06-26 23:51:23.152 +04:00 [INF] Chat request processed successfully for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6". Conversation: "155885c8-b862-4c4c-a1ab-0f4e9bd12636", Tokens: 1009, Cost: 0
2025-06-26 23:51:23.154 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.AIAssistant.Application.DTOs.ChatResponseDto'.
2025-06-26 23:51:23.167 +04:00 [INF] Executed action LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API) in 298175.3327ms
2025-06-26 23:51:23.169 +04:00 [INF] Executed endpoint 'LexAI.AIAssistant.API.Controllers.ChatController.SendMessage (LexAI.AIAssistant.API)'
2025-06-26 23:51:23.170 +04:00 [INF] Request POST /api/chat/message completed in 298205ms with status 200 (Correlation ID: 9c31097c-bd9f-494d-bc93-1b4a5f947061)
2025-06-26 23:51:23.173 +04:00 [INF] Request finished HTTP/2 POST https://localhost:5003/api/chat/message - 200 null application/json; charset=utf-8 298231.5176ms
