<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.DataPreprocessing.Application</name>
    </assembly>
    <members>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommand">
            <summary>
            Command to upload a document for processing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommand.Request">
            <summary>
            Upload request
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommandHandler">
            <summary>
            Handler for UploadDocumentCommand
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommandHandler.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository,LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService,LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommandHandler})">
            <summary>
            Initializes a new instance of the UploadDocumentCommandHandler
            </summary>
            <param name="documentRepository">Document repository</param>
            <param name="fileStorageService">File storage service</param>
            <param name="orchestrationAgent">Orchestration agent</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommandHandler.Handle(LexAI.DataPreprocessing.Application.Commands.UploadDocumentCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the UploadDocumentCommand
            </summary>
            <param name="request">Upload command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Upload response</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommand">
            <summary>
            Command to process a document through the pipeline
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommand.DocumentId">
            <summary>
            Document ID to process
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommand.Configuration">
            <summary>
            Processing configuration
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommand.UserId">
            <summary>
            User who initiated processing
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommandHandler">
            <summary>
            Handler for ProcessDocumentCommand
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommandHandler.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository,LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommandHandler})">
            <summary>
            Initializes a new instance of the ProcessDocumentCommandHandler
            </summary>
            <param name="documentRepository">Document repository</param>
            <param name="orchestrationAgent">Orchestration agent</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommandHandler.Handle(LexAI.DataPreprocessing.Application.Commands.ProcessDocumentCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the ProcessDocumentCommand
            </summary>
            <param name="request">Process command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommand">
            <summary>
            Command to retry failed document processing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommand.DocumentId">
            <summary>
            Document ID to retry
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommand.FromStep">
            <summary>
            Step to retry from
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommand.UserId">
            <summary>
            User who initiated retry
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommandHandler">
            <summary>
            Handler for RetryDocumentProcessingCommand
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommandHandler.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository,LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommandHandler})">
            <summary>
            Initializes a new instance of the RetryDocumentProcessingCommandHandler
            </summary>
            <param name="documentRepository">Document repository</param>
            <param name="orchestrationAgent">Orchestration agent</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommandHandler.Handle(LexAI.DataPreprocessing.Application.Commands.RetryDocumentProcessingCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the RetryDocumentProcessingCommand
            </summary>
            <param name="request">Retry command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommand">
            <summary>
            Command to cancel document processing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommand.DocumentId">
            <summary>
            Document ID to cancel
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommand.Reason">
            <summary>
            Cancellation reason
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommand.UserId">
            <summary>
            User who initiated cancellation
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommandHandler">
            <summary>
            Handler for CancelDocumentProcessingCommand
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommandHandler.#ctor(LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent,Microsoft.Extensions.Logging.ILogger{LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommandHandler})">
            <summary>
            Initializes a new instance of the CancelDocumentProcessingCommandHandler
            </summary>
            <param name="orchestrationAgent">Orchestration agent</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommandHandler.Handle(LexAI.DataPreprocessing.Application.Commands.CancelDocumentProcessingCommand,System.Threading.CancellationToken)">
            <summary>
            Handles the CancelDocumentProcessingCommand
            </summary>
            <param name="request">Cancel command</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if cancellation was successful</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto">
            <summary>
            Document upload request DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto.FileContent">
            <summary>
            File content (base64 encoded)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto.MimeType">
            <summary>
            MIME type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto.Configuration">
            <summary>
            Processing configuration
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto.UserId">
            <summary>
            User ID who uploaded the document
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadRequestDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto">
            <summary>
            Document upload response DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.FileSize">
            <summary>
            File size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.Status">
            <summary>
            Upload status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.StoragePath">
            <summary>
            Storage path
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.ProcessingStarted">
            <summary>
            Processing started
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.EstimatedProcessingTime">
            <summary>
            Estimated processing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentUploadResponseDto.UploadedAt">
            <summary>
            Upload timestamp
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto">
            <summary>
            Processing configuration DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.Chunking">
            <summary>
            Chunking configuration
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.EmbeddingModel">
            <summary>
            Embedding model to use
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.TargetDatabases">
            <summary>
            Target vector databases
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.PerformQualityAssurance">
            <summary>
            Whether to perform quality assurance
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.ExtractNamedEntities">
            <summary>
            Whether to extract named entities
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.ExtractKeywords">
            <summary>
            Whether to extract keywords
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.Priority">
            <summary>
            Processing priority
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto.CustomOptions">
            <summary>
            Custom processing options
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto">
            <summary>
            Chunking configuration DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.Strategy">
            <summary>
            Chunking strategy
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.MaxChunkSize">
            <summary>
            Maximum chunk size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.OverlapSize">
            <summary>
            Overlap size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.MinChunkSize">
            <summary>
            Minimum chunk size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.PreserveSentences">
            <summary>
            Preserve sentence boundaries
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.PreserveParagraphs">
            <summary>
            Preserve paragraph boundaries
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingConfigurationDto.CustomSeparators">
            <summary>
            Custom separators
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto">
            <summary>
            Extraction result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.ExtractedText">
            <summary>
            Extracted text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.Success">
            <summary>
            Extraction success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.ExtractionTime">
            <summary>
            Extraction time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.AgentName">
            <summary>
            Agent used for extraction
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.Confidence">
            <summary>
            Extraction confidence
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.Metadata">
            <summary>
            Document metadata extracted
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.Errors">
            <summary>
            Extraction errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ExtractionResultDto.Warnings">
            <summary>
            Extraction warnings
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto">
            <summary>
            Document metadata DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.Title">
            <summary>
            Document title
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.Author">
            <summary>
            Document author
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.Language">
            <summary>
            Document language
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.CreatedAt">
            <summary>
            Creation date
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.ModifiedAt">
            <summary>
            Modification date
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.PageCount">
            <summary>
            Page count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.WordCount">
            <summary>
            Word count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.CharacterCount">
            <summary>
            Character count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentMetadataDto.AdditionalMetadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto">
            <summary>
            Classification result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.DetectedDomain">
            <summary>
            Detected legal domain
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.Confidence">
            <summary>
            Classification confidence
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.DomainScores">
            <summary>
            All domain scores
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.Success">
            <summary>
            Classification success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.ClassificationTime">
            <summary>
            Classification time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.AgentName">
            <summary>
            Agent used for classification
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.Keywords">
            <summary>
            Extracted keywords
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.NamedEntities">
            <summary>
            Named entities
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ClassificationResultDto.Errors">
            <summary>
            Classification errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto">
            <summary>
            Named entity DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto.Text">
            <summary>
            Entity text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto.Type">
            <summary>
            Entity type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto.StartPosition">
            <summary>
            Start position
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto.EndPosition">
            <summary>
            End position
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto.Confidence">
            <summary>
            Confidence score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.NamedEntityDto.Metadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto">
            <summary>
            Chunking result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.Chunks">
            <summary>
            Created chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.Success">
            <summary>
            Chunking success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.ChunkingTime">
            <summary>
            Chunking time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.AgentName">
            <summary>
            Agent used for chunking
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.Strategy">
            <summary>
            Chunking strategy used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.TotalChunks">
            <summary>
            Total chunks created
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.AverageChunkSize">
            <summary>
            Average chunk size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.Errors">
            <summary>
            Chunking errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ChunkingResultDto.Warnings">
            <summary>
            Chunking warnings
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto">
            <summary>
            Document chunk DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.Id">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.SequenceNumber">
            <summary>
            Sequence number
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.Content">
            <summary>
            Chunk content
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.StartPosition">
            <summary>
            Start position
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.EndPosition">
            <summary>
            End position
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.TokenCount">
            <summary>
            Token count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.CharacterCount">
            <summary>
            Character count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.QualityScore">
            <summary>
            Quality score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.ImportanceScore">
            <summary>
            Importance score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.Keywords">
            <summary>
            Keywords
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.NamedEntities">
            <summary>
            Named entities
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.DomainRelevance">
            <summary>
            Domain relevance scores
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.IsVectorized">
            <summary>
            Whether vectorized
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.DocumentChunkDto.VectorId">
            <summary>
            Vector ID
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto">
            <summary>
            Vectorization result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.VectorizedChunks">
            <summary>
            Vectorized chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.Success">
            <summary>
            Vectorization success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.VectorizationTime">
            <summary>
            Vectorization time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.AgentName">
            <summary>
            Agent used for vectorization
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.EmbeddingModel">
            <summary>
            Embedding model used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.VectorDimension">
            <summary>
            Vector dimension
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.TotalTokens">
            <summary>
            Total tokens processed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizationResultDto.Errors">
            <summary>
            Vectorization errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.VectorizedChunkDto">
            <summary>
            Vectorized chunk DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizedChunkDto.ChunkId">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizedChunkDto.VectorId">
            <summary>
            Vector ID in database
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizedChunkDto.EmbeddingVector">
            <summary>
            Embedding vector
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorizedChunkDto.Metadata">
            <summary>
            Vector metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto">
            <summary>
            Vector metadata DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto.DatabaseType">
            <summary>
            Vector database type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto.Collection">
            <summary>
            Collection name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto.Dimension">
            <summary>
            Vector dimension
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto.EmbeddingModel">
            <summary>
            Embedding model
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto.Domain">
            <summary>
            Legal domain
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.VectorMetadataDto.AdditionalMetadata">
            <summary>
            Additional metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.RoutingResultDto">
            <summary>
            Routing result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutingResultDto.RoutedChunks">
            <summary>
            Routed chunks
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutingResultDto.Success">
            <summary>
            Routing success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutingResultDto.RoutingTime">
            <summary>
            Routing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutingResultDto.AgentName">
            <summary>
            Agent used for routing
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutingResultDto.Errors">
            <summary>
            Routing errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.RoutedChunkDto">
            <summary>
            Routed chunk DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutedChunkDto.ChunkId">
            <summary>
            Chunk ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutedChunkDto.TargetDatabase">
            <summary>
            Target database
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutedChunkDto.TargetCollection">
            <summary>
            Target collection
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutedChunkDto.RoutingReason">
            <summary>
            Routing reason
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.RoutedChunkDto.Confidence">
            <summary>
            Routing confidence
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto">
            <summary>
            Quality assessment DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.OverallScore">
            <summary>
            Overall quality score (0-1)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.QualityPassed">
            <summary>
            Quality passed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.AssessmentTime">
            <summary>
            Assessment time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.AgentName">
            <summary>
            Agent used for assessment
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.QualityMetrics">
            <summary>
            Quality metrics
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.Issues">
            <summary>
            Quality issues found
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityAssessmentDto.Recommendations">
            <summary>
            Recommendations
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.QualityIssueDto">
            <summary>
            Quality issue DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityIssueDto.IssueType">
            <summary>
            Issue type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityIssueDto.Description">
            <summary>
            Issue description
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityIssueDto.Severity">
            <summary>
            Issue severity
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityIssueDto.SuggestedFix">
            <summary>
            Suggested fix
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.QualityIssueDto.Location">
            <summary>
            Issue location
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto">
            <summary>
            Pipeline validation DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.ValidationPassed">
            <summary>
            Validation passed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.ValidationTime">
            <summary>
            Validation time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.AgentName">
            <summary>
            Agent used for validation
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.StepValidations">
            <summary>
            Validation results by step
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.ValidationErrors">
            <summary>
            Validation errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.ValidationWarnings">
            <summary>
            Validation warnings
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.PipelineValidationDto.PerformanceMetrics">
            <summary>
            Performance metrics
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto">
            <summary>
            Improvement suggestion DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto.SuggestionType">
            <summary>
            Suggestion type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto.Description">
            <summary>
            Suggestion description
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto.ExpectedImprovement">
            <summary>
            Expected improvement
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto.ImplementationEffort">
            <summary>
            Implementation effort
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto.Priority">
            <summary>
            Priority
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ImprovementSuggestionDto.AffectedComponents">
            <summary>
            Affected components
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto">
            <summary>
            Processing result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.Success">
            <summary>
            Processing success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.FinalStatus">
            <summary>
            Final status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.TotalProcessingTime">
            <summary>
            Total processing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.CompletedSteps">
            <summary>
            Processing steps completed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.TotalChunks">
            <summary>
            Total chunks created
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.TotalTokens">
            <summary>
            Total tokens processed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.VectorDatabasesUsed">
            <summary>
            Vector databases used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.Errors">
            <summary>
            Processing errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.Warnings">
            <summary>
            Processing warnings
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingResultDto.QualityAssessment">
            <summary>
            Quality assessment
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto">
            <summary>
            Processing step result DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto.StepName">
            <summary>
            Step name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto.Success">
            <summary>
            Step success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto.Duration">
            <summary>
            Step duration
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto.AgentName">
            <summary>
            Agent used
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto.Metadata">
            <summary>
            Step metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStepResultDto.Errors">
            <summary>
            Step errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto">
            <summary>
            Processing status DTO
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.Status">
            <summary>
            Current status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.CurrentStep">
            <summary>
            Current step
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.ProgressPercentage">
            <summary>
            Progress percentage (0-100)
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.EstimatedTimeRemaining">
            <summary>
            Estimated time remaining
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.StartedAt">
            <summary>
            Processing started at
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.LastUpdatedAt">
            <summary>
            Last updated at
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.CompletedSteps">
            <summary>
            Completed steps
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.RemainingSteps">
            <summary>
            Remaining steps
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.CurrentAgent">
            <summary>
            Current agent
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.DTOs.ProcessingStatusDto.Errors">
            <summary>
            Processing errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.DTOs.ProcessingPriority">
            <summary>
            Processing priority enumeration
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Application.DTOs.ProcessingPriority.Low">
            <summary>
            Low priority
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Application.DTOs.ProcessingPriority.Normal">
            <summary>
            Normal priority
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Application.DTOs.ProcessingPriority.High">
            <summary>
            High priority
            </summary>
        </member>
        <member name="F:LexAI.DataPreprocessing.Application.DTOs.ProcessingPriority.Critical">
            <summary>
            Critical priority
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository">
            <summary>
            Interface for document repository
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a document by ID
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.GetByFileHashAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a document by file hash
            </summary>
            <param name="fileHash">File hash</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.GetByStatusAsync(LexAI.DataPreprocessing.Domain.ValueObjects.DocumentStatus,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets documents by status
            </summary>
            <param name="status">Document status</param>
            <param name="limit">Maximum number of documents</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.GetByUserAsync(System.Guid,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets documents by user
            </summary>
            <param name="userId">User ID</param>
            <param name="limit">Maximum number of documents</param>
            <param name="offset">Offset for pagination</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Documents</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.AddAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Adds a new document
            </summary>
            <param name="document">Document to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.UpdateAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Updates a document
            </summary>
            <param name="document">Document to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.UpdateAsyncWithNewContext(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Updates a document using a new context (for background operations)
            </summary>
            <param name="document">Document to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.DeleteAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a document
            </summary>
            <param name="id">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IDocumentRepository.GetProcessingStatisticsAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Gets processing statistics
            </summary>
            <param name="fromDate">From date</param>
            <param name="toDate">To date</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing statistics</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService">
            <summary>
            Interface for file storage service
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService.StoreFileAsync(System.String,System.Byte[],System.String,System.Threading.CancellationToken)">
            <summary>
            Stores a file
            </summary>
            <param name="fileName">File name</param>
            <param name="fileContent">File content</param>
            <param name="mimeType">MIME type</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Storage path</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService.RetrieveFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieves a file
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>File content</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService.DeleteFileAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a file
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService.FileExistsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Checks if a file exists
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if file exists</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IFileStorageService.GetFileMetadataAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets file metadata
            </summary>
            <param name="storagePath">Storage path</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>File metadata</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService">
            <summary>
            Interface for vector storage service
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService.SupportedDatabases">
            <summary>
            Supported database types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService.StoreVectorsAsync(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.DocumentChunk},LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Threading.CancellationToken)">
            <summary>
            Stores vectors in the database
            </summary>
            <param name="chunks">Chunks with vectors to store</param>
            <param name="databaseType">Target database type</param>
            <param name="collection">Collection name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Storage result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService.SearchSimilarAsync(System.Single[],LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Searches for similar vectors
            </summary>
            <param name="queryVector">Query vector</param>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="limit">Maximum results</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Similar vectors</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService.DeleteVectorsAsync(System.Collections.Generic.IEnumerable{System.String},LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes vectors from the database
            </summary>
            <param name="vectorIds">Vector IDs to delete</param>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorStorageService.EnsureCollectionExistsAsync(LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType,System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Creates a collection if it doesn't exist
            </summary>
            <param name="databaseType">Database type</param>
            <param name="collection">Collection name</param>
            <param name="dimension">Vector dimension</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Task</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.ITextExtractionService">
            <summary>
            Interface for text extraction service
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ITextExtractionService.SupportedMimeTypes">
            <summary>
            Supported MIME types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.ITextExtractionService.ExtractTextAsync(System.Byte[],System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts text from a file
            </summary>
            <param name="fileContent">File content</param>
            <param name="mimeType">MIME type</param>
            <param name="fileName">File name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted text and metadata</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.ITextExtractionService.CanHandle(System.String)">
            <summary>
            Checks if the service can handle the MIME type
            </summary>
            <param name="mimeType">MIME type</param>
            <returns>True if supported</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IEmbeddingService">
            <summary>
            Interface for embedding service
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IEmbeddingService.SupportedModels">
            <summary>
            Supported embedding models
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IEmbeddingService.GenerateEmbeddingsAsync(System.Collections.Generic.IEnumerable{System.String},LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Generates embeddings for texts
            </summary>
            <param name="texts">Texts to embed</param>
            <param name="modelType">Embedding model</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embeddings</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IEmbeddingService.GetEmbeddingDimension(LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Gets the dimension of an embedding model
            </summary>
            <param name="modelType">Embedding model</param>
            <returns>Vector dimension</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IEmbeddingService.EstimateCost(System.Int32,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Estimates the cost of generating embeddings
            </summary>
            <param name="tokenCount">Number of tokens</param>
            <param name="modelType">Embedding model</param>
            <returns>Estimated cost</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IChunkingService">
            <summary>
            Interface for chunking service
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IChunkingService.SupportedStrategies">
            <summary>
            Supported chunking strategies
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IChunkingService.ChunkTextAsync(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration,System.Threading.CancellationToken)">
            <summary>
            Chunks text into smaller pieces
            </summary>
            <param name="text">Text to chunk</param>
            <param name="configuration">Chunking configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Text chunks</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IChunkingService.EstimateChunkCount(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Estimates the number of chunks
            </summary>
            <param name="text">Text to analyze</param>
            <param name="configuration">Chunking configuration</param>
            <returns>Estimated chunk count</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IClassificationService">
            <summary>
            Interface for classification service
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationService.ClassifyTextAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Classifies text into legal domains
            </summary>
            <param name="text">Text to classify</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Classification result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationService.ExtractKeywordsAsync(System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from text
            </summary>
            <param name="text">Text to analyze</param>
            <param name="maxKeywords">Maximum keywords to extract</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationService.ExtractNamedEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts named entities from text
            </summary>
            <param name="text">Text to analyze</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Named entities</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.FileMetadata">
            <summary>
            File metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.FileMetadata.Size">
            <summary>
            File size in bytes
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.FileMetadata.MimeType">
            <summary>
            MIME type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.FileMetadata.LastModified">
            <summary>
            Last modified date
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.FileMetadata.Hash">
            <summary>
            File hash
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.VectorStorageResult">
            <summary>
            Vector storage result
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorStorageResult.Success">
            <summary>
            Storage success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorStorageResult.VectorsStored">
            <summary>
            Number of vectors stored
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorStorageResult.StorageTime">
            <summary>
            Storage time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorStorageResult.VectorIds">
            <summary>
            Vector IDs assigned
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorStorageResult.Errors">
            <summary>
            Storage errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.VectorSearchResult">
            <summary>
            Vector search result
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorSearchResult.VectorId">
            <summary>
            Vector ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorSearchResult.Score">
            <summary>
            Similarity score
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.VectorSearchResult.Metadata">
            <summary>
            Associated metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.TextExtractionResult">
            <summary>
            Text extraction result
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextExtractionResult.Text">
            <summary>
            Extracted text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextExtractionResult.Success">
            <summary>
            Extraction success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextExtractionResult.Metadata">
            <summary>
            Document metadata
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextExtractionResult.Errors">
            <summary>
            Extraction errors
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult">
            <summary>
            Embedding result
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult.Text">
            <summary>
            Original text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult.Vector">
            <summary>
            Embedding vector
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult.TokenCount">
            <summary>
            Token count
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult.Success">
            <summary>
            Embedding success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult.ErrorMessage">
            <summary>
            Error message if failed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.EmbeddingResult.Embedding">
            <summary>
            Embedded
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.TextChunk">
            <summary>
            Text chunk
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextChunk.Text">
            <summary>
            Chunk text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextChunk.StartPosition">
            <summary>
            Start position in original text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextChunk.EndPosition">
            <summary>
            End position in original text
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextChunk.Type">
            <summary>
            Chunk type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.TextChunk.Metadata">
            <summary>
            Chunk metadata
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics">
            <summary>
            Processing statistics
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.DocumentId">
            <summary>
            Document ID
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.FileName">
            <summary>
            File name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.FileSize">
            <summary>
            File size
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.Status">
            <summary>
            Processing status
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.ChunkCount">
            <summary>
            Number of chunks created
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.TotalTokens">
            <summary>
            Total tokens processed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.ProcessingTime">
            <summary>
            Processing time
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.EstimatedCost">
            <summary>
            Estimated cost
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.StepCount">
            <summary>
            Number of processing steps
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.ErrorCount">
            <summary>
            Number of errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ProcessingStatistics.IsVectorized">
            <summary>
            Whether document is vectorized
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.ClassificationResult">
            <summary>
            Classification result
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ClassificationResult.DetectedDomain">
            <summary>
            Detected domain
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ClassificationResult.Confidence">
            <summary>
            Classification confidence
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ClassificationResult.DomainScores">
            <summary>
            All domain scores
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ClassificationResult.Success">
            <summary>
            Classification success
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ClassificationResult.ErrorMessage">
            <summary>
            Error message if failed
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IAgent">
            <summary>
            Base interface for all processing agents
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent">
            <summary>
            Interface for text extraction agent
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent.SupportedMimeTypes">
            <summary>
            Supported MIME types
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent.ExtractTextAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Extracts text from a document
            </summary>
            <param name="document">Document to extract text from</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extraction result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent.CanHandle(System.String)">
            <summary>
            Checks if the agent can handle the document
            </summary>
            <param name="mimeType">Document MIME type</param>
            <returns>True if agent can handle the document</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IExtractionAgent.GetExtractionConfidenceAsync(LexAI.DataPreprocessing.Domain.Entities.Document)">
            <summary>
            Gets extraction confidence for a document
            </summary>
            <param name="document">Document to evaluate</param>
            <returns>Confidence score (0-1)</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent">
            <summary>
            Interface for document classification agent
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent.ClassifyDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Classifies a document into legal domains
            </summary>
            <param name="document">Document to classify</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Classification result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent.GetDomainConfidenceAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets classification confidence for a document
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Domain confidence scores</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent.ExtractKeywordsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts keywords from document text
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Extracted keywords</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IClassificationAgent.ExtractNamedEntitiesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Extracts named entities from document text
            </summary>
            <param name="text">Document text</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Named entities</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent">
            <summary>
            Interface for document chunking agent
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent.AgentName">
            <summary>
            Agent name
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent.AgentType">
            <summary>
            Agent type
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent.SupportedStrategies">
            <summary>
            Supported chunking strategies
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent.ChunkDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration,System.Threading.CancellationToken)">
            <summary>
            Chunks a document into smaller pieces
            </summary>
            <param name="document">Document to chunk</param>
            <param name="configuration">Chunking configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Chunking result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent.EstimateChunkCount(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Estimates the number of chunks for a document
            </summary>
            <param name="text">Document text</param>
            <param name="configuration">Chunking configuration</param>
            <returns>Estimated chunk count</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IChunkingAgent.ValidateConfiguration(LexAI.DataPreprocessing.Domain.ValueObjects.ChunkingConfiguration)">
            <summary>
            Validates chunking configuration
            </summary>
            <param name="configuration">Configuration to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent">
            <summary>
            Interface for vectorization agent
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent.SupportedModels">
            <summary>
            Supported embedding models
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent.VectorizeChunksAsync(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.DocumentChunk},LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Vectorizes document chunks
            </summary>
            <param name="chunks">Chunks to vectorize</param>
            <param name="modelType">Embedding model to use</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Vectorization result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent.GenerateEmbeddingAsync(System.String,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType,System.Threading.CancellationToken)">
            <summary>
            Generates embedding for a single text
            </summary>
            <param name="text">Text to embed</param>
            <param name="modelType">Embedding model</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Embedding vector</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent.GetEmbeddingDimension(LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Gets the dimension of the embedding model
            </summary>
            <param name="modelType">Embedding model</param>
            <returns>Vector dimension</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IVectorizationAgent.EstimateVectorizationCost(System.Int32,LexAI.DataPreprocessing.Domain.ValueObjects.EmbeddingModelType)">
            <summary>
            Estimates the cost of vectorization
            </summary>
            <param name="tokenCount">Number of tokens</param>
            <param name="modelType">Embedding model</param>
            <returns>Estimated cost</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent">
            <summary>
            Interface for routing agent
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent.SupportedDatabases">
            <summary>
            Supported vector databases
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent.RouteChunksAsync(System.Collections.Generic.IEnumerable{LexAI.DataPreprocessing.Domain.Entities.DocumentChunk},System.Threading.CancellationToken)">
            <summary>
            Routes chunks to appropriate vector databases
            </summary>
            <param name="chunks">Chunks to route</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Routing result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent.GetRecommendedDatabase(LexAI.Shared.Application.DTOs.LegalDomain)">
            <summary>
            Determines the best vector database for a domain
            </summary>
            <param name="domain">Legal domain</param>
            <returns>Recommended vector database</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IRoutingAgent.GetCollectionName(LexAI.Shared.Application.DTOs.LegalDomain,LexAI.DataPreprocessing.Domain.ValueObjects.VectorDatabaseType)">
            <summary>
            Gets the collection name for a domain
            </summary>
            <param name="domain">Legal domain</param>
            <param name="databaseType">Database type</param>
            <returns>Collection name</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent">
            <summary>
            Interface for quality assurance agent
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent.AssessDocumentQualityAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Validates document quality
            </summary>
            <param name="document">Document to validate</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Quality assessment</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent.AssessChunkQualityAsync(LexAI.DataPreprocessing.Domain.Entities.DocumentChunk,System.Threading.CancellationToken)">
            <summary>
            Validates chunk quality
            </summary>
            <param name="chunk">Chunk to validate</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Quality assessment</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent.ValidatePipelineResultsAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Validates processing pipeline results
            </summary>
            <param name="document">Processed document</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Pipeline validation result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IQualityAssuranceAgent.SuggestImprovementsAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.Threading.CancellationToken)">
            <summary>
            Suggests improvements for document processing
            </summary>
            <param name="document">Document to analyze</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Improvement suggestions</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent">
            <summary>
            Interface for orchestration agent
            </summary>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent.ProcessDocumentAsync(LexAI.DataPreprocessing.Domain.Entities.Document,LexAI.DataPreprocessing.Application.DTOs.ProcessingConfigurationDto,System.Threading.CancellationToken)">
            <summary>
            Orchestrates the complete document processing pipeline
            </summary>
            <param name="document">Document to process</param>
            <param name="configuration">Processing configuration</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent.RetryProcessingAsync(LexAI.DataPreprocessing.Domain.Entities.Document,System.String,System.Threading.CancellationToken)">
            <summary>
            Retries failed processing steps
            </summary>
            <param name="document">Document to retry</param>
            <param name="fromStep">Step to retry from</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Retry result</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent.GetProcessingStatusAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets processing status for a document
            </summary>
            <param name="documentId">Document ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Processing status</returns>
        </member>
        <member name="M:LexAI.DataPreprocessing.Application.Interfaces.IOrchestrationAgent.CancelProcessingAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Cancels document processing
            </summary>
            <param name="documentId">Document ID</param>
            <param name="reason">Cancellation reason</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Cancellation result</returns>
        </member>
        <member name="T:LexAI.DataPreprocessing.Application.Interfaces.ValidationResult">
            <summary>
            Validation result
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ValidationResult.IsValid">
            <summary>
            Whether validation passed
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ValidationResult.Errors">
            <summary>
            Validation errors
            </summary>
        </member>
        <member name="P:LexAI.DataPreprocessing.Application.Interfaces.ValidationResult.Warnings">
            <summary>
            Validation warnings
            </summary>
        </member>
    </members>
</doc>
