using LexAI.LegalResearch.Application.DTOs;
using LexAI.LegalResearch.Application.Interfaces;
using LexAI.LegalResearch.Domain.ValueObjects;
using LexAI.Shared.Application.DTOs;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.LegalResearch.Infrastructure.Services;

/// <summary>
/// Service de traitement de requêtes amélioré avec LLM unifié
/// </summary>
public class EnhancedQueryProcessingService : IQueryProcessingService
{
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<EnhancedQueryProcessingService> _logger;
    private readonly IConfiguration _configuration;

    public EnhancedQueryProcessingService(
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<EnhancedQueryProcessingService> logger)
    {
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Traite et normalise la requête de recherche
    /// </summary>
    public async Task<string> ProcessQueryAsync(string query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Processing query with enhanced LLM: {Query}", query);

        if (string.IsNullOrWhiteSpace(query))
            return string.Empty;

        try
        {
            var processingPrompt = $@"Normalisez et améliorez cette requête juridique pour optimiser la recherche.
Corrigez l'orthographe, standardisez la terminologie juridique, et reformulez pour plus de clarté.
Conservez le sens original mais rendez la requête plus précise.

Requête originale: {query}

Répondez uniquement avec la requête améliorée:";

            var llmOptions = LLMServiceExtensions.ForClassification(500);
            var response = await _llmService.SendPromptAsync(processingPrompt, llmOptions, cancellationToken);

            if (response.Success && !string.IsNullOrWhiteSpace(response.Content))
            {
                var processedQuery = response.Content.Trim().Trim('"');
                _logger.LogDebug("Query processed: '{Original}' -> '{Processed}'", query, processedQuery);
                return processedQuery;
            }

            // Fallback vers le traitement simple
            return ProcessQuerySimple(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing query with LLM, falling back to simple processing");
            return ProcessQuerySimple(query);
        }
    }

    /// <summary>
    /// Détecte l'intention de la requête
    /// </summary>
    public async Task<QueryIntent> DetectIntentAsync(string query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Detecting intent for query: {Query}", query);

        if (string.IsNullOrWhiteSpace(query))
            return QueryIntent.General;

        try
        {
            var intentPrompt = $@"Analysez cette requête juridique et déterminez l'intention principale.
Répondez uniquement avec l'un de ces mots-clés:
- Definition: recherche de définition ou explication
- Procedure: recherche de procédure ou démarche
- Jurisprudence: recherche de jurisprudence ou précédents
- Legislation: recherche de lois ou règlements
- Document: recherche de modèles de documents
- General: recherche générale

Requête: {query}

Intention:";

            var llmOptions = LLMServiceExtensions.ForClassification(100);
            var response = await _llmService.SendPromptAsync(intentPrompt, llmOptions, cancellationToken);

            if (response.Success && !string.IsNullOrWhiteSpace(response.Content))
            {
                var intentText = response.Content.Trim().ToLowerInvariant();
                var intent = intentText switch
                {
                    "definition" => QueryIntent.Definition,
                    "procedure" => QueryIntent.Procedure,
                    "jurisprudence" => QueryIntent.Jurisprudence,
                    "legislation" => QueryIntent.Legislation,
                    "document" => QueryIntent.DocumentTemplate,
                    _ => QueryIntent.General
                };

                _logger.LogDebug("Detected intent: {Intent} for query: {Query}", intent, query);
                return intent;
            }

            // Fallback vers la détection simple
            return DetectIntentSimple(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting intent with LLM, falling back to simple detection");
            return DetectIntentSimple(query);
        }
    }

    /// <summary>
    /// Extrait les entités juridiques de la requête
    /// </summary>
    public async Task<IEnumerable<LegalEntityDto>> ExtractEntitiesAsync(string query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Extracting entities from query: {Query}", query);

        if (string.IsNullOrWhiteSpace(query))
            return new List<LegalEntityDto>();

        try
        {
            var entityPrompt = $@"Extrayez les entités juridiques importantes de cette requête.
Identifiez les personnes, organisations, lois, articles, dates, montants, etc.
Répondez en JSON avec ce format:
{{
  ""entities"": [
    {{
      ""text"": ""texte de l'entité"",
      ""type"": ""PERSON|ORGANIZATION|LAW|ARTICLE|DATE|AMOUNT|LOCATION|OTHER"",
      ""startPosition"": 0,
      ""endPosition"": 10,
      ""confidence"": 0.95
    }}
  ]
}}

Requête: {query}";

            var llmOptions = LLMServiceExtensions.ForClassification(1000);
            var entityResponse = await _llmService.AnalyzeStructuredAsync<EntityExtractionResult>(
                query, entityPrompt, llmOptions, cancellationToken);

            if (entityResponse?.Entities != null)
            {
                var entities = entityResponse.Entities.Select(e => new LegalEntityDto
                {
                    Text = e.Text,
                    Type = Enum.TryParse<LegalEntityType>(e.Type, true, out var entityType) ? entityType : LegalEntityType.Other,
                    StartPosition = e.StartPosition,
                    EndPosition = e.EndPosition,
                    Confidence = e.Confidence
                }).ToList();

                _logger.LogDebug("Extracted {Count} entities from query", entities.Count);
                return entities;
            }

            // Fallback vers l'extraction simple
            return ExtractEntitiesSimple(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting entities with LLM, falling back to simple extraction");
            return ExtractEntitiesSimple(query);
        }
    }

    /// <summary>
    /// Expanse la requête avec des termes associés
    /// </summary>
    public async Task<IEnumerable<string>> ExpandQueryAsync(string query, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Expanding query: {Query}", query);

        if (string.IsNullOrWhiteSpace(query))
            return new List<string>();

        try
        {
            var expansionPrompt = $@"Générez des termes et expressions juridiques associés à cette requête.
Incluez des synonymes, termes techniques, expressions latines, et concepts connexes.
Limitez-vous aux termes les plus pertinents (maximum 10).

Requête: {query}

Termes associés (un par ligne):";

            var llmOptions = LLMServiceExtensions.ForClassification(800);
            var response = await _llmService.SendPromptAsync(expansionPrompt, llmOptions, cancellationToken);

            if (response.Success && !string.IsNullOrWhiteSpace(response.Content))
            {
                var expandedTerms = response.Content
                    .Split('\n', StringSplitOptions.RemoveEmptyEntries)
                    .Select(term => term.Trim().Trim('-', '*', '•').Trim())
                    .Where(term => !string.IsNullOrWhiteSpace(term) && term.Length > 2)
                    .Take(10)
                    .ToList();

                _logger.LogDebug("Expanded query with {Count} terms", expandedTerms.Count);
                return expandedTerms;
            }

            // Fallback vers l'expansion simple
            return ExpandQuerySimple(query);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error expanding query with LLM, falling back to simple expansion");
            return ExpandQuerySimple(query);
        }
    }

    /// <summary>
    /// Génère des suggestions de recherche
    /// </summary>
    public async Task<IEnumerable<string>> GenerateSearchSuggestionsAsync(string partialQuery, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Generating search suggestions for: {PartialQuery}", partialQuery);

        if (string.IsNullOrWhiteSpace(partialQuery) || partialQuery.Length < 3)
            return new List<string>();

        try
        {
            var suggestionPrompt = $@"Générez des suggestions de recherche juridique basées sur ce début de requête.
Proposez des requêtes complètes et pertinentes que l'utilisateur pourrait vouloir rechercher.
Limitez-vous à 5 suggestions maximum.

Début de requête: {partialQuery}

Suggestions (une par ligne):";

            var llmOptions = LLMServiceExtensions.ForClassification(600);
            var response = await _llmService.SendPromptAsync(suggestionPrompt, llmOptions, cancellationToken);

            if (response.Success && !string.IsNullOrWhiteSpace(response.Content))
            {
                var suggestions = response.Content
                    .Split('\n', StringSplitOptions.RemoveEmptyEntries)
                    .Select(suggestion => suggestion.Trim().Trim('-', '*', '•').Trim())
                    .Where(suggestion => !string.IsNullOrWhiteSpace(suggestion) && suggestion.Length > partialQuery.Length)
                    .Take(5)
                    .ToList();

                _logger.LogDebug("Generated {Count} search suggestions", suggestions.Count);
                return suggestions;
            }

            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating search suggestions with LLM");
            return new List<string>();
        }
    }

    // Classes pour l'analyse structurée
    private class EntityExtractionResult
    {
        public List<ExtractedEntity> Entities { get; set; } = new();
    }

    private class ExtractedEntity
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public double Confidence { get; set; }
    }

    // Méthodes de fallback simples
    private string ProcessQuerySimple(string query)
    {
        // Traitement simple : normalisation de base
        return query.Trim()
            .Replace("  ", " ")
            .ToLowerInvariant();
    }

    private QueryIntent DetectIntentSimple(string query)
    {
        var lowerQuery = query.ToLowerInvariant();
        
        if (lowerQuery.Contains("définition") || lowerQuery.Contains("qu'est-ce que"))
            return QueryIntent.Definition;
        if (lowerQuery.Contains("procédure") || lowerQuery.Contains("comment"))
            return QueryIntent.Procedure;
        if (lowerQuery.Contains("jurisprudence") || lowerQuery.Contains("arrêt"))
            return QueryIntent.Jurisprudence;
        if (lowerQuery.Contains("loi") || lowerQuery.Contains("article"))
            return QueryIntent.Legislation;
        if (lowerQuery.Contains("modèle") || lowerQuery.Contains("template"))
            return QueryIntent.DocumentTemplate;
        
        return QueryIntent.General;
    }

    private IEnumerable<LegalEntityDto> ExtractEntitiesSimple(string query)
    {
        var entities = new List<LegalEntityDto>();
        var words = query.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var position = 0;

        foreach (var word in words)
        {
            if (word.Length > 3 && char.IsUpper(word[0]))
            {
                entities.Add(new LegalEntityDto
                {
                    Text = word,
                    Type = LegalEntityType.Other,
                    StartPosition = position,
                    EndPosition = position + word.Length,
                    Confidence = 0.6
                });
            }
            position += word.Length + 1;
        }

        return entities.Take(5);
    }

    private IEnumerable<string> ExpandQuerySimple(string query)
    {
        // Expansion simple basée sur des mots-clés
        var expansions = new List<string>();
        var lowerQuery = query.ToLowerInvariant();

        if (lowerQuery.Contains("contrat"))
            expansions.AddRange(new[] { "accord", "convention", "engagement" });
        if (lowerQuery.Contains("responsabilité"))
            expansions.AddRange(new[] { "faute", "dommage", "réparation" });
        if (lowerQuery.Contains("travail"))
            expansions.AddRange(new[] { "emploi", "salarié", "employeur" });

        return expansions.Take(5);
    }
}
