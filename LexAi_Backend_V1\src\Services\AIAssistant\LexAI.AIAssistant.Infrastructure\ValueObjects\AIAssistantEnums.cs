namespace LexAI.AIAssistant.Domain.ValueObjects;

/// <summary>
/// Message role enumeration
/// </summary>
public enum MessageRole
{
    /// <summary>
    /// Message from user
    /// </summary>
    User,

    /// <summary>
    /// Message from AI assistant
    /// </summary>
    Assistant,

    /// <summary>
    /// System message
    /// </summary>
    System
}


/// <summary>
/// Message status enumeration
/// </summary>
public enum MessageStatus
{
    /// <summary>
    /// Message is being processed
    /// </summary>
    Processing,

    /// <summary>
    /// Message has been sent
    /// </summary>
    Sent,

    /// <summary>
    /// Message processing failed
    /// </summary>
    Failed,

    /// <summary>
    /// Message was cancelled
    /// </summary>
    Cancelled
}

/// <summary>
/// Conversation status enumeration
/// </summary>
public enum ConversationStatus
{
    /// <summary>
    /// Conversation is active
    /// </summary>
    Active,

    /// <summary>
    /// Conversation is closed
    /// </summary>
    Closed,

    /// <summary>
    /// Conversation is archived
    /// </summary>
    Archived,

    /// <summary>
    /// Conversation is deleted
    /// </summary>
    Deleted
}


/// <summary>
/// AI model type enumeration
/// </summary>
public enum AIModelType
{
    /// <summary>
    /// GPT-4 model
    /// </summary>
    GPT4,

    /// <summary>
    /// GPT-4 Turbo model
    /// </summary>
    GPT4Turbo,

    /// <summary>
    /// GPT-3.5 Turbo model
    /// </summary>
    GPT35Turbo,

    /// <summary>
    /// Claude model
    /// </summary>
    Claude,

    /// <summary>
    /// Custom legal model
    /// </summary>
    CustomLegal
}


/// <summary>
/// Citation type enumeration
/// </summary>
public enum CitationType
{
    /// <summary>
    /// Legal document citation
    /// </summary>
    LegalDocument,

    /// <summary>
    /// Case law citation
    /// </summary>
    CaseLaw,

    /// <summary>
    /// Statute citation
    /// </summary>
    Statute,

    /// <summary>
    /// Regulation citation
    /// </summary>
    Regulation,

    /// <summary>
    /// Academic source citation
    /// </summary>
    Academic,

    /// <summary>
    /// News article citation
    /// </summary>
    News,

    /// <summary>
    /// Website citation
    /// </summary>
    Website,

    /// <summary>
    /// Other citation type
    /// </summary>
    Other
}

/// <summary>
/// Attachment type enumeration
/// </summary>
public enum AttachmentType
{
    /// <summary>
    /// PDF document
    /// </summary>
    PDF,

    /// <summary>
    /// Word document
    /// </summary>
    Word,

    /// <summary>
    /// Text file
    /// </summary>
    Text,

    /// <summary>
    /// Image file
    /// </summary>
    Image,

    /// <summary>
    /// Spreadsheet
    /// </summary>
    Spreadsheet,

    /// <summary>
    /// Other file type
    /// </summary>
    Other
}

/// <summary>
/// Conversation mode enumeration
/// </summary>
public enum ConversationMode
{
    /// <summary>
    /// Standard conversation mode
    /// </summary>
    Standard,

    /// <summary>
    /// Research mode with enhanced search
    /// </summary>
    Research,

    /// <summary>
    /// Document analysis mode
    /// </summary>
    DocumentAnalysis,

    /// <summary>
    /// Legal advice mode
    /// </summary>
    LegalAdvice,

    /// <summary>
    /// Quick question mode
    /// </summary>
    QuickQuestion
}
