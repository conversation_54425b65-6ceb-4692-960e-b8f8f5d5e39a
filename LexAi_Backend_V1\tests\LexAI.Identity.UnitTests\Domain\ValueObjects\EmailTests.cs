using FluentAssertions;
using LexAI.Identity.Domain.ValueObjects;
using Xunit;

namespace LexAI.Identity.UnitTests.Domain.ValueObjects;

/// <summary>
/// Unit tests for Email value object
/// </summary>
public class EmailTests
{
    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("email@***************")] // IP address
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Create_WithValidEmail_ShouldCreateEmailObject(string validEmail)
    {
        // Act
        var email = Email.Create(validEmail);

        // Assert
        email.Should().NotBeNull();
        email.Value.Should().Be(validEmail.ToLowerInvariant());
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    [InlineData("invalid-email")]
    [InlineData("@example.com")]
    [InlineData("test@")]
    [InlineData("<EMAIL>")]
    [InlineData("test@example")]
    [InlineData("test@.com")]
    [InlineData("test@example.")]
    public void Create_WithInvalidEmail_ShouldThrowArgumentException(string invalidEmail)
    {
        // Act & Assert
        var action = () => Email.Create(invalidEmail);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Create_WithTooLongEmail_ShouldThrowArgumentException()
    {
        // Arrange
        var longEmail = new string('a', 250) + "@example.com"; // Over 254 characters

        // Act & Assert
        var action = () => Email.Create(longEmail);
        action.Should().Throw<ArgumentException>()
            .WithMessage("*too long*");
    }

    [Fact]
    public void Create_ShouldNormalizeEmailToLowerCase()
    {
        // Arrange
        var mixedCaseEmail = "<EMAIL>";

        // Act
        var email = Email.Create(mixedCaseEmail);

        // Assert
        email.Value.Should().Be("<EMAIL>");
    }

    [Fact]
    public void GetDomain_ShouldReturnCorrectDomain()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");

        // Act
        var domain = email.GetDomain();

        // Assert
        domain.Should().Be("example.com");
    }

    [Fact]
    public void GetLocalPart_ShouldReturnCorrectLocalPart()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");

        // Act
        var localPart = email.GetLocalPart();

        // Assert
        localPart.Should().Be("test.user");
    }

    [Theory]
    [InlineData("<EMAIL>", "example.com", true)]
    [InlineData("<EMAIL>", "EXAMPLE.COM", true)]
    [InlineData("<EMAIL>", "other.com", false)]
    [InlineData("<EMAIL>", "", false)]
    [InlineData("<EMAIL>", null, false)]
    public void BelongsToDomain_ShouldReturnCorrectResult(string emailAddress, string domain, bool expected)
    {
        // Arrange
        var email = Email.Create(emailAddress);

        // Act
        var result = email.BelongsToDomain(domain);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void Equals_WithSameEmail_ShouldReturnTrue()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.Should().Be(email2);
        (email1 == email2).Should().BeTrue();
        (email1 != email2).Should().BeFalse();
    }

    [Fact]
    public void Equals_WithDifferentEmail_ShouldReturnFalse()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.Should().NotBe(email2);
        (email1 == email2).Should().BeFalse();
        (email1 != email2).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_WithSameEmail_ShouldReturnSameHashCode()
    {
        // Arrange
        var email1 = Email.Create("<EMAIL>");
        var email2 = Email.Create("<EMAIL>");

        // Act & Assert
        email1.GetHashCode().Should().Be(email2.GetHashCode());
    }

    [Fact]
    public void ImplicitConversion_ToString_ShouldReturnEmailValue()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");

        // Act
        string emailString = email;

        // Assert
        emailString.Should().Be("<EMAIL>");
    }

    [Fact]
    public void ToString_ShouldReturnEmailValue()
    {
        // Arrange
        var email = Email.Create("<EMAIL>");

        // Act
        var result = email.ToString();

        // Assert
        result.Should().Be("<EMAIL>");
    }
}
