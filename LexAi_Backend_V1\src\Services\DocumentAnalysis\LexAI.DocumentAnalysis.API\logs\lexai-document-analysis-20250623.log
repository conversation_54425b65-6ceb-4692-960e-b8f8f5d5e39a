2025-06-23 21:10:34.228 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-23 21:10:34.566 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 21:10:35.454 +04:00 [INF] Now listening on: https://localhost:5004
2025-06-23 21:10:35.459 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-23 21:10:35.569 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 21:10:35.571 +04:00 [INF] Hosting environment: Development
2025-06-23 21:10:35.581 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-23 21:10:36.427 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/ - null null
2025-06-23 21:10:36.819 +04:00 [INF] Request GET / started with correlation ID d7a5b781-0ea3-4093-be64-f3a236b806f3
2025-06-23 21:10:39.208 +04:00 [INF] Request GET / completed in 2375ms with status 404 (Correlation ID: d7a5b781-0ea3-4093-be64-f3a236b806f3)
2025-06-23 21:10:39.221 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/ - 404 0 null 2796.1745ms
2025-06-23 21:10:39.252 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:5004/, Response status code: 404
2025-06-23 21:12:18.696 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-23 21:12:18.765 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 9a4c58c9-32d0-484e-982c-da40e83aa5af
2025-06-23 21:12:18.799 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:12:18.809 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 17ms with status 204 (Correlation ID: 9a4c58c9-32d0-484e-982c-da40e83aa5af)
2025-06-23 21:12:18.817 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 120.8081ms
2025-06-23 21:12:18.825 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-23 21:12:18.845 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 03e93557-f7a4-46bd-873e-383ae40013d6
2025-06-23 21:12:18.851 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:12:19.145 +04:00 [INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 7:26:05 PM', Current time (UTC): '6/23/2025 5:12:19 PM'.
   at Microsoft.IdentityModel.Tokens.ValidatorUtilities.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(Nullable`1 notBefore, Nullable`1 expires, SecurityToken securityToken, TokenValidationParameters validationParameters)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
2025-06-23 21:12:19.258 +04:00 [WRN] JWT Authentication failed: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 7:26:05 PM', Current time (UTC): '6/23/2025 5:12:19 PM'.
2025-06-23 21:12:19.265 +04:00 [INF] Bearer was not authenticated. Failure message: IDX10223: Lifetime validation failed. The token is expired. ValidTo (UTC): '6/19/2025 7:26:05 PM', Current time (UTC): '6/23/2025 5:12:19 PM'.
2025-06-23 21:12:19.281 +04:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-23 21:12:19.297 +04:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-06-23 21:12:19.301 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 451ms with status 401 (Correlation ID: 03e93557-f7a4-46bd-873e-383ae40013d6)
2025-06-23 21:12:19.315 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 401 0 null 490.1028ms
2025-06-23 21:12:22.482 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-23 21:12:22.497 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID 3697415a-9d8a-4765-bf00-7f88c5a8a932
2025-06-23 21:12:22.503 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:12:22.532 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:12:22.541 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-23 21:12:22.609 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-23 21:12:23.787 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-23 21:12:25.187 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:12:28.004 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-23 21:12:28.096 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:12:28.112 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:12:29.439 +04:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 21:12:29.489 +04:00 [WRN] The property 'ClauseAnalysis.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.497 +04:00 [WRN] The property 'DocumentAnalysisResult.FinancialTerms' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.501 +04:00 [WRN] The property 'DocumentAnalysisResult.ImportantDates' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.505 +04:00 [WRN] The property 'DocumentAnalysisResult.KeyPoints' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.507 +04:00 [WRN] The property 'DocumentAnalysisResult.MainParties' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.512 +04:00 [WRN] The property 'DocumentRecommendation.RelatedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.515 +04:00 [WRN] The property 'ExtractedEntity.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:29.518 +04:00 [WRN] The property 'RiskAssessment.AffectedClauses' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-06-23 21:12:30.962 +04:00 [INF] Executed DbCommand (132ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-23 21:12:31.337 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-23 21:12:31.458 +04:00 [INF] Executed DbCommand (24ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-23 21:12:31.885 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:12:31.900 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-23 21:12:31.944 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 9320.7802ms
2025-06-23 21:12:31.949 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-23 21:12:31.955 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 9452ms with status 200 (Correlation ID: 3697415a-9d8a-4765-bf00-7f88c5a8a932)
2025-06-23 21:12:31.978 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 9496.1308ms
2025-06-23 21:15:24.005 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-23 21:15:24.017 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID fa8fa6a1-dd23-404a-93ba-ac88c7f44f32
2025-06-23 21:15:24.023 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:15:24.025 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: fa8fa6a1-dd23-404a-93ba-ac88c7f44f32)
2025-06-23 21:15:24.030 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 25.8482ms
2025-06-23 21:15:24.034 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-23 21:15:24.042 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID c0e442be-243f-40c2-99ae-755d9f22ae8d
2025-06-23 21:15:24.049 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:15:24.051 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:15:24.058 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-23 21:15:24.061 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-23 21:15:24.069 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-23 21:15:24.071 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:15:24.074 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-23 21:15:24.122 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:15:24.129 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:15:24.206 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-23 21:15:24.218 +04:00 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-23 21:15:24.227 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:15:24.231 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-23 21:15:24.236 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 168.722ms
2025-06-23 21:15:24.239 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-23 21:15:24.244 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 195ms with status 200 (Correlation ID: c0e442be-243f-40c2-99ae-755d9f22ae8d)
2025-06-23 21:15:24.250 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 216.4454ms
2025-06-23 21:15:46.156 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - null null
2025-06-23 21:15:46.167 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID 7b75034e-019b-4273-819b-f9fd983876a3
2025-06-23 21:15:46.173 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:15:46.175 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 2ms with status 204 (Correlation ID: 7b75034e-019b-4273-819b-f9fd983876a3)
2025-06-23 21:15:46.186 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 204 null null 30.552ms
2025-06-23 21:15:46.189 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - application/json null
2025-06-23 21:15:46.200 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 started with correlation ID e4e21b58-cd02-4aae-b7c3-193ddc899e57
2025-06-23 21:15:46.203 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:15:46.204 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:15:46.205 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-23 21:15:46.215 +04:00 [INF] Route matched with {action = "GetAnalysisResult", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto]] GetAnalysisResult(System.Guid, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-23 21:15:46.223 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-23 21:15:46.226 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:15:46.236 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-23 21:15:46.241 +04:00 [INF] Retrieving analysis "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:15:46.250 +04:00 [INF] Retrieving analysis result: "8974a3b1-2c68-4cb0-a081-7954e2010a82" for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:15:46.431 +04:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-23 21:15:46.634 +04:00 [INF] Executed DbCommand (47ms) [Parameters=[@__id_0='8974a3b1-2c68-4cb0-a081-7954e2010a82', @__userId_1='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT d2."Id", d2."AnalysisContent", d2."AnalyzedAt", d2."ConfidenceScore", d2."CreatedAt", d2."CreatedBy", d2."DeletedAt", d2."DeletedBy", d2."DocumentHash", d2."DocumentName", d2."DocumentPurpose", d2."DocumentStoragePath", d2."DocumentType", d2."EstimatedCost", d2."ExecutiveSummary", d2."ExtractedText", d2."FinancialTerms", d2."ImportantDates", d2."IsDeleted", d2."KeyPoints", d2."MainParties", d2."ModelUsed", d2."OverallRiskLevel", d2."ProcessingTimeMs", d2."Status", d2."TokensUsed", d2."UpdatedAt", d2."UpdatedBy", d2."UserId", c."Id", c."Analysis", c."ClauseText", c."ClauseType", c."ConfidenceScore", c."CreatedAt", c."CreatedBy", c."DeletedAt", c."DeletedBy", c."DocumentAnalysisResultId", c."EndPosition", c."IsDeleted", c."RiskLevel", c."StartPosition", c."SuggestedRevision", c."Tags", c."UpdatedAt", c."UpdatedBy", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy", e."Id", e."ConfidenceScore", e."CreatedAt", e."CreatedBy", e."DeletedAt", e."DeletedBy", e."DocumentAnalysisResultId", e."EndPosition", e."IsDeleted", e."Metadata", e."NormalizedValue", e."StartPosition", e."Text", e."Type", e."UpdatedAt", e."UpdatedBy", d1."Id", d1."Context", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentAnalysisResultId", d1."IsDeleted", d1."Reference", d1."RelevanceScore", d1."Source", d1."Title", d1."Type", d1."UpdatedAt", d1."UpdatedBy", d1."Url"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."Id" = @__id_0 AND d."UserId" = @__userId_1
    LIMIT 1
) AS d2
LEFT JOIN clause_analyses AS c ON d2."Id" = c."DocumentAnalysisResultId"
LEFT JOIN risk_assessments AS r ON d2."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d2."Id" = d0."DocumentAnalysisResultId"
LEFT JOIN extracted_entities AS e ON d2."Id" = e."DocumentAnalysisResultId"
LEFT JOIN document_citations AS d1 ON d2."Id" = d1."DocumentAnalysisResultId"
ORDER BY d2."Id", c."Id", r."Id", d0."Id", e."Id"
2025-06-23 21:15:47.138 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisResponseDto'.
2025-06-23 21:15:47.207 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API) in 987.823ms
2025-06-23 21:15:47.211 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetAnalysisResult (LexAI.DocumentAnalysis.API)'
2025-06-23 21:15:47.213 +04:00 [INF] Request GET /api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 completed in 1010ms with status 200 (Correlation ID: e4e21b58-cd02-4aae-b7c3-193ddc899e57)
2025-06-23 21:15:47.217 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis/8974a3b1-2c68-4cb0-a081-7954e2010a82 - 200 null application/json; charset=utf-8 1027.5781ms
2025-06-23 21:25:21.770 +04:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - null null
2025-06-23 21:25:21.780 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis started with correlation ID 9d7445aa-2193-4cf7-bdb8-618a6defab71
2025-06-23 21:25:21.784 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:25:21.786 +04:00 [INF] Request OPTIONS /api/v1/DocumentAnalysis completed in 1ms with status 204 (Correlation ID: 9d7445aa-2193-4cf7-bdb8-618a6defab71)
2025-06-23 21:25:21.790 +04:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 204 null null 19.7833ms
2025-06-23 21:25:21.796 +04:00 [INF] Request starting HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - application/json null
2025-06-23 21:25:21.815 +04:00 [INF] Request GET /api/v1/DocumentAnalysis started with correlation ID efbee265-e798-436c-b1d6-613725a8969b
2025-06-23 21:25:21.823 +04:00 [INF] CORS policy execution successful.
2025-06-23 21:25:21.830 +04:00 [INF] JWT Token validated for user: Kevin Lawyer
2025-06-23 21:25:21.837 +04:00 [INF] Executing endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-23 21:25:21.847 +04:00 [INF] Route matched with {action = "GetUserAnalyses", controller = "DocumentAnalysis"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto]] GetUserAnalyses(LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListRequestDto, System.Threading.CancellationToken) on controller LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController (LexAI.DocumentAnalysis.API).
2025-06-23 21:25:21.864 +04:00 [INF] Azure Document Intelligence client initialized successfully
2025-06-23 21:25:21.870 +04:00 [INF] Configured Azure OpenAI LLM with deployment: lexai-gpt-4.1-nano at https://lexai-az-openai.openai.azure.com/
2025-06-23 21:25:21.880 +04:00 [INF] Document storage initialized at: ./storage/documents
2025-06-23 21:25:21.887 +04:00 [INF] Retrieving analyses list for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:25:21.891 +04:00 [INF] Retrieving analyses for user: "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:25:21.924 +04:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6'], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM document_analysis_results AS d
WHERE d."UserId" = @__userId_0
2025-06-23 21:25:21.945 +04:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='ebafe5e7-5450-4d33-9568-eb2a874ea6c6', @__p_2='20', @__p_1='0'], CommandType='"Text"', CommandTimeout='30']
SELECT d1."Id", d1."AnalysisContent", d1."AnalyzedAt", d1."ConfidenceScore", d1."CreatedAt", d1."CreatedBy", d1."DeletedAt", d1."DeletedBy", d1."DocumentHash", d1."DocumentName", d1."DocumentPurpose", d1."DocumentStoragePath", d1."DocumentType", d1."EstimatedCost", d1."ExecutiveSummary", d1."ExtractedText", d1."FinancialTerms", d1."ImportantDates", d1."IsDeleted", d1."KeyPoints", d1."MainParties", d1."ModelUsed", d1."OverallRiskLevel", d1."ProcessingTimeMs", d1."Status", d1."TokensUsed", d1."UpdatedAt", d1."UpdatedBy", d1."UserId", r."Id", r."AffectedClauses", r."CreatedAt", r."CreatedBy", r."DeletedAt", r."DeletedBy", r."Description", r."DocumentAnalysisResultId", r."Impact", r."IsDeleted", r."Mitigation", r."Probability", r."RiskType", r."Severity", r."UpdatedAt", r."UpdatedBy", d0."Id", d0."CreatedAt", d0."CreatedBy", d0."DeletedAt", d0."DeletedBy", d0."Description", d0."DocumentAnalysisResultId", d0."IsDeleted", d0."LegalBasis", d0."Priority", d0."RelatedClauses", d0."SuggestedAction", d0."Title", d0."Type", d0."UpdatedAt", d0."UpdatedBy"
FROM (
    SELECT d."Id", d."AnalysisContent", d."AnalyzedAt", d."ConfidenceScore", d."CreatedAt", d."CreatedBy", d."DeletedAt", d."DeletedBy", d."DocumentHash", d."DocumentName", d."DocumentPurpose", d."DocumentStoragePath", d."DocumentType", d."EstimatedCost", d."ExecutiveSummary", d."ExtractedText", d."FinancialTerms", d."ImportantDates", d."IsDeleted", d."KeyPoints", d."MainParties", d."ModelUsed", d."OverallRiskLevel", d."ProcessingTimeMs", d."Status", d."TokensUsed", d."UpdatedAt", d."UpdatedBy", d."UserId"
    FROM document_analysis_results AS d
    WHERE d."UserId" = @__userId_0
    ORDER BY d."AnalyzedAt" DESC
    LIMIT @__p_2 OFFSET @__p_1
) AS d1
LEFT JOIN risk_assessments AS r ON d1."Id" = r."DocumentAnalysisResultId"
LEFT JOIN document_recommendations AS d0 ON d1."Id" = d0."DocumentAnalysisResultId"
ORDER BY d1."AnalyzedAt" DESC, d1."Id", r."Id"
2025-06-23 21:25:21.956 +04:00 [INF] Retrieved 1 analyses for user "ebafe5e7-5450-4d33-9568-eb2a874ea6c6"
2025-06-23 21:25:21.960 +04:00 [INF] Executing OkObjectResult, writing value of type 'LexAI.DocumentAnalysis.Application.DTOs.DocumentAnalysisListResponseDto'.
2025-06-23 21:25:21.964 +04:00 [INF] Executed action LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API) in 104.0836ms
2025-06-23 21:25:21.967 +04:00 [INF] Executed endpoint 'LexAI.DocumentAnalysis.API.Controllers.DocumentAnalysisController.GetUserAnalyses (LexAI.DocumentAnalysis.API)'
2025-06-23 21:25:21.971 +04:00 [INF] Request GET /api/v1/DocumentAnalysis completed in 148ms with status 200 (Correlation ID: efbee265-e798-436c-b1d6-613725a8969b)
2025-06-23 21:25:21.978 +04:00 [INF] Request finished HTTP/2 GET https://localhost:5004/api/v1/DocumentAnalysis?page=1&pageSize=20&sortBy=analyzedAt&sortDescending=true - 200 null application/json; charset=utf-8 181.7409ms
