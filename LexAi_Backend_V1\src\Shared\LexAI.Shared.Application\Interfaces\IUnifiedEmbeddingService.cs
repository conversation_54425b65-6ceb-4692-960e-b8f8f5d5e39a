﻿using LexAI.Shared.Application.DTOs.Embedding;

namespace LexAI.Shared.Application.Interfaces;

/// <summary>
/// Interface pour le service d'embedding unifié
/// </summary>
public interface IUnifiedEmbeddingService
{
    /// <summary>
    /// Génère un embedding pour un texte
    /// </summary>
    Task<EmbeddingResult> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default);

    /// <summary>
    /// Génère des embeddings pour plusieurs textes
    /// </summary>
    Task<BatchEmbeddingResult> GenerateBatchEmbeddingsAsync(IEnumerable<string> texts, CancellationToken cancellationToken = default);

    /// <summary>
    /// Vérifie si le service d'embedding est disponible
    /// </summary>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtient les informations sur le service d'embedding
    /// </summary>
    EmbeddingServiceInfo GetServiceInfo();
}
