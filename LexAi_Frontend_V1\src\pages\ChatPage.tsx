import React, { useState, useRef, useEffect } from 'react'
import { Send, Plus, MessageSquare, Bot, User } from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { useChatStore } from '../store/chatStore'
import { cn } from '../lib/utils'

export function ChatPage() {
  const [message, setMessage] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const {
    sessions,
    currentSession,
    isLoading,
    createSession,
    selectSession,
    deleteSession,
    sendMessage,
    clearCurrentSession
  } = useChatStore()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [currentSession?.messages])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!message.trim() || isLoading) return

    if (!currentSession) {
      createSession()
    }

    await sendMessage(message.trim())
    setMessage('')
  }

  const handleNewChat = () => {
    createSession()
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex">
      {/* Sidebar avec les sessions */}
      <div className="w-80 border-r bg-white">
        <div className="p-4 border-b">
          <Button onClick={handleNewChat} className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle conversation
          </Button>
        </div>
        
        <div className="overflow-y-auto h-full pb-20">
          {sessions.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Aucune conversation</p>
            </div>
          ) : (
            <div className="p-2 space-y-2">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className={cn(
                    "p-3 rounded-lg cursor-pointer transition-colors",
                    currentSession?.id === session.id
                      ? "bg-blue-50 border border-blue-200"
                      : "hover:bg-gray-50"
                  )}
                  onClick={() => selectSession(session.id)}
                >
                  <h3 className="font-medium text-sm truncate">
                    {session.title}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {session.messages.length} messages
                  </p>
                  <p className="text-xs text-gray-400">
                    {formatTime(session.updatedAt)}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Zone de chat principale */}
      <div className="flex-1 flex flex-col">
        {currentSession ? (
          <>
            {/* En-tête du chat */}
            <div className="p-4 border-b bg-white">
              <h2 className="font-semibold text-lg">{currentSession.title}</h2>
              <p className="text-sm text-gray-500">
                Assistant juridique IA • {currentSession.messages.length} messages
              </p>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {currentSession.messages.length === 0 ? (
                <div className="text-center py-12">
                  <Bot className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Bonjour ! Je suis votre assistant juridique IA
                  </h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    Posez-moi vos questions juridiques, demandez-moi d'analyser des documents 
                    ou de vous aider dans vos recherches. Je suis là pour vous assister !
                  </p>
                </div>
              ) : (
                currentSession.messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={cn(
                      "flex",
                      msg.role === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[70%] rounded-lg p-3",
                        msg.role === 'user'
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-900"
                      )}
                    >
                      <div className="flex items-start space-x-2">
                        {msg.role === 'assistant' && (
                          <Bot className="h-5 w-5 mt-0.5 flex-shrink-0" />
                        )}
                        {msg.role === 'user' && (
                          <User className="h-5 w-5 mt-0.5 flex-shrink-0" />
                        )}
                        <div className="flex-1">
                          <p className="text-sm whitespace-pre-wrap">
                            {msg.content}
                          </p>
                          <p className={cn(
                            "text-xs mt-1",
                            msg.role === 'user' ? "text-blue-100" : "text-gray-500"
                          )}>
                            {formatTime(msg.timestamp)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 rounded-lg p-3 max-w-[70%]">
                    <div className="flex items-center space-x-2">
                      <Bot className="h-5 w-5" />
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Zone de saisie */}
            <div className="p-4 border-t bg-white">
              <form onSubmit={handleSendMessage} className="flex space-x-2">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Tapez votre question juridique..."
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button
                  type="submit"
                  disabled={!message.trim() || isLoading}
                  size="icon"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </form>
              <p className="text-xs text-gray-500 mt-2">
                L'IA peut faire des erreurs. Vérifiez les informations importantes.
              </p>
            </div>
          </>
        ) : (
          /* État vide - pas de session sélectionnée */
          <div className="flex-1 flex items-center justify-center">
            <Card className="max-w-md">
              <CardHeader className="text-center">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <CardTitle>Assistant Juridique IA</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 mb-4">
                  Commencez une nouvelle conversation pour poser vos questions juridiques
                  et obtenir une assistance personnalisée.
                </p>
                <Button onClick={handleNewChat} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Démarrer une conversation
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
