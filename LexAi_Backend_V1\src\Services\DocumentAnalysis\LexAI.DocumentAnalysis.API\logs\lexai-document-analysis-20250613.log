2025-06-13 02:01:22.195 +04:00 [FTL] LexAI Document Analysis Service failed to start
System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationManager.AddSource(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.ConfigurationManager.Microsoft.Extensions.Configuration.IConfigurationBuilder.Add(IConfigurationSource source)
   at Microsoft.Extensions.Configuration.JsonConfigurationExtensions.AddJsonFile(IConfigurationBuilder builder, String path, Boolean optional, Boolean reloadOnChange)
   at Program.<Main>$(String[] args) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 32
2025-06-13 02:02:22.501 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-13 02:02:22.543 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 02:02:22.718 +04:00 [INF] Now listening on: https://localhost:51405
2025-06-13 02:02:22.719 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-13 02:02:22.722 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 02:02:22.723 +04:00 [INF] Hosting environment: Development
2025-06-13 02:02:22.724 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-13 02:02:48.008 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/api/v1/health - null null
2025-06-13 02:02:48.159 +04:00 [INF] Request GET /api/v1/health started with correlation ID c55d3d51-83cd-4728-9d8d-3be9b1f52100
2025-06-13 02:02:48.205 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentException: IDX10703: Cannot create a 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey', key length is zero.
   at Microsoft.IdentityModel.Tokens.SymmetricSecurityKey..ctor(Byte[] key)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__1(JwtBearerOptions options) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 104
   at Microsoft.Extensions.Options.OptionsFactory`1.Create(String name)
   at System.Lazy`1.ViaFactory(LazyThreadSafetyMode mode)
   at System.Lazy`1.ExecutionAndPublication(LazyHelper executionAndPublication, Boolean useDefaultConstructor)
   at System.Lazy`1.CreateValue()
   at Microsoft.Extensions.Options.OptionsCache`1.GetOrAdd[TArg](String name, Func`3 createOptions, TArg factoryArgument)
   at Microsoft.Extensions.Options.OptionsMonitor`1.Get(String name)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.InitializeAsync(AuthenticationScheme scheme, HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandlerProvider.GetHandlerAsync(HttpContext context, String authenticationScheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_10>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 259
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__9>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 244
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-13 02:02:48.300 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/api/v1/health - 500 null text/html; charset=utf-8 301.5847ms
2025-06-13 02:02:48.594 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/favicon.ico - null null
2025-06-13 02:02:48.610 +04:00 [INF] Request GET /favicon.ico started with correlation ID 81b526de-6540-4a5d-aee2-df6b3efec99f
2025-06-13 02:02:48.617 +04:00 [ERR] An unhandled exception has occurred while executing the request.
System.ArgumentException: IDX10703: Cannot create a 'Microsoft.IdentityModel.Tokens.SymmetricSecurityKey', key length is zero.
   at Microsoft.IdentityModel.Tokens.SymmetricSecurityKey..ctor(Byte[] key)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__1(JwtBearerOptions options) in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 104
   at Microsoft.Extensions.Options.OptionsFactory`1.Create(String name)
   at System.Lazy`1.ViaFactory(LazyThreadSafetyMode mode)
--- End of stack trace from previous location ---
   at System.Lazy`1.CreateValue()
   at Microsoft.Extensions.Options.OptionsCache`1.GetOrAdd[TArg](String name, Func`3 createOptions, TArg factoryArgument)
   at Microsoft.Extensions.Options.OptionsMonitor`1.Get(String name)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.InitializeAsync(AuthenticationScheme scheme, HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationHandlerProvider.GetHandlerAsync(HttpContext context, String authenticationScheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_10>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 259
--- End of stack trace from previous location ---
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__9>d.MoveNext() in D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API\Program.cs:line 244
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-13 02:02:48.656 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/favicon.ico - 500 null text/plain; charset=utf-8 62.0195ms
2025-06-13 02:13:08.631 +04:00 [INF] LexAI Document Analysis Service started successfully
2025-06-13 02:13:08.695 +04:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 02:13:08.798 +04:00 [INF] Now listening on: https://localhost:51405
2025-06-13 02:13:08.799 +04:00 [INF] Now listening on: http://localhost:51406
2025-06-13 02:13:08.800 +04:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 02:13:08.801 +04:00 [INF] Hosting environment: Development
2025-06-13 02:13:08.802 +04:00 [INF] Content root path: D:\Projects\AI\LexIA\V1\LexAi_Backend_V1\src\Services\DocumentAnalysis\LexAI.DocumentAnalysis.API
2025-06-13 02:13:27.445 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/health - null null
2025-06-13 02:13:27.520 +04:00 [INF] Request GET /health started with correlation ID 7a26298d-cfc7-4034-b081-c27ec89c8b69
2025-06-13 02:13:27.584 +04:00 [INF] Executing endpoint 'Health checks'
2025-06-13 02:13:27.596 +04:00 [INF] Executed endpoint 'Health checks'
2025-06-13 02:13:27.598 +04:00 [INF] Request GET /health completed in 68ms with status 200 (Correlation ID: 7a26298d-cfc7-4034-b081-c27ec89c8b69)
2025-06-13 02:13:27.610 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/health - 200 null text/plain 168.8664ms
2025-06-13 02:13:27.676 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/favicon.ico - null null
2025-06-13 02:13:27.691 +04:00 [INF] Request GET /favicon.ico started with correlation ID c766d694-d986-46e4-9950-9cec8a1bf538
2025-06-13 02:13:27.697 +04:00 [INF] Request GET /favicon.ico completed in 2ms with status 404 (Correlation ID: c766d694-d986-46e4-9950-9cec8a1bf538)
2025-06-13 02:13:27.704 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/favicon.ico - 404 0 null 27.015ms
2025-06-13 02:13:27.710 +04:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:51405/favicon.ico, Response status code: 404
2025-06-13 02:13:33.790 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger - null null
2025-06-13 02:13:34.851 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger - 301 0 null 1060.3964ms
2025-06-13 02:13:34.870 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/index.html - null null
2025-06-13 02:13:35.034 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/index.html - 200 null text/html;charset=utf-8 164.5048ms
2025-06-13 02:13:35.102 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/swagger-ui.css - null null
2025-06-13 02:13:35.113 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/index.css - null null
2025-06-13 02:13:35.113 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/swagger-ui-bundle.js - null null
2025-06-13 02:13:35.113 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/swagger-ui-standalone-preset.js - null null
2025-06-13 02:13:35.113 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/index.js - null null
2025-06-13 02:13:35.281 +04:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-13 02:13:35.640 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/swagger-ui.css - 200 152035 text/css 537.1668ms
2025-06-13 02:13:35.605 +04:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-13 02:13:35.635 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/index.js - 200 null application/javascript;charset=utf-8 521.8083ms
2025-06-13 02:13:35.563 +04:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-06-13 02:13:35.662 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/swagger-ui-standalone-preset.js - 200 230007 text/javascript 548.363ms
2025-06-13 02:13:35.664 +04:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-13 02:13:35.684 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/index.css - 200 202 text/css 571.5218ms
2025-06-13 02:13:35.694 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/swagger-ui-bundle.js - 200 1426001 text/javascript 581.3764ms
2025-06-13 02:13:36.073 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/v1/swagger.json - null null
2025-06-13 02:13:36.119 +04:00 [INF] Request starting HTTP/2 GET https://localhost:51405/swagger/favicon-32x32.png - null null
2025-06-13 02:13:36.169 +04:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-13 02:13:36.171 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/favicon-32x32.png - 200 628 image/png 52.0723ms
2025-06-13 02:13:36.414 +04:00 [INF] Request finished HTTP/2 GET https://localhost:51405/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 340.5027ms
