﻿
namespace LexAI.Shared.Application.DTOs.Embedding
{
    /// <summary>
    /// Métriques d'embedding
    /// </summary>
    public class EmbeddingMetrics
    {
        public int TotalEmbeddings { get; set; }
        public int SuccessfulEmbeddings { get; set; }
        public int FailedEmbeddings { get; set; }
        public double AverageProcessingTime { get; set; }
        public decimal TotalCost { get; set; }
        public int TotalTokensUsed { get; set; }
        public Dictionary<string, int> ProviderUsage { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

}
