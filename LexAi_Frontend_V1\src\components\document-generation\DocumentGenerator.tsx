import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/Card'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { FileText, Download, Clock, Copy, Check } from 'lucide-react'
import { chatApi } from '../../services/api'

interface DocumentGeneratorProps {
  onDocumentGenerated?: (document: any) => void
}

export const DocumentGenerator: React.FC<DocumentGeneratorProps> = ({ onDocumentGenerated }) => {
  const [documentType, setDocumentType] = useState('')
  const [requirements, setRequirements] = useState('')
  const [template, setTemplate] = useState('')
  const [jurisdiction, setJurisdiction] = useState('France')
  const [parameters, setParameters] = useState<Record<string, any>>({})
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedDocument, setGeneratedDocument] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)

  const documentTypes = [
    { value: 'contract', label: 'Contrat' },
    { value: 'letter', label: 'Lettre' },
    { value: 'legal_notice', label: 'Mise en demeure' },
    { value: 'employment_contract', label: 'Contrat de travail' },
    { value: 'lease_agreement', label: 'Contrat de bail' },
    { value: 'nda', label: 'Accord de confidentialité' },
    { value: 'partnership_agreement', label: 'Accord de partenariat' },
    { value: 'terms_of_service', label: 'Conditions générales' },
    { value: 'privacy_policy', label: 'Politique de confidentialité' },
    { value: 'court_filing', label: 'Acte de procédure' },
    { value: 'power_of_attorney', label: 'Procuration' },
    { value: 'other', label: 'Autre' },
  ]

  const templates = [
    { value: '', label: 'Génération libre' },
    { value: 'standard', label: 'Modèle standard' },
    { value: 'detailed', label: 'Modèle détaillé' },
    { value: 'simple', label: 'Modèle simplifié' },
  ]

  const jurisdictions = [
    'France',
    'Belgique',
    'Suisse',
    'Canada (Québec)',
    'Luxembourg',
    'Monaco',
  ]

  const handleParameterChange = (key: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleGenerate = async () => {
    if (!documentType || !requirements) {
      setError('Veuillez remplir le type de document et les exigences')
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      const response = await chatApi.generateDocument({
        documentType,
        requirements,
        template: template || undefined,
        jurisdiction,
        parameters: Object.keys(parameters).length > 0 ? parameters : undefined,
      })

      setGeneratedDocument(response)
      onDocumentGenerated?.(response)
    } catch (err: any) {
      setError(err.message || 'Erreur lors de la génération du document')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopy = async () => {
    if (generatedDocument?.generatedContent) {
      try {
        await navigator.clipboard.writeText(generatedDocument.generatedContent)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Erreur lors de la copie:', err)
      }
    }
  }

  const handleDownload = () => {
    if (generatedDocument?.generatedContent) {
      const blob = new Blob([generatedDocument.generatedContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${documentType}_${new Date().toISOString().split('T')[0]}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Configuration de génération */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Générateur de Documents Juridiques
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Type de document */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type de document *
            </label>
            <select
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Sélectionner un type de document</option>
              {documentTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Exigences */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Exigences et spécifications *
            </label>
            <textarea
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder="Décrivez en détail ce que doit contenir le document, les clauses spécifiques, les parties impliquées, etc."
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Modèle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Modèle de base
            </label>
            <select
              value={template}
              onChange={(e) => setTemplate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {templates.map((tmpl) => (
                <option key={tmpl.value} value={tmpl.value}>
                  {tmpl.label}
                </option>
              ))}
            </select>
          </div>

          {/* Juridiction */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Juridiction
            </label>
            <select
              value={jurisdiction}
              onChange={(e) => setJurisdiction(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {jurisdictions.map((j) => (
                <option key={j} value={j}>
                  {j}
                </option>
              ))}
            </select>
          </div>

          {/* Paramètres spécifiques selon le type */}
          {documentType === 'contract' && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Paramètres du contrat</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Input
                  placeholder="Partie 1"
                  value={parameters.party1 || ''}
                  onChange={(e) => handleParameterChange('party1', e.target.value)}
                />
                <Input
                  placeholder="Partie 2"
                  value={parameters.party2 || ''}
                  onChange={(e) => handleParameterChange('party2', e.target.value)}
                />
                <Input
                  placeholder="Durée"
                  value={parameters.duration || ''}
                  onChange={(e) => handleParameterChange('duration', e.target.value)}
                />
                <Input
                  placeholder="Montant"
                  value={parameters.amount || ''}
                  onChange={(e) => handleParameterChange('amount', e.target.value)}
                />
              </div>
            </div>
          )}

          {documentType === 'employment_contract' && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Paramètres du contrat de travail</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Input
                  placeholder="Employeur"
                  value={parameters.employer || ''}
                  onChange={(e) => handleParameterChange('employer', e.target.value)}
                />
                <Input
                  placeholder="Employé"
                  value={parameters.employee || ''}
                  onChange={(e) => handleParameterChange('employee', e.target.value)}
                />
                <Input
                  placeholder="Poste"
                  value={parameters.position || ''}
                  onChange={(e) => handleParameterChange('position', e.target.value)}
                />
                <Input
                  placeholder="Salaire"
                  value={parameters.salary || ''}
                  onChange={(e) => handleParameterChange('salary', e.target.value)}
                />
                <Input
                  placeholder="Date de début"
                  type="date"
                  value={parameters.startDate || ''}
                  onChange={(e) => handleParameterChange('startDate', e.target.value)}
                />
                <select
                  value={parameters.contractType || ''}
                  onChange={(e) => handleParameterChange('contractType', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Type de contrat</option>
                  <option value="CDI">CDI</option>
                  <option value="CDD">CDD</option>
                  <option value="Stage">Stage</option>
                  <option value="Freelance">Freelance</option>
                </select>
              </div>
            </div>
          )}

          {documentType === 'letter' && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Paramètres de la lettre</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Input
                  placeholder="Expéditeur"
                  value={parameters.sender || ''}
                  onChange={(e) => handleParameterChange('sender', e.target.value)}
                />
                <Input
                  placeholder="Destinataire"
                  value={parameters.recipient || ''}
                  onChange={(e) => handleParameterChange('recipient', e.target.value)}
                />
                <Input
                  placeholder="Objet"
                  value={parameters.subject || ''}
                  onChange={(e) => handleParameterChange('subject', e.target.value)}
                />
                <select
                  value={parameters.tone || ''}
                  onChange={(e) => handleParameterChange('tone', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Ton de la lettre</option>
                  <option value="formal">Formel</option>
                  <option value="friendly">Amical</option>
                  <option value="firm">Ferme</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>
          )}

          {/* Bouton de génération */}
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !documentType || !requirements}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Génération en cours...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Générer le document
              </>
            )}
          </Button>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
              {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Document généré */}
      {generatedDocument && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Document généré</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopy}
                    className="flex items-center gap-2"
                  >
                    {copied ? (
                      <>
                        <Check className="h-4 w-4" />
                        Copié
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        Copier
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Télécharger
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {generatedDocument.generatedContent}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Métadonnées */}
          <Card>
            <CardHeader>
              <CardTitle>Informations sur la génération</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium text-gray-900">Type de document</div>
                  <div className="text-gray-600">{generatedDocument.documentType}</div>
                </div>
                {generatedDocument.processingTimeMs && (
                  <div>
                    <div className="font-medium text-gray-900">Temps de génération</div>
                    <div className="text-gray-600">{generatedDocument.processingTimeMs}ms</div>
                  </div>
                )}
                {generatedDocument.tokensUsed && (
                  <div>
                    <div className="font-medium text-gray-900">Tokens utilisés</div>
                    <div className="text-gray-600">{generatedDocument.tokensUsed}</div>
                  </div>
                )}
                {generatedDocument.estimatedCost && (
                  <div>
                    <div className="font-medium text-gray-900">Coût estimé</div>
                    <div className="text-gray-600">${generatedDocument.estimatedCost.toFixed(4)}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
