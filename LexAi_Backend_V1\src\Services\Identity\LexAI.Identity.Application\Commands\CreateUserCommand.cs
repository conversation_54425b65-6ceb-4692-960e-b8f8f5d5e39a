using LexAI.Identity.Application.DTOs;
using LexAI.Identity.Application.Interfaces;
using LexAI.Identity.Domain.Entities;
using LexAI.Shared.Domain.Enums;
using LexAI.Shared.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LexAI.Identity.Application.Commands;

/// <summary>
/// Command to create a new user
/// </summary>
public class CreateUserCommand : IRequest<UserDto>
{
    /// <summary>
    /// User's email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's phone number (optional)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// User's password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// User's role in the system
    /// </summary>
    public UserRole Role { get; set; }

    /// <summary>
    /// User's preferred language
    /// </summary>
    public string PreferredLanguage { get; set; } = "fr-FR";

    /// <summary>
    /// User's timezone
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// ID of the user creating this user
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// Handler for CreateUserCommand
/// </summary>
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordService _passwordService;
    private readonly ILogger<CreateUserCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the CreateUserCommandHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="passwordService">Password service</param>
    /// <param name="logger">Logger</param>
    public CreateUserCommandHandler(
        IUserRepository userRepository,
        IPasswordService passwordService,
        ILogger<CreateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _passwordService = passwordService;
        _logger = logger;
    }

    /// <summary>
    /// Handles the CreateUserCommand
    /// </summary>
    /// <param name="request">Create user command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created user DTO</returns>
    public async Task<UserDto> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating new user with email {Email}", request.Email);

        // Check if user already exists
        var existingUser = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (existingUser != null)
        {
            _logger.LogWarning("Attempt to create user with existing email {Email}", request.Email);
            throw new EntityAlreadyExistsException("User", request.Email);
        }

        // Validate password strength
        var passwordValidation = _passwordService.ValidatePasswordStrength(request.Password);
        if (!passwordValidation.IsValid)
        {
            _logger.LogWarning("Password validation failed for user {Email}: {Errors}", 
                request.Email, string.Join(", ", passwordValidation.Errors));
            throw new Shared.Domain.Exceptions.InvalidDataException($"Password validation failed: {string.Join(", ", passwordValidation.Errors)}", "Password");
        }

        try
        {
            // Create user entity
            var user = User.Create(
                request.Email,
                request.FirstName,
                request.LastName,
                request.Role,
                request.CreatedBy);

            // Set password
            user.SetPassword(request.Password, request.CreatedBy);

            // Set optional properties
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                user.UpdateProfile(
                    request.FirstName,
                    request.LastName,
                    request.PhoneNumber,
                    request.CreatedBy);
            }

            // Save user
            var createdUser = await _userRepository.AddAsync(user, cancellationToken);

            _logger.LogInformation("User {UserId} created successfully with email {Email}", 
                createdUser.Id, createdUser.Email);

            // Map to DTO
            return new UserDto
            {
                Id = createdUser.Id,
                Email = createdUser.Email,
                FirstName = createdUser.FirstName,
                LastName = createdUser.LastName,
                FullName = createdUser.FullName,
                PhoneNumber = createdUser.PhoneNumber?.Value,
                Role = createdUser.Role,
                IsEmailVerified = createdUser.IsEmailVerified,
                IsActive = createdUser.IsActive,
                IsLocked = createdUser.IsLocked,
                LastLoginAt = createdUser.LastLoginAt,
                PreferredLanguage = createdUser.PreferredLanguage,
                TimeZone = createdUser.TimeZone,
                ProfilePictureUrl = createdUser.ProfilePictureUrl,
                CreatedAt = createdUser.CreatedAt,
                UpdatedAt = createdUser.UpdatedAt
            };
        }
        catch (Exception ex) when (!(ex is DomainException))
        {
            _logger.LogError(ex, "Error creating user with email {Email}", request.Email);
            throw;
        }
    }
}

/// <summary>
/// Command to update user information
/// </summary>
public class UpdateUserCommand : IRequest<UserDto>
{
    /// <summary>
    /// User ID to update
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// User's first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's phone number (optional)
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// User's preferred language
    /// </summary>
    public string PreferredLanguage { get; set; } = "fr-FR";

    /// <summary>
    /// User's timezone
    /// </summary>
    public string TimeZone { get; set; } = "Europe/Paris";

    /// <summary>
    /// ID of the user performing the update
    /// </summary>
    public string UpdatedBy { get; set; } = string.Empty;
}

/// <summary>
/// Handler for UpdateUserCommand
/// </summary>
public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly ILogger<UpdateUserCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the UpdateUserCommandHandler
    /// </summary>
    /// <param name="userRepository">User repository</param>
    /// <param name="logger">Logger</param>
    public UpdateUserCommandHandler(
        IUserRepository userRepository,
        ILogger<UpdateUserCommandHandler> logger)
    {
        _userRepository = userRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handles the UpdateUserCommand
    /// </summary>
    /// <param name="request">Update user command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated user DTO</returns>
    public async Task<UserDto> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating user {UserId}", request.UserId);

        var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
        if (user == null)
        {
            _logger.LogWarning("User {UserId} not found for update", request.UserId);
            throw new EntityNotFoundException("User", request.UserId);
        }

        try
        {
            // Update user profile
            user.UpdateProfile(
                request.FirstName,
                request.LastName,
                request.PhoneNumber,
                request.UpdatedBy);

            // Save changes
            var updatedUser = await _userRepository.UpdateAsync(user, cancellationToken);

            _logger.LogInformation("User {UserId} updated successfully", updatedUser.Id);

            // Map to DTO
            return new UserDto
            {
                Id = updatedUser.Id,
                Email = updatedUser.Email,
                FirstName = updatedUser.FirstName,
                LastName = updatedUser.LastName,
                FullName = updatedUser.FullName,
                PhoneNumber = updatedUser.PhoneNumber?.Value,
                Role = updatedUser.Role,
                IsEmailVerified = updatedUser.IsEmailVerified,
                IsActive = updatedUser.IsActive,
                IsLocked = updatedUser.IsLocked,
                LastLoginAt = updatedUser.LastLoginAt,
                PreferredLanguage = updatedUser.PreferredLanguage,
                TimeZone = updatedUser.TimeZone,
                ProfilePictureUrl = updatedUser.ProfilePictureUrl,
                CreatedAt = updatedUser.CreatedAt,
                UpdatedAt = updatedUser.UpdatedAt
            };
        }
        catch (Exception ex) when (!(ex is DomainException))
        {
            _logger.LogError(ex, "Error updating user {UserId}", request.UserId);
            throw;
        }
    }
}
