{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\openaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\openaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\logs\\lexai-legal-research-20250614.log||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\logs\\lexai-legal-research-20250614.log||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\identity\\lexai.identity.api\\logs\\lexai-identity-20250614.log||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{5D1F99A8-603B-4BE3-B46C-C1C31355B012}|src\\Services\\Identity\\LexAI.Identity.API\\LexAI.Identity.API.csproj|solutionrelative:src\\services\\identity\\lexai.identity.api\\logs\\lexai-identity-20250614.log||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\enhancedaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\enhancedaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\legalresearchintegrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\legalresearchintegrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "lexai-legal-research-20250614.log", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\logs\\lexai-legal-research-20250614.log", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\logs\\lexai-legal-research-20250614.log", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\logs\\lexai-legal-research-20250614.log", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\logs\\lexai-legal-research-20250614.log", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-14T11:52:34.403Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "lexai-identity-20250614.log", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250614.log", "RelativeDocumentMoniker": "src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250614.log", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250614.log", "RelativeToolTip": "src\\Services\\Identity\\LexAI.Identity.API\\logs\\lexai-identity-20250614.log", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-14T11:52:14.019Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "OpenAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "ViewState": "AgIAAE8BAAAAAAAAAAAAwF4BAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T11:49:02.822Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "EnhancedAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "ViewState": "AgIAAOEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T11:48:49.772Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "LegalResearchIntegrationService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T11:48:46.361Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T11:43:32.756Z", "EditorCaption": ""}]}]}]}