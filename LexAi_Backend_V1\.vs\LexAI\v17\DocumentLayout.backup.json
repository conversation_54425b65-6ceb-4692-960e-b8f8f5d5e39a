{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\scripts\\manage-migrations.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:scripts\\manage-migrations.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\data\\datapreprocessingdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3748F65C-D6B0-E1F0-29CD-42BC4B75AE5A}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\LexAI.DataPreprocessing.Infrastructure.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.infrastructure\\data\\datapreprocessingdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\data\\documentanalysisdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6D295248-13FA-424E-BB3F-56634A365D3A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\LexAI.DocumentAnalysis.Infrastructure.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.infrastructure\\data\\documentanalysisdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\enhancedaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\enhancedaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\controllers\\chatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\controllers\\chatcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.application\\commands\\chatcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.application\\commands\\chatcommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.application\\dtos\\additionaldtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.application\\dtos\\additionaldtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.application\\dtos\\chatdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.application\\dtos\\chatdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.application\\interfaces\\iaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE1769B7-9DE9-43F2-B601-7404D9CF3FE2}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\LexAI.AIAssistant.Application.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.application\\interfaces\\iaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\legalresearchintegrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\legalresearchintegrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\openaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\services\\openaiassistantservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\repositories\\conversationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{314B5DBD-C13A-429D-ACDD-341D23D394E1}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\LexAI.AIAssistant.Infrastructure.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.infrastructure\\repositories\\conversationrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\conversation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\conversation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\message.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{843CDE3F-5014-4D54-8F86-7EE31FE3403B}|src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\LexAI.AIAssistant.Domain.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.domain\\entities\\message.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\legalresearch\\lexai.legalresearch.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{ED6FD455-0D95-4CF0-81B4-7BC4EC117AA3}|src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\LexAI.LegalResearch.API.csproj|solutionrelative:src\\services\\legalresearch\\lexai.legalresearch.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\documentanalysis\\lexai.documentanalysis.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DD016E76-776E-4842-BE3C-7997C6046B0A}|src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\LexAI.DocumentAnalysis.API.csproj|solutionrelative:src\\services\\documentanalysis\\lexai.documentanalysis.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56FD0B3A-1C48-FAF7-8742-9CCE45B643BA}|src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\LexAI.DataPreprocessing.API.csproj|solutionrelative:src\\services\\datapreprocessing\\lexai.datapreprocessing.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|d:\\projects\\ai\\lexia\\v1\\lexai_backend_v1\\src\\services\\aiassistant\\lexai.aiassistant.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EC161366-B352-4B2A-88C0-334CAC92CD41}|src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\LexAI.AIAssistant.API.csproj|solutionrelative:src\\services\\aiassistant\\lexai.aiassistant.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Program.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Program.cs", "ViewState": "AgIAAOgAAAAAAAAAAAAAAOEAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T11:21:11.588Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DataPreprocessingDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.Infrastructure\\Data\\DataPreprocessingDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T11:13:32.408Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DocumentAnalysisDbContext.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.Infrastructure\\Data\\DocumentAnalysisDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T11:13:04.225Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "manage-migrations.ps1", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\scripts\\manage-migrations.ps1", "RelativeDocumentMoniker": "scripts\\manage-migrations.ps1", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\scripts\\manage-migrations.ps1", "RelativeToolTip": "scripts\\manage-migrations.ps1", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-14T11:08:44.547Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.json", "ViewState": "AgIAAAQAAAAAAAAAAAAAwBIAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T10:00:15.993Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T10:00:02.851Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.Development.json", "ViewState": "AgIAAFcAAAAAAAAAAAAAAGQAAACRAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:59:50.845Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\appsettings.json", "ViewState": "AgIAAEgAAAAAAAAAAAAAAGUAAACRAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:58:48.257Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:58:14.86Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:57:20.419Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:23:26.568Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ChatCommands.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Commands\\ChatCommands.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:23:20.146Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "AdditionalDTOs.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\AdditionalDTOs.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\AdditionalDTOs.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\AdditionalDTOs.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\AdditionalDTOs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:23:17.09Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "ChatDTOs.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\ChatDTOs.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\ChatDTOs.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\ChatDTOs.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\DTOs\\ChatDTOs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:23:14.145Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "IAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Application\\Interfaces\\IAIAssistantService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:23:01.886Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "OpenAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\OpenAIAssistantService.cs", "ViewState": "AgIAACsAAAAAAAAAAAAQwFQAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:16:05.556Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "LegalResearchIntegrationService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\LegalResearchIntegrationService.cs", "ViewState": "AgIAAOoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:15:44.205Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "EnhancedAIAssistantService.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Services\\EnhancedAIAssistantService.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAewEYAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:13:40.622Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "ConversationRepository.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Infrastructure\\Repositories\\ConversationRepository.cs", "ViewState": "AgIAAMsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:13:17.8Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "Conversation.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Conversation.cs", "ViewState": "AgIAAHcBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:12:50.846Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "Message.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Message.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Message.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Message.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.Domain\\Entities\\Message.cs", "ViewState": "AgIAAOcAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:12:11.569Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "ChatController.cs", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Controllers\\ChatController.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAABsAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T09:11:45.173Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\Properties\\launchSettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\Properties\\launchSettings.json", "RelativeToolTip": "src\\Services\\LegalResearch\\LexAI.LegalResearch.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:11:17.24Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Properties\\launchSettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Properties\\launchSettings.json", "RelativeToolTip": "src\\Services\\DocumentAnalysis\\LexAI.DocumentAnalysis.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:10:21.563Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Properties\\launchSettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Properties\\launchSettings.json", "RelativeToolTip": "src\\Services\\DataPreprocessing\\LexAI.DataPreprocessing.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:10:01.963Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Properties\\launchSettings.json", "ToolTip": "D:\\Projects\\AI\\LexIA\\V1\\LexAi_Backend_V1\\src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Properties\\launchSettings.json", "RelativeToolTip": "src\\Services\\AIAssistant\\LexAI.AIAssistant.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-14T09:09:00.676Z", "EditorCaption": ""}]}]}]}