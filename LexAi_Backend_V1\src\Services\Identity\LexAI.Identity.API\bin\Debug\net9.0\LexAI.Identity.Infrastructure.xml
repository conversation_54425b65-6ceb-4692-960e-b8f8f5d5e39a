<?xml version="1.0"?>
<doc>
    <assembly>
        <name>LexAI.Identity.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:LexAI.Identity.Infrastructure.Data.DesignTimeDbContextFactory">
            <summary>
            Design-time factory for creating IdentityDbContext instances during migrations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.DesignTimeDbContextFactory.CreateDbContext(System.String[])">
            <summary>
            Creates a new instance of IdentityDbContext for design-time operations
            </summary>
            <param name="args">Command line arguments</param>
            <returns>Configured IdentityDbContext instance</returns>
        </member>
        <member name="T:LexAI.Identity.Infrastructure.Data.IdentityDbContext">
            <summary>
            Entity Framework DbContext for Identity service
            </summary>
        </member>
        <member name="P:LexAI.Identity.Infrastructure.Data.IdentityDbContext.Users">
            <summary>
            Users DbSet
            </summary>
        </member>
        <member name="P:LexAI.Identity.Infrastructure.Data.IdentityDbContext.RefreshTokens">
            <summary>
            Refresh tokens DbSet
            </summary>
        </member>
        <member name="P:LexAI.Identity.Infrastructure.Data.IdentityDbContext.UserPermissions">
            <summary>
            User permissions DbSet
            </summary>
        </member>
        <member name="P:LexAI.Identity.Infrastructure.Data.IdentityDbContext.AuditEntries">
            <summary>
            Audit entries DbSet
            </summary>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.IdentityDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{LexAI.Identity.Infrastructure.Data.IdentityDbContext})">
            <summary>
            Initializes a new instance of the IdentityDbContext
            </summary>
            <param name="options">DbContext options</param>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.IdentityDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            Configures the model that was discovered by convention from the entity types
            </summary>
            <param name="modelBuilder">Model builder</param>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.IdentityDbContext.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            Saves all changes made in this context to the database
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Number of state entries written to the database</returns>
        </member>
        <member name="T:LexAI.Identity.Infrastructure.Data.Migrations.InitialMigration">
            <inheritdoc />
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.Migrations.InitialMigration.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.Migrations.InitialMigration.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Data.Migrations.InitialMigration.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
        <member name="T:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository">
            <summary>
            Repository implementation for RefreshToken entity
            </summary>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.#ctor(LexAI.Identity.Infrastructure.Data.IdentityDbContext,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository})">
            <summary>
            Initializes a new instance of the RefreshTokenRepository
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.GetByTokenAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a refresh token by its value
            </summary>
            <param name="token">Token value</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>RefreshToken entity or null if not found</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.GetActiveTokensByUserIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets all active refresh tokens for a user
            </summary>
            <param name="userId">User ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of active refresh tokens</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.AddAsync(LexAI.Identity.Domain.Entities.RefreshToken,System.Threading.CancellationToken)">
            <summary>
            Adds a new refresh token
            </summary>
            <param name="refreshToken">RefreshToken entity to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Added refresh token</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.UpdateAsync(LexAI.Identity.Domain.Entities.RefreshToken,System.Threading.CancellationToken)">
            <summary>
            Updates an existing refresh token
            </summary>
            <param name="refreshToken">RefreshToken entity to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated refresh token</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.RevokeAllUserTokensAsync(System.Guid,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Revokes all refresh tokens for a user
            </summary>
            <param name="userId">User ID</param>
            <param name="revokedBy">ID of the user revoking the tokens</param>
            <param name="reason">Reason for revocation</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Number of tokens revoked</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.RefreshTokenRepository.RemoveExpiredTokensAsync(System.Threading.CancellationToken)">
            <summary>
            Removes expired refresh tokens
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Number of tokens removed</returns>
        </member>
        <member name="T:LexAI.Identity.Infrastructure.Repositories.UserRepository">
            <summary>
            Repository implementation for User entity
            </summary>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.#ctor(LexAI.Identity.Infrastructure.Data.IdentityDbContext,Microsoft.Extensions.Logging.ILogger{LexAI.Identity.Infrastructure.Repositories.UserRepository})">
            <summary>
            Initializes a new instance of the UserRepository
            </summary>
            <param name="context">Database context</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.GetByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a user by their unique identifier
            </summary>
            <param name="id">User ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User entity or null if not found</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.GetByEmailAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a user by their email address
            </summary>
            <param name="email">Email address</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>User entity or null if not found</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.GetAllAsync(System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets all users with pagination
            </summary>
            <param name="pageNumber">Page number (1-based)</param>
            <param name="pageSize">Number of items per page</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Paginated list of users</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.SearchAsync(System.String,System.Nullable{LexAI.Shared.Domain.Enums.UserRole},System.Nullable{System.Boolean},System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Searches users by criteria
            </summary>
            <param name="searchTerm">Search term for name or email</param>
            <param name="role">Optional role filter</param>
            <param name="isActive">Optional active status filter</param>
            <param name="pageNumber">Page number (1-based)</param>
            <param name="pageSize">Number of items per page</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Paginated search results</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.AddAsync(LexAI.Identity.Domain.Entities.User,System.Threading.CancellationToken)">
            <summary>
            Adds a new user to the repository
            </summary>
            <param name="user">User entity to add</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Added user entity</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.UpdateAsync(LexAI.Identity.Domain.Entities.User,System.Threading.CancellationToken)">
            <summary>
            Updates an existing user
            </summary>
            <param name="user">User entity to update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated user entity</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.DeleteAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Soft deletes a user
            </summary>
            <param name="id">User ID to delete</param>
            <param name="deletedBy">ID of the user performing the deletion</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if deletion was successful</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.ExistsAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            Checks if a user exists with the given email
            </summary>
            <param name="email">Email address to check</param>
            <param name="excludeUserId">Optional user ID to exclude from the check</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>True if user exists</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.GetByRoleAsync(LexAI.Shared.Domain.Enums.UserRole,System.Threading.CancellationToken)">
            <summary>
            Gets users by role
            </summary>
            <param name="role">User role</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of users with the specified role</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.GetLockedUsersAsync(System.Threading.CancellationToken)">
            <summary>
            Gets locked users
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of locked users</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Repositories.UserRepository.GetUnverifiedUsersAsync(System.Threading.CancellationToken)">
            <summary>
            Gets users with unverified emails
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>List of users with unverified emails</returns>
        </member>
        <member name="T:LexAI.Identity.Infrastructure.Services.PasswordService">
            <summary>
            Service implementation for password operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.PasswordService.HashPassword(System.String)">
            <summary>
            Hashes a password using BCrypt
            </summary>
            <param name="password">Plain text password</param>
            <returns>Hashed password</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.PasswordService.VerifyPassword(System.String,System.String)">
            <summary>
            Verifies a password against its hash
            </summary>
            <param name="password">Plain text password</param>
            <param name="hash">Hashed password</param>
            <returns>True if password matches hash</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.PasswordService.GeneratePassword(System.Int32,System.Boolean)">
            <summary>
            Generates a secure random password
            </summary>
            <param name="length">Password length (minimum 8)</param>
            <param name="includeSpecialChars">Include special characters</param>
            <returns>Generated password</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.PasswordService.ValidatePasswordStrength(System.String)">
            <summary>
            Validates password strength
            </summary>
            <param name="password">Password to validate</param>
            <returns>Password validation result</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.PasswordService.GeneratePasswordResetToken">
            <summary>
            Generates a password reset token
            </summary>
            <returns>Password reset token</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.PasswordService.GenerateEmailVerificationToken">
            <summary>
            Generates an email verification token
            </summary>
            <returns>Email verification token</returns>
        </member>
        <member name="T:LexAI.Identity.Infrastructure.Services.TokenService">
            <summary>
            Service implementation for JWT token operations
            </summary>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.#ctor(Microsoft.Extensions.Options.IOptions{LexAI.Shared.Infrastructure.Configuration.JwtSettings})">
            <summary>
            Initializes a new instance of the TokenService
            </summary>
            <param name="jwtSettings">JWT configuration settings</param>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.GenerateAccessToken(LexAI.Identity.Domain.Entities.User,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Generates a JWT access token for the user
            </summary>
            <param name="user">User entity</param>
            <param name="additionalClaims">Additional claims to include in the token</param>
            <returns>JWT access token</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.GenerateRefreshToken(System.Guid,System.String,System.String)">
            <summary>
            Generates a refresh token for the user
            </summary>
            <param name="userId">User ID</param>
            <param name="ipAddress">IP address where the token was created</param>
            <param name="userAgent">User agent of the client</param>
            <returns>Refresh token entity</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.ValidateAccessToken(System.String)">
            <summary>
            Validates a JWT access token
            </summary>
            <param name="token">JWT token to validate</param>
            <returns>ClaimsPrincipal if valid, null otherwise</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.GetUserIdFromToken(System.String)">
            <summary>
            Extracts user ID from a JWT token
            </summary>
            <param name="token">JWT token</param>
            <returns>User ID if found, null otherwise</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.GetClaimsFromToken(System.String)">
            <summary>
            Extracts claims from a JWT token
            </summary>
            <param name="token">JWT token</param>
            <returns>Collection of claims</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.IsTokenExpired(System.String)">
            <summary>
            Checks if a JWT token is expired
            </summary>
            <param name="token">JWT token</param>
            <returns>True if expired, false otherwise</returns>
        </member>
        <member name="M:LexAI.Identity.Infrastructure.Services.TokenService.GetTokenExpiration(System.String)">
            <summary>
            Gets the expiration time of a JWT token
            </summary>
            <param name="token">JWT token</param>
            <returns>Expiration time or null if invalid</returns>
        </member>
    </members>
</doc>
