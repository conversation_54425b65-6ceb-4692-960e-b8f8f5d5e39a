using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Queries;
using LexAI.DocumentAnalysis.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Application.Handlers;

/// <summary>
/// Handler pour la query GetAnalysisResultQuery
/// </summary>
public class GetAnalysisResultQueryHandler : IRequestHandler<GetAnalysisResultQuery, DocumentAnalysisResponseDto?>
{
    private readonly IDocumentAnalysisRepository _repository;
    private readonly ILogger<GetAnalysisResultQueryHandler> _logger;

    public GetAnalysisResultQueryHandler(
        IDocumentAnalysisRepository repository,
        ILogger<GetAnalysisResultQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<DocumentAnalysisResponseDto?> Handle(
        GetAnalysisResultQuery request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving analysis result: {AnalysisId} for user: {UserId}", 
            request.AnalysisId, request.UserId);

        try
        {
            var analysis = await _repository.GetByIdAsync(request.AnalysisId, request.UserId, cancellationToken);

            if (analysis == null)
            {
                _logger.LogWarning("Analysis not found: {AnalysisId} for user: {UserId}", 
                    request.AnalysisId, request.UserId);
                return null;
            }

            return new DocumentAnalysisResponseDto
            {
                Id = analysis.Id,
                DocumentName = analysis.DocumentName,
                DocumentType = analysis.DocumentType,
                Status = analysis.Status.ToString(),
                AnalysisContent = analysis.AnalysisContent,
                ConfidenceScore = analysis.ConfidenceScore,
                ProcessingTimeMs = analysis.ProcessingTimeMs,
                TokensUsed = analysis.TokensUsed,
                EstimatedCost = analysis.EstimatedCost,
                ModelUsed = analysis.ModelUsed,
                UserId = analysis.UserId,
                AnalyzedAt = analysis.AnalyzedAt,
                
                // Mapper les entités liées
                Clauses = analysis.Clauses?.Select(c => new ClauseAnalysisDto
                {
                    Id = c.Id,
                    ClauseText = c.ClauseText,
                    ClauseType = c.ClauseType,
                    Analysis = c.Analysis,
                    RiskLevel = c.RiskLevel.ToString(),
                    ConfidenceScore = c.ConfidenceScore,
                    SuggestedRevision = c.SuggestedRevision,
                    Tags = c.Tags,
                    StartPosition = c.StartPosition,
                    EndPosition = c.EndPosition
                }).ToList() ?? new List<ClauseAnalysisDto>(),

                Risks = analysis.Risks?.Select(r => new RiskAssessmentDto
                {
                    Id = r.Id,
                    RiskType = r.RiskType,
                    Description = r.Description,
                    Severity = r.Severity.ToString(),
                    Probability = r.Probability,
                    Impact = r.Impact,
                    Mitigation = r.Mitigation,
                    AffectedClauses = r.AffectedClauses
                }).ToList() ?? new List<RiskAssessmentDto>(),

                Recommendations = analysis.Recommendations?.Select(r => new DocumentRecommendationDto
                {
                    Id = r.Id,
                    Type = r.Type,
                    Title = r.Title,
                    Description = r.Description,
                    Priority = r.Priority.ToString(),
                    SuggestedAction = r.SuggestedAction,
                    LegalBasis = r.LegalBasis,
                    RelatedClauses = r.RelatedClauses
                }).ToList() ?? new List<DocumentRecommendationDto>(),

                Entities = analysis.Entities?.Select(e => new ExtractedEntityDto
                {
                    Id = e.Id,
                    Text = e.Text,
                    Type = e.Type,
                    ConfidenceScore = e.ConfidenceScore,
                    StartPosition = e.StartPosition,
                    EndPosition = e.EndPosition,
                    NormalizedValue = e.NormalizedValue,
                    Metadata = e.Metadata
                }).ToList() ?? new List<ExtractedEntityDto>(),

                Citations = analysis.Citations?.Select(c => new DocumentCitationDto
                {
                    Id = c.Id,
                    Type = c.Type,
                    Title = c.Title,
                    Source = c.Source,
                    Url = c.Url,
                    Reference = c.Reference,
                    RelevanceScore = c.RelevanceScore,
                    Context = c.Context
                }).ToList() ?? new List<DocumentCitationDto>(),

                Summary = new DocumentSummaryDto
                {
                    ExecutiveSummary = analysis.ExecutiveSummary ?? string.Empty,
                    KeyPoints = analysis.KeyPoints ?? new List<string>(),
                    //OverallRiskLevel = analysis.Risks?.Any() == true ?
                    //    analysis.Risks.Max(r => r.Severity).ToString() : "Unknown",
                    MainParties = analysis.MainParties ?? new List<string>(),
                    ImportantDates = analysis.ImportantDates ?? new List<string>(),
                    FinancialTerms = analysis.FinancialTerms ?? new List<string>(),
                    DocumentPurpose = analysis.DocumentPurpose ?? string.Empty,
                    OverallRiskLevel = analysis.OverallRiskLevel
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving analysis result: {AnalysisId} for user: {UserId}", 
                request.AnalysisId, request.UserId);
            throw;
        }
    }
}
