import React, { useState } from 'react';
import { MainSidebar } from './MainSidebar';
import { SecondarySidebar } from './SecondarySidebar';
import { ChatPanel } from './ChatPanel';
import { Button } from '../ui/Button';

export const ChatLayout: React.FC = () => {
  const [mainCollapsed, setMainCollapsed] = useState(false);
  const [secondaryCollapsed, setSecondaryCollapsed] = useState(false);
  const [selectedSection, setSelectedSection] = useState('chats');

  return (
    <div className="flex h-screen w-full bg-gray-100 dark:bg-gray-950">
      {/* Sidebar principale (mobile: drawer) */}
      <div className="hidden md:block">
        <MainSidebar
          collapsed={mainCollapsed}
          onToggle={() => setMainCollapsed((v) => !v)}
          selected={selectedSection}
          onSelect={setSelectedSection}
        />
      </div>
      {/* Sidebar secondaire (mobile: drawer) */}
      <div className="hidden md:block">
        <SecondarySidebar
          collapsed={secondaryCollapsed}
          section={selectedSection}
          onToggle={() => setSecondaryCollapsed((v) => !v)}
        />
      </div>
      {/* Mobile toggles */}
      <div className="fixed top-2 left-2 z-30 md:hidden flex gap-2">
        <Button size="icon" variant="outline" onClick={() => setMainCollapsed((v) => !v)}>
          <span className="sr-only">Ouvrir menu principal</span>
          <svg width="24" height="24" fill="none" stroke="currentColor">
            <rect width="18" height="2" x="3" y="6" rx="1" />
            <rect width="18" height="2" x="3" y="11" rx="1" />
            <rect width="18" height="2" x="3" y="16" rx="1" />
          </svg>
        </Button>
        <Button size="icon" variant="outline" onClick={() => setSecondaryCollapsed((v) => !v)}>
          <span className="sr-only">Ouvrir menu secondaire</span>
          <svg width="24" height="24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10" />
            <rect width="2" height="10" x="11" y="7" rx="1" />
          </svg>
        </Button>
      </div>
      {/* Main chat panel */}
      <div className="flex-1 min-w-0 flex flex-col">
        <ChatPanel />
      </div>
    </div>
  );
};
