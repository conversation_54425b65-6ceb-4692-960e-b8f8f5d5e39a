using LexAI.DocumentAnalysis.Domain.Entities;
using LexAI.DocumentAnalysis.Domain.Repositories;
using LexAI.DocumentAnalysis.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Repositories;

/// <summary>
/// Implémentation du repository pour les analyses de documents
/// </summary>
public class DocumentAnalysisRepository : IDocumentAnalysisRepository
{
    private readonly DocumentAnalysisDbContext _context;
    private readonly ILogger<DocumentAnalysisRepository> _logger;

    public DocumentAnalysisRepository(
        DocumentAnalysisDbContext context,
        ILogger<DocumentAnalysisRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<DocumentAnalysisResult?> GetByIdAsync(Guid id, Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting analysis {AnalysisId} for user {UserId}", id, userId);

        return await _context.DocumentAnalysisResults
            .Include(a => a.Claus<PERSON>)
            .Include(a => a.Risks)
            .Include(a => a.Recommendations)
            .Include(a => a.Entities)
            .Include(a => a.Citations)
            .FirstOrDefaultAsync(a => a.Id == id && a.UserId == userId, cancellationToken);
    }

    public async Task<DocumentAnalysisResult?> GetByIdWithoutUserCheckAsync(Guid id, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting analysis {AnalysisId} without user check", id);

        return await _context.DocumentAnalysisResults
            .Include(a => a.Clauses)
            .Include(a => a.Risks)
            .Include(a => a.Recommendations)
            .Include(a => a.Entities)
            .Include(a => a.Citations)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
    }

    public async Task<(IEnumerable<DocumentAnalysisResult> Items, int TotalCount)> GetUserAnalysesAsync(
        Guid userId,
        string? documentType = null,
        DocumentAnalysisStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int page = 1,
        int pageSize = 10,
        string? sortBy = null,
        bool sortDescending = true,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting analyses for user {UserId} with filters", userId);

        var query = _context.DocumentAnalysisResults
            .Where(a => a.UserId == userId);

        // Filtres
        if (!string.IsNullOrEmpty(documentType))
        {
            query = query.Where(a => a.DocumentType == documentType);
        }

        if (status.HasValue)
        {
            query = query.Where(a => a.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(a => a.AnalyzedAt >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(a => a.AnalyzedAt <= toDate.Value.AddDays(1));
        }

        // Tri
        query = !string.IsNullOrEmpty(sortBy) && sortBy.ToLower() == "analyzedat" && sortDescending
            ? query.OrderByDescending(a => a.AnalyzedAt)
            : query.OrderByDescending(a => a.AnalyzedAt);

        var totalCount = await query.CountAsync(cancellationToken);

        var skip = (page - 1) * pageSize;
        var items = await query
            .Skip(skip)
            .Take(pageSize)
            .Include(a => a.Risks)
            .Include(a => a.Recommendations)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<DocumentAnalysisResult> AddAsync(DocumentAnalysisResult analysis, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding new analysis {AnalysisId}", analysis.Id);

        _context.DocumentAnalysisResults.Add(analysis);
        await _context.SaveChangesAsync(cancellationToken);
        return analysis;
    }

    public async Task UpdateAsync(DocumentAnalysisResult analysis, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Updating analysis {AnalysisId}", analysis.Id);

        _context.DocumentAnalysisResults.Update(analysis);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> DeleteAsync(Guid id, Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Deleting analysis {AnalysisId} for user {UserId}", id, userId);

        var analysis = await _context.DocumentAnalysisResults
            .FirstOrDefaultAsync(a => a.Id == id && a.UserId == userId, cancellationToken);

        if (analysis == null)
        {
            return false;
        }

        _context.DocumentAnalysisResults.Remove(analysis);
        await _context.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<bool> ExistsAsync(Guid id, Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Checking if analysis {AnalysisId} exists for user {UserId}", id, userId);

        return await _context.DocumentAnalysisResults
            .AnyAsync(a => a.Id == id && a.UserId == userId, cancellationToken);
    }

    // Implémentation des méthodes pour les entités liées

    public async Task<ClauseAnalysis> AddClauseAsync(ClauseAnalysis clause, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding clause {ClauseId} for analysis {AnalysisId}", clause.Id, clause.DocumentAnalysisResultId);

        _context.ClauseAnalyses.Add(clause);
        await _context.SaveChangesAsync(cancellationToken);
        return clause;
    }

    public async Task<RiskAssessment> AddRiskAsync(RiskAssessment risk, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding risk {RiskId} for analysis {AnalysisId}", risk.Id, risk.DocumentAnalysisResultId);

        _context.RiskAssessments.Add(risk);
        await _context.SaveChangesAsync(cancellationToken);
        return risk;
    }

    public async Task<DocumentRecommendation> AddRecommendationAsync(DocumentRecommendation recommendation, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding recommendation {RecommendationId} for analysis {AnalysisId}", recommendation.Id, recommendation.DocumentAnalysisResultId);

        _context.DocumentRecommendations.Add(recommendation);
        await _context.SaveChangesAsync(cancellationToken);
        return recommendation;
    }

    public async Task<ExtractedEntity> AddEntityAsync(ExtractedEntity entity, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding entity {EntityId} for analysis {AnalysisId}", entity.Id, entity.DocumentAnalysisResultId);

        _context.ExtractedEntities.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);
        return entity;
    }

    public async Task<DocumentCitation> AddCitationAsync(DocumentCitation citation, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Adding citation {CitationId} for analysis {AnalysisId}", citation.Id, citation.DocumentAnalysisResultId);

        _context.DocumentCitations.Add(citation);
        await _context.SaveChangesAsync(cancellationToken);
        return citation;
    }
}
