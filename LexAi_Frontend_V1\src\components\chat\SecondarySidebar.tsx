import React from 'react';
import { Plus, ChevronLeft, ChevronRight } from 'lucide-react';

interface SecondarySidebarProps {
  collapsed: boolean;
  section: string;
  onToggle: () => void;
}

export const SecondarySidebar: React.FC<SecondarySidebarProps> = ({ collapsed, section, onToggle }) => {
  // Example: show different content based on section
  let content;
  if (section === 'chats') {
    content = (
      <div className="flex flex-col gap-2 p-2">
        <button className="flex items-center gap-2 p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
          <Plus size={18} /> {!collapsed && <span>New Chat</span>}
        </button>
        <div className="mt-4">
          {!collapsed && <div className="font-semibold text-xs text-gray-500 mb-2">Pinned chats</div>}
          <div className="text-gray-400 text-xs italic">{!collapsed && 'No pinned chats yet :('}</div>
        </div>
        <div className="mt-4">
          {!collapsed && <div className="font-semibold text-xs text-gray-500 mb-2">History</div>}
          <div className="text-gray-400 text-xs italic">{!collapsed && 'History is empty'}</div>
        </div>
      </div>
    );
  } else if (section === 'search') {
    content = <div className="p-2 text-xs text-gray-400">Search sidebar content</div>;
  } else {
    content = <div className="p-2 text-xs text-gray-400">Section: {section}</div>;
  }

  return (
    <aside className={`h-full bg-gray-50 dark:bg-gray-900 border-r border-border flex flex-col transition-all duration-200 ${collapsed ? 'w-12' : 'w-56'}`}>
      <div className="flex items-center justify-end p-1 border-b border-border">
        <button onClick={onToggle} className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-800">
          <span className="sr-only">Toggle secondary sidebar</span>
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      </div>
      <div className="flex-1 overflow-y-auto">{content}</div>
    </aside>
  );
};
