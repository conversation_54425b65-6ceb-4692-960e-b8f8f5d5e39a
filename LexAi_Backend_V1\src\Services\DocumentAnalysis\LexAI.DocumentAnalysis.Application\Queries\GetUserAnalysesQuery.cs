using LexAI.DocumentAnalysis.Application.DTOs;
using MediatR;

namespace LexAI.DocumentAnalysis.Application.Queries;

/// <summary>
/// Query pour récupérer la liste des analyses d'un utilisateur
/// </summary>
public class GetUserAnalysesQuery : IRequest<DocumentAnalysisListResponseDto>
{
    public Guid UserId { get; set; }
    public string? DocumentType { get; set; }
    public string? Status { get; set; }
    public string? FromDate { get; set; }
    public string? ToDate { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = true;

    public GetUserAnalysesQuery(DocumentAnalysisListRequestDto request)
    {
        UserId = request.UserId ?? Guid.Empty;
        DocumentType = request.DocumentType;
        Status = request.Status;
        FromDate = request.FromDate?.ToString("yyyy-MM-dd");
        ToDate = request.ToDate?.ToString("yyyy-MM-dd");
        Page = request.Page > 0 ? request.Page : 1;
        PageSize = request.PageSize > 0 ? request.PageSize : 10;
        SortBy = request.SortBy;
        SortDescending = request.SortDescending;
    }
}
