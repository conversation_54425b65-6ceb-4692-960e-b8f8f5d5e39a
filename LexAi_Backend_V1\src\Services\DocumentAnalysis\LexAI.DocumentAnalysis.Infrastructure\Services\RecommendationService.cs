using LexAI.DocumentAnalysis.Application.DTOs;
using LexAI.DocumentAnalysis.Application.Interfaces;
using LexAI.Shared.Application.Extensions;
using LexAI.Shared.Application.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LexAI.DocumentAnalysis.Infrastructure.Services;

/// <summary>
/// Service de génération de recommandations juridiques utilisant l'IA
/// </summary>
public class RecommendationService : IRecommendationService
{
    private readonly IUnifiedLLMService _llmService;
    private readonly ILogger<RecommendationService> _logger;
    private readonly IConfiguration _configuration;

    public RecommendationService(
        IUnifiedLLMService llmService,
        IConfiguration configuration,
        ILogger<RecommendationService> logger)
    {
        _llmService = llmService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Génère des recommandations pour améliorer un document juridique
    /// </summary>
    public async Task<List<DocumentRecommendationDto>> GenerateRecommendationsAsync(
        string documentText, 
        string documentType, 
        List<ClauseAnalysisDto> clauses, 
        List<RiskAssessmentDto> risks, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating recommendations for document type: {DocumentType}", documentType);

        try
        {
            var recommendations = new List<DocumentRecommendationDto>();

            // 1. Recommandations basées sur les risques identifiés
            var riskBasedRecommendations = await GenerateRiskBasedRecommendationsAsync(
                risks, documentType, cancellationToken);
            recommendations.AddRange(riskBasedRecommendations);

            // 2. Recommandations basées sur l'analyse des clauses
            var clauseBasedRecommendations = await GenerateClauseBasedRecommendationsAsync(
                clauses, documentType, cancellationToken);
            recommendations.AddRange(clauseBasedRecommendations);

            // 3. Recommandations générales d'amélioration
            var generalRecommendations = await GenerateGeneralRecommendationsAsync(
                documentText, documentType, cancellationToken);
            recommendations.AddRange(generalRecommendations);

            // 4. Recommandations de conformité
            var complianceRecommendations = await GenerateComplianceRecommendationsAsync(
                documentText, documentType, cancellationToken);
            recommendations.AddRange(complianceRecommendations);

            // 5. Prioriser et déduplicater les recommandations
            var prioritizedRecommendations = await PrioritizeRecommendationsAsync(recommendations, cancellationToken);

            _logger.LogInformation("Generated {RecommendationCount} recommendations", prioritizedRecommendations.Count);
            return prioritizedRecommendations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating recommendations");
            throw;
        }
    }

    /// <summary>
    /// Priorise les recommandations selon leur importance
    /// </summary>
    public async Task<List<DocumentRecommendationDto>> PrioritizeRecommendationsAsync(
        List<DocumentRecommendationDto> recommendations, 
        CancellationToken cancellationToken = default)
    {
        if (!recommendations.Any())
            return recommendations;

        _logger.LogInformation("Prioritizing {Count} recommendations", recommendations.Count);

        // Supprimer les doublons basés sur le titre
        var uniqueRecommendations = recommendations
            .GroupBy(r => r.Title.ToLowerInvariant())
            .Select(g => g.First())
            .ToList();

        // Analyser et ajuster les priorités avec l'IA
        var prioritizedRecommendations = await AnalyzePrioritiesWithAIAsync(uniqueRecommendations, cancellationToken);

        // Trier par priorité
        var priorityOrder = new Dictionary<string, int>
        {
            { "Critical", 4 },
            { "High", 3 },
            { "Medium", 2 },
            { "Low", 1 }
        };

        return prioritizedRecommendations
            .OrderByDescending(r => priorityOrder.GetValueOrDefault(r.Priority, 0))
            .Take(10) // Limiter à 10 recommandations principales
            .ToList();
    }

    // Méthodes privées

    private async Task<List<DocumentRecommendationDto>> GenerateRiskBasedRecommendationsAsync(
        List<RiskAssessmentDto> risks, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        if (!risks.Any())
            return new List<DocumentRecommendationDto>();

        var highRisks = risks.Where(r => r.Severity == "High" || r.Severity == "Critical").ToList();
        if (!highRisks.Any())
            return new List<DocumentRecommendationDto>();

        var riskSummary = string.Join("\n", highRisks.Select(r => $"- {r.RiskType}: {r.Description}"));

        var prompt = $@"Basé sur ces risques identifiés dans un document {documentType}, générez des recommandations spécifiques pour les atténuer.

Risques identifiés:
{riskSummary}

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans retours à la ligne dans les valeurs.

Répondez en JSON avec ce format:
{{
  ""recommendations"": [
    {{
      ""type"": ""Risk Mitigation"",
      ""title"": ""titre de la recommandation"",
      ""description"": ""description détaillée"",
      ""priority"": ""Critical|High|Medium|Low"",
      ""suggestedAction"": ""action concrète à prendre"",
      ""legalBasis"": ""base légale ou justification"",
      ""relatedClauses"": [""clause1"", ""clause2""]
    }}
  ]
}}";

        var options = LLMServiceExtensions.ForLegalAnalysis(3000);
        var result = await _llmService.AnalyzeStructuredAsync<RecommendationResponse>(
            riskSummary, prompt, options, cancellationToken);

        return ConvertToRecommendationDtos(result?.Recommendations ?? new List<RecommendationInfo>());
    }

    private async Task<List<DocumentRecommendationDto>> GenerateClauseBasedRecommendationsAsync(
        List<ClauseAnalysisDto> clauses, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var problematicClauses = clauses.Where(c => 
            c.RiskLevel == "High" || c.RiskLevel == "Critical" || 
            !string.IsNullOrEmpty(c.SuggestedRevision)).ToList();

        if (!problematicClauses.Any())
            return new List<DocumentRecommendationDto>();

        var clauseSummary = string.Join("\n", problematicClauses.Select(c => 
            $"- {c.ClauseType} (Risque: {c.RiskLevel}): {c.Analysis}"));

        var prompt = $@"Basé sur l'analyse de ces clauses problématiques d'un document {documentType}, générez des recommandations d'amélioration.

Clauses analysées:
{clauseSummary}

Générez des recommandations pour améliorer, clarifier ou réviser ces clauses.
Utilisez le format JSON standard des recommandations.";

        var options = LLMServiceExtensions.ForLegalAnalysis(3000);
        var result = await _llmService.AnalyzeStructuredAsync<RecommendationResponse>(
            clauseSummary, prompt, options, cancellationToken);

        return ConvertToRecommendationDtos(result?.Recommendations ?? new List<RecommendationInfo>());
    }

    private async Task<List<DocumentRecommendationDto>> GenerateGeneralRecommendationsAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Analysez ce document {documentType} et proposez des recommandations générales d'amélioration.
Concentrez-vous sur la structure, la clarté, la complétude et les bonnes pratiques juridiques.

Document: {documentText.Substring(0, Math.Min(documentText.Length, 6000))}

Générez des recommandations pour améliorer la qualité globale du document.
Utilisez le format JSON standard des recommandations.";

        var options = LLMServiceExtensions.ForLegalAnalysis(3000);
        var result = await _llmService.AnalyzeStructuredAsync<RecommendationResponse>(
            documentText, prompt, options, cancellationToken);

        return ConvertToRecommendationDtos(result?.Recommendations ?? new List<RecommendationInfo>());
    }

    private async Task<List<DocumentRecommendationDto>> GenerateComplianceRecommendationsAsync(
        string documentText, 
        string documentType, 
        CancellationToken cancellationToken)
    {
        var prompt = $@"Analysez ce document {documentType} pour identifier les améliorations de conformité nécessaires.
Vérifiez la conformité aux réglementations actuelles (RGPD, droit du travail, etc.).

Document: {documentText.Substring(0, Math.Min(documentText.Length, 5000))}

Générez des recommandations pour améliorer la conformité légale.
Utilisez le format JSON standard des recommandations avec type ""Compliance"".";

        var options = LLMServiceExtensions.ForLegalAnalysis(2500);
        var result = await _llmService.AnalyzeStructuredAsync<RecommendationResponse>(
            documentText, prompt, options, cancellationToken);

        return ConvertToRecommendationDtos(result?.Recommendations ?? new List<RecommendationInfo>());
    }

    private async Task<List<DocumentRecommendationDto>> AnalyzePrioritiesWithAIAsync(
        List<DocumentRecommendationDto> recommendations, 
        CancellationToken cancellationToken)
    {
        if (recommendations.Count <= 3)
            return recommendations;

        var recommendationSummary = string.Join("\n", recommendations.Select((r, i) => 
            $"{i + 1}. {r.Title} (Priorité actuelle: {r.Priority})"));

        var prompt = $@"Analysez ces recommandations juridiques et ajustez leurs priorités selon leur importance relative.
Considérez l'impact juridique, financier et opérationnel de chaque recommandation.

Recommandations:
{recommendationSummary}

IMPORTANT: Répondez UNIQUEMENT avec un JSON valide, sans retours à la ligne dans les valeurs.

Répondez en JSON avec ce format:
{{
  ""prioritizedRecommendations"": [
    {{
      ""index"": 1,
      ""newPriority"": ""Critical|High|Medium|Low"",
      ""justification"": ""raison de cette priorité""
    }}
  ]
}}";

        var options = LLMServiceExtensions.ForLegalAnalysis(2000);
        var result = await _llmService.AnalyzeStructuredAsync<PriorityAnalysisResponse>(
            recommendationSummary, prompt, options, cancellationToken);

        // Appliquer les nouvelles priorités
        if (result?.PrioritizedRecommendations != null)
        {
            foreach (var priorityUpdate in result.PrioritizedRecommendations)
            {
                if (priorityUpdate.Index > 0 && priorityUpdate.Index <= recommendations.Count)
                {
                    recommendations[priorityUpdate.Index - 1].Priority = priorityUpdate.NewPriority;
                }
            }
        }

        return recommendations;
    }

    private List<DocumentRecommendationDto> ConvertToRecommendationDtos(List<RecommendationInfo> recommendations)
    {
        return recommendations.Select(r => new DocumentRecommendationDto
        {
            Id = Guid.NewGuid(),
            Type = r.Type,
            Title = r.Title,
            Description = r.Description,
            Priority = r.Priority,
            SuggestedAction = r.SuggestedAction,
            LegalBasis = r.LegalBasis,
            RelatedClauses = r.RelatedClauses ?? new List<string>()
        }).ToList();
    }

    // Classes pour la désérialisation JSON

    private class RecommendationResponse
    {
        public List<RecommendationInfo> Recommendations { get; set; } = new();
    }

    private class RecommendationInfo
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string? SuggestedAction { get; set; }
        public string? LegalBasis { get; set; }
        public List<string>? RelatedClauses { get; set; }
    }

    private class PriorityAnalysisResponse
    {
        public List<PriorityUpdate> PrioritizedRecommendations { get; set; } = new();
    }

    private class PriorityUpdate
    {
        public int Index { get; set; }
        public string NewPriority { get; set; } = string.Empty;
        public string Justification { get; set; } = string.Empty;
    }
}
