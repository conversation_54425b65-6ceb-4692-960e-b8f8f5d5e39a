import React from 'react'
import { 
  Users, 
  FolderOpen, 
  FileText, 
  Calendar,
  TrendingUp,
  Clock,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { useAuthStore } from '../store/authStore'

// Données simulées pour le dashboard
const stats = [
  {
    title: 'Clients Actifs',
    value: '24',
    change: '+12%',
    changeType: 'positive' as const,
    icon: Users
  },
  {
    title: 'Dossiers en Cours',
    value: '18',
    change: '+5%',
    changeType: 'positive' as const,
    icon: FolderOpen
  },
  {
    title: 'Documents Générés',
    value: '156',
    change: '+23%',
    changeType: 'positive' as const,
    icon: FileText
  },
  {
    title: 'Échéances à Venir',
    value: '7',
    change: '-2',
    changeType: 'neutral' as const,
    icon: Calendar
  }
]

const recentActivities = [
  {
    id: 1,
    type: 'document',
    title: 'Contrat de travail généré',
    client: 'Société ABC',
    time: 'Il y a 2 heures',
    status: 'completed'
  },
  {
    id: 2,
    type: 'case',
    title: 'Nouveau dossier créé',
    client: 'Jean Dupont',
    time: 'Il y a 4 heures',
    status: 'in_progress'
  },
  {
    id: 3,
    type: 'deadline',
    title: 'Échéance tribunal',
    client: 'Marie Martin',
    time: 'Demain 14h00',
    status: 'urgent'
  },
  {
    id: 4,
    type: 'document',
    title: 'Analyse de contrat terminée',
    client: 'Entreprise XYZ',
    time: 'Il y a 1 jour',
    status: 'completed'
  }
]

const upcomingDeadlines = [
  {
    id: 1,
    title: 'Audience tribunal de commerce',
    client: 'Société ABC',
    date: '2024-01-15',
    time: '14:00',
    priority: 'high'
  },
  {
    id: 2,
    title: 'Remise de conclusions',
    client: 'Jean Dupont',
    date: '2024-01-18',
    time: '17:00',
    priority: 'medium'
  },
  {
    id: 3,
    title: 'Signature contrat',
    client: 'Marie Martin',
    date: '2024-01-20',
    time: '10:00',
    priority: 'low'
  }
]

export function DashboardPage() {
  const { user } = useAuthStore()

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'urgent':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-50'
      case 'medium':
        return 'text-yellow-600 bg-yellow-50'
      case 'low':
        return 'text-green-600 bg-green-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Bonjour, {user?.firstName} 👋
        </h1>
        <p className="text-gray-600">
          Voici un aperçu de votre activité juridique aujourd'hui.
        </p>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                  <stat.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className={`h-4 w-4 ${
                  stat.changeType === 'positive' ? 'text-green-500' : 'text-gray-500'
                }`} />
                <span className={`ml-1 text-sm ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-gray-600'
                }`}>
                  {stat.change} ce mois
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activités récentes */}
        <Card>
          <CardHeader>
            <CardTitle>Activités Récentes</CardTitle>
            <CardDescription>
              Vos dernières actions dans LexAi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  {getStatusIcon(activity.status)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.title}
                    </p>
                    <p className="text-sm text-gray-500">
                      {activity.client} • {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                Voir toutes les activités
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Échéances à venir */}
        <Card>
          <CardHeader>
            <CardTitle>Échéances à Venir</CardTitle>
            <CardDescription>
              Vos prochains rendez-vous et deadlines
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingDeadlines.map((deadline) => (
                <div key={deadline.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {deadline.title}
                    </p>
                    <p className="text-sm text-gray-500">
                      {deadline.client}
                    </p>
                    <p className="text-xs text-gray-400">
                      {new Date(deadline.date).toLocaleDateString('fr-FR')} à {deadline.time}
                    </p>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(deadline.priority)}`}>
                    {deadline.priority === 'high' && 'Urgent'}
                    {deadline.priority === 'medium' && 'Moyen'}
                    {deadline.priority === 'low' && 'Faible'}
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                Voir l'agenda complet
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions rapides */}
      <Card>
        <CardHeader>
          <CardTitle>Actions Rapides</CardTitle>
          <CardDescription>
            Accédez rapidement aux fonctionnalités principales
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-20 flex-col space-y-2">
              <FileText className="h-6 w-6" />
              <span>Générer un Document</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Users className="h-6 w-6" />
              <span>Nouveau Client</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <FolderOpen className="h-6 w-6" />
              <span>Créer un Dossier</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
