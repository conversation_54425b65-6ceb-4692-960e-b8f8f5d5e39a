<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="FluentValidation" Version="11.9.2" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LexAI.DocumentAnalysis.Domain\LexAI.DocumentAnalysis.Domain.csproj" />
    <ProjectReference Include="..\..\..\Shared\LexAI.Shared.Application\LexAI.Shared.Application.csproj" />
  </ItemGroup>

</Project>
