﻿
namespace LexAI.Shared.Application.Extensions
{
    /// <summary>
    /// Extensions utiles pour le service d'embedding
    /// </summary>
    public static class EmbeddingServiceExtensions
    {
        /// <summary>
        /// Calcule la similarité cosinus entre deux embeddings
        /// </summary>
        public static double CalculateCosineSimilarity(float[] embedding1, float[] embedding2)
        {
            if (embedding1.Length != embedding2.Length)
                throw new ArgumentException("Embeddings must have the same dimensions");

            double dotProduct = 0;
            double norm1 = 0;
            double norm2 = 0;

            for (int i = 0; i < embedding1.Length; i++)
            {
                dotProduct += embedding1[i] * embedding2[i];
                norm1 += embedding1[i] * embedding1[i];
                norm2 += embedding2[i] * embedding2[i];
            }

            if (norm1 == 0 || norm2 == 0)
                return 0;

            return dotProduct / (Math.Sqrt(norm1) * Math.Sqrt(norm2));
        }

        /// <summary>
        /// Normalise un embedding
        /// </summary>
        public static float[] NormalizeEmbedding(float[] embedding)
        {
            var norm = Math.Sqrt(embedding.Sum(x => x * x));
            if (norm == 0) return embedding;

            return embedding.Select(x => (float)(x / norm)).ToArray();
        }

        /// <summary>
        /// Trouve les embeddings les plus similaires
        /// </summary>
        public static List<(int Index, double Similarity)> FindMostSimilar(
            float[] queryEmbedding,
            IEnumerable<float[]> embeddings,
            int topK = 10)
        {
            var similarities = embeddings
                .Select((embedding, index) => new
                {
                    Index = index,
                    Similarity = CalculateCosineSimilarity(queryEmbedding, embedding)
                })
                .OrderByDescending(x => x.Similarity)
                .Take(topK)
                .Select(x => (x.Index, x.Similarity))
                .ToList();

            return similarities;
        }

        /// <summary>
        /// Vérifie si un embedding est valide
        /// </summary>
        public static bool IsValidEmbedding(float[] embedding, int expectedDimensions = -1)
        {
            if (embedding == null || embedding.Length == 0)
                return false;

            if (expectedDimensions > 0 && embedding.Length != expectedDimensions)
                return false;

            // Vérifier qu'il n'y a pas de valeurs NaN ou infinies
            return embedding.All(x => !float.IsNaN(x) && !float.IsInfinity(x));
        }

        /// <summary>
        /// Combine plusieurs embeddings en un seul (moyenne pondérée)
        /// </summary>
        public static float[] CombineEmbeddings(IEnumerable<(float[] Embedding, double Weight)> weightedEmbeddings)
        {
            var embeddingsList = weightedEmbeddings.ToList();
            if (!embeddingsList.Any())
                throw new ArgumentException("No embeddings provided");

            var dimensions = embeddingsList.First().Embedding.Length;
            if (embeddingsList.Any(e => e.Embedding.Length != dimensions))
                throw new ArgumentException("All embeddings must have the same dimensions");

            var result = new float[dimensions];
            var totalWeight = embeddingsList.Sum(e => e.Weight);

            if (totalWeight == 0)
                throw new ArgumentException("Total weight cannot be zero");

            for (int i = 0; i < dimensions; i++)
            {
                result[i] = (float)(embeddingsList.Sum(e => e.Embedding[i] * e.Weight) / totalWeight);
            }

            return result;
        }

        /// <summary>
        /// Réduit la dimensionnalité d'un embedding (PCA simple)
        /// </summary>
        public static float[] ReduceDimensions(float[] embedding, int targetDimensions)
        {
            if (targetDimensions >= embedding.Length)
                return embedding;

            // Implémentation simple : prendre les premières dimensions
            // Dans une vraie implémentation, utiliser PCA ou t-SNE
            return embedding.Take(targetDimensions).ToArray();
        }

        /// <summary>
        /// Calcule la distance euclidienne entre deux embeddings
        /// </summary>
        public static double CalculateEuclideanDistance(float[] embedding1, float[] embedding2)
        {
            if (embedding1.Length != embedding2.Length)
                throw new ArgumentException("Embeddings must have the same dimensions");

            double sum = 0;
            for (int i = 0; i < embedding1.Length; i++)
            {
                var diff = embedding1[i] - embedding2[i];
                sum += diff * diff;
            }

            return Math.Sqrt(sum);
        }

        /// <summary>
        /// Calcule la distance de Manhattan entre deux embeddings
        /// </summary>
        public static double CalculateManhattanDistance(float[] embedding1, float[] embedding2)
        {
            if (embedding1.Length != embedding2.Length)
                throw new ArgumentException("Embeddings must have the same dimensions");

            return embedding1.Zip(embedding2, (a, b) => Math.Abs(a - b)).Sum();
        }

        /// <summary>
        /// Groupe les embeddings par similarité (clustering simple)
        /// </summary>
        public static List<List<int>> ClusterBySimilarity(
            IEnumerable<float[]> embeddings,
            double similarityThreshold = 0.8)
        {
            var embeddingsList = embeddings.ToList();
            var clusters = new List<List<int>>();
            var assigned = new bool[embeddingsList.Count];

            for (int i = 0; i < embeddingsList.Count; i++)
            {
                if (assigned[i]) continue;

                var cluster = new List<int> { i };
                assigned[i] = true;

                for (int j = i + 1; j < embeddingsList.Count; j++)
                {
                    if (assigned[j]) continue;

                    var similarity = CalculateCosineSimilarity(embeddingsList[i], embeddingsList[j]);
                    if (similarity >= similarityThreshold)
                    {
                        cluster.Add(j);
                        assigned[j] = true;
                    }
                }

                clusters.Add(cluster);
            }

            return clusters;
        }
    }

}
